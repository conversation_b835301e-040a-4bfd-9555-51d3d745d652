package com.nimble.irisservices.appcontroller;

import java.io.File;
import java.io.FileInputStream;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TimeZone;

import com.nimble.irisservices.dto.*;
import com.nimble.irisservices.entity.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;

import com.chargebee.Environment;
import com.chargebee.ListResult;
import com.chargebee.Result;
import com.chargebee.filters.enums.SortOrder;
import com.chargebee.models.CouponCode;
import com.chargebee.models.CreditNote;
import com.chargebee.models.CreditNote.ReasonCode;
import com.chargebee.models.CreditNote.Type;
import com.chargebee.models.Customer;
import com.chargebee.models.HostedPage;
import com.chargebee.models.HostedPage.CheckoutExistingRequest;
import com.chargebee.models.HostedPage.CheckoutNewRequest;
import com.chargebee.models.Invoice;
import com.chargebee.models.Plan;
import com.chargebee.models.Subscription;
import com.chargebee.models.Subscription.Status;
import com.chargebee.models.Transaction;
import com.chargebee.models.enums.CreditOptionForCurrentTermCharges;
import com.chargebee.models.enums.PauseOption;
import com.chargebee.models.enums.PaymentMethod;
import com.chargebee.models.enums.ResumeOption;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.controller.PushNotificatonController;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.job.FlexiPlanRenewal;
import com.nimble.irisservices.quartz.JobService;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IAvatarService;
import com.nimble.irisservices.service.ICancelService;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IRVCentricDetailsService;
import com.nimble.irisservices.service.IReferAndEarnService;
import com.nimble.irisservices.service.IReportServiceV4;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

import freemarker.template.Template;

@Controller
public class ChargebeeControllerApp {
	@Autowired
	IUserService userService;

	@Autowired
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;
	
	@Autowired
	IReportServiceV4 iReportServiceV4;

	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Autowired
	@Lazy
	IReferAndEarnService refService;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	IRVCentricDetailsService rvcentricServ;

	@Autowired
	@Lazy
	IChargebeeService cbService;
	
	@Autowired
	@Lazy
	JobService jobService;

	@Autowired
	@Lazy
	IAvatarService iAvaService;

	private static final Logger log = LogManager.getLogger(ChargebeeControllerApp.class);
	Helper _helper = new Helper();

	@Value("#{${supportcontactnumber}}")
	private Map<String, String> supportcontactnumber;

	@Value("#{${supportemail}}")
	private Map<String, String> supportemail;

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;
	
	@Value("${show_nextrenewal_popup}")
	private boolean show_nextrenewal_popup;	

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	@Value("${redirtPetUrl}")
	private String redirtPetUrl;

	@Value("${redirtFurbitUrl}")
	private String redirtFurbitUrl;

	@Value("${redirtVpmUrl}")
	private String redirtVpmUrl;

	@Value("${ordersuccessurl}")
	private String ordersuccessurl;

	@Value("${orderfailedurl}")
	private String orderfailedurl;

	@Value("${embedv2}")
	private boolean embedv2;

	@Value("${embedupdate}")
	private boolean embedupdate;

	@Value("${referralurl}")
	private String referralurl;

	@Value("${customerdelete}")
	private String customerdelete;

	@Value("${paymentupdateurl}")
	private String paymentupdateurl;

	@Value("#{${chargebee.addonid}}")
	private Map<String, String> activationAddonId;

	@Value("#{${chargebee.updateaddonid}}")
	private Map<String, String> updateAddonId;
	
	@Value("#{${chargebee.downgradeaddonid}}")
	private Map<String, String> downgradeAddonId;
	
	@Value("#{${chargebee.retainaddonid}}")
	private Map<String, String> retainAddonId;

	@Value("#{${chargebee.reactivationid}}")
	private Map<String, String> reactivateAddonId;

	@Value("${checkoutversion}")
	private String checkoutversion;

	@Value("${amazonlaunchpadfreetrialenable}")
	private boolean amazonLaunchPadFreeTrialEnable;

	@Value("${enable_ios_inapp_purchase}")
	private boolean enable_ios_inapp_purchase;

	@Value("${freeplan}")
	private String freeplan;

	@Value("${omitplan}")
	private String omitplan;

	@Value("${vpmplan}")
	private String vpmplan;

	@Value("${addonplan}")
	private String addonplan;

	@Value("${show_upgrade_msg}")
	private boolean show_upgrade_msg;
	
	@Value("${show_addon_button}")
	private boolean show_addon_button;

	@Value("${validation_authkey}")
	private String validation_authkey = "NA";

	@Value("#{${alert_msg}}")
	private Map<String, String> alert_msg;

	@Value("${minus_refund_amount}")
	private String minus_refund_amount;

	@Value("${refund_by_prorated}")
	private String refund_by_prorated;
	
	@Value("${waggle_merch_coupon_exp_time}")
	private int waggle_merch_coupon_exp_time;
	
	@Autowired
	ICancelService cancelService;
	
	@Autowired
	freemarker.template.Configuration templates;
	
	@Value("${days_tohandle_nextrenewal}")
	private int days_tohandle_nextrenewal;
	
	@Value("${resume_notify}")
	private int resume_notify;
	
	@Value("${priorityemail}")
	private String priorityemail;
	
	@Value("${priorityphone}")
	private String priorityphone;


	@Value("${combo_compare_4g_Img}")
	private String comboCompareImage4g;

	@Value("${flexi_plan_activation_count}")
	private String flexi_plan_activation_count;

	@Value("${vet_chat_img_url}")
	private String vetChatImageUrl;
	
	
	@Autowired
	PushNotificatonController pushNotificatonController;

	@Autowired
	private Helper helper;


    @Value("${txnservice_url}")
    private String txnservice_url;

    @Value("${allowVetChatFreeTrial}")
    private boolean allowVetChatFreeTrial;


	@RequestMapping(value = "/app/v5.0/generateAdditionalBenefits", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse generateAdditionalBenefitsV5(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestParam("period") String period,
			Authentication authentication) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		log.info("generateadditionalfeatures: " + auth);
		try {

			user = userServiceV4.verifyAuthV4("authkey", auth);

		} catch (Exception e) {
			log.error("generateadditionalfeatures: user by id : " + e.getMessage());
		}

		try {
			if (user != null) {

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
				boolean isAvail = iAvaService.checkBenefitsAvail(period); // period id
				String createdFrom = "cancel";
				
				if (isAvail) {
					boolean benefits_created = crService.checkAdditionalBenifitsCreated(user.getEmail(),
							Integer.parseInt(period));

					log.info("benefits_created:" + benefits_created);

					if (!benefits_created)
						async.generateAddiBenefits(user, period, createdFrom);

					response.put("iscreated", true);
					response.put("Status", 1);
					response.put("Msg", "Success");

				} else {
					response.put("iscreated", false);
					response.put("Status", 0);
					response.put("Msg", "Additional benefits not applicable");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			// e.printStackTrace();
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");
			log.error("generateadditionalfeatures:" + e.getLocalizedMessage());
		}
		return response;
	}
	
	@RequestMapping(value = "/app/v5.0/updatesubscriptionplanV2", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateSubscriptionPlanV5(@RequestHeader HttpHeaders header, @RequestParam("planid") long planid,
			@RequestParam("periodid") long periodid, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestParam("plan_ver") String plan_ver,
			@RequestParam("cb_subid") String cb_subid, Authentication authentication,
			@RequestParam(value = "freetrial", defaultValue = "false", required = false) boolean freetrial,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "subsAction", defaultValue = "update", required = false) String subsAction,
			@RequestParam(value = "req_ver", defaultValue = "V1", required = false) String req_ver,
			@RequestParam(value = "cur_planid", defaultValue = "0", required = false) String cur_PlanId,
			@RequestParam(value = "cur_periodid", defaultValue = "0", required = false) String cur_PeriodId,
			@RequestParam(value = "resumeDate", defaultValue = "NA", required = false) String resumeDate,
			@RequestParam(value = "feedbackid", defaultValue = "0", required = false) String feedbackid,
			@RequestParam(value = "review", defaultValue = "NA", required = false) String review,
			@RequestParam(value = "timezone", defaultValue = "+00:00", required = false) String timezone) {
		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		UserV4 user = null;
		log.info("Entered into updatesubscriptionplanV2");
		try {

			String auth = header.getFirst("auth");
			log.info("updateSubscriptionPlanV2 :Auth: " + auth + " cb_subid:" + cb_subid);

			user = userServiceV4.verifyAuthV3("authkey", auth);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			long curPlanId = Long.valueOf( cur_PlanId );
			long curPeriodId = Long.valueOf( cur_PeriodId );
			
			if (user != null) {
				log.info("user_id : "+ user.getId() +":: cur_planid : "+ curPlanId+" :: cur_periodid : "+curPeriodId+" :: upgrade_planid : "+ planid+ " :: upgrade_periodid : "+ periodid+" :: plan_ver : "+ plan_ver);
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					String cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(),
							user.getEmail(), user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
					if(cbId.equalsIgnoreCase("NA")||cbId.isEmpty()){
						response.put("Status", 0);
						response.put("Msg", "Action failed. Contact our support team to proceed.");
						return response;
					}
				}

				// Reactivation - resume paused subscription
				
				if (!cb_subid.isEmpty() && subsAction.equalsIgnoreCase("reactivate")) {
					try {
						
						Result res = Subscription.retrieve(cb_subid).request();
						
						if (!cb_subid.substring(0, 3).equalsIgnoreCase("RE-")) {
							Subscription subscription = res.subscription();
							String status = subscription.status().toString();
							if (status.equalsIgnoreCase("paused")) {
								Subscription.resume(cb_subid).resumeOption(ResumeOption.IMMEDIATELY).request();
							} else {
								Subscription.removeScheduledPause(cb_subid).request();
							}
						}
						 
						try {
							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
							Date resumeDateFormate = sdf.parse(resumeDate);
							
							SimpleDateFormat dateformate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							
							crService.saveOrUpdatePauseHistory(cb_subid,user.getId(),dateformate.format(resumeDateFormate),0,feedbackid,review);
						} catch (Exception e) {
							response.put("Status", 0);
							response.put("Return Time", System.currentTimeMillis());
							response.put("Msg", "Pause history save failed.");
							return response;
						}
						
						if (cb_subid.substring(0, 3).equalsIgnoreCase("RE-")) {
							try {
								Properties prop = new Properties();
								File file = ResourceUtils.getFile("classpath:iris3.properties");
								prop.load(new FileInputStream(file));

								String to_address = prop.getProperty("to_address");
								String cc_address = prop.getProperty("cc_address");
								String bcc_address = prop.getProperty("bcc_address");

								String mailSub = "Reg Recharge user Resume Immediately Subscription : " + user.getUsername();
								String mailContent = "<p>Hi Team,</p>" + "<p>Find the user gateway immediately resume subscription request</p>";
								mailContent += "<br /><p>Email                : " + user.getEmail() + "</p>";
								mailContent += "<br /><p>Recharge customer ID : " + user.getRecharge_custid() + "</p>";
								mailContent += "<br /><p>Chargebee Id         : " + user.getChargebeeid() + "</p>";
								mailContent += "<br /><p>Gateway Id           : " + cb_subid + "</p>";
								mailContent += "<br /><p>Reason               : " + review + "</p>";

								mailContent = mailContent + "<br><br>Thanks,<br> Irisservice ";
								async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);
							} catch (Exception e) {
								log.error("Error in email for resume subscription:" + e.getMessage());
							}

						} else {
						
						try {
							File file = ResourceUtils.getFile("classpath:iris3.properties");
							Properties prop = new Properties();
							prop.load(new FileInputStream(file));

							String toAddr = prop.getProperty("to_address");
							String ccAddr = prop.getProperty("cc_address");
							String bccAddr = prop.getProperty("bcc_address");

							String sub = "CB Reactivated Subscription: " + user.getUsername();
							String mailmsg = "Hi Team, " + "<br />" + ""
									+ " for the below customer. <br /><b> Username :</b>" + user.getUsername()
									+ "<br /><b> CB customer ID:</b> " + user.getChargebeeid()
									+ "<br /><b> CB Subscription ID:</b> " + cb_subid;
							
							mailmsg = mailmsg + "<br><br>Thanks,<br> Irisservice ";
							
							async.SendEmail_SES(toAddr, ccAddr, bccAddr, sub, mailmsg);
							
							JSendNotification sendNotification  = new JSendNotification();
							
								
								long[] user_id = { user.getId() };
								String[] emailArr = { user.getEmail() };
								
								sendNotification.setPushNotificationId( resume_notify );
								sendNotification.setUserID(user_id);
								sendNotification.setEmaiID(emailArr);
								
								pushNotificatonController.sendNotificationstoplanpage( auth, sendNotification);	
							
							
						} catch (Exception e) {
							log.error("Error in email for resume subscription:" + e.getMessage());
						}
						}
						response.put("checkOutURL", "NA");
						response.put("Status", 1);
						response.put("Return Time", System.currentTimeMillis());
						response.put("Msg", "Plan resumed successfully");
						return response;
					} catch (Exception e) {
						response.put("checkOutURL", "NA");
						response.put("Status", 0);
						response.put("Return Time", System.currentTimeMillis());
						response.put("Msg", "Reactivation failed. Please contact support.");
						return response;
					}
				}
				
				if (!cb_subid.isEmpty() && subsAction.equalsIgnoreCase("pause") && !resumeDate.equalsIgnoreCase("NA")) {
					try {
						
						if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
							timezone = "+" + timezone;
			            
			            Date convertedDate = _helper.timeZoneConverter( "yyyy-MM-dd HH:mm:ss",timezone, "+00:00", resumeDate + " 00:00:00");
			            
			            Timestamp resumeTimestamp = new Timestamp(convertedDate.getTime());
						if (!cb_subid.substring(0, 3).equalsIgnoreCase("RE-")) {
							Result res = Subscription.pause(cb_subid).pauseOption(PauseOption.END_OF_TERM)
									.resumeDate(resumeTimestamp).request();
						}
						try {
							SimpleDateFormat dateformate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							crService.saveOrUpdatePauseHistory(cb_subid,user.getId(),dateformate.format(resumeTimestamp),1,feedbackid,review);
						} catch (Exception e) {
							response.put("Status", 0);
							response.put("Msg", "Pause history save failed.");
							return response;
						}
						
						if (cb_subid.substring(0, 3).equalsIgnoreCase("RE-")) {
							try {
								Properties prop = new Properties();
								File file = ResourceUtils.getFile("classpath:iris3.properties");
								prop.load(new FileInputStream(file));

								String to_address = prop.getProperty("to_address");
								String cc_address = prop.getProperty("cc_address");
								String bcc_address = prop.getProperty("bcc_address");

								String mailSub = "Reg Recharge user Pause Subscription : " + user.getUsername();
								String mailContent = "<p>Hi Team,</p>" + "<p>Find the user gateway pause subscription request</p>";
								mailContent += "<br /><p>Email                : " + user.getEmail() + "</p>";
								mailContent += "<br /><p>Recharge customer ID : " + user.getRecharge_custid() + "</p>";
								mailContent += "<br /><p>Chargebee Id         : " + user.getChargebeeid() + "</p>";
								mailContent += "<br /><p>Gateway Id           : " + cb_subid + "</p>";
								mailContent += "<br /><p>Reason               : " + review + "</p>";

								mailContent = mailContent + "<br><br>Thanks,<br> Irisservice ";
								async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);
							} catch (Exception e) {
								log.error("Error in email for resume subscription:" + e.getMessage());
							}

						} else {
						
						try {
							File file = ResourceUtils.getFile("classpath:iris3.properties");
							Properties prop = new Properties();
							prop.load(new FileInputStream(file));

							String toAddr = prop.getProperty("to_address");
							String ccAddr = prop.getProperty("cc_address");
							String bccAddr = prop.getProperty("bcc_address");

							String sub = "CB Pause Subscription: " + user.getUsername();
							String mailmsg = "Hi Team, " + "<br />" + ""
									+ " for the below customer. <br /><b> Username :</b>" + user.getUsername()
									+ "<br /><b> CB customer ID:</b> " + user.getChargebeeid()
									+ "<br /><b> CB Subscription ID:</b> " + cb_subid;
							
							mailmsg = mailmsg + "<br><br>Thanks,<br> Irisservice ";
							
							async.SendEmail_SES(toAddr, ccAddr, bccAddr, sub, mailmsg);
							
						} catch (Exception e) {
							log.error("Error in email for resume subscription:" + e.getMessage());
						}
						}
						response.put("checkOutURL", "NA");
						response.put("Status", 1);
						response.put("Return Time", System.currentTimeMillis());
						SimpleDateFormat df = new SimpleDateFormat("dd-MMM-yyyy");
						response.put("Msg", "Your plan will resume on \n"+df.format(resumeTimestamp));
						
						return response;
					} catch (Exception e) {
						response.put("checkOutURL", "NA");
						response.put("Status", 0);
						response.put("Return Time", System.currentTimeMillis());
						response.put("Msg", "Pause failed. Please contact support.");
						return response;
					}
				}

				int addonStatus = 0;// #0 - free to paid #1-paid to paid #2- reactivation
				// boolean disableUpgrade = false; //false - upgrading from one paid to another
				// paid
				// true - upgrading from free to paid
				String activationId = "";
				String reactivationId = "";
				String upgradeId = "";
				String downgradeId = "";
				String country = user.getCountry().toUpperCase();
				country = "US";

				String[] cbPlanAndTrialPeriod = crService.getChargebeePlanById(planid, periodid, country);

				activationId = activationAddonId == null ? "setup_charges" : activationAddonId.get(country);
				reactivationId = reactivateAddonId == null ? "reactivation-charges-onetime"
						: reactivateAddonId.get(country);
				upgradeId = updateAddonId == null ? "upgrade_charges" : updateAddonId.get(country);
				downgradeId = downgradeAddonId == null ? "downgrade_charges" : downgradeAddonId.get(country);

				String cb_plan = cbPlanAndTrialPeriod[0];
				int freeTrialPeriod = Integer.parseInt(cbPlanAndTrialPeriod[1]);
				String cb_coupon_id = cbPlanAndTrialPeriod[2];
				String cb_addon_id = cbPlanAndTrialPeriod[3];
				int plan_to_period_id = Integer.parseInt( cbPlanAndTrialPeriod[4] );
				int price = 0;

				int cur_plan_to_period_id = 0;
				boolean lowerPlan = false;
				JPlanToUpgrade planToUpgrade = null;
				if( req_ver.equalsIgnoreCase("v2") ) {
					String[] curCBPlanAndTrialPeriod = crService.getChargebeePlanById(curPlanId, curPeriodId, country);
					cur_plan_to_period_id = Integer.parseInt( curCBPlanAndTrialPeriod[4] );
					planToUpgrade = crService.getPlanToUpgrade(plan_to_period_id, cur_plan_to_period_id);
				}
				
				List<String> addonIdList = Arrays.asList(cb_addon_id.split(","));
				String oneTimeAddonId = "";
				String subStatus = "";
				Subscription subscription = null;

				if (cb_subid.isEmpty())
					cb_subid = "NA";

				if (!cb_plan.equalsIgnoreCase("NA")) {
					if (!cb_subid.equalsIgnoreCase("NA")) {

						Result result = Subscription.retrieve(cb_subid).request();
						subscription = result.subscription();
						subStatus = subscription.status().name();
						
						if(cb_plan.equalsIgnoreCase(subscription.planId())) {
							result = Plan.retrieve(cb_plan).request();
							Plan plan = result.plan();
							price = plan.price();
						}
//
//						if(subStatus.equalsIgnoreCase("PAUSED")) {
//							try {
//								Result res = Subscription.resume(subscription.id()).resumeOption(ResumeOption.IMMEDIATELY).request();
//								response.put("checkOutURL", "NA");
//								response.put("Status", 1);
//								response.put("Msg", "Subscription reactivated successfully!");
//								return response;
//							}catch (Exception e) {
//								response.put("checkOutURL", "NA");
//								response.put("Status", 0);
//								response.put("Msg", "Reactivation process is failed");
//								return response;
//							}
//						}else 
						if (freeplan.contains(subscription.planId())) {
							addonStatus = 0; // activation
						} else if (!freeplan.contains(subscription.planId())
								// && !vpmplan.contains(subscription.planId()) &&
								// !addonplan.contains(subscription.planId())
								&& !(subStatus.equalsIgnoreCase("CANCELLED"))) {
							addonStatus = 1; // upgrade
						} else if (!freeplan.contains(subscription.planId())
								// && !vpmplan.contains(subscription.planId()) &&
								// !addonplan.contains(subscription.planId())
								&& (subStatus.equalsIgnoreCase("CANCELLED"))) {
							addonStatus = 2; // re-activation
						}
					} else {
						addonStatus = 0;
					}
					
					if( req_ver.equalsIgnoreCase("V2") && planToUpgrade == null && !cb_subid.equalsIgnoreCase("NA") && periodid < curPeriodId) {
						addonStatus = 3;
					}

					switch (addonStatus) {
					case 0:
						oneTimeAddonId = activationId;
						break;

					case 1:
						oneTimeAddonId = upgradeId;
						break;

					case 2:
						oneTimeAddonId = reactivationId;
						break;
						
					case 3:
						oneTimeAddonId = downgradeId;
						break;
					}					
					
//					int days = 0;
//					int daysBetween = 0;
//					OrderMappingDetails order = null;
					Timestamp trialEnd = null;

					// this for customer merge
					HashMap<String, Object> metaData = new HashMap<String, Object>();
					metaData.put("userid", user.getId());
					metaData.put("chargebeeid", user.getChargebeeid());
					String cancel_subid = "NA";
					String order_id = "NA";
					String orderchannel = "RV";
					CheckoutNewRequest checkoutNewRequest;
					CheckoutExistingRequest checkoutExitingRequest;
					Result res;
					String priceHighCoupon="NA";
					
//Currently not used
//					if (amazonLaunchPadFreeTrialEnable) {
//						order = userService.getOrderMappingByUser(user.getId());
//					}
//					if (order != null) {
//						orderchannel = order.getOrderchannel();
//						days = (int) userService.getCreditAmountBySKU(order.getExternalsku());
//
//						if (days > 0) {
//							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//							Date todayDate = sdf.parse(sdf.format(new Date()));
//
//							Timestamp orderdate = order.getOrderdate();
//							log.info("orderdate: " + orderdate);
//							Date expirydate = new Date(orderdate.getTime());
//
//							Calendar cal = Calendar.getInstance();
//							cal.setTime(expirydate);
//							cal.add(Calendar.DATE, days);
//
//							expirydate = cal.getTime();
//
//							long difference = expirydate.getTime() - todayDate.getTime();
//							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
//
//							if (daysBetween > 0) {
//								String trialendDt = sdf.format(expirydate);
//								// tialendDt =
//								// tialendDt.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.ENDTIME);
//								trialEnd = Timestamp.valueOf(trialendDt);
//							} else
//								daysBetween = 0;
//						}
//					}
//					// String addonId = "setup_charge";
//					if (daysBetween > 0) {
//						log.info("updateSubscriptionPlanV2 : daysBetween > 0");
//						if (!cb_subid.equalsIgnoreCase("NA"))
//							cancel_subid = cb_subid;
//
//						order_id = String.valueOf(order.getId());
//
//						metaData.put("order_id", order_id);
//						metaData.put("cancel_subid", cancel_subid);
//						metaData.put("regarding", orderchannel);
//
//						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
//
//						checkoutNewRequest = HostedPage.checkoutNew().subscriptionTrialEnd(trialEnd)
//								.subscriptionPlanId(cb_plan).subscriptionPlanQuantity(1).addonId(0, oneTimeAddonId)
//								.addonQuantity(0, 1).addonBillingCycles(0, 1).customerFirstName(user.getFirstname())
//								.customerLastName(user.getLastname()).customerEmail(user.getEmail())
//								.customerPhone(user.getMobileno());
//
//						if (!cb_coupon_id.equalsIgnoreCase("NA")) {
//							checkoutNewRequest.subscriptionCoupon(cb_coupon_id);
//						}
//
//						if (!cb_addon_id.equalsIgnoreCase("NA")) {
//							int i = 0;
//							// index - 0 updateAddonId/activation addon. remaining addon starts with 1
//							for (int j = 0; j < addonIdList.size(); j++) {
//								i = j + 1;
//								checkoutNewRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1);
//							}
//						}
//						res = checkoutNewRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString())
//								.embed(embedupdate).request();
//
//					} else 
					if (!cb_subid.equalsIgnoreCase("NA") && freetrial && freeTrialPeriod > 0) {// &&
																								// !disableUpgrade
						log.info(
								"updateSubscriptionPlanV2 : !cb_subid.equalsIgnoreCase(\"NA\")&& freetrial && freeTrialPeriod >0");
						metaData.put("order_id", order_id);
						metaData.put("cancel_subid", cancel_subid);
						metaData.put("regarding", orderchannel);

						Timestamp trialEndDate = new Timestamp(
								Instant.now().plus(freeTrialPeriod, ChronoUnit.DAYS).toEpochMilli());

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);

						if (!subStatus.equalsIgnoreCase("CANCELLED") || freeplan.contains(subscription.planId())) {
							Result res1 = Subscription.cancel(cb_subid)
									.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.PRORATE)
									.request();
						}

						checkoutExitingRequest = HostedPage.checkoutExisting().subscriptionId(cb_subid)
								.subscriptionPlanId(cb_plan).addonId(0, oneTimeAddonId).addonQuantity(0, 1)
								.addonBillingCycles(0, 1).replaceAddonList(true).redirectUrl(redirtPetUrl)
								.passThruContent(jsonObj.toString()).embed(embedupdate).forceTermReset(true)
								.subscriptionTrialEnd(trialEndDate);

						if(price>0) {
							checkoutExitingRequest.subscriptionPlanUnitPrice(price);
						}
						
						if (!cb_coupon_id.equalsIgnoreCase("NA")) {
							checkoutExitingRequest.subscriptionCoupon(cb_coupon_id);
						}

						if (!cb_addon_id.equalsIgnoreCase("NA")) {
							int i = 0;
							for (int j = 0; j < addonIdList.size(); j++) {
								i = j + 1;
								checkoutExitingRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1);
							}
						}

						res = checkoutExitingRequest.request();

					} else if (!cb_subid.equalsIgnoreCase("NA")) {
						log.info("updateSubscriptionPlanV2 : !cb_subid.equalsIgnoreCase(\"NA\")");

						metaData.put("order_id", order_id);
						metaData.put("cancel_subid", cancel_subid);
						metaData.put("regarding", orderchannel);

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
//n
						checkoutExitingRequest = HostedPage.checkoutExisting().subscriptionId(cb_subid)
								.subscriptionPlanId(cb_plan).addonId(0, oneTimeAddonId).addonQuantity(0, 1)
								.addonBillingCycles(0, 1).replaceAddonList(true).redirectUrl(redirtPetUrl)
								.passThruContent(jsonObj.toString()).embed(embedupdate).forceTermReset(true);
						if(price>0) {
							checkoutExitingRequest.subscriptionPlanUnitPrice(price);
						}
						
						if (!cb_coupon_id.equalsIgnoreCase("NA")) {
							checkoutExitingRequest.subscriptionCoupon(cb_coupon_id);
						}

						if (!cb_addon_id.equalsIgnoreCase("NA")) {
							int i = 0;
							for (int j = 0; j < addonIdList.size(); j++) {
								i = j + 1;
								checkoutExitingRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1);
							}
						}

						res = checkoutExitingRequest.request();

					} else {
						log.info("updateSubscriptionPlanV2 : none of the if case matched :inside else case block");

						metaData.put("order_id", order_id);
						metaData.put("cancel_subid", cancel_subid);
						metaData.put("regarding", orderchannel);

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
						checkoutNewRequest = HostedPage.checkoutNew().subscriptionPlanId(cb_plan)
								.subscriptionPlanQuantity(1).addonId(0, oneTimeAddonId).addonQuantity(0, 1)
								.addonBillingCycles(0, 1).customerId(user.getChargebeeid());

						if (freeTrialPeriod > 0) {
							Timestamp trialEndDate = new Timestamp(
									Instant.now().plus(freeTrialPeriod, ChronoUnit.DAYS).toEpochMilli());
							checkoutNewRequest.subscriptionTrialEnd(trialEndDate);
						}

						if (!cb_coupon_id.equalsIgnoreCase("NA")) {
							checkoutNewRequest.subscriptionCoupon(cb_coupon_id);
						}

						if (!cb_addon_id.equalsIgnoreCase("NA")) {
							int i = 0;
							for (int j = 0; j < addonIdList.size(); j++) {
								i = j + 1;
								checkoutNewRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1);
							}
						}

						res = checkoutNewRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString())
								.embed(embedupdate).request();

					}

					HostedPage hostedPage = res.hostedPage();

					response.put("checkOutURL", hostedPage.url());
					response.put("Status", 1);
					response.put("Msg", "Success");

				} else {
					response.put("Status", 0);
					response.put("Msg", "chargebee plan id not available in DB");
				}
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Failed to update subscription");
			log.error("update subscriptionplan:" + e.getMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "/app/v5.0/updatesubscriptioncoupon", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updatesubscriptioncoupon(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("planid") long planid,@RequestParam("periodid") long periodid,
			@RequestParam("os") String os,@RequestParam("app_ver") String app_ver,
			@RequestParam("cb_subid") String cb_subid,@RequestParam("cancel_type") int cancel_restype,
			@RequestParam(value = "confirm_refund", defaultValue = "false", required = false) boolean confirm_refund) {
		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		UserV4 user = null;
		try {

			String auth = header.getFirst("auth");
			log.info("updatesubscriptioncoupon :Auth: " + auth + " cb_subid:" + cb_subid);

			user = userServiceV4.verifyAuthV3("authkey", auth);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (user != null) {
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					String cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(),
							user.getEmail(), user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}

				String country = user.getCountry().toUpperCase();
				country = "US";
				String confirm_txt = "";
				String checkouturl = "";
				boolean old_flow = false;

				int newplan_id = 0;
				String priceHighCoupon = "NA";
				String one_device = "3,15,16,17,19,23,24,30,32";
				String two_device = "4,5,25,31";
				String three_device = "7,26";
				String four_device = "8,27";
				String five_device = "6,9,11,13,18,21,28";
				String popup_msg1 = "";
				String popup_msg2 = "";
				String popup_msg3 = "";

				if (one_device.contains(String.valueOf(planid))) {
					newplan_id = 36; // wag1
				} else if (two_device.contains(String.valueOf(planid))) {
					newplan_id = 37;// wag2
				} else if (three_device.contains(String.valueOf(planid))) {
					newplan_id = 38;// wag3
				} else if (four_device.contains(String.valueOf(planid))) {
					newplan_id = 39;// wag4
				} else if (five_device.contains(String.valueOf(planid))) {
					newplan_id = 40;// wag5
				} else
					newplan_id = (int) planid;

				String[] cbPlanAndTrialPeriod = crService.getChargebeePlanById(newplan_id, periodid, country);

				String cb_plan = cbPlanAndTrialPeriod[0];
				String cb_addon_id = cbPlanAndTrialPeriod[3];

				List<String> addonIdList = Arrays.asList(cb_addon_id.split(","));
				String oneTimeAddonId = retainAddonId == null ? "retain_charges" : retainAddonId.get(country);
				Subscription subscription = null;
				boolean refund = false;

				if (cb_subid.isEmpty())
					cb_subid = "NA";

				if (!cb_plan.equalsIgnoreCase("NA")) {

					// this for customer merge
					HashMap<String, Object> metaData = new HashMap<String, Object>();
					metaData.put("userid", user.getId());
					metaData.put("chargebeeid", user.getChargebeeid());

					CheckoutExistingRequest checkoutExitingRequest;
					Result res;

					// Yearly & 2 yearly customers - 40% discount on current plan Half yearly
					// customers LTV - 20% discount Quaterly plan - 15% discount when they try to
					// cancel

					int percent = 0;
					if (cancel_restype == 2 && periodid > 1) {
						String coupon = cbPlanAndTrialPeriod[5];
						percent = Integer.parseInt(cbPlanAndTrialPeriod[6]);

						ListResult result = CouponCode.list().couponId().is(coupon).status()
								.is(com.chargebee.models.CouponCode.Status.NOT_REDEEMED).limit(1).request();
						for (ListResult.Entry entry : result) {
							CouponCode couponCode = entry.couponCode();
							priceHighCoupon = couponCode.code();
						}
						// TODO: need to check coupon availability
					}

					if (!cb_subid.equalsIgnoreCase("NA")) {
						log.info("updateSubscriptionPlanV2 : !cb_subid.equalsIgnoreCase(\"NA\")");

						ListResult res1 = com.chargebee.models.Subscription.list().id().is(cb_subid).request();
						Subscription subscrip = null;

						for (ListResult.Entry subs : res1) {
							subscrip = subs.subscription();
						}
						int invoice_due = 0;
						String msg = "";
						String emailMsg = "";
						// String cancel_type = "NA";
						// String curr_code = "NA";
						// double exchange_rate = 1;
						double balence_amount = 0;
						String cancelled_at = "1753-01-01 00:00:00";

						SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						sdf.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
						Date curStart = sdf.parse(sdf.format(subscrip.currentTermStart().getTime()));
						Date today = sdf.parse(new Helper().getCurrentTimeinUTC());
						Date nextRenewDate = sdf.parse(sdf.format(subscrip.nextBillingAt().getTime()));
						long difference = today.getTime() - curStart.getTime();
						double daysused = Math.ceil((float) ((float) difference / (float) (1000 * 60 * 60 * 24)));
						String subStatus = subscrip.status().toString();

						msg = "Your offer has been applied.";
						if (periodid == 1) {
							old_flow = true;
						} else if (daysused <= 7) {

							ListResult invoiceList = Invoice.list().customerId().is(user.getChargebeeid())
									.subscriptionId().is(subscrip.id()).sortByDate(SortOrder.DESC).limit(1).request();

							if (!invoiceList.isEmpty()) {
								for (ListResult.Entry inv : invoiceList) {
									JSONObject invResponse = new JSONObject();
									invResponse = new JSONObject(inv.invoice().toJson());

									// exchange_rate =
									// Double.parseDouble(invResponse.get("exchange_rate").toString());
									// curr_code = inv.invoice().currencyCode();
									// Todo: need to take plan amt for prorate calculation

									double amount_paid = inv.invoice().amountPaid();
									double used_amount = 0;
									// boolean refundByProrated = Boolean.parseBoolean(refund_by_prorated);

									if (amount_paid > 0) {
										// if (refundByProrated) {
										long daysDiff = nextRenewDate.getTime() - curStart.getTime();
										double totalSubDays = Math.ceil((daysDiff / (1000 * 60 * 60 * 24)));
										used_amount = Math.round(((amount_paid / totalSubDays) * daysused) / 100) * 100;

//												} else
//													refund_minus_amount = Integer.parseInt(minus_refund_amount);

										// new logic

										if (amount_paid < used_amount) {
											old_flow = true;
											log.info("Amt paid is 0. last invoice amt is 0");
											// already coupon applied
										} else {
											balence_amount = (amount_paid - used_amount);
											float finalprice = 0;
											Plan plan = null;
											try {
												Result planResult = Plan.retrieve(cb_plan).request();
												plan = planResult.plan();

											} catch (Exception e) {
												log.error("Error While fetch PlanId:" + e.getMessage());
											}

											if (plan != null) {
												float planPrice = (float) plan.price();

												float discountAmount = (float) (((float) (planPrice * percent) / 100));

												finalprice = planPrice - discountAmount;
											}

											if (balence_amount > finalprice) {
												int refund_amount = (int) (balence_amount - finalprice);
												NumberFormat formatter = new DecimalFormat("##.##");

												// refund to cx bank accnt refund = balence_amount - finalprice
												popup_msg1 = "Save " + percent + "%";
												popup_msg2 = " on your plan! Eligible refunds return to your original payment";
												msg = "The refund will be processed in 24-48 hours.";
												emailMsg = formatter.format(refund_amount / 100) + "";
												refund = true;
												old_flow = false;
												confirm_txt = "Balance will be credited to your account within 3 to 5 Biz days.";
												popup_msg3 = "You've been Upgraged to new plan";
												
												if (planid == newplan_id)
													popup_msg3 = "NA";
												
												// On cx confirmation refund and plan change will happen
												if (refund && confirm_refund) {
													msg = "Waggle Subscription Updated. The refund will be processed in 24-48 hours.";
													refund = true;
													if (planid == newplan_id) {
														popup_msg3 = "NA";
														try {
															Result result = Invoice.refund(inv.invoice().id())
																	.refundAmount(refund_amount)
																	.creditNoteReasonCode(
																			ReasonCode.SERVICE_UNSATISFACTORY)
																	.request();
															Invoice invoice = result.invoice();
														} catch (Exception e) {
															refund = false;
															response.put("Refund", refund);
															response.put("Error", e.getLocalizedMessage());
															log.error("cancelsubplan : refund exception : "
																	+ e.getLocalizedMessage());
														}
													} else {
														Result subscripRes = Subscription.update(cb_subid)
																.forceTermReset(true).planId(cb_plan)
																.couponIds(priceHighCoupon).invoiceImmediately(true)
																.prorate(true).endOfTerm(false).request();
														subscrip = subscripRes.subscription();
														nextRenewDate = sdf.parse(sdf.format(subscrip.nextBillingAt().getTime()));
														Thread.sleep(500);
														//Customer cxObj = subscripRes.customer();
														CreditNote cr = subscripRes.creditNote();
														
//														if(!cxObj.balances().isEmpty()) {
//															Balance bal = cxObj.balances().get(0);
//															refund_amount = bal.refundableCredits();
//														}else 
//															refund = false;
														
														if (cr == null) {															
															ListResult result = CreditNote.list().limit(1).customerId()
																	.is(user.getChargebeeid()).subscriptionId()
																	.is(cb_subid).type().is(Type.REFUNDABLE)
																	.sortByDate(SortOrder.DESC).request();
															for (ListResult.Entry entry : result) {
																cr = entry.creditNote();
																if (cr == null)
																	refund = false;
																else
																	refund_amount=cr.amountAvailable();
															}
														}
														
														if(refund) {
															Result result = CreditNote.recordRefund(cr.id())
																	.comment("Refunding for subscription cancelled.")
																	.transactionAmount(refund_amount)
																	.transactionPaymentMethod(PaymentMethod.BANK_TRANSFER)
																	.transactionDate(new Timestamp(System.currentTimeMillis()))
																	.request();
															cr = result.creditNote();
//															int resCode = issueCBRefund(cr.id(),refund_amount);
//															if(resCode != 200) {
//																
//																refund = false;
//																response.put("Refund", refund);
//																response.put("Error", "Refund failed");
//															}
														}														

													}
													UserRetained urObj = crService
															.getUserRetainedByUserId(user.getId());

													if (urObj == null)
														urObj = new UserRetained(user.getId(), priceHighCoupon,
																sdf.format(nextRenewDate),
																Long.parseLong(cbPlanAndTrialPeriod[4]), true,
																new Helper().getCurrentTimeinUTC(), 1);
													else {
														urObj.setApplied_count(urObj.getApplied_count() + 1);
														urObj.setCoupon_id(priceHighCoupon);
														urObj.setNext_renewal_date(sdf.format(nextRenewDate));
														urObj.setPlan_to_period_id(
																Long.parseLong(cbPlanAndTrialPeriod[4]));
														urObj.setActivated(true);
														urObj.setUpdated_on(new Helper().getCurrentTimeinUTC());
													}
													crService.saveOrUpdateUserRetained(urObj);

													// sending cx email
													File file = ResourceUtils.getFile("classpath:iris3.properties");
													Properties prop = new Properties();
													prop.load(new FileInputStream(file));

													String toAddr = prop.getProperty("to_address");
													String ccAddr = prop.getProperty("cc_address");
													String bccAddr = prop.getProperty("bcc_address");

													// Internal mail
													if(!refund) {
														emailMsg = emailMsg + "<br /><b> Refund failed. Pls verify in CB portal and process refund manually.</b> ";
													}
													String sub = "CB Retained Refund for Subscription ";
													String mailmsg = "Hi Team, " + "<br />"
															+ " Pls find the refund details for the below customer.<br/> <br /><b> Username :</b>"
															+ user.getUsername() + "<br /><b> CB customer ID:</b> "
															+ user.getChargebeeid()
															+ "<br /><b> CB Subscription ID:</b> " + cb_subid
															+ "<br /><b> CB Refund Amount:</b> " + emailMsg;

													async.SendEmail_SES(toAddr, ccAddr, bccAddr, sub, mailmsg);

													if(refund) {
													// Customer mail
													Template emailTemplate = (Template) templates
															.getTemplate("PartialRefundEmail.ftl");
													Map<String, String> emailParams = new HashMap<>();
													sub = "Confirmation of Waggle Pet Monitor Subscription Refund";

													emailParams.put("Name", String.valueOf(user.getFirstname()));
													emailParams.put("Year",
															String.valueOf(Calendar.getInstance().get(Calendar.YEAR)));

													ResponseEntity<String> newEmailContent = ResponseEntity
															.ok(FreeMarkerTemplateUtils.processTemplateIntoString(
																	emailTemplate, emailParams));

													mailmsg = newEmailContent.getBody();

													async.SendEmail_SES(user.getEmail(), null, bccAddr, sub, mailmsg);
													}
												}
											} else {
												com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(
														metaData);
												checkoutExitingRequest = HostedPage.checkoutExisting()
														.subscriptionId(cb_subid).subscriptionPlanId(cb_plan)
														.addonId(0, oneTimeAddonId).addonQuantity(0, 1)
														.addonBillingCycles(0, 1).replaceAddonList(true)
														.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString())
														.embed(embedupdate).forceTermReset(true);

												if (!priceHighCoupon.equalsIgnoreCase("NA") && cancel_restype == 2) {
													checkoutExitingRequest.subscriptionCoupon(priceHighCoupon);
												}

												if (!cb_addon_id.equalsIgnoreCase("NA")) {
													int i = 0;
													for (int j = 0; j < addonIdList.size(); j++) {
														i = j + 1;
														checkoutExitingRequest.addonId(i, addonIdList.get(j))
																.addonQuantity(i, 1);
													}
												}

												res = checkoutExitingRequest.request();
												checkouturl = res.hostedPage().url();
												old_flow = false;
												popup_msg1 = "Save " + percent + "%";
												popup_msg2 = " on your plan! Eligible refunds return to your original payment";
												popup_msg3 = "You've been Upgraged to new plan";

												UserRetained urObj = crService.getUserRetainedByUserId(user.getId());

												if (urObj == null)
													urObj = new UserRetained(user.getId(), priceHighCoupon,
															sdf.format(nextRenewDate),
															Long.parseLong(cbPlanAndTrialPeriod[4]), false,
															new Helper().getCurrentTimeinUTC(), 1);
												else {
													urObj.setApplied_count(urObj.getApplied_count() + 1);
													urObj.setCoupon_id(priceHighCoupon);
													urObj.setNext_renewal_date(sdf.format(nextRenewDate));
													urObj.setPlan_to_period_id(Long.parseLong(cbPlanAndTrialPeriod[4]));
													urObj.setActivated(false);
													urObj.setUpdated_on(new Helper().getCurrentTimeinUTC());
												}
												crService.saveOrUpdateUserRetained(urObj);
											}
										}
									} else {
										old_flow = true;
										log.info("Amt paid is 0. last invoice amt is 0");
									}
								}
							} else {
								old_flow = true;
							}

						} else {

							difference = nextRenewDate.getTime() - today.getTime();
							daysused = Math.ceil((float) ((float) difference / (float) (1000 * 60 * 60 * 24)));

							if (daysused <= 30) {
								com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(
										metaData);
								checkoutExitingRequest = HostedPage.checkoutExisting().subscriptionId(cb_subid)
										.subscriptionPlanId(cb_plan).addonId(0, oneTimeAddonId).addonQuantity(0, 1)
										.addonBillingCycles(0, 1).replaceAddonList(true).redirectUrl(redirtPetUrl)
										.passThruContent(jsonObj.toString()).embed(embedupdate).forceTermReset(true);

								if (!priceHighCoupon.equalsIgnoreCase("NA") && cancel_restype == 2) {
									checkoutExitingRequest.subscriptionCoupon(priceHighCoupon);
								}

								if (!cb_addon_id.equalsIgnoreCase("NA")) {
									int i = 0;
									for (int j = 0; j < addonIdList.size(); j++) {
										i = j + 1;
										checkoutExitingRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1);
									}
								}

								UserRetained urObj = crService.getUserRetainedByUserId(user.getId());

								if (urObj == null)
									urObj = new UserRetained(user.getId(), priceHighCoupon, sdf.format(nextRenewDate),
											Long.parseLong(cbPlanAndTrialPeriod[4]), false,
											new Helper().getCurrentTimeinUTC(), 1);
								else {
									urObj.setApplied_count(urObj.getApplied_count() + 1);
									urObj.setCoupon_id(priceHighCoupon);
									urObj.setNext_renewal_date(sdf.format(nextRenewDate));
									urObj.setPlan_to_period_id(Long.parseLong(cbPlanAndTrialPeriod[4]));
									urObj.setActivated(false);
									urObj.setUpdated_on(new Helper().getCurrentTimeinUTC());
								}

								crService.saveOrUpdateUserRetained(urObj);

								res = checkoutExitingRequest.request();
								checkouturl = res.hostedPage().url();
								old_flow = false;
								popup_msg1 = "Save " + percent + "%";
								popup_msg2 = " on your plan! Eligible refunds return to your original payment";
								popup_msg3 = "You've been Upgraged to new plan";

							} else
								old_flow = true;
						}
					} else
						old_flow = true;
				}
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("popup_msg1", popup_msg1);
				response.put("popup_msg2", popup_msg2);
				response.put("popup_msg3", popup_msg3);
				response.put("refund", refund);
				response.put("checkouturl", checkouturl);
				response.put("old_flow", old_flow);
				response.put("confirm_txt", confirm_txt);
			}

		} catch (Exception e) {
			// e.printStackTrace();
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Please try again later!");
			log.error("updatesubscriptioncoupon:" + e.getMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;

	}

	@RequestMapping(value = "/app/v5.0/generatesubscriptioncoupon", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse generateSubscriptionCouponV5(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, Authentication authentication) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		log.info("generateSubscriptionCoupon : " + auth);
		try {

			user = userServiceV4.verifyAuthV4("authkey", auth);

		} catch (Exception e) {
			log.error("generateSubscriptionCoupon : user by id : " + e.getMessage());
		}

		try {
			if (user != null) {

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				SimpleDateFormat sdfResponse = new SimpleDateFormat("yyyy-MM-dd");
				Calendar cal = Calendar.getInstance();
				
				String startDate = sdf.format( cal.getTime() );
				String startDateResponse = sdfResponse.format( cal.getTime() );
				
				cal.add(Calendar.MONTH, waggle_merch_coupon_exp_time);
				
				String due_date = sdf.format( cal.getTime() );
				String endDateResponse = sdfResponse.format( cal.getTime() );
				String period = String.valueOf(waggle_merch_coupon_exp_time);
				
				response = crService.generateSubCoupon(user, period, due_date);

				if( (int) response.get("Status") == 1) { //KART6280937
					String code = (String) response.get("couponCode");
					String endDate = (String) response.get("endDate");
					
					
					response.put("startDate", startDateResponse);
					response.put("endDate", endDateResponse);
					
					AdditionBenefitsCancelReward additionBenefitsCancelReward = null;
					
					additionBenefitsCancelReward = cancelService.getAdditionBenefitsCancelReward( user.getId() );
					if( additionBenefitsCancelReward == null ) {
						additionBenefitsCancelReward = new AdditionBenefitsCancelReward();
						additionBenefitsCancelReward.setCode(code);
						additionBenefitsCancelReward.setEnd_date(due_date);
						additionBenefitsCancelReward.setStart_date( startDate );
						additionBenefitsCancelReward.setUser_id( user.getId() );
					}
					
					additionBenefitsCancelReward = cancelService.saveOrUpdateAdditionBenefitsCancelReward( additionBenefitsCancelReward );
					if( additionBenefitsCancelReward == null ) {
						response.put("Status", 0);
						response.put("Msg", "Invalid session, Please try again");
						return response;
					}
					
				}
				
				response.put("iscreated", true);
				response.put("Status", 1);
				response.put("Msg", "Success");

			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			// e.printStackTrace();
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");
			log.error("generateadditionalfeatures:" + e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "/app/v5.0/getavailupgradeplans", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getavailupgradeplans(@RequestHeader HttpHeaders header, @RequestParam("planid") long planid,
			@RequestParam("period") long period, @RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
			@RequestParam("plan_ver") String plan_ver, Authentication authentication,
			@RequestParam(value = "freetrial", defaultValue = "false", required = false) boolean freetrial,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country,
			@RequestParam(defaultValue = "upgrade", required = false) String type) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth").trim();

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);
		} catch (Exception e) {
			log.error("get AvailableUpgrade:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			if (user != null) {
				if (user.getInapp_purchase() == 1) {

					String country = user.getCountry();
					country = "US";
					response = crService.upgradePlanList_v5(user, planid, period, freetrial, country, type);

					String free_trial = "NA";
					String note = "NA";
					response.put("free_trial", free_trial);
					response.put("note", note);

				} else {
					response.put("Status", 0);
					response.put("Msg", "Upgrade list not found");
				}

			}

		} catch (Exception e) {
			log.error("get AvailableUpgrade plans:" + e.getMessage());
			response.put("Error", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "/app/v5.0/savebillingaddress", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveBillingAddressV5(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, Authentication authentication,
			@RequestBody BillingAddress address) {
		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");

		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (Exception e) {
			log.error("saveBillingAddress Exception : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid User");
			response.put("Error", e.getMessage());
		}

		try {
			if (user.getId() == address.getUser_id()) {
				Environment.configure(chargebeeSiteName, chargebeeSiteKey);
				Result res = Customer.updateBillingInfo(user.getChargebeeid()).billingAddressCity(address.getCity())
						.billingAddressCountry(address.getCountry()).billingAddressFirstName(address.getFirst_name())
						.billingAddressLastName(address.getLast_name())
						.billingAddressLine1(address.getBilling_address_1())
						.billingAddressLine2(address.getBilling_address_2()).billingAddressState(address.getState())
						.billingAddressZip(address.getZip_code()).billingAddressPhone(address.getPhone_num())
						.billingAddressStateCode(address.getState_code()).request();

				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid User");
			}
		} catch (Exception e) {
			log.error("saveBillingAddress Excep:" + e.getMessage());
			response.put("Status", 0);
			if (e.getMessage().toLowerCase().contains("zip"))
				response.put("Msg", "Invalid zip/postal code");
			else
				response.put("Msg", e.getMessage());
			response.put("Error", e.getMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "/app/v5.0/updatecbpaymentmethod/", headers = "Accept=application/json", method = RequestMethod.GET)
	@ResponseBody
	public JResponse updateCBPaymentMethodV5(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		String auth = header.getFirst("auth");
		log.info("auth :" + auth);
		String chargebeeid = "NA";
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (Exception e) {
				log.error("updatePaymentMethod:" + e.getMessage());
			}

			if (user != null) {

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				if (!user.getRecharge_custid().equalsIgnoreCase("NA")) {
					log.info("It is recharge customer chargebee payment method not applicable");
					response.put("portalUrl", "");
					response.put("Status", 0);
					response.put("Msg", "Please contact support to update payment method.");
					return response;
				}

				Environment.configure(chargebeeSiteName, chargebeeSiteKey);

				chargebeeid = user.getChargebeeid();

				if (!chargebeeid.equalsIgnoreCase("NA")) {
					ListResult resultSet = Customer.list().id().is(chargebeeid).request();
					boolean isDeviceAvailable = gatewayServiceV4.isDeviceConfigured(user.getId());

					if (resultSet.size() > 0 && isDeviceAvailable) {
						
						HostedPage hostedPage = null;

						if (checkoutversion.equalsIgnoreCase("v2")) {
							// v2
							Result res = HostedPage.updatePaymentMethod().customerId(chargebeeid).embed(false)
									.request();
							hostedPage = res.hostedPage();
						} else {
							// v3
							Result res = HostedPage.managePaymentSources().customerId(chargebeeid).request();
							hostedPage = res.hostedPage();
						}

						response.put("portalUrl", hostedPage.url());
						response.put("Status", 1);
						response.put("Msg", "Success");
					} else {
						response.put("portalUrl", "");
						response.put("Status", 0);
						response.put("Msg", "Unable to update payment method. Please try after some time.");
					}
//					
//					if (resultSet.size() > 0) {					
//						Result result = PortalSession.create().customerId(chargebeeid).redirectUrl(paymentupdateurl).request();
//						PortalSession portalSession = result.portalSession();
//						response.put("Status", 1);
//						response.put("Msg", "Success");
//						response.put("portalUrl", portalSession.accessUrl());
//					} 
				}
			}

		} catch (Exception e) {
			log.error("updatePaymentMethod:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "/app/v5.0/applyoffer", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse applyOfferV5(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
			@RequestParam("cb_planid") String cb_planid, @RequestParam("cb_subid") String cb_subid,
			@RequestParam("cb_couponid") String cb_couponid,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		UserV4 user = null;
		String offer_status = "";
		String offer_status_dark = "";
		String offer_success_content = "";
		
		try {
			String auth = header.getFirst("auth");
			log.info("applyoffer :Auth: " + auth + " cb_subid:" + cb_subid);

			user = userServiceV4.verifyAuthV3("authkey", auth);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (user != null) {
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					String cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(),
							user.getEmail(), user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}

				int addonStatus = 0;
				String oneTimeAddonId = "";
				String activationId = "";
				String reactivationId = "";
				String upgradeId = "";
				String country = user.getCountry().toUpperCase();
				country = "US";

				activationId = activationAddonId == null ? "setup_charges" : activationAddonId.get(country);
				reactivationId = reactivateAddonId == null ? "reactivation-charges-onetime"
						: reactivateAddonId.get(country);
				upgradeId = updateAddonId == null ? "upgrade_charges" : updateAddonId.get(country);

				String subStatus = "";
				Subscription subscription = null;
				if (cb_subid.isEmpty())
					cb_subid = "NA";
				if (!cb_planid.equalsIgnoreCase("NA")) {
					if (!cb_subid.equalsIgnoreCase("NA")) {

						Result result = Subscription.retrieve(cb_subid).request();
						subscription = result.subscription();
						subStatus = subscription.status().name();

						if (freeplan.contains(subscription.planId())) {
							addonStatus = 0; // activation
						} else if (!freeplan.contains(subscription.planId()) && !vpmplan.contains(subscription.planId())
								&& !addonplan.contains(subscription.planId())
								&& !(subStatus.equalsIgnoreCase("CANCELLED"))) {
							addonStatus = 1; // upgrade
						} else if (!freeplan.contains(subscription.planId()) && !vpmplan.contains(subscription.planId())
								&& !addonplan.contains(subscription.planId())
								&& (subStatus.equalsIgnoreCase("CANCELLED"))) {
							addonStatus = 2; // re-activation
						}
					} else {
						addonStatus = 0;
					}

					switch (addonStatus) {
					case 0:
						oneTimeAddonId = activationId;
						break;

					case 1:
						oneTimeAddonId = upgradeId;
						break;

					case 2:
						oneTimeAddonId = reactivationId;
						break;
					}

					// this for customer merge
					HashMap<String, Object> metaData = new HashMap<String, Object>();
					metaData.put("userid", user.getId());
					metaData.put("chargebeeid", user.getChargebeeid());
					Result res;

					if (!cb_subid.equalsIgnoreCase("NA")) {
						log.info("applyOffer : cb_subid !=NA case ");

						metaData.put("regarding", "apply-applied");

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);

						CheckoutExistingRequest checkoutExitingRequest = HostedPage.checkoutExisting()
								.subscriptionId(cb_subid).subscriptionPlanId(cb_planid).addonId(0, oneTimeAddonId)
								.addonQuantity(0, 1).addonBillingCycles(0, 1).replaceAddonList(true)
								.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString()).embed(embedupdate)
								.forceTermReset(true);

						if (!cb_couponid.equalsIgnoreCase("NA")) {
							checkoutExitingRequest.subscriptionCoupon(cb_couponid);
						}

						res = checkoutExitingRequest.request();

					} else {
						log.info("applyOffer : cb_subid=NA case");

						metaData.put("regarding", "offer-applied");

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
						CheckoutNewRequest checkoutNewRequest = HostedPage.checkoutNew().subscriptionPlanId(cb_planid)
								.subscriptionPlanQuantity(1).addonId(0, oneTimeAddonId).addonQuantity(0, 1)
								.addonBillingCycles(0, 1).customerId(user.getChargebeeid());

						if (!cb_couponid.equalsIgnoreCase("NA")) {
							checkoutNewRequest.subscriptionCoupon(cb_couponid);
						}

						res = checkoutNewRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString())
								.embed(embedupdate).request();

					}

					HostedPage hostedPage = res.hostedPage();
					String checkout_url = hostedPage.url();

					String[] offer_status_arr = new String[3];

					if (!checkout_url.isEmpty() || checkout_url != null)
						offer_status_arr = crService.getOfferStatus(cb_planid);

					if (offer_status_arr == null) {
						offer_status = "<center><p style='color:#000;font-size: 12px;padding-left: 5px;'>Oops! Can't apply coupon. Try again</p></center>";
						response.put("checkOutURL", "NA");
						response.put("Status", 0);
						response.put("Msg", "Error");
						response.put("offer_status", offer_status);
					} else {
						offer_status = offer_status_arr[0];
						offer_status_dark = offer_status_arr[1];
						offer_success_content = offer_status_arr[2];

						response.put("checkOutURL", checkout_url);
						response.put("Status", 1);
						response.put("Msg", "Success");
						response.put("offer_status", offer_status);
						response.put("offer_status_dark", offer_status_dark);
						response.put("offer_success_content", offer_success_content);
					}

				} else {
					response.put("Status", 0);
					response.put("Msg", "chargebee plan id NA");
					offer_status = "<center><p style='color:#000;font-size: 12px;padding-left: 5px;'>Oops! Can't apply coupon. Try again</p></center>";
					offer_status_dark = "<center><p style='color:#ffffff;font-size: 12px;padding-left: 5px;'>Oops! Can't apply coupon. Try again</p></center>";
					response.put("checkOutURL", "NA");
					response.put("offer_status", offer_status);
					response.put("offer_status_dark", offer_status_dark);
				}
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("checkOutURL", "NA");
			offer_status = "<center><p style='color:#000;font-size: 12px;padding-left: 5px;'>Oops! Can't apply coupon. Try again</p></center>";
			offer_status_dark = "<center><p style='color:#ffffff;font-size: 12px;padding-left: 5px;'>Oops! Can't apply coupon. Try again</p></center>";
			response.put("offer_status", offer_status);
			response.put("offer_status_dark", offer_status_dark);
			response.put("Msg", "Failed to update subscription");
			log.error("applyoffer:" + e.getMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "/app/v5.0/cbpaymentnow/", headers = "Accept=application/json", method = RequestMethod.GET)
	@ResponseBody
	public JResponse cbpaymentnowV5(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		String auth = header.getFirst("auth");
		log.info("auth :" + auth);
		String chargebeeid = "NA";
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (Exception e) {
				log.error("cbpaymentnow:" + e.getMessage());
			}

			if (user != null) {

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				if (!user.getRecharge_custid().equalsIgnoreCase("NA")) {
					log.info("It is recharge customer chargebee payment method not applicable");
					response.put("paynowUrl", "");
					response.put("Status", 0);
					response.put("Msg", "Please contact support to update payment method.");
					return response;
				}

				Environment.configure(chargebeeSiteName, chargebeeSiteKey);

				chargebeeid = user.getChargebeeid();

				if (!chargebeeid.equalsIgnoreCase("NA")) {
					ListResult resultSet = Customer.list().id().is(chargebeeid).request();

					if (resultSet.size() > 0) {
						HostedPage hostedPage = null;
						Result result = HostedPage.collectNow().customerId(chargebeeid).request();
						// .redirectUrl(redirtPetUrl).request();
						hostedPage = result.hostedPage();

						response.put("paynowUrl", hostedPage.url());
						response.put("Status", 1);
						response.put("Msg", "Success");
					} else {
						response.put("paynowUrl", "");
						response.put("Status", 0);
						response.put("Msg", "Unable to process payment. Please try after some time.");
					}
//					
//					if (resultSet.size() > 0) {					
//						Result result = PortalSession.create().customerId(chargebeeid).redirectUrl(paymentupdateurl).request();
//						PortalSession portalSession = result.portalSession();
//						response.put("Status", 1);
//						response.put("Msg", "Success");
//						response.put("portalUrl", portalSession.accessUrl());
//					} 
				}
			}

		} catch (Exception e) {
			log.error("updatePaymentMethod:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "/app/v5.0/getcurrentsubscriptionplanV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCurrentSubscriptionPlanV5(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestParam("plan_ver") String plan_ver,
			Authentication authentication,
			@RequestParam(value = "create_benefit", defaultValue = "true", required = false) boolean create_benefit,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "timezone", defaultValue = "", required = false) String timezone,
			@RequestParam(defaultValue = "0", required = false) Long gateway_id) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		Timestamp start = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("start : " + start + " getcurrentsubscriptionplan: " + auth);

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);

		} catch (Exception e) {
			log.error("getCurrentSubscriptionPlan: user by authkey : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		try {
			if (user != null) {

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				if (user.isEnable()) {
					int inapp_redirect = 1;
					boolean cb_checkout = true;
//					boolean show_alertlimit = false;
//					VersionMapping verObj = crService.getVersionMapping(app_ver, os);
//
//					if (verObj != null) {
//						inapp_redirect = verObj.getInapp_redirect();
//						cb_checkout = verObj.isCb_checkout();
//					}
//
//					if (!user.getPlan_ver().equalsIgnoreCase("V1"))
//						show_alertlimit = true;
//					
//					if (user.getInapp_purchase() == 1) {

					if (timezone == null || timezone.isEmpty() || timezone.equalsIgnoreCase("NA"))
						timezone = "-08:00";

					if (!timezone.contains("-") && !timezone.contains("+"))
						timezone = "+" + timezone.trim();

					response = cbService.getSubscriptionFromCB(user, inapp_redirect, os, app_ver, timezone,
							false, gateway_id);

					long gatewayId = userServiceV4.getUserGateway("userId", user.getId());
					boolean show_upgrade_button = false;
					if (gatewayId != 0)
						show_upgrade_button = true;

					response.put("show_addon", show_addon_button);
					// response.put("show_alertlimit", show_alertlimit);
					response.put("cb_checkout", cb_checkout);
					response.put("show_nextRenewalPopup", show_nextrenewal_popup);
					response.put("show_upgrade", show_upgrade_button);

					boolean restrict_alert = true;

					if (user.getPlan_ver().equalsIgnoreCase("V1"))
						restrict_alert = false;

					response.put("restrict_alert", restrict_alert);
//					} else {
//						response.put("Status", 0);
//						response.put("Msg", "Subscription Details not found");
//
//					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "User not enabled");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Please try again.");
			log.error("getCurrentSubscriptionPlan:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "/app/v5.0/getproductsubscription", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getProductSubscription(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, Authentication authentication,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "timezone", defaultValue = "", required = false) String timezone,
			@RequestParam(defaultValue = "0", required = false) Long gateway_id,
			@RequestParam(defaultValue = "0", required = false) Long monitor_id) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		Timestamp start = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("start : " + start + " getcurrentsubscriptionplan: " + auth);

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);

		} catch (Exception e) {
			log.error("getCurrentSubscriptionPlan: user by authkey : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		try {
			if (user != null) {
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				if (user.isEnable()) {
					int inapp_redirect = 1;
					boolean cb_checkout = true;

					if (timezone == null || timezone.isEmpty() || timezone.equalsIgnoreCase("NA"))
						timezone = "-08:00";

					if (!timezone.contains("-") && !timezone.contains("+"))
						timezone = "+" + timezone.trim();
					
					 response = cbService.getProductSubscriptionFromDB(user, os, app_ver, timezone,
							gateway_id, monitor_id);
					
					response.put("Status", 1);
					response.put("Msg", "Success");
				} else {
					response.put("Status", 0);
					response.put("Msg", "User not enabled");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Please try again.");
			log.error("getCurrentSubscriptionPlan:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	
	@RequestMapping(value = "/app/v5.0/getbillingaddress", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getBillingAddressV5(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, Authentication authentication,
			@RequestParam("user_id") long user_id) {
		log.error("Entered getBillingAddress for user_id :" + user_id);

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");

		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (Exception e) {
			log.error("getBillingAddress Exception : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid User");
			response.put("Error", e.getMessage());
		}

		try {
			if (user.getId() == user_id) {
				Environment.configure(chargebeeSiteName, chargebeeSiteKey);
				Result cusData = Customer.retrieve(user.getChargebeeid()).request();
				BillingAddress addressObj = new BillingAddress();

				addressObj.setUser_id(user_id);
				if (cusData.customer().billingAddress().firstName() != null)
					addressObj.setFirst_name(cusData.customer().billingAddress().firstName());
				if (cusData.customer().billingAddress().lastName() != null)
					addressObj.setLast_name(cusData.customer().billingAddress().lastName());
				if (cusData.customer().billingAddress().phone() != null)
					addressObj.setPhone_num(cusData.customer().billingAddress().phone());
				if (cusData.customer().billingAddress().city() != null)
					addressObj.setCity(cusData.customer().billingAddress().city());
				if (cusData.customer().billingAddress().state() != null)
					addressObj.setState(cusData.customer().billingAddress().state());
				if (cusData.customer().billingAddress().stateCode() != null)
					addressObj.setState_code(cusData.customer().billingAddress().stateCode());
				if (cusData.customer().billingAddress().country() != null)
					addressObj.setCountry(cusData.customer().billingAddress().country());
				if (cusData.customer().billingAddress().zip() != null)
					addressObj.setZip_code(cusData.customer().billingAddress().zip());

				com.chargebee.org.json.JSONObject obj = cusData.customer().billingAddress().jsonObj;

				if (!cusData.customer().billingAddress().line1().isEmpty())
					addressObj.setBilling_address_1(cusData.customer().billingAddress().line1());
				// For few address line 2 will not be there.
				if (obj.has("line2"))
					addressObj.setBilling_address_2(cusData.customer().billingAddress().line2());

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("Address", addressObj);
			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid User");
			}
		} catch (Exception e) {
			log.error("getBillingAddress Excep:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Error while getting address!");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	
	@RequestMapping(value = "/app/v5.0/getsettingfeaturesV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getSettingFeaturesV5(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestParam("plan_ver") String plan_ver,
			Authentication authentication,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "gatewayid", defaultValue = "0", required = false) String gatewayid) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		log.info("getsettingfeaturesV2: " + auth);
		try {

			user = userServiceV4.verifyAuthV4("authkey", auth);

		} catch (Exception e) {
			log.error("getsettingfeaturesV2: user by id : " + e.getMessage());
		}

		try {
			if (user != null) {

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				String country = user.getCountry().toUpperCase();
				country = "US";
				
				ArrayList<JFeatureCredit> setting_features = new ArrayList<JFeatureCredit>();
				
				if(gatewayid != null && !gatewayid.equalsIgnoreCase("0") && plan_ver.equalsIgnoreCase("V3")) {
					setting_features  = crService.getSettingGatewayFeatures(Long.valueOf(gatewayid));
				}else {
					setting_features = crService.getSettingFeatures(user.getId());
				}

				response.put("setting_features", setting_features);
				response.put("alert_msg", alert_msg.get(country));
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");
			log.error("getCurrentSubscriptionPlan:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "/app/v5.0/cancelsubplan/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse cancelsubplanV5(@RequestBody JCancelSubDetail jCancelSubDetail, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "end_term_cancel", defaultValue = "false", required = false) boolean end_term_cancel,
			@RequestParam(value = "isVetchat", defaultValue = "false", required = false) boolean isVetchat) {
		String auth = header.getFirst("auth");
		log.info(" Entered cancelsubplan :" + auth);
		JResponse response = new JResponse();
		boolean refund = false;

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", auth);

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			if (isVetchat) {
				try {
					
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					sdf.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
					Date today = sdf.parse(new Helper().getCurrentTimeinUTC());
					
					boolean status = cbService.saveCancelSubscription(jCancelSubDetail, usr.getId(), "vet_chat_request",
							sdf.format(today), 0, 1, "NA");
					
					UserCancelFeedBack userCancelFeedBack = null;
					userCancelFeedBack = cancelService.getUserCancelFeedBackByUserId(usr.getId());
					if( userCancelFeedBack == null ) {
						userCancelFeedBack = new UserCancelFeedBack();
						userCancelFeedBack.setUser_id( usr.getId() );
					} 
					
					userCancelFeedBack.setCancel_feedback_id( Long.valueOf(jCancelSubDetail.getReason_type()) );
					userCancelFeedBack.setUpdated_on( _helper.getCurrentTimeinUTC() );
					userCancelFeedBack.setCustomer_review( "Vet chat enable - " + jCancelSubDetail.getReason_desc() );
					userCancelFeedBack.setShow_cancel_sub( false );
					
					userCancelFeedBack = cancelService.saveOrUpdateUserCancelFeedBack( userCancelFeedBack );
					if( userCancelFeedBack == null ) {
						log.error("Error while saving userCancelFeedBack");
						response.put("Status", 0);
						response.put("Msg", "Invalid session, Please try again");
						return response;
					}
					
					async.saveOrUpdateUserCancelFeedBackHistory( new UserCancelFeedBackHistory( userCancelFeedBack ) );
					
					
					File file = ResourceUtils.getFile("classpath:iris3.properties");
					Properties prop = new Properties();
					prop.load(new FileInputStream(file));

					String toAddr = prop.getProperty("to_address");
					String ccAddr = prop.getProperty("cc_address");
					String bccAddr = prop.getProperty("bcc_address");

					String emailMsg = "";
					String sub = "Vet chat enable request: " + usr.getUsername();
					String mailmsg = "Hi Team, " + "<br />" + emailMsg
							+ " Kindly enable vet chat to this user <br /><b> Username :</b>" + usr.getUsername()
							+ "<br /><b> CB customer ID:</b> " + usr.getChargebeeid()
							+ "<br /><b> CB Subscription ID:</b> " + jCancelSubDetail.getCb_subid();
					async.SendEmail_SES(toAddr, ccAddr, bccAddr, sub, mailmsg);

					response.put("Status", 1);
					response.put("Msg", "Our support team will contact you within 24 hours to activate it.");
					response.put("vet_content", "$$1 Year FREE Vet Chat With $750 emergency fund$$ is now yours");
					response.put("Refund", refund);
					return response;
				} catch (Exception e) {
					response.put("Status", 0);
					response.put("Msg", "Error occured");
					response.put("Error", e.getLocalizedMessage());
					log.error("cancelsubplan : " + e.getLocalizedMessage());
				}

			} else {
			response = cbService.cancelsubplanV5(usr, jCancelSubDetail, end_term_cancel);
			}
			
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured");
			response.put("Error", e.getLocalizedMessage());
			log.error("cancelsubplan : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "/app/v3.0/getcurrentsubscriptionplan/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCurrentSubscriptionPlan(
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		User user = null;
		Timestamp start = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		String auth = header.getFirst("auth");
		log.info("start : " + start + " getcurrentsubscriptionplan: " + auth);
		try {

			try {
				user = userService.verifyAuthKey(auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			Helper _helper = new Helper();

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

		} catch (Exception e) {
			log.error("getCurrentSubscriptionPlan: user by id : " + e.getMessage());
		}

		try {
			if (user == null)
				user = userService.verifyAuthKey(auth);
		} catch (Exception e) {
			log.error("getCurrentSubscriptionPlan:userby auth : " + e.getMessage());
		}

		try {
			if (user != null) {
				if (user.isEnable()) {
					int inapp_redirect = 1;
					boolean cb_checkout = true;
//					VersionMapping verObj = crService.getVersionMapping(app_ver, os);
//
//					if (verObj != null) {
//						inapp_redirect = verObj.getInapp_redirect();
//						cb_checkout = verObj.isCb_checkout();
//					}
//
//					if (user.getInapp_purchase() == 1) {
					response = getSubscription(user, inapp_redirect);
					response.put("cb_checkout", cb_checkout);
//					} else if (user.getInapp_purchase() == 2) {
//						response = getIosSubscription(user, inapp_redirect);
//						response.put("cb_checkout", cb_checkout);
//					} else {
//						response.put("Status", 0);
//						response.put("Msg", "Subscription Details not found");
//
//					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "User not enabled");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");
			log.error("getCurrentSubscriptionPlan:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "/app/v4.0/getcurrentsubscriptionplan/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCurrentSubscriptionPlanV4(
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		UserV4 user = null;
		Timestamp start = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		String auth = header.getFirst("auth");
		log.info("start : " + start + " getcurrentsubscriptionplan: " + auth);

		try {
			try {
				user = userServiceV4.verifyAuthV3("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

		} catch (Exception e) {
			log.error("getCurrentSubscriptionPlan: user by id : " + e.getMessage());
		}

		try {
			if (user != null) {
				if (user.isEnable()) {
					int inapp_redirect = 1;
					boolean cb_checkout = true;

					response = cbService.getSubscriptionFromDB(user, inapp_redirect);
					response.put("cb_checkout", cb_checkout);

//					VersionMapping verObj = crService.getVersionMapping(app_ver, os);
//					
//					if(verObj != null) {
//						inapp_redirect = verObj.getInapp_redirect();
//						cb_checkout= verObj.isCb_checkout();
//					}
//					
//					if(user.getInapp_purchase()==1) {
//						response = cbService.getSubscriptionV2(user,inapp_redirect);
//						response.put("cb_checkout", cb_checkout);
//					}
//					else if(user.getInapp_purchase()==2) {
//						response = cbService.getIosSubscriptionV2(user,inapp_redirect);
//						response.put("cb_checkout", cb_checkout);
//					}
//					else {
//						response.put("Status", 0);
//						response.put("Msg", "Subscription Details not found");
//	
//					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "User not enabled");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error while getting Subscription plan");
			log.error("getCurrentSubscriptionPlan:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "/app/v4.0/getavailableupgradeplan/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAvailableUpgradeSubscriptionPlanV4(@RequestParam("planid") long planid,
			@RequestParam("period") long period, Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		JResponse response = new JResponse();
		Map<String, Object> resp = new HashMap<String, Object>();
		String auth = header.getFirst("auth");
		UserV4 user = null;
		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (Exception e) {
			log.error("get AvailableUpgrade:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invaid Authkey!");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			if (user != null) {
				resp = crService.getAvailUpgradePlanV4(planid, period, user.getId(), 0, "US");
				response.setResponse(resp);

				// checking for free subscription
				int days = userService.getRemainingDays(user.getId());
				String free_trial = "NA";
				String note = "NA";
				if (days > 0) {
					free_trial = String.valueOf(days).concat(" Days");
					note = "* Please buy a subscription to proceed further. You will be billed only after the FREE subscription period ends and "
							+ " you can choose to cancel anytime before the FREE subscription ends.";
				}
				response.put("free_trial", free_trial);
				response.put("note", note);

			}

		} catch (Exception e) {
			log.error("get AvailableUpgrade plans:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "/app/v4.0/getsubsplanbymonitortype/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getSubscriptionPlanByMonitortypeV4(@RequestParam("monitortype") long monitortype,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		JResponse response = new JResponse();
		Map<String, Object> resp = new HashMap<String, Object>();
		String auth = header.getFirst("auth");
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
//		String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
//		String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);

		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (user != null) {

				ListResult result = com.chargebee.models.Subscription.list().customerId().is(user.getChargebeeid())
						.status().in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL).request();
				Subscription subscription = null;

				if (result.isEmpty()) {
//					log.info("CB sub_create : getSubscriptionPlanByMonitortypeV4 : userid : "+ user.getId());
//					subscription = createDefaultSubsPlan(user);
				} else {
					int ssize = result.size();
					for (ListResult.Entry subs : result) {

						if (ssize == 1 && !omitplan.contains(subs.subscription().planId())) {
							subscription = subs.subscription();
						} else if (!freeplan.contains(subs.subscription().planId())
								&& !omitplan.contains(subs.subscription().planId())) {
							subscription = subs.subscription();
						}
					}
				}

				int planid = 1;
				int period = 1;
				if (subscription != null) {
					ArrayList<Integer> ids = crService.getPlanAndPeriod(subscription.planId());
					if (!ids.isEmpty()) {
						planid = ids.get(0);
						period = ids.get(1);
					}
				}

				String country = user.getCountry().toUpperCase();
				country = "US";
				resp = crService.getAvailUpgradePlanV4(planid, period, user.getId(), monitortype, country);
				response.setResponse(resp);

			}
		} catch (Exception e) {
			log.error("getPlanByMonitorType : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error occured, while getting subscription");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	public JResponse getSubscription(User user, int inapp_redirect) {
//		String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
//		String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);
//		String vpmplan = _helper.getExternalConfigValue("vpmplan", externalConfigService);

		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		try {
			String cbId = "NA";
//			if (user.getChargebeeid().equalsIgnoreCase("NA")) {
//				cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
//						user.getMobileno(), user.getUsername(), 0, "NA");
//				user.setChargebeeid(cbId);
//			}
			if (user.getChargebeeid().equalsIgnoreCase("NA")) {
				ListResult rs = null;
				int i = 1;
				Loop: while (rs == null || i <= 3) {
					i = i + 1;
					Thread.sleep(2000);
					rs = Customer.list().email().is(user.getEmail()).sortByCreatedAt(SortOrder.ASC).request();

					if (!rs.isEmpty()) {
						for (ListResult.Entry entry : rs) {
							Customer customer = entry.customer();
							user.setChargebeeid(customer.id());
						}
						break Loop;
					}
				}
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
							user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}
			}
			com.chargebee.models.Subscription subscrip = null;
			com.chargebee.models.Subscription vpmSubscrip = null;
			boolean isPaidPlan = false;
			boolean vpm_addon = false;
			boolean payment_due = false;

			if (!user.getChargebeeid().equalsIgnoreCase("NA")) {
				ListResult result = com.chargebee.models.Subscription.list().customerId().is(user.getChargebeeid())
						.status().in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL)
						.sortByUpdatedAt(SortOrder.DESC).request();
				int ssize = 0;

				if (!result.isEmpty()) {
					ssize = result.size();
					for (ListResult.Entry subs : result) {
						if (ssize == 1 && !omitplan.contains(subs.subscription().planId())
								&& !vpmplan.contains(subs.subscription().planId())
								&& !addonplan.contains(subs.subscription().planId())) {
							subscrip = subs.subscription();

							if (!freeplan.contains(subs.subscription().planId())
									&& !vpmplan.contains(subs.subscription().planId())
									&& !addonplan.contains(subs.subscription().planId()))
								isPaidPlan = true;

						} else if (!freeplan.contains(subs.subscription().planId())
								&& !omitplan.contains(subs.subscription().planId())
								&& !vpmplan.contains(subs.subscription().planId())
								&& !addonplan.contains(subs.subscription().planId())) {
							subscrip = subs.subscription();
							isPaidPlan = true;

//							List<Addon> addonList = subscrip.addons();
//							for (Addon addon : addonList) {
//								if (addon.id().contains("vetchat")) {
//									vpm_addon = true;
//									log.info(subs.subscription().planId() + ": vpm included with plan");
//								}
//							}
							break;
						}
					}

					if (subscrip == null) {
						for (ListResult.Entry subs : result) {
							if (freeplan.contains(subs.subscription().planId())) {
								subscrip = subs.subscription();
								break;
							}
						}
					}
//					if (vpmSubscrip == null) {
//						for (ListResult.Entry subs : result) {
//							if (vpmplan.contains(subs.subscription().planId())) {
//								vpmSubscrip = subs.subscription();
//								break;
//							}
//						}
//					}
				}
				if ((result.isEmpty() || subscrip == null) && (inapp_redirect != 2)) {
					// ios inapp user.here checking is there any CB subscription available
					log.info("CB sub_create : getSubscription : userid : " + user.getId());
					subscrip = cbService.createDefaultSubsPlan(user.getChargebeeid());
				}
			}
			// initialize values
			JVpmSubscription vpmSubs = new JVpmSubscription();
			String strprice1 = "NA";
			int availCnt = 0;
			int totalCnt = 0;
			int usedCnt = 0;

			String billingPeriod = "NA";
			String periodUnit = "MONTH";
			String planid = "chum";
			String planname = "Chum";
			String availCredit = "0";
			int period = 0;

			float price = (float) 0.0;
			String strprice = "$0.0";
			String status = "ACTIVE";
			int daysBetween = -1;
			int days_remaining = -1;
			String startedAt = "NA";
			String nextPaymentDate = "NA";
			String createDate = "NA";
			String updateDate = "NA";
			String cbSubId = "NA";
			String cbSubStatus = "NA";
			boolean setupAutoRenewal = false;
			String autoRenewalStatus = "NA";
			boolean cancel = false;
			boolean vetchat_cancel = false;
			String cbvet_cancelId = "";
			String cbvet_planId = "";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date dateobj = new Date();
			Timestamp nextpaymentTS;
			boolean cb_vetchat = false;

			int iris_splan = 1;
			int iris_speriod = 1;
			String desc = "Free Plan";
			JSubscriptionPlanReport rpt = new JSubscriptionPlanReport();
			boolean vpm_enable = false;// companyService.getVetCallStaus(user.giveCompany().getId());
			int substatus_code = 0;

			if (subscrip != null || vpmSubscrip != null) {
				if (subscrip != null) {
					CreditNote cr = null;
					ListResult crLst = CreditNote.list().customerId().is(user.getChargebeeid()).request();
					cr = crLst.isEmpty() ? null : crLst.get(0).creditNote();
					ListResult planRes = Plan.list().id().is(subscrip.planId()).request();
					availCredit = (cr != null) ? String.valueOf(cr.amountAvailable() / 100.0) : "0";

					for (ListResult.Entry planR : planRes) {
						Plan plan = planR.plan();
						period = plan.period();
						periodUnit = plan.periodUnit().name();
						planid = plan.id();
						planname = plan.name();

						if (periodUnit.equalsIgnoreCase("YEAR")) {
							if (period == 2)
								billingPeriod = "2 Year";
							else if (period >= 5)
								billingPeriod = planname;
							else
								billingPeriod = "Yearly";
						} else if (periodUnit.equalsIgnoreCase("MONTH")) {
							if (period == 3)
								billingPeriod = "Quarterly";
							else if (period == 6)
								billingPeriod = "Half-Yearly";
							else if (period == 12)
								billingPeriod = "Yearly";
							else if (period == 24)
								billingPeriod = "2 Year";
							else
								billingPeriod = "Monthly";
						} else if (periodUnit.equalsIgnoreCase("DAY")) {
							billingPeriod = "Daily";
						} else if (periodUnit.equalsIgnoreCase("WEEK")) {
							billingPeriod = "Weekly";
						}

						price = (float) subscrip.planUnitPrice() / 100;
						strprice = "$" + String.valueOf(price);
						status = subscrip.status().name();

						if ((status.equalsIgnoreCase("ACTIVE")) || (status.equalsIgnoreCase("IN_TRIAL"))) {
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							if (status.equalsIgnoreCase("IN_TRIAL")) {
								cancel = true;
							}
							substatus_code = 1;
							try {
								nextpaymentTS = subscrip.nextBillingAt();

								if (nextpaymentTS == null)
									nextpaymentTS = subscrip.currentTermEnd();

							} catch (Exception ex) {
								nextpaymentTS = subscrip.currentTermEnd();
							}
							nextPaymentDate = sdf.format(nextpaymentTS.getTime());

							Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
							Date todayDate = sdf.parse(sdf.format(dateobj));

							long difference = nextPaymentDate1.getTime() - todayDate.getTime();
							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
							days_remaining = daysBetween;

							sdf = new SimpleDateFormat("yyyy-MM-dd");
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							nextPaymentDate = sdf.format(nextpaymentTS.getTime());
							// status = "ACTIVE";
							autoRenewalStatus = "Enabled";

							if (freeplan.contains(subscrip.planId())) {
								nextPaymentDate = "NA";
								days_remaining = -1;
								autoRenewalStatus = "NA";
								billingPeriod = "NA";
							}
						} else if (status.equalsIgnoreCase("NON_RENEWING")) {
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							status = "ACTIVE";
							nextPaymentDate = sdf.format(subscrip.cancelledAt().getTime());
							Date cancelledAt = sdf.parse(nextPaymentDate);
							Date todayDate = sdf.parse(sdf.format(dateobj));

							long difference = cancelledAt.getTime() - todayDate.getTime();
							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
							days_remaining = daysBetween;

							sdf = new SimpleDateFormat("yyyy-MM-dd");
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							nextPaymentDate = sdf.format(subscrip.cancelledAt().getTime());
							substatus_code = 1;
							autoRenewalStatus = "Disabled";

						}

						if (daysBetween < 0)
							days_remaining = -1;

						ArrayList<Integer> ids = crService.getPlanAndPeriod(planid);
						boolean alert_setting = true;
						if (!ids.isEmpty()) {
							iris_splan = ids.get(0);
							iris_speriod = ids.get(1);

							SubscriptionPlan splan = crService.getSubsPlanById(ids.get(0));
							desc = splan.getDescription();
							planname = splan.getPlan_name();
							alert_setting = splan.isAlert_setting();
						}

						// safety
						try {
							sdf = new SimpleDateFormat("yyyy-MM-dd");
							createDate = sdf.format(subscrip.createdAt().getTime());
							updateDate = sdf.format(subscrip.updatedAt().getTime());
							startedAt = sdf.format(subscrip.startedAt().getTime());
							cbSubId = subscrip.id();
							cbSubStatus = subscrip.status().name();
						} catch (Exception e) {
							log.error("subs dates: ", e.getLocalizedMessage());
						}

						List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(String.valueOf(iris_splan),
								user.getId(), days_remaining);

						rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
								days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus,
								createDate, updateDate, cbSubId, cbSubStatus, availCredit, alert_setting,
								user.getChargebeeid(), startedAt, planid, String.valueOf(iris_speriod), cancel,
								substatus_code, payment_due);
					}
				} else {
					List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(String.valueOf(iris_splan), user.getId(),
							days_remaining);

					rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
							days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus, createDate,
							updateDate, cbSubId, cbSubStatus, availCredit, true, user.getChargebeeid(), startedAt, "",
							String.valueOf(iris_speriod), cancel, substatus_code, payment_due);
				}
				int vpmstatus_code = 0;
				String str_total_cnt = "0";
				float price1 = 0;
//				if (vpmSubscrip != null) {
//					price1 = (float) vpmSubscrip.planUnitPrice() / 100;
//					if (vpmSubscrip.planId().equalsIgnoreCase("vet-chat")) {
//						strprice1 = "$" + String.valueOf(price1);
//						nextPaymentDate = "NA";
//						autoRenewalStatus = "Disabled";
//						billingPeriod = "NA";
//						vpmstatus_code = 1;
//					} else {
//						sdf = new SimpleDateFormat("yyyy-MM-dd");
//						sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
//						cb_vetchat = true;
//
//						if (status.equalsIgnoreCase("ACTIVE") || (status.equalsIgnoreCase("IN_TRIAL"))) {
//							if (status.equalsIgnoreCase("IN_TRIAL")) {
//								vetchat_cancel = true;
//								cbvet_cancelId = vpmSubscrip.id();
//								cbvet_planId = vpmSubscrip.planId();
//							}
//							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
//							try {
//								autoRenewalStatus = "Enabled";
//								nextpaymentTS = vpmSubscrip.nextBillingAt();
//
//								if (nextpaymentTS == null)
//									nextpaymentTS = vpmSubscrip.currentTermEnd();
//
//							} catch (Exception ex) {
//								nextpaymentTS = vpmSubscrip.currentTermEnd();
//							}
//
//							nextPaymentDate = sdf.format(nextpaymentTS.getTime());
//
//							Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
//							Date todayDate = sdf.parse(sdf.format(dateobj));
//
//							long difference = nextPaymentDate1.getTime() - todayDate.getTime();
//							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
//							days_remaining = daysBetween;
//							autoRenewalStatus = "Enabled";
//							vpmstatus_code = 1;
//
//						} else if (status.equalsIgnoreCase("NON_RENEWING")) {
//							autoRenewalStatus = "Disabled";
//							status = "ACTIVE";
//							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
//							nextPaymentDate = sdf.format(vpmSubscrip.cancelledAt().getTime());
//							Date cancelledAt = sdf.parse(nextPaymentDate);
//							Date todayDate = sdf.parse(sdf.format(dateobj));
//
//							long difference = cancelledAt.getTime() - todayDate.getTime();
//							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
//							days_remaining = daysBetween;
//							vpmstatus_code = 1;
//							autoRenewalStatus = "Disabled";
//						} else {
//							status = "INACTIVE";
//							vpmstatus_code = 0;
//							strprice1 = "$0.0";
//						}
//						billingPeriod = "Monthly";
//						strprice1 = "$" + String.valueOf(price1);
//						str_total_cnt = "Unlimited";
//					}
//
//					ArrayList<Integer> vpmlist = crService.getVPMAvailabilty(user.getId(), "VPM", 0,
//							vpmSubscrip.planId());
//
//					if (!vpmlist.isEmpty()) {
//						availCnt = vpmlist.get(0);
//						totalCnt = vpmlist.get(1);
//						usedCnt = totalCnt - availCnt;
//
//						if (availCnt == 0) {
//							status = "INACTIVE";
//							vpmstatus_code = 0;
//							strprice1 = "$0.0";
//							nextPaymentDate = "NA";
//							billingPeriod = "NA";
//							str_total_cnt = "0";
//						}
//						if (vpmSubscrip.planId().equalsIgnoreCase("vet-chat") && totalCnt > 0)
//							str_total_cnt = totalCnt + "";
//					}
//
//					vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
//							str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod, vetchat_cancel,
//							cbvet_cancelId, cbvet_planId, cb_vetchat);
//				} else {
//
//					if (vpm_enable) {
//						ArrayList<Integer> vpmlist = crService.getVPMAvailabilty(user.getId(), "VPM", 0, "");
//						availCnt = vpmlist.get(0);
//						totalCnt = vpmlist.get(1);
//						usedCnt = totalCnt - availCnt;
//						status = "ACTIVE";
//						vpmstatus_code = 1;
//						str_total_cnt = availCnt + "";
//						if (vpm_addon)
//							str_total_cnt = "Unlimited";
//						if (availCnt == 0) {
//							status = "INACTIVE";
//							vpmstatus_code = 0;
//							strprice1 = "$0.0";
//							nextPaymentDate = "NA";
//							billingPeriod = "NA";
//							str_total_cnt = "0";
//						}
//					} else {
				availCnt = 0;
				totalCnt = 0;
				usedCnt = 0;
				status = "INACTIVE";
				vpmstatus_code = 0;
				strprice1 = "$0.0";
				nextPaymentDate = "NA";
				billingPeriod = "NA";
				str_total_cnt = "0";
//					}
				autoRenewalStatus = "Disabled";
				nextPaymentDate = "NA";
				billingPeriod = "NA";
				strprice1 = "$0.0";
				vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
						str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod, vetchat_cancel, cbvet_cancelId,
						cbvet_planId, cb_vetchat);
//				}

			} else {

				List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(String.valueOf(iris_splan), user.getId(),
						days_remaining);

				rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
						days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus, createDate,
						updateDate, cbSubId, cbSubStatus, availCredit, true, user.getChargebeeid(), startedAt, "",
						String.valueOf(iris_speriod), cancel, substatus_code, payment_due);

				availCnt = 0;
				totalCnt = 0;
				usedCnt = 0;
				status = "INACTIVE";
				autoRenewalStatus = "Disabled";
				nextPaymentDate = "NA";
				int vpmstatus_code = 0;
				String str_total_cnt = "0";
				billingPeriod = "NA";
				strprice1 = "0";

				vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
						str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod, vetchat_cancel, cbvet_cancelId,
						cbvet_planId, cb_vetchat);

			}

			boolean is_upgrade = true;
			int redirect = user.getInapp_purchase(); // default 1
			String upgrade_msg = "Success";

			if (isPaidPlan == true && (inapp_redirect == 2 || user.getInapp_purchase() == 2)) {
				inapp_redirect = 1;
				redirect = inapp_redirect;
				user.setInapp_purchase(inapp_redirect);
				// call user update method
				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());

			} else if ((isPaidPlan == false && inapp_redirect == 2 && user.getInapp_purchase() == 1)
					|| (isPaidPlan == false && inapp_redirect == 1 && user.getInapp_purchase() == 2)) {
				// update CB purchase status to 2[inapp] in user table
				user.setInapp_purchase(inapp_redirect);
				redirect = inapp_redirect;
				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());
			}

			redirect = user.getInapp_purchase();

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("planid", iris_splan);
			response.put("periodid", iris_speriod);
			response.put("subscriptionplan", rpt);
			response.put("vpm_enable", vpm_enable);
			response.put("vpmsubs", vpmSubs);

			response.put("is_upgrade", is_upgrade);
			response.put("redirect_inapp", redirect);
			response.put("upgrade_msg", upgrade_msg);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error while getting Subscription plan");
			log.error("getSubscription:" + e.getMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	public JResponse getIosSubscription(User user, int inapp_redirect) {
		// String vpmplan = _helper.getExternalConfigValue("vpmplan",
		// externalConfigService);

		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		try {

			// initialize values
			JVpmSubscription vpmSubs = new JVpmSubscription();
			String strprice1 = "NA";
			int availCnt = 0;
			int totalCnt = 0;
			int usedCnt = 0;

			int iris_splan = 1;
			int iris_speriod = 1;
			boolean vpm_enable = companyService.getVetCallStaus(user.giveCompany().getId());

			JSubscriptionPlanReport rpt = crService.getInappSubscriptionByUser(user.getId());

			if (rpt != null) {

				iris_splan = Integer.valueOf(rpt.getPlanid());
				iris_speriod = Integer.valueOf(rpt.getPeriodid());

				List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(rpt.getPlanid(), user.getId(),
						rpt.getDays_remaining());
				rpt.setListJGatewaySubSetup(setupList);

			} else {
				response = getSubscription(user, inapp_redirect);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (vpm_enable) {
				ArrayList<Integer> vpmlist = crService.getVPMAvailabilty(user.getId(), "VPM", 0, "NA");
				availCnt = vpmlist.get(0);
				totalCnt = vpmlist.get(1);
				usedCnt = totalCnt - availCnt;
			}
			vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, "NA", "NA", "NA", 0, "NA", "NA",
					false, "", "", false);

			boolean is_upgrade = true;
			int redirect = user.getInapp_purchase();
			String upgrade_msg = "Success";

			if (iris_splan != 1 && user.getInapp_purchase() == 2 && inapp_redirect == 1) {
				is_upgrade = false;
				redirect = user.getInapp_purchase();
				upgrade_msg = " You have purchased in App Store. Pls cancel subscription in app store and then purchase in android ";
			} else if (iris_splan == 1 && user.getInapp_purchase() == 2 && inapp_redirect == 1) {
				user.setInapp_purchase(inapp_redirect);
				redirect = inapp_redirect;
				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("planid", iris_splan);
			response.put("periodid", iris_speriod);
			response.put("subscriptionplan", rpt);
			response.put("vpm_enable", vpm_enable);
			response.put("vpmsubs", vpmSubs);
			response.put("is_upgrade", is_upgrade);
			response.put("redirect_inapp", redirect);
			response.put("upgrade_msg", upgrade_msg);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error while getting Subscription plan");
			log.error("getSubscription:" + e.getMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	public List<JGatewaySubSetup> checkDeviceConfigStatus(String planid, long userid, int days_remaining) {
		List<JGatewaySubSetup> setupList = new ArrayList<JGatewaySubSetup>();

		ArrayList<String> config = null;
		String mType = null;
		String[] dConfig = null;
		String[] devCount = null;
		int maxDevCnt = 0;
		boolean setupActivate = false;
		String showNextRenewal_withContent = "";
		HashMap<String, Long> newDevice = new HashMap<String, Long>();
		List<Gateway> gateways = gatewayService.getGatewayByUser("", "", "", "", userid, "");

		long cnt = 0;
		long curCnt = 0;
		long add_device_cnt = 0;
		int remaindays = -1;

		if (!gateways.isEmpty()) {
			cnt = gateways.size();
		}

		// ArrayList<Integer> ids = crService.getPlanAndPeriod(planid);

//		if (!planid.isEmpty()) {
//			config = crService.getDeviceConfig(Long.parseLong(planid));
//			mType = config.get(0);
//			dConfig = config.get(1).split(",");
//			maxDevCnt = Integer.parseInt(config.get(2));
//
//			if (dConfig[0].length() > 1) {
//				devCount = dConfig[0].split("/");
//
//				newDevice.put("1", Long.parseLong(devCount[0])); // key - monitortype , value - device count
//				newDevice.put("2", Long.parseLong(devCount[1]));
//			}
//		}

		// Reading device count from plan table
		if (!planid.isEmpty()) {
			mType = "1";
			dConfig = new String[1];
			dConfig[0] = "0";
			maxDevCnt = crService.getMaxDeviceCount(Long.parseLong(planid));
		}

		LinkedHashMap<String, Gateway> maplastGateway = crService.getGatewaysByReportTime(userid, 1);
		// LinkedHashMap<String, Gateway> mapfurbitlastGateway =
		// crService.getGatewaysByReportTime(userid, 2);

		// maplastGateway.putAll(mapfurbitlastGateway);

		for (Gateway gateway : gateways) {
			maplastGateway.put(gateway.getId() + "", gateway);
		}

		gateways = new ArrayList<Gateway>(maplastGateway.values());

		for (Gateway gateway : gateways) {
			if (!planid.isEmpty()) {
				if (Long.parseLong(planid) == 1) {
					setupActivate = true;
				} else {
					String gmonitor = String.valueOf(gateway.getModel().getMonitor_type().getId());

					if (mType.contains(gmonitor)) {
						// if dConfig contains 0 means can add any type upto no_cnt value
						if (dConfig[0].length() == 1 && dConfig[0].equalsIgnoreCase("0")) {

							if (maxDevCnt > add_device_cnt) {
								setupActivate = false;
								add_device_cnt = add_device_cnt + 1;
								remaindays = days_remaining;
							} else {
								setupActivate = true;
								remaindays = -1;
							}

						} else {
							curCnt = newDevice.get(gmonitor);

							if (curCnt >= 1) {
								newDevice.put(gmonitor, curCnt - 1);
								add_device_cnt = add_device_cnt + 1;
								setupActivate = false;
								remaindays = days_remaining;
							} else {
								setupActivate = true;
								remaindays = -1;
							}
						}
					} else {
						setupActivate = true;
						remaindays = -1;
					}
				}
			} else {
				setupActivate = true;
				remaindays = -1;
			}
			
			if((remaindays < days_tohandle_nextrenewal) && (remaindays > 0) && show_nextrenewal_popup) {
				showNextRenewal_withContent = "Subscription renews in " + remaindays + " days";
			}

			JGatewaySubSetup setup = new JGatewaySubSetup(gateway.getId(), setupActivate, remaindays,
					gateway.getMeid(), showNextRenewal_withContent);

			setupList.add(setup);
		}
		return setupList;
	}

	// Cancel Trial Subscription - Savitha.
	@RequestMapping(value = "/app/v4.0/cancelsubscription/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse cancelTrialSubscription(@RequestParam("os") String os,
			@RequestParam(value = "cancel_id", required = true) String cancel_id,
			@RequestParam(value = "product_id", required = true) String product_id,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		String auth = header.getFirst("auth");
		log.info(" Entered cancelTrialSubscription :" + auth);
		JResponse response = new JResponse();

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", auth);

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			Environment.configure(chargebeeSiteName, chargebeeSiteKey);
			if (usr != null) {
				Result res = Subscription.cancel(cancel_id)
						.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.PRORATE).request();

				Subscription cancelSubscription = res.subscription();

				if (cancelSubscription.status().toString().equalsIgnoreCase("cancelled")) {
//					Subscription createdSub = cbService.createDefaultSubsPlan(usr.getChargebeeid());

					response.put("Status", 1);
					response.put("Msg", "Success");
				} else {
					response.put("Status", 0);
					response.put("Msg", "Failed to cancel subscription!!!");
				}
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured");
			response.put("Error", e.getLocalizedMessage());
			log.error("cancelTrialSubscription : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// Cancel Subscription Plan- Kalai.
	@RequestMapping(value = "/app/v4.0/cancelsubplan/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse cancelsubplan(@RequestBody JCancelSubDetail jCancelSubDetail, @RequestParam("os") String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "end_term_cancel", defaultValue = "false", required = false) boolean end_term_cancel) {
		String auth = header.getFirst("auth");
		log.info(" Entered cancelsubplan :" + auth);
		JResponse response = new JResponse();
		boolean refund = false;

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", auth);

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			Environment.configure(chargebeeSiteName, chargebeeSiteKey);
			if (usr != null && !jCancelSubDetail.getCb_subid().equalsIgnoreCase("NA")) {

				ListResult res1 = com.chargebee.models.Subscription.list().id().is(jCancelSubDetail.getCb_subid())
						.request();
				Subscription subscrip = null;

				for (ListResult.Entry subs : res1) {
					subscrip = subs.subscription();
				}
				int invoice_due = 0;
				if (subscrip != null) {
					invoice_due = subscrip.dueInvoicesCount();
					if (invoice_due > 0) {
						ListResult invoiceList = Invoice.list().customerId().is(usr.getChargebeeid()).subscriptionId()
								.is(subscrip.id()).status().is(com.chargebee.models.Invoice.Status.PAYMENT_DUE)
								.request();
						if (invoiceList.isEmpty()) {
							invoice_due = 0; // no pending due
						} else {
							invoice_due = 1; // pending due is there
						}
					}
				}
				Result res = null;
				String msg = "";
				String emailMsg = "", cancel_type = "NA", curr_code = "NA";
				double exchange_rate = 1;
				int refund_amount = 0;
				String cancelled_at = "1753-01-01 00:00:00";

				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				sdf.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
				Date curStart = sdf.parse(sdf.format(subscrip.currentTermStart().getTime()));
				Date today = sdf.parse(new Helper().getCurrentTimeinUTC());

				long difference = today.getTime() - curStart.getTime();
				double daysBetween = Math.ceil((difference / (1000 * 60 * 60 * 24)));
				String subStatus = subscrip.status().toString();

				msg = "Your plan has been cancelled.";
				if (invoice_due > 0 || (subStatus.equalsIgnoreCase("paused"))) {
					res = Subscription.cancel(jCancelSubDetail.getCb_subid()).endOfTerm(false)
							.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.NONE).request();
					cancel_type = "immediate_cancel";
					cancelled_at = sdf.format(today);
				} else if (daysBetween <= 7) {
					if (end_term_cancel) {
						res = Subscription.cancel(jCancelSubDetail.getCb_subid()).endOfTerm(true)
								.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.NONE).request();
						Date nextRenewDate = sdf.parse(sdf.format(subscrip.nextBillingAt().getTime()));
						cancel_type = "end_of_the_term";
						cancelled_at = sdf.format(nextRenewDate);
						msg = "Your plan has been cancelled.  It'll take effect at the end date of the plan period.";
					} else {
						res = Subscription.cancel(jCancelSubDetail.getCb_subid()).endOfTerm(false)
								.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.NONE).request();
						cancel_type = "immediate_cancel";
						cancelled_at = sdf.format(today);

						Subscription cancelSubscription = res.subscription();
						subStatus = cancelSubscription.status().toString();

						if (subStatus.equalsIgnoreCase("cancelled") || subStatus.equalsIgnoreCase("non_renewing")) {
							ListResult invoiceList = Invoice.list().customerId().is(usr.getChargebeeid())
									.subscriptionId().is(subscrip.id()).sortByDate(SortOrder.DESC).limit(1).request();

							if (!invoiceList.isEmpty()) {
								for (ListResult.Entry inv : invoiceList) {
									JSONObject invResponse = new JSONObject();
									invResponse = new JSONObject(inv.invoice().toJson());

									exchange_rate = Double.parseDouble(invResponse.get("exchange_rate").toString());
									curr_code = inv.invoice().currencyCode();

									int amount_paid = inv.invoice().amountPaid();
									int refund_minus_amount = 0;
									boolean refundByProrated = Boolean.parseBoolean(refund_by_prorated);

									if (amount_paid > 0) {
										if (refundByProrated) {
											Date nextRenewDate = sdf
													.parse(sdf.format(subscrip.nextBillingAt().getTime()));
											long daysDiff = nextRenewDate.getTime() - curStart.getTime();
											double daysForRenewal = Math.ceil((daysDiff / (1000 * 60 * 60 * 24)));
											refund_minus_amount = (int) ((amount_paid / daysForRenewal) * daysBetween);
										} else
											refund_minus_amount = Integer.parseInt(minus_refund_amount);

										if (amount_paid < refund_minus_amount)
											refund_amount = amount_paid;
										else
											refund_amount = amount_paid - refund_minus_amount;

										try {
											Result result = Invoice.refund(inv.invoice().id())
													.refundAmount(refund_amount)
													.creditNoteReasonCode(ReasonCode.SERVICE_UNSATISFACTORY).request();
											Invoice invoice = result.invoice();
											Transaction transaction = result.transaction();
											CreditNote creditNote = result.creditNote();

											msg = "Waggle Subscription Cancelled. The refund will be processed in 24-48 hours.";
											refund = true;
										} catch (Exception e) {
											response.put("Refund", false);
											response.put("Error in refund", e.getLocalizedMessage());
											log.error("cancelsubplan : refund exception : " + e.getLocalizedMessage());
										}
									}
								}
							}
						}
					}
				} else {
					res = Subscription.cancel(jCancelSubDetail.getCb_subid()).endOfTerm(true)
							.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.NONE).request();
					Date nextRenewDate = sdf.parse(sdf.format(subscrip.nextBillingAt().getTime()));
					cancel_type = "end_of_the_term";
					cancelled_at = sdf.format(nextRenewDate);
					msg = "Your plan has been cancelled.  It'll take effect at the end date of the plan period.";
				}

				Subscription cancelSubscription = res.subscription();
				subStatus = cancelSubscription.status().toString();

				if (subStatus.equalsIgnoreCase("cancelled") || subStatus.equalsIgnoreCase("non_renewing")) {
					boolean status = cbService.saveCancelSubscription(jCancelSubDetail, usr.getId(), cancel_type,
							cancelled_at, refund_amount, exchange_rate, curr_code);
					File file = ResourceUtils.getFile("classpath:iris3.properties");
					Properties prop = new Properties();
					prop.load(new FileInputStream(file));

					String toAddr = prop.getProperty("to_address");
					String ccAddr = prop.getProperty("cc_address");
					String bccAddr = prop.getProperty("bcc_address");

					String sub = "CB Cancel Subscription: " + usr.getUsername();
					String mailmsg = "Hi Team, " + "<br />" + emailMsg
							+ " for the below customer. <br /><b> Username :</b>" + usr.getUsername()
							+ "<br /><b> CB customer ID:</b> " + usr.getChargebeeid()
							+ "<br /><b> CB Subscription ID:</b> " + jCancelSubDetail.getCb_subid();

//					async.SendEmail_SES(toAddr, ccAddr, bccAddr, sub, mailmsg);
					Template emailTemplate = (Template) templates.getTemplate("RefundEmail.ftl");
					Map<String, String> emailParams = new HashMap<>();
					if (refund) {
						emailTemplate = (Template) templates.getTemplate("RefundEmail.ftl");
						sub = "Confirmation of Waggle Pet Monitor Subscription Cancellation and Refund";
					} else if(!cancel_type.equalsIgnoreCase("immediate_cancel")) {
						emailTemplate = (Template) templates.getTemplate("ScheduledCancel.ftl");
						sub = "Confirmation: Cancelation Scheduled";
					}

					emailParams.put("Name", String.valueOf(usr.getFirstname()));
					emailParams.put("Year", String.valueOf(Calendar.getInstance().get(Calendar.YEAR)));

					ResponseEntity<String> newEmailContent = ResponseEntity
							.ok(FreeMarkerTemplateUtils.processTemplateIntoString(emailTemplate, emailParams));
					mailmsg = newEmailContent.getBody();
					async.SendEmail_SES(usr.getEmail(), null, bccAddr, sub, mailmsg);

					response.put("Status", 1);
					response.put("Msg", msg);
					response.put("Refund", refund);
				} else {
					response.put("Status", 0);
					response.put("Msg", "Failed to cancel subscription!!!");
				}
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured");
			response.put("Error", e.getLocalizedMessage());
			log.error("cancelsubplan : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "/app/v4.0/getavailableupgradeplanV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAvailableUpgradeSubscriptionPlanV2(@RequestHeader HttpHeaders header,
			@RequestParam("planid") long planid, @RequestParam("period") long period, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestParam("plan_ver") String plan_ver,
			Authentication authentication,
			@RequestParam(value = "freetrial", defaultValue = "false", required = false) boolean freetrial,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {

		JResponse response = new JResponse();
		Map<String, Object> resp = new HashMap<String, Object>();
		UserV4 user = null;
		String auth = header.getFirst("auth").trim();

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);
		} catch (Exception e) {
			log.error("get AvailableUpgrade:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			if (user != null) {
				if (user.getInapp_purchase() == 1) {

					String country = user.getCountry();
					country = "US";
					response = crService.upgradePlanList(user.getId(), planid, period, freetrial, country);

//						int days = userService.getRemainingDays(user.getId());
					String free_trial = "NA";
					String note = "NA";
//						if (days > 0) {
//							free_trial = String.valueOf(days).concat(" Days");
//							note = "* Please buy a subscription to proceed further. You will be billed only after the FREE subscription period ends and "
//									+ " you can choose to cancel anytime before the FREE subscription ends.";
//						}
					response.put("free_trial", free_trial);
					response.put("note", note);

				} else {
					response.put("Status", 0);
					response.put("Msg", "Upgrade list not found");
				}

			}
//			else {
//				response.put("Status", 0);
//				response.put("Msg", "User not exists");
//			}

		} catch (Exception e) {
			log.error("get AvailableUpgrade plans:" + e.getMessage());
			response.put("Error", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "/app/v4.0/updatesubscriptionplanV2", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateSubscriptionPlanV2(@RequestHeader HttpHeaders header, @RequestParam("planid") long planid,
			@RequestParam("periodid") long periodid, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestParam("plan_ver") String plan_ver,
			@RequestParam("cb_subid") String cb_subid, Authentication authentication,
			@RequestParam(value = "freetrial", defaultValue = "false", required = false) boolean freetrial,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "subsAction", defaultValue = "update", required = false) String subsAction,
			@RequestParam(value = "req_ver", defaultValue = "V1", required = false) String req_ver,
			@RequestParam(value = "cur_planid", defaultValue = "0", required = false) String cur_PlanId,
			@RequestParam(value = "cur_periodid", defaultValue = "0", required = false) String cur_PeriodId) {
		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		UserV4 user = null;
		log.info("Entered into updatesubscriptionplanV2");
		try {

			String auth = header.getFirst("auth");
			log.info("updateSubscriptionPlanV2 :Auth: " + auth + " cb_subid:" + cb_subid);

			user = userServiceV4.verifyAuthV3("authkey", auth);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			long curPlanId = Long.valueOf( cur_PlanId );
			long curPeriodId = Long.valueOf( cur_PeriodId );
			
			if (user != null) {
				log.info("user_id : "+ user.getId() +":: cur_planid : "+ curPlanId+" :: cur_periodid : "+curPeriodId+" :: upgrade_planid : "+ planid+ " :: upgrade_periodid : "+ periodid+" :: plan_ver : "+ plan_ver);
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					String cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(),
							user.getEmail(), user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}

				// Reactivation - resume paused subscription

				if (!cb_subid.isEmpty() && subsAction.equalsIgnoreCase("reactivate")) {
					try {
						Result res = Subscription.resume(cb_subid).resumeOption(ResumeOption.IMMEDIATELY).request();
						response.put("checkOutURL", "NA");
						response.put("Status", 1);
						response.put("Msg", "Subscription reactivated successfully!");
						return response;
					} catch (Exception e) {
						response.put("checkOutURL", "NA");
						response.put("Status", 0);
						response.put("Msg", "Reactivation failed. Please contact support.");
						return response;
					}
				}

				int addonStatus = 0;// #0 - free to paid #1-paid to paid #2- reactivation
				// boolean disableUpgrade = false; //false - upgrading from one paid to another
				// paid
				// true - upgrading from free to paid
				String activationId = "";
				String reactivationId = "";
				String upgradeId = "";
				String downgradeId = "";
				String country = user.getCountry().toUpperCase();
				country = "US";

				String[] cbPlanAndTrialPeriod = crService.getChargebeePlanById(planid, periodid, country);

				activationId = activationAddonId == null ? "setup_charges" : activationAddonId.get(country);
				reactivationId = reactivateAddonId == null ? "reactivation-charges-onetime"
						: reactivateAddonId.get(country);
				upgradeId = updateAddonId == null ? "upgrade_charges" : updateAddonId.get(country);
				downgradeId = downgradeAddonId == null ? "downgrade_charges" : downgradeAddonId.get(country);

				String cb_plan = cbPlanAndTrialPeriod[0];
				int freeTrialPeriod = Integer.parseInt(cbPlanAndTrialPeriod[1]);
				String cb_coupon_id = cbPlanAndTrialPeriod[2];
				String cb_addon_id = cbPlanAndTrialPeriod[3];
				int plan_to_period_id = Integer.parseInt( cbPlanAndTrialPeriod[4] );
				
				int cur_plan_to_period_id = 0;
				boolean lowerPlan = false;
				JPlanToUpgrade planToUpgrade = null;
				if( req_ver.equalsIgnoreCase("v2") ) {
					String[] curCBPlanAndTrialPeriod = crService.getChargebeePlanById(curPlanId, curPeriodId, country);
					cur_plan_to_period_id = Integer.parseInt( curCBPlanAndTrialPeriod[4] );
					planToUpgrade = crService.getPlanToUpgrade(plan_to_period_id, cur_plan_to_period_id);
				}
				
				List<String> addonIdList = Arrays.asList(cb_addon_id.split(","));
				String oneTimeAddonId = "";
				String subStatus = "";
				Subscription subscription = null;

				if (cb_subid.isEmpty())
					cb_subid = "NA";

				if (!cb_plan.equalsIgnoreCase("NA")) {
					if (!cb_subid.equalsIgnoreCase("NA")) {

						Result result = Subscription.retrieve(cb_subid).request();
						subscription = result.subscription();
						subStatus = subscription.status().name();
//
//						if(subStatus.equalsIgnoreCase("PAUSED")) {
//							try {
//								Result res = Subscription.resume(subscription.id()).resumeOption(ResumeOption.IMMEDIATELY).request();
//								response.put("checkOutURL", "NA");
//								response.put("Status", 1);
//								response.put("Msg", "Subscription reactivated successfully!");
//								return response;
//							}catch (Exception e) {
//								response.put("checkOutURL", "NA");
//								response.put("Status", 0);
//								response.put("Msg", "Reactivation process is failed");
//								return response;
//							}
//						}else 
						if (freeplan.contains(subscription.planId())) {
							addonStatus = 0; // activation
						} else if (!freeplan.contains(subscription.planId())
								// && !vpmplan.contains(subscription.planId()) &&
								// !addonplan.contains(subscription.planId())
								&& !(subStatus.equalsIgnoreCase("CANCELLED"))) {
							addonStatus = 1; // upgrade
						} else if (!freeplan.contains(subscription.planId())
								// && !vpmplan.contains(subscription.planId()) &&
								// !addonplan.contains(subscription.planId())
								&& (subStatus.equalsIgnoreCase("CANCELLED"))) {
							addonStatus = 2; // re-activation
						}
					} else {
						addonStatus = 0;
					}
					
					if( req_ver.equalsIgnoreCase("V2") && planToUpgrade == null && !cb_subid.equalsIgnoreCase("NA") && periodid < curPeriodId) {
						addonStatus = 3;
					}

					switch (addonStatus) {
					case 0:
						oneTimeAddonId = activationId;
						break;

					case 1:
						oneTimeAddonId = upgradeId;
						break;

					case 2:
						oneTimeAddonId = reactivationId;
						break;
						
					case 3:
						oneTimeAddonId = downgradeId;
						break;
					}					
					
//					int days = 0;
//					int daysBetween = 0;
//					OrderMappingDetails order = null;
					Timestamp trialEnd = null;

					// this for customer merge
					HashMap<String, Object> metaData = new HashMap<String, Object>();
					metaData.put("userid", user.getId());
					metaData.put("chargebeeid", user.getChargebeeid());
					String cancel_subid = "NA";
					String order_id = "NA";
					String orderchannel = "RV";
					CheckoutNewRequest checkoutNewRequest;
					CheckoutExistingRequest checkoutExitingRequest;
					Result res;
//Currently not used
//					if (amazonLaunchPadFreeTrialEnable) {
//						order = userService.getOrderMappingByUser(user.getId());
//					}
//					if (order != null) {
//						orderchannel = order.getOrderchannel();
//						days = (int) userService.getCreditAmountBySKU(order.getExternalsku());
//
//						if (days > 0) {
//							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//							Date todayDate = sdf.parse(sdf.format(new Date()));
//
//							Timestamp orderdate = order.getOrderdate();
//							log.info("orderdate: " + orderdate);
//							Date expirydate = new Date(orderdate.getTime());
//
//							Calendar cal = Calendar.getInstance();
//							cal.setTime(expirydate);
//							cal.add(Calendar.DATE, days);
//
//							expirydate = cal.getTime();
//
//							long difference = expirydate.getTime() - todayDate.getTime();
//							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
//
//							if (daysBetween > 0) {
//								String trialendDt = sdf.format(expirydate);
//								// tialendDt =
//								// tialendDt.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.ENDTIME);
//								trialEnd = Timestamp.valueOf(trialendDt);
//							} else
//								daysBetween = 0;
//						}
//					}
//					// String addonId = "setup_charge";
//					if (daysBetween > 0) {
//						log.info("updateSubscriptionPlanV2 : daysBetween > 0");
//						if (!cb_subid.equalsIgnoreCase("NA"))
//							cancel_subid = cb_subid;
//
//						order_id = String.valueOf(order.getId());
//
//						metaData.put("order_id", order_id);
//						metaData.put("cancel_subid", cancel_subid);
//						metaData.put("regarding", orderchannel);
//
//						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
//
//						checkoutNewRequest = HostedPage.checkoutNew().subscriptionTrialEnd(trialEnd)
//								.subscriptionPlanId(cb_plan).subscriptionPlanQuantity(1).addonId(0, oneTimeAddonId)
//								.addonQuantity(0, 1).addonBillingCycles(0, 1).customerFirstName(user.getFirstname())
//								.customerLastName(user.getLastname()).customerEmail(user.getEmail())
//								.customerPhone(user.getMobileno());
//
//						if (!cb_coupon_id.equalsIgnoreCase("NA")) {
//							checkoutNewRequest.subscriptionCoupon(cb_coupon_id);
//						}
//
//						if (!cb_addon_id.equalsIgnoreCase("NA")) {
//							int i = 0;
//							// index - 0 updateAddonId/activation addon. remaining addon starts with 1
//							for (int j = 0; j < addonIdList.size(); j++) {
//								i = j + 1;
//								checkoutNewRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1);
//							}
//						}
//						res = checkoutNewRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString())
//								.embed(embedupdate).request();
//
//					} else 
					if (!cb_subid.equalsIgnoreCase("NA") && freetrial && freeTrialPeriod > 0) {// &&
																								// !disableUpgrade
						log.info(
								"updateSubscriptionPlanV2 : !cb_subid.equalsIgnoreCase(\"NA\")&& freetrial && freeTrialPeriod >0");
						metaData.put("order_id", order_id);
						metaData.put("cancel_subid", cancel_subid);
						metaData.put("regarding", orderchannel);

						Timestamp trialEndDate = new Timestamp(
								Instant.now().plus(freeTrialPeriod, ChronoUnit.DAYS).toEpochMilli());

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);

						if (!subStatus.equalsIgnoreCase("CANCELLED") || freeplan.contains(subscription.planId())) {
							Result res1 = Subscription.cancel(cb_subid)
									.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.PRORATE)
									.request();
						}

						checkoutExitingRequest = HostedPage.checkoutExisting().subscriptionId(cb_subid)
								.subscriptionPlanId(cb_plan).addonId(0, oneTimeAddonId).addonQuantity(0, 1)
								.addonBillingCycles(0, 1).replaceAddonList(true).redirectUrl(redirtPetUrl)
								.passThruContent(jsonObj.toString()).embed(embedupdate).forceTermReset(true)
								.subscriptionTrialEnd(trialEndDate);

						if (!cb_coupon_id.equalsIgnoreCase("NA")) {
							checkoutExitingRequest.subscriptionCoupon(cb_coupon_id);
						}

						if (!cb_addon_id.equalsIgnoreCase("NA")) {
							int i = 0;
							for (int j = 0; j < addonIdList.size(); j++) {
								i = j + 1;
								checkoutExitingRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1);
							}
						}

						res = checkoutExitingRequest.request();

					} else if (!cb_subid.equalsIgnoreCase("NA")) {
						log.info("updateSubscriptionPlanV2 : !cb_subid.equalsIgnoreCase(\"NA\")");

						metaData.put("order_id", order_id);
						metaData.put("cancel_subid", cancel_subid);
						metaData.put("regarding", orderchannel);

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
//n
						checkoutExitingRequest = HostedPage.checkoutExisting().subscriptionId(cb_subid)
								.subscriptionPlanId(cb_plan).addonId(0, oneTimeAddonId).addonQuantity(0, 1)
								.addonBillingCycles(0, 1).replaceAddonList(true).redirectUrl(redirtPetUrl)
								.passThruContent(jsonObj.toString()).embed(embedupdate).forceTermReset(true);

						if (!cb_coupon_id.equalsIgnoreCase("NA")) {
							checkoutExitingRequest.subscriptionCoupon(cb_coupon_id);
						}

						if (!cb_addon_id.equalsIgnoreCase("NA")) {
							int i = 0;
							for (int j = 0; j < addonIdList.size(); j++) {
								i = j + 1;
								checkoutExitingRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1);
							}
						}

						res = checkoutExitingRequest.request();

					} else {
						log.info("updateSubscriptionPlanV2 : none of the if case matched :inside else case block");

						metaData.put("order_id", order_id);
						metaData.put("cancel_subid", cancel_subid);
						metaData.put("regarding", orderchannel);

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
						checkoutNewRequest = HostedPage.checkoutNew().subscriptionPlanId(cb_plan)
								.subscriptionPlanQuantity(1).addonId(0, oneTimeAddonId).addonQuantity(0, 1)
								.addonBillingCycles(0, 1).customerId(user.getChargebeeid());

						if (freeTrialPeriod > 0) {
							Timestamp trialEndDate = new Timestamp(
									Instant.now().plus(freeTrialPeriod, ChronoUnit.DAYS).toEpochMilli());
							checkoutNewRequest.subscriptionTrialEnd(trialEndDate);
						}

						if (!cb_coupon_id.equalsIgnoreCase("NA")) {
							checkoutNewRequest.subscriptionCoupon(cb_coupon_id);
						}

						if (!cb_addon_id.equalsIgnoreCase("NA")) {
							int i = 0;
							for (int j = 0; j < addonIdList.size(); j++) {
								i = j + 1;
								checkoutNewRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1);
							}
						}

						res = checkoutNewRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString())
								.embed(embedupdate).request();

					}

					HostedPage hostedPage = res.hostedPage();

					response.put("checkOutURL", hostedPage.url());
					response.put("Status", 1);
					response.put("Msg", "Success");

				} else {
					response.put("Status", 0);
					response.put("Msg", "chargebee plan id not available in DB");
				}
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Failed to update subscription");
			log.error("update subscriptionplan:" + e.getMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "/app/v4.0/getcurrentsubscriptionplanV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCurrentSubscriptionPlanV2(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestParam("plan_ver") String plan_ver,
			Authentication authentication,
			@RequestParam(value = "create_benefit", defaultValue = "true", required = false) boolean create_benefit,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "timezone", defaultValue = "", required = false) String timezone,
			@RequestParam(defaultValue = "0", required = false) Long gateway_id) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		Timestamp start = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("start : " + start + " getcurrentsubscriptionplan: " + auth);

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);

		} catch (Exception e) {
			log.error("getCurrentSubscriptionPlan: user by authkey : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		try {
			if (user != null) {

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				if (user.isEnable()) {
					int inapp_redirect = 1;
					boolean cb_checkout = true;
//					boolean show_alertlimit = false;
//					VersionMapping verObj = crService.getVersionMapping(app_ver, os);
//
//					if (verObj != null) {
//						inapp_redirect = verObj.getInapp_redirect();
//						cb_checkout = verObj.isCb_checkout();
//					}
//
//					if (!user.getPlan_ver().equalsIgnoreCase("V1"))
//						show_alertlimit = true;
//					
//					if (user.getInapp_purchase() == 1) {

					if (timezone == null || timezone.isEmpty() || timezone.equalsIgnoreCase("NA"))
						timezone = "-08:00";

					if (!timezone.contains("-") && !timezone.contains("+"))
						timezone = "+" + timezone.trim();

					response = cbService.getSubscriptionFromCB(user, inapp_redirect, os, app_ver, timezone,
							create_benefit, gateway_id);

					long gatewayId = userServiceV4.getUserGateway("userId", user.getId());
					boolean show_upgrade_button = false;
					if (gatewayId != 0)
						show_upgrade_button = true;

					response.put("show_addon", show_addon_button);
					// response.put("show_alertlimit", show_alertlimit);
					response.put("cb_checkout", cb_checkout);
					response.put("show_upgrade", show_upgrade_button);

					boolean restrict_alert = true;

					if (user.getPlan_ver().equalsIgnoreCase("V1"))
						restrict_alert = false;

					response.put("restrict_alert", restrict_alert);
//					} else {
//						response.put("Status", 0);
//						response.put("Msg", "Subscription Details not found");
//
//					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "User not enabled");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Please try again.");
			log.error("getCurrentSubscriptionPlan:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "/app/v5.0/getplanoffer", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getPlanOffersV5(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, Authentication authentication,
			@RequestParam("plan_id") long plan_id, @RequestParam("period_id") long period_id,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		log.info("getplanoffer :" + auth);

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);
		} catch (Exception e) {
			log.error("getplanoffer:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid User");
			response.put("Error", e.getMessage());
		}

		try {
			if (user != null) {
				String country = user.getCountry().toUpperCase();
				country = "US";
				response = crService.getplanoffers(user.getPlan_ver(), plan_id, period_id, country);
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			log.error("getPlanOffers Excep:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "/app/v4.0/getplanoffer", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getPlanOffers(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, Authentication authentication,
			@RequestParam("plan_id") long plan_id, @RequestParam("period_id") long period_id,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		log.info("getplanoffer :" + auth);

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);
		} catch (Exception e) {
			log.error("getplanoffer:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid User");
			response.put("Error", e.getMessage());
		}

		try {
			if (user != null) {
				String country = user.getCountry().toUpperCase();
				country = "US";
				response = crService.getplanoffers(user.getPlan_ver(), plan_id, period_id, country);
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			log.error("getPlanOffers Excep:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "/app/v4.0/applyoffer", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse applyOffer(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
			@RequestParam("cb_planid") String cb_planid, @RequestParam("cb_subid") String cb_subid,
			@RequestParam("cb_couponid") String cb_couponid,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		UserV4 user = null;
		String offer_status = "";
		String offer_status_dark = "";
		String offer_success_content = "";
		
		try {
			String auth = header.getFirst("auth");
			log.info("applyoffer :Auth: " + auth + " cb_subid:" + cb_subid);

			user = userServiceV4.verifyAuthV3("authkey", auth);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (user != null) {
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					String cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(),
							user.getEmail(), user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}

				int addonStatus = 0;
				String oneTimeAddonId = "";
				String activationId = "";
				String reactivationId = "";
				String upgradeId = "";
				String country = user.getCountry().toUpperCase();
				country = "US";

				activationId = activationAddonId == null ? "setup_charges" : activationAddonId.get(country);
				reactivationId = reactivateAddonId == null ? "reactivation-charges-onetime"
						: reactivateAddonId.get(country);
				upgradeId = updateAddonId == null ? "upgrade_charges" : updateAddonId.get(country);

				String subStatus = "";
				Subscription subscription = null;
				if (cb_subid.isEmpty())
					cb_subid = "NA";
				if (!cb_planid.equalsIgnoreCase("NA")) {
					if (!cb_subid.equalsIgnoreCase("NA")) {

						Result result = Subscription.retrieve(cb_subid).request();
						subscription = result.subscription();
						subStatus = subscription.status().name();

						if (freeplan.contains(subscription.planId())) {
							addonStatus = 0; // activation
						} else if (!freeplan.contains(subscription.planId()) && !vpmplan.contains(subscription.planId())
								&& !addonplan.contains(subscription.planId())
								&& !(subStatus.equalsIgnoreCase("CANCELLED"))) {
							addonStatus = 1; // upgrade
						} else if (!freeplan.contains(subscription.planId()) && !vpmplan.contains(subscription.planId())
								&& !addonplan.contains(subscription.planId())
								&& (subStatus.equalsIgnoreCase("CANCELLED"))) {
							addonStatus = 2; // re-activation
						}
					} else {
						addonStatus = 0;
					}

					switch (addonStatus) {
					case 0:
						oneTimeAddonId = activationId;
						break;

					case 1:
						oneTimeAddonId = upgradeId;
						break;

					case 2:
						oneTimeAddonId = reactivationId;
						break;
					}

					// this for customer merge
					HashMap<String, Object> metaData = new HashMap<String, Object>();
					metaData.put("userid", user.getId());
					metaData.put("chargebeeid", user.getChargebeeid());
					Result res;

					if (!cb_subid.equalsIgnoreCase("NA")) {
						log.info("applyOffer : cb_subid !=NA case ");

						metaData.put("regarding", "apply-applied");

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);

						CheckoutExistingRequest checkoutExitingRequest = HostedPage.checkoutExisting()
								.subscriptionId(cb_subid).subscriptionPlanId(cb_planid).addonId(0, oneTimeAddonId)
								.addonQuantity(0, 1).addonBillingCycles(0, 1).replaceAddonList(true)
								.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString()).embed(embedupdate)
								.forceTermReset(true);

						if (!cb_couponid.equalsIgnoreCase("NA")) {
							checkoutExitingRequest.subscriptionCoupon(cb_couponid);
						}

						res = checkoutExitingRequest.request();

					} else {
						log.info("applyOffer : cb_subid=NA case");

						metaData.put("regarding", "offer-applied");

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
						CheckoutNewRequest checkoutNewRequest = HostedPage.checkoutNew().subscriptionPlanId(cb_planid)
								.subscriptionPlanQuantity(1).addonId(0, oneTimeAddonId).addonQuantity(0, 1)
								.addonBillingCycles(0, 1).customerId(user.getChargebeeid());

						if (!cb_couponid.equalsIgnoreCase("NA")) {
							checkoutNewRequest.subscriptionCoupon(cb_couponid);
						}

						res = checkoutNewRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString())
								.embed(embedupdate).request();

					}

					HostedPage hostedPage = res.hostedPage();
					String checkout_url = hostedPage.url();

					String[] offer_status_arr = new String[3];

					if (!checkout_url.isEmpty() || checkout_url != null)
						offer_status_arr = crService.getOfferStatus(cb_planid);

					if (offer_status_arr == null) {
						offer_status = "<center><p style='color:#000;font-size: 12px;padding-left: 5px;'>Oops! Can't apply coupon. Try again</p></center>";
						response.put("checkOutURL", "NA");
						response.put("Status", 0);
						response.put("Msg", "Error");
						response.put("offer_status", offer_status);
					} else {
						offer_status = offer_status_arr[0];
						offer_status_dark = offer_status_arr[1];
						offer_success_content = offer_status_arr[2];

						response.put("checkOutURL", checkout_url);
						response.put("Status", 1);
						response.put("Msg", "Success");
						response.put("offer_status", offer_status);
						response.put("offer_status_dark", offer_status_dark);
						response.put("offer_success_content", offer_success_content);
					}

				} else {
					response.put("Status", 0);
					response.put("Msg", "chargebee plan id NA");
					offer_status = "<center><p style='color:#000;font-size: 12px;padding-left: 5px;'>Oops! Can't apply coupon. Try again</p></center>";
					offer_status_dark = "<center><p style='color:#ffffff;font-size: 12px;padding-left: 5px;'>Oops! Can't apply coupon. Try again</p></center>";
					response.put("checkOutURL", "NA");
					response.put("offer_status", offer_status);
					response.put("offer_status_dark", offer_status_dark);
				}
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("checkOutURL", "NA");
			offer_status = "<center><p style='color:#000;font-size: 12px;padding-left: 5px;'>Oops! Can't apply coupon. Try again</p></center>";
			offer_status_dark = "<center><p style='color:#ffffff;font-size: 12px;padding-left: 5px;'>Oops! Can't apply coupon. Try again</p></center>";
			response.put("offer_status", offer_status);
			response.put("offer_status_dark", offer_status_dark);
			response.put("Msg", "Failed to update subscription");
			log.error("applyoffer:" + e.getMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/chatbotsubscription", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getChatBot(@RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		String auth = header.getFirst("Authorization");
		if (auth == null || !auth.contains("Basic")) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authentication");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		String userid_ios = header.getFirst("userid_ios");
		String userid_android = header.getFirst("userid_android");
		String userId = "";
		if (userid_android == null || userid_android.isEmpty()) {
			userId = userid_ios;
		} else {
			userId = userid_android;
		}
		User user = null;
		Timestamp start = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("start : " + start + " chatbotsubscription : " + userId);
		try {
			user = userService.getUserById(userId);
		} catch (Exception e) {
			log.info("Invalid userid : " + user.getId() + " Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Userid");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		try {
			if (user != null) {
				if (user.isEnable()) {
					int inapp_redirect = 1;
					boolean cb_checkout = true;
					response = getSubscription(user, inapp_redirect);
					response.put("cb_checkout", cb_checkout);
				} else {
					response.put("Status", 0);
					response.put("Msg", "User not enabled");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");
			log.error("getCurrentSubscriptionPlan:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/updatepaymentchatbot", headers = "Accept=application/json", method = RequestMethod.GET)
	@ResponseBody
	public JResponse updatePaymentChatBot(@RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		String auth = header.getFirst("Authorization");
		if (auth == null || !auth.contains("Basic")) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authentication");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		log.info("auth :" + auth);
		String userid_ios = header.getFirst("userid_ios");
		String userid_android = header.getFirst("userid_android");
		String userId = "";
		if (userid_android == null || userid_android.isEmpty()) {
			userId = userid_ios;
		} else {
			userId = userid_android;
		}
		String chargebeeid = "NA";
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("id", userId);
			} catch (Exception e) {
				log.error("updatePaymentMethod:" + e.getMessage());
			}

			if (user != null) {

				Environment.configure(chargebeeSiteName, chargebeeSiteKey);

				chargebeeid = user.getChargebeeid();

				if (!chargebeeid.equalsIgnoreCase("NA")) {
					ListResult resultSet = Customer.list().id().is(chargebeeid).request();

					if (resultSet.size() > 0) {
						HostedPage hostedPage = null;

						if (checkoutversion.equalsIgnoreCase("v2")) {
							// v2
							Result res = HostedPage.updatePaymentMethod().customerId(chargebeeid).embed(false)
									.request();
							hostedPage = res.hostedPage();
						} else {
							// v3
							Result res = HostedPage.managePaymentSources().customerId(chargebeeid).request();
							hostedPage = res.hostedPage();
						}

						response.put("portalUrl", hostedPage.url());
						response.put("Status", 1);
						response.put("Msg", "Success");
					} else {
						response.put("portalUrl", "");
						response.put("Status", 0);
						response.put("Msg", "Unable to update payment method. Please try after some time.");
					}
				}
			}

		} catch (Exception e) {
			log.error("updatePaymentMethod:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// Flutter Api - Savitha
	@RequestMapping(value = "/app/v5.0/getavailableupgradeplan", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse geUpgradeSubscriptionPlanV2(@RequestHeader HttpHeaders header, @RequestParam("planid") long planid,
			@RequestParam("period") long period, @RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
			@RequestParam("plan_ver") String plan_ver, Authentication authentication,
			@RequestParam(value = "freetrial", defaultValue = "false", required = false) boolean freetrial,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country,
			@RequestParam(defaultValue = "upgrade", required = false) String type
			) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth").trim();

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);
		} catch (Exception e) {
			log.error("get AvailableUpgrade:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			if (user != null) {
				if (user.getInapp_purchase() == 1) {

					String country = user.getCountry();
					country = "US";

					response = crService.upgradePlanList_v5(user, planid, period, freetrial, country, type);

					String free_trial = "NA";
					String note = "NA";
					response.put("free_trial", free_trial);
					response.put("note", note);

				} else {
					response.put("Status", 0);
					response.put("Msg", "Upgrade list not found");
				}
			}

		} catch (Exception e) {
			log.error("get AvailableUpgrade plans:" + e.getMessage());
			response.put("Error", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// Get chargebee billing address - Savitha
	@RequestMapping(value = "/app/v4.0/getbillingaddress", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getBillingAddress(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, Authentication authentication,
			@RequestParam("user_id") long user_id) {
		log.error("Entered getBillingAddress for user_id :" + user_id);

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");

		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (Exception e) {
			log.error("getBillingAddress Exception : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid User");
			response.put("Error", e.getMessage());
		}

		try {
			if (user.getId() == user_id) {
				Environment.configure(chargebeeSiteName, chargebeeSiteKey);
				Result cusData = Customer.retrieve(user.getChargebeeid()).request();
				BillingAddress addressObj = new BillingAddress();

				addressObj.setUser_id(user_id);
				if (cusData.customer().billingAddress().firstName() != null)
					addressObj.setFirst_name(cusData.customer().billingAddress().firstName());
				if (cusData.customer().billingAddress().lastName() != null)
					addressObj.setLast_name(cusData.customer().billingAddress().lastName());
				if (cusData.customer().billingAddress().phone() != null)
					addressObj.setPhone_num(cusData.customer().billingAddress().phone());
				if (cusData.customer().billingAddress().city() != null)
					addressObj.setCity(cusData.customer().billingAddress().city());
				if (cusData.customer().billingAddress().state() != null)
					addressObj.setState(cusData.customer().billingAddress().state());
				if (cusData.customer().billingAddress().stateCode() != null)
					addressObj.setState_code(cusData.customer().billingAddress().stateCode());
				if (cusData.customer().billingAddress().country() != null)
					addressObj.setCountry(cusData.customer().billingAddress().country());
				if (cusData.customer().billingAddress().zip() != null)
					addressObj.setZip_code(cusData.customer().billingAddress().zip());

				com.chargebee.org.json.JSONObject obj = cusData.customer().billingAddress().jsonObj;

				if (!cusData.customer().billingAddress().line1().isEmpty())
					addressObj.setBilling_address_1(cusData.customer().billingAddress().line1());
				// For few address line 2 will not be there.
				if (obj.has("line2"))
					addressObj.setBilling_address_2(cusData.customer().billingAddress().line2());

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("Address", addressObj);
			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid User");
			}
		} catch (Exception e) {
			log.error("getBillingAddress Excep:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Error while getting address!");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// Save chargebee billing address - Savitha
	@RequestMapping(value = "/app/v4.0/savebillingaddress", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveBillingAddress(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, Authentication authentication,
			@RequestBody BillingAddress address) {
		log.error("Entered saveBillingAddress :");
		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");

		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (Exception e) {
			log.error("saveBillingAddress Exception : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid User");
			response.put("Error", e.getMessage());
		}

		try {
			if (user.getId() == address.getUser_id()) {
				Environment.configure(chargebeeSiteName, chargebeeSiteKey);
				Result res = Customer.updateBillingInfo(user.getChargebeeid()).billingAddressCity(address.getCity())
						.billingAddressCountry(address.getCountry()).billingAddressFirstName(address.getFirst_name())
						.billingAddressLastName(address.getLast_name())
						.billingAddressLine1(address.getBilling_address_1())
						.billingAddressLine2(address.getBilling_address_2()).billingAddressState(address.getState())
						.billingAddressZip(address.getZip_code()).billingAddressPhone(address.getPhone_num())
						.billingAddressStateCode(address.getState_code()).request();

				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid User");
			}
		} catch (Exception e) {
			log.error("saveBillingAddress Excep:" + e.getMessage());
			response.put("Status", 0);
			if (e.getMessage().toLowerCase().contains("zip"))
				response.put("Msg", "Invalid zip/postal code");
			else
				response.put("Msg", e.getMessage());
			response.put("Error", e.getMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "/app/v4.0/generateAdditionalBenefits", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse generateAdditionalBenefits(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestParam("period") String period,
			Authentication authentication) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		log.info("generateadditionalfeatures: " + auth);
		try {

			user = userServiceV4.verifyAuthV4("authkey", auth);

		} catch (Exception e) {
			log.error("generateadditionalfeatures: user by id : " + e.getMessage());
		}

		try {
			if (user != null) {

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
				boolean isAvail = iAvaService.checkBenefitsAvail(period); // period id
				String createdFrom = "cancel";
				
				if (isAvail) {
					boolean benefits_created = crService.checkAdditionalBenifitsCreated(user.getEmail(),
							Integer.parseInt(period));

					log.info("benefits_created:" + benefits_created);

					if (!benefits_created)
						async.generateAddiBenefits(user, period, createdFrom);

					response.put("iscreated", true);
					response.put("Status", 1);
					response.put("Msg", "Success");

				} else {
					response.put("iscreated", false);
					response.put("Status", 0);
					response.put("Msg", "Additional benefits not applicable");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			// e.printStackTrace();
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");
			log.error("generateadditionalfeatures:" + e.getLocalizedMessage());
		}
		return response;
	}
	
	@RequestMapping(value = "/app/v4.0/generatesubscriptioncoupon", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse generateSubscriptionCoupon(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, Authentication authentication) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		log.info("generateSubscriptionCoupon : " + auth);
		try {

			user = userServiceV4.verifyAuthV4("authkey", auth);

		} catch (Exception e) {
			log.error("generateSubscriptionCoupon : user by id : " + e.getMessage());
		}

		try {
			if (user != null) {

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				SimpleDateFormat sdfResponse = new SimpleDateFormat("yyyy-MM-dd");
				Calendar cal = Calendar.getInstance();
				
				String startDate = sdf.format( cal.getTime() );
				String startDateResponse = sdfResponse.format( cal.getTime() );
				
				cal.add(Calendar.MONTH, waggle_merch_coupon_exp_time);
				
				String due_date = sdf.format( cal.getTime() );
				String endDateResponse = sdfResponse.format( cal.getTime() );
				String period = String.valueOf(waggle_merch_coupon_exp_time);
				
				response = crService.generateSubCoupon(user, period, due_date);

				if( (int) response.get("Status") == 1) { //KART6280937
					String code = (String) response.get("couponCode");
					String endDate = (String) response.get("endDate");
					
					
					response.put("startDate", startDateResponse);
					response.put("endDate", endDateResponse);
					
					AdditionBenefitsCancelReward additionBenefitsCancelReward = null;
					
					additionBenefitsCancelReward = cancelService.getAdditionBenefitsCancelReward( user.getId() );
					if( additionBenefitsCancelReward == null ) {
						additionBenefitsCancelReward = new AdditionBenefitsCancelReward();
						additionBenefitsCancelReward.setCode(code);
						additionBenefitsCancelReward.setEnd_date(due_date);
						additionBenefitsCancelReward.setStart_date( startDate );
						additionBenefitsCancelReward.setUser_id( user.getId() );
					}
					
					additionBenefitsCancelReward = cancelService.saveOrUpdateAdditionBenefitsCancelReward( additionBenefitsCancelReward );
					if( additionBenefitsCancelReward == null ) {
						response.put("Status", 0);
						response.put("Msg", "Invalid session, Please try again");
						return response;
					}
					
				}
				
				response.put("iscreated", true);
				response.put("Status", 1);
				response.put("Msg", "Success");

			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			// e.printStackTrace();
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");
			log.error("generateadditionalfeatures:" + e.getLocalizedMessage());
		}
		return response;
	}
	
	@RequestMapping(value = "/app/v5.0/getupgradesubplans", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getupgradesubplans(@RequestHeader HttpHeaders header,Authentication authentication,
			@RequestParam("planid") long planid,@RequestParam("period") long period, @RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
			@RequestParam(value = "plan_ver", defaultValue = "V3", required = false) String plan_ver, 
			@RequestParam("monitortype_id") long monitortype_id,
			@RequestParam(value = "gatewayid", defaultValue = "0", required = false) long gatewayid,
			@RequestParam(defaultValue = "upgrade", required = false) String type) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth").trim();

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);
		} catch (Exception e) {
			log.error("getupgradesubplans:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			if (user != null) {

				String country = user.getCountry();

				country = "US";
				
				boolean defaultFreePlan = false;
				if(planid == 0) {
					if(planid==0 && (monitortype_id==4 || monitortype_id==1) ) {
						planid = 1;						
					}else {
						planid = crService.getplanByCountryCode(planid, country, monitortype_id);
					}
					period = 1;
					defaultFreePlan = true;
				}
				response = crService.getupgradesubplansV5(user, planid, period, monitortype_id, country, defaultFreePlan,gatewayid, type);
				response.put("monitortype_id", monitortype_id);
			}

		} catch (Exception e) {
			log.error("getupgradesubplans :" + e.getMessage());
			response.put("Error", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "/app/v5.0/getupgradesubplansv2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getupgradesubplansV2(@RequestHeader HttpHeaders header,Authentication authentication,
			@RequestParam("planid") long planid,@RequestParam("period") long period, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam(value = "plan_ver", defaultValue = "V3", required = false) String plan_ver, 
			@RequestParam("monitortype_id") long monitortype_id,
			@RequestParam(value = "gatewayid", defaultValue = "0", required = false) long gatewayid,
			@RequestParam(defaultValue = "upgrade", required = false) String type,
			@RequestParam(defaultValue = "Data-Plan,Flexi-Plan", required = false) String plantype,
			@RequestParam(defaultValue = "NA") String sub_id,
			@RequestParam(defaultValue = "false") boolean is_flexi) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth").trim();

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);
		} catch (Exception e) {
			log.error("getupgradesubplans:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			if (user != null) {

				String country = user.getCountry();

				country = "US";
				
				boolean defaultFreePlan = false;
				if(planid == 0) {
					if(planid==0 && (monitortype_id==4 || monitortype_id==1 || monitortype_id==11) ) {
						planid = 1;						
					}else {
						planid = crService.getplanByCountryCode(planid, country, monitortype_id);
					}
					period = 1;
					defaultFreePlan = true;
				}

				boolean restrict_flexi_plan_period = false;
				if(is_flexi){
					AllProductSubscription sub = cbService.getProductSubscriptionByGatewayId(gatewayid, user.getChargebeeid(),monitortype_id);
					FlexiPlanHistory flexiPlanDetails = crService.getFlexiplandetailsbySubid(sub_id);
					if(flexiPlanDetails != null){
						long hours = helper.getHoursBetweenDate(helper.getCurrentTimeinUTC(), flexiPlanDetails.getCurrent_cycle_end_at());
						if((flexiPlanDetails.getCurrent_cycle() < 3
								|| (flexiPlanDetails.getCurrent_cycle() == 3 && hours > 0))
						   		&& type.equalsIgnoreCase("upgrade")){
							restrict_flexi_plan_period = true;
						} else if(flexiPlanDetails.getCurrent_cycle() >= 3 && hours < 0
								&& sub.getSubscriptionStatus().equalsIgnoreCase("active")) {
							planid = 1;
							period = 1;
						}
					} else if(sub != null && sub.getPlanId().contains("flexi")
							&& !sub.getSubscriptionStatus().contains("cancel")
							&& type.equalsIgnoreCase("upgrade")) {
						restrict_flexi_plan_period = true;
					}

					if(type.equalsIgnoreCase("downgrade")) {
						type = "cancelall";
					}
				}

				response = crService.getUpgradeSubPlanV6(user, planid, period, monitortype_id, country, defaultFreePlan,gatewayid, type,plantype, sub_id, restrict_flexi_plan_period, is_flexi);
				response.put("monitortype_id", monitortype_id);
			}

		} catch (Exception e) {
			log.error("getupgradesubplans :" + e.getMessage());
			response.put("Error", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "/app/v5.0/updateproductsub", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateproductsub(@RequestHeader HttpHeaders header, @RequestBody JProdSubReq subReq,
			@RequestParam("os") String os,@RequestParam("app_ver") String app_ver,Authentication authentication,
			@RequestParam(value = "subsAction", defaultValue = "update", required = false) String subsAction,
			@RequestParam(value = "resumeDate", defaultValue = "NA", required = false) String resumeDate,
			@RequestParam(value = "feedbackid", defaultValue = "0", required = false) String feedbackid,
			@RequestParam(value = "review", defaultValue = "NA", required = false) String review,
			@RequestParam(value = "timezone", defaultValue = "+00:00", required = false) String timezone) {
		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		UserV4 user = null;
		log.info("Entered into updateproductsub");
		try {

			String auth = header.getFirst("auth");
			log.info("updateproductsub :Auth: " + auth + " cb_subid:" + subReq.getCbsubid());

			user = userServiceV4.verifyAuthV3("authkey", auth);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			if (user != null) {
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					String cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(),
							user.getEmail(), user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}
				
				// Reactivation - resume paused subscription

				if (subReq != null && subReq.getCbsubid() != null && subsAction.equalsIgnoreCase("reactivate")) {
					try {
						
						Result res = Subscription.retrieve(subReq.getCbsubid()).request();
						
						Subscription subscription = res.subscription();
						String status = subscription.status().toString();
						
						if (!subReq.getCbsubid().substring(0, 3).equalsIgnoreCase("RE-")) {
							if (status.equalsIgnoreCase("paused")) {
								Subscription.resume(subReq.getCbsubid()).resumeOption(ResumeOption.IMMEDIATELY)
										.request();
							} else {
								Subscription.removeScheduledPause(subReq.getCbsubid()).request();
							}

						}
						try {
							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
							Date resumeDateFormate = sdf.parse(resumeDate);
							
							SimpleDateFormat dateformate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							
							crService.saveOrUpdatePauseHistory(subReq.getCbsubid(),user.getId(),dateformate.format(resumeDateFormate),0,feedbackid,review);
						} catch (Exception e) {
							response.put("Status", 0);
							response.put("Return Time", System.currentTimeMillis());
							response.put("Msg", "Pause history save failed.");
							return response;
						}
						
						if (subReq.getCbsubid().substring(0, 3).equalsIgnoreCase("RE-")) {
							try {
								Properties prop = new Properties();
								File file = ResourceUtils.getFile("classpath:iris3.properties");
								prop.load(new FileInputStream(file));

								String to_address = prop.getProperty("to_address");
								String cc_address = prop.getProperty("cc_address");
								String bcc_address = prop.getProperty("bcc_address");

								String mailSub = "Reg Recharge user Resume Immediately Subscription : " + user.getUsername();
								String mailContent = "<p>Hi Team,</p>" + "<p>Find the user gateway immediately resume subscription request</p>";
								mailContent += "<br /><p>Email                : " + user.getEmail() + "</p>";
								mailContent += "<br /><p>Recharge customer ID : " + user.getRecharge_custid() + "</p>";
								mailContent += "<br /><p>Chargebee Id         : " + user.getChargebeeid() + "</p>";
								mailContent += "<br /><p>Gateway Id           : " + subReq.getGateway_id() + "</p>";
								mailContent += "<br /><p>Reason               : " + review + "</p>";

								mailContent = mailContent + "<br><br>Thanks,<br> Irisservice ";
								async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);
							} catch (Exception e) {
								log.error("Error in email for resume subscription:" + e.getMessage());
							}

						} else {

							try {
								File file = ResourceUtils.getFile("classpath:iris3.properties");
								Properties prop = new Properties();
								prop.load(new FileInputStream(file));

								String toAddr = prop.getProperty("to_address");
								String ccAddr = prop.getProperty("cc_address");
								String bccAddr = prop.getProperty("bcc_address");

								String sub = "CB Reactivated Subscription: " + user.getUsername();
								String mailmsg = "Hi Team, " + "<br />" + ""
										+ " for the below customer. <br /><b> Username :</b>" + user.getUsername()
										+ "<br /><b> CB customer ID:</b> " + user.getChargebeeid()
										+ "<br /><b> CB Subscription ID:</b> " + subReq.getCbsubid();

								mailmsg = mailmsg + "<br><br>Thanks,<br> Irisservice ";

								async.SendEmail_SES(toAddr, ccAddr, bccAddr, sub, mailmsg);

								JSendNotification sendNotification = new JSendNotification();

								if (subReq.getGateway_id() != 0) {

									long[] user_id = { user.getId() };
									String[] emailArr = { user.getEmail() };
									long[] gatewayid = {subReq.getGateway_id()};

									sendNotification.setPushNotificationId(resume_notify);
									sendNotification.setUserID(user_id);
									sendNotification.setEmaiID(emailArr);
									sendNotification.setGatewayId(gatewayid);

									pushNotificatonController.sendNotificationstoplanpage(auth, sendNotification);
								}

							} catch (Exception e) {
								log.error("Error in email for resume subscription:" + e.getMessage());
							}
						}
						response.put("checkOutURL", "NA");
						response.put("Status", 1);
						response.put("Return Time", System.currentTimeMillis());
						response.put("Msg", "Plan resumed successfully");
						return response;
					} catch (Exception e) {
						response.put("checkOutURL", "NA");
						response.put("Status", 0);
						response.put("Return Time", System.currentTimeMillis());
						response.put("Msg", "Reactivation failed. Please contact support.");
						return response;
					}
				}
				
				if (subReq != null && subReq.getCbsubid() != null && subsAction.equalsIgnoreCase("pause") && !resumeDate.equalsIgnoreCase("NA")) {
					try {
						
						timezone = timezone.replaceAll("\\s+", "");
						if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
							timezone = "+" + timezone;

						Date convertedDate = _helper.timeZoneConverter("yyyy-MM-dd HH:mm:ss", timezone, "+00:00", resumeDate + " 00:00:00");
						
						Timestamp resumeTimestamp = new Timestamp(convertedDate.getTime());
						
						if (!subReq.getCbsubid().substring(0, 3).equalsIgnoreCase("RE-")) {

							Result res = Subscription.pause(subReq.getCbsubid()).pauseOption(PauseOption.END_OF_TERM)
									.resumeDate(resumeTimestamp).request();
						}
						try {
							SimpleDateFormat dateformate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							crService.saveOrUpdatePauseHistory(subReq.getCbsubid(),user.getId(),dateformate.format(resumeTimestamp),1,feedbackid,review);
						} catch (Exception e) {
							response.put("Status", 0);
							response.put("Msg", "Pause history save failed.");
							return response;
						}
						
						if (subReq.getCbsubid().substring(0, 3).equalsIgnoreCase("RE-")) {
							try {
								Properties prop = new Properties();
								File file = ResourceUtils.getFile("classpath:iris3.properties");
								prop.load(new FileInputStream(file));

								String to_address = prop.getProperty("to_address");
								String cc_address = prop.getProperty("cc_address");
								String bcc_address = prop.getProperty("bcc_address");

								String mailSub = "Reg Recharge user Pause Subscription : " + user.getUsername();
								String mailContent = "<p>Hi Team,</p>" + "<p>Find the user gateway pause subscription request</p>";
								mailContent += "<br /><p>Email                : " + user.getEmail() + "</p>";
								mailContent += "<br /><p>Recharge customer ID : " + user.getRecharge_custid() + "</p>";
								mailContent += "<br /><p>Chargebee Id         : " + user.getChargebeeid() + "</p>";
								mailContent += "<br /><p>Gateway Id           : " + subReq.getGateway_id() + "</p>";
								mailContent += "<br /><p>Reason               : " + review + "</p>";

								mailContent = mailContent + "<br><br>Thanks,<br> Irisservice ";
								async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);
							} catch (Exception e) {
								log.error("Error in email for resume subscription:" + e.getMessage());
							}

						} else {
							try {
								File file = ResourceUtils.getFile("classpath:iris3.properties");
								Properties prop = new Properties();
								prop.load(new FileInputStream(file));

								String toAddr = prop.getProperty("to_address");
								String ccAddr = prop.getProperty("cc_address");
								String bccAddr = prop.getProperty("bcc_address");

								String sub = "CB Pause Subscription: " + user.getUsername();
								String mailmsg = "Hi Team, " + "<br />" + ""
										+ " for the below customer. <br /><b> Username :</b>" + user.getUsername()
										+ "<br /><b> CB customer ID:</b> " + user.getChargebeeid()
										+ "<br /><b> CB Subscription ID:</b> " + subReq.getCbsubid();

								mailmsg = mailmsg + "<br><br>Thanks,<br> Irisservice ";

								async.SendEmail_SES(toAddr, ccAddr, bccAddr, sub, mailmsg);

							} catch (Exception e) {
								log.error("Error in email for resume subscription:" + e.getMessage());
							}
						}
						response.put("checkOutURL", "NA");
						response.put("Status", 1);
						response.put("Return Time", System.currentTimeMillis());
						SimpleDateFormat df = new SimpleDateFormat("dd-MMM-yyyy");
						response.put("Msg", "Your plan will resume on \n"+df.format(resumeTimestamp));
						return response;
					} catch (Exception e) {
						response.put("checkOutURL", "NA");
						response.put("Status", 0);
						response.put("Return Time", System.currentTimeMillis());
						response.put("Msg", "Pause failed. Please contact support.");
						return response;
					}
				}
				
				response = cbService.updateproductsub(user, subReq);
				
				
			}

		} catch (Exception e) {
			e.printStackTrace();
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Failed to update subscription");
			log.error("Error updateproductsub:" + e.getMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "/app/v5.0/getmearisubcode", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getMeariSubCode(@RequestHeader HttpHeaders header, @RequestParam("gateway_id") long gateway_id,@RequestParam("period_id") long period_id,
			@RequestParam("os") String os,@RequestParam("app_ver") String app_ver,Authentication authentication,
                                     @RequestParam(value = "isSolarCam", defaultValue = "false", required = false) boolean isSolarCam) {
		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		UserV4 user = null;
		log.info("Entered into getMeariSubCode");
		try {

			String auth = header.getFirst("auth");
			user = userServiceV4.verifyAuthV3("authkey", auth);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			if (user != null) {
				String key = cbService.getMeariSubCode(user.getId(), gateway_id,period_id,isSolarCam);
				
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("keyname",key);
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Failed to get MeariSubCode");
			log.error("Error getMeariSubCode:" + e.getMessage());
		}
		return response;
	}

	@RequestMapping(value = "/app/v5.0/updatemeariactivation", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updatemeariactivation(@RequestHeader HttpHeaders header,Authentication authentication,
			@RequestParam("os") String os,@RequestParam("app_ver") String app_ver,@RequestParam("keyname") String keyname,
			 @RequestParam("gateway_id") long gateway_id, @RequestParam("period_id") long period_id) {
		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		UserV4 user = null;
		log.info("Entered into updatemeariactivation");
		try {

			String auth = header.getFirst("auth");
			log.info("updatemeariactivation :Auth: " + auth + " gateway_id:" + gateway_id);

			user = userServiceV4.verifyAuthV3("authkey", auth);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			if (user != null) {
				boolean status = cbService.updateMeariSubStatus(user.getId(), gateway_id,period_id, "renewed", keyname);
				if(status) {
					response.put("Status",1);
					response.put("Msg", "Success");
				}else {
					response.put("Status",0);
					response.put("Msg", "Failed");
				}
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Failed to update meari subscription");
			log.error("Error updatemeariactivation:" + e.getMessage());
		}
		return response;
	}

	@RequestMapping(value = "/app/v5.0/getproductsubscriptionV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getProductSubscriptionV2(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, Authentication authentication,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "timezone", defaultValue = "", required = false) String timezone,
			@RequestParam(defaultValue = "0", required = false) Long gateway_id,
			@RequestParam(defaultValue = "0", required = false) Long monitor_id) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		Timestamp start = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("start : " + start + " getProductSubscriptionV2: " + auth);

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);

		} catch (Exception e) {
			log.error("getProductSubscriptionV2: user by authkey : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		try {
			if (user != null) {
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				if (user.isEnable()) {
					int inapp_redirect = 1;
					boolean cb_checkout = true;

					if (timezone == null || timezone.isEmpty() || timezone.equalsIgnoreCase("NA"))
						timezone = "-08:00";

					if (!timezone.contains("-") && !timezone.contains("+"))
						timezone = "+" + timezone.trim();
					List<JGatewayInfo> gatewayList = gatewayServiceV4.getJGatewayInfo(user.getId(),monitor_id);
					
					List<JProductSubRes> subGatewayList = new ArrayList<JProductSubRes>();
					if(gatewayList != null) {
					for (JGatewayInfo gateway : gatewayList) {
						
						JProductSubRes response1 = cbService.getProductSubscriptionFromDBV1(user, os, app_ver, timezone,
								gateway.getGateway_id(), monitor_id);
						subGatewayList.add(response1);
						
					}
					}
					response.put("gatewaySubList", subGatewayList);
					String allProd = cbService.getProductSubscriptionByChargebee(user.getChargebeeid());
					boolean isActiveyearplan = false;
					if(allProd != null) {
						isActiveyearplan = true;
					}
					response.put("is_priorityuser", isActiveyearplan);
					response.put("priorityemail", priorityemail);
					response.put("priorityphone", priorityphone);
					response.put("Status", 1);
					response.put("Msg", "Success");
				} else {
					response.put("Status", 0);
					response.put("Msg", "User not enabled");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Please try again.");
			log.error("getProductSubscriptionV2:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "/app/v5.0/getproductsubscriptionplanV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getproductsubscriptionplanV2(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, Authentication authentication,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "timezone", defaultValue = "", required = false) String timezone,
			@RequestParam(defaultValue = "0", required = false) Long gateway_id,
			@RequestParam("monitor_id") Long monitor_id) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		Timestamp start = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("start : " + start + " getproductsubscriptionplanV2: " + auth);

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);

		} catch (Exception e) {
			log.error("getproductsubscriptionplanV2: user by authkey : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		try {
			if (user != null) {
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				if (user.isEnable()) {
					int inapp_redirect = 1;
					boolean cb_checkout = true;

					if (timezone == null || timezone.isEmpty() || timezone.equalsIgnoreCase("NA"))
						timezone = "-08:00";

					if (!timezone.contains("-") && !timezone.contains("+"))
						timezone = "+" + timezone.trim();
					List<JProductSubResV2> subGatewayList = new ArrayList<JProductSubResV2>();

					if (monitor_id != 11) {
						List<JGatewayInfo> gatewayList = gatewayServiceV4.getJGatewayInfo(user.getId(), monitor_id);

						if (gatewayList != null) {
							for (JGatewayInfo gateway : gatewayList) {

								JProductSubResV2 response1 = cbService.getProductSubscriptionplanV2FromDB(user, os,
										app_ver, timezone, gateway.getGateway_id(), monitor_id);
								subGatewayList.add(response1);

							}
							response.put("gatewaySubList", subGatewayList);

						}
					} else {//For vetchat
						JProductSubResV2 response1 = cbService.getProductSubscriptionplanV2FromDB(user, os, app_ver,
								timezone, (long)0, (long)11);
						subGatewayList.add(response1);
						response.put("gatewaySubList", subGatewayList);

					}
					String allProd = cbService.getProductSubscriptionByChargebee(user.getChargebeeid());
					boolean isActiveyearplan = false;
					if(allProd != null) {
						isActiveyearplan = true;
					}
					response.put("is_priorityuser", isActiveyearplan);
					response.put("priorityemail", priorityemail);
					response.put("priorityphone", priorityphone);
					response.put("Status", 1);
					response.put("Msg", "Success");
				} else {
					response.put("Status", 0);
					response.put("Msg", "User not enabled");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Please try again.");
			log.error("getproductsubscriptionplanV2:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "/app/v5.0/updateproductsubV2", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateproductsubV2(@RequestHeader HttpHeaders header, @RequestBody JProdSubReq subReq,
			@RequestParam("os") String os,@RequestParam("app_ver") String app_ver,Authentication authentication,
			@RequestParam(value = "subsAction", defaultValue = "update", required = false) String subsAction,
			@RequestParam(value = "timezone", defaultValue = "+00:00", required = false) String timezone,
			@RequestParam(value = "plan_name", defaultValue = "NA") String planName) {
		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		UserV4 user = null;
		log.info("Entered into updateproductsubV2");
		try {

			String auth = header.getFirst("auth");
			log.info("updateproductsubV2 :Auth: " + auth + " cb_subid:" + subReq.getCbsubid());

			user = userServiceV4.verifyAuthV3("authkey", auth);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (user != null) {

				// Reactivation - resume paused subscription

				if (subReq != null && subReq.getCbsubid() != null && subsAction.equalsIgnoreCase("reactivate")) {

					try {

						if (subReq.isIs_flexi()) {

							Result res = Subscription.retrieve(subReq.getCbsubid()).request();

							Subscription subscription = res.subscription();

							FlexiPlanHistory flexiplan = crService.getFlexiplandetailsbySubid(subReq.getCbsubid());
							if (flexiplan == null) {
								FlexiPlanHistory newFlexi = new FlexiPlanHistory();
								newFlexi.setSubscription_id(subReq.getCbsubid());
								newFlexi.setGateway_id(subReq.getGateway_id());

								SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
								sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
								newFlexi.setPlan_started_at(sdf.format(subscription.startedAt()));

								Date today = sdf.parse(new Helper().getCurrentTimeinUTC());

								newFlexi.setCurrent_cycle_started_at(sdf.format(today));

								Date thirtyDaysLater = new Date(today.getTime() + (30L * 24 * 60 * 60 * 1000));

								newFlexi.setCurrent_cycle_end_at(sdf.format(thirtyDaysLater));
								newFlexi.setIs_paused(0);
								newFlexi.setCurrent_cycle(1);
								newFlexi.setUpdated_on(sdf.format(today));
								crService.saveorupdateflexiplanhistory(newFlexi);

								JSubManage jSubManage = new JSubManage();
								jSubManage.setCbsubid(subReq.getCbsubid());
								ArrayList<Long> gatewayId = new ArrayList<>();
								gatewayId.add(subReq.getGateway_id());
								jSubManage.setGatewayid(gatewayId);
								jSubManage.setChargebeeid(user.getChargebeeid());
								jSubManage.setCbPlan(planName);
								crService.assignGatewayFeatureForPetMonitor(user, jSubManage,
										"subscription_changed", false);

								updateFlexiPlanRecuring(user, timezone, subReq.getCbsubid());
							} else if (flexiplan != null && flexiplan.getCurrent_cycle() == 0) {
								SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
								sdf.setTimeZone(TimeZone.getTimeZone("UTC"));

								Date today = sdf.parse(new Helper().getCurrentTimeinUTC());

								flexiplan.setCurrent_cycle_started_at(sdf.format(today));
								Date thirtyDaysLater = new Date(today.getTime() + (30L * 24 * 60 * 60 * 1000));

								flexiplan.setCurrent_cycle_end_at(sdf.format(thirtyDaysLater));
								flexiplan.setIs_paused(0);
								flexiplan.setCurrent_cycle(flexiplan.getCurrent_cycle() + 1);
								flexiplan.setUpdated_on(new Helper().getCurrentTimeinUTC());
								crService.saveorupdateflexiplanhistory(flexiplan);

								JSubManage jSubManage = new JSubManage();
								jSubManage.setCbsubid(subReq.getCbsubid());
								ArrayList<Long> gatewayId = new ArrayList<>();
								gatewayId.add(subReq.getGateway_id());
								jSubManage.setGatewayid(gatewayId);
								jSubManage.setChargebeeid(user.getChargebeeid());
								jSubManage.setCbPlan(planName);
								crService.assignGatewayFeatureForPetMonitor(user, jSubManage,
										"subscription_changed", false);

								updateFlexiPlanRecuring(user, timezone, subReq.getCbsubid());
							} else if (flexiplan != null) {
								if (flexiplan.getCurrent_cycle() < 3) {

									SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
									sdf.setTimeZone(TimeZone.getTimeZone("UTC"));

									Date today = sdf.parse(new Helper().getCurrentTimeinUTC());

									flexiplan.setCurrent_cycle_started_at(sdf.format(today));
									Date thirtyDaysLater = new Date(today.getTime() + (30L * 24 * 60 * 60 * 1000));

									flexiplan.setCurrent_cycle_end_at(sdf.format(thirtyDaysLater));

									flexiplan.setIs_paused(0);
									flexiplan.setCurrent_cycle(flexiplan.getCurrent_cycle() + 1);
									flexiplan.setUpdated_on(new Helper().getCurrentTimeinUTC());
									crService.saveorupdateflexiplanhistory(flexiplan);

									JSubManage jSubManage = new JSubManage();
									jSubManage.setCbsubid(subReq.getCbsubid());
									ArrayList<Long> gatewayId = new ArrayList<>();
									gatewayId.add(subReq.getGateway_id());
									jSubManage.setGatewayid(gatewayId);
									jSubManage.setChargebeeid(user.getChargebeeid());
									jSubManage.setCbPlan(planName);
									crService.assignGatewayFeatureForPetMonitor(user, jSubManage,
											"subscription_changed", false);

									updateFlexiPlanRecuring(user, timezone, subReq.getCbsubid());
								}
							}
                            String msurl = txnservice_url + "/v3.0/activateverizonbygateway?"
                                    + "cbid=" + user.getChargebeeid()
                                    +"&cbemail="+user.getEmail()
                                    +"&eventname=subscription_created"
                                    + "&gatewayid=" + subReq.getGateway_id()
                                    + "&subId=" + subReq.getCbsubid()
                                    +"&FlexiPause=true";
                            log.info("Call txnservice gatewayfeature API :" + msurl);

                            _helper.httpPOSTRequest(msurl, null, null);

                            response.put("checkOutURL", "NA");
							response.put("Status", 1);
							response.put("Return Time", System.currentTimeMillis());
							response.put("Msg", "Activated successfully");
							return response;

						}
					} catch (Exception e) {
						response.put("checkOutURL", "NA");
						response.put("Status", 0);
						response.put("Return Time", System.currentTimeMillis());
						response.put("Msg", "Activation failed. Please contact support.");
						return response;
					}
				}

				if (subReq != null && subReq.getCbsubid() != null && subsAction.equalsIgnoreCase("pause")) {
					try {

						if (subReq.isIs_flexi()) {

							FlexiPlanHistory flexiplan = crService.getFlexiplandetailsbySubid(subReq.getCbsubid());
							if (flexiplan != null && flexiplan.getPaused_count() < 2) {
								if (flexiplan.getCurrent_cycle() < 3) {
									flexiplan.setIs_paused(1);
									flexiplan.setPaused_count(flexiplan.getPaused_count() + 1);
									flexiplan.setUpdated_on(new Helper().getCurrentTimeinUTC());
									crService.saveorupdateflexiplanhistory(flexiplan);

//									try {
//										String jobName = user.getId() + "_" + subReq.getCbsubid();
//										jobService.deleteJob(jobName, "FLEXI_PLAN_RENEWAL");
//									} catch (Exception e) {
//										log.error("Error in deleteJob subscription:" + e.getMessage());
//									}
//
//									JSubManage jSubManage = new JSubManage();
//									jSubManage.setCbsubid(subReq.getCbsubid());
//									ArrayList<Long> gatewayId = new ArrayList<>();
//									gatewayId.add(subReq.getGateway_id());
//									jSubManage.setGatewayid(gatewayId);
//									jSubManage.setChargebeeid(user.getChargebeeid());
//									jSubManage.setCbPlan(planName);
//									crService.assignGatewayFeatureForPetMonitor(user, jSubManage,
//											"subscription_cancelled", true);

									response.put("checkOutURL", "NA");
									response.put("Status", 1);
									response.put("Return Time", System.currentTimeMillis());
									response.put("Msg", "Paused successfully.");
									return response;
								}




							} else {
								response.put("checkOutURL", "NA");
								response.put("Status", 0);
								response.put("Return Time", System.currentTimeMillis());
								response.put("Msg", "Already exceeded pause limit.");
								return response;
							}

						}

						response.put("checkOutURL", "NA");
						response.put("Status", 0);
						response.put("Return Time", System.currentTimeMillis());
						response.put("Msg", "Pause failed. Please contact support.");
						return response;

					} catch (Exception e) {
						response.put("checkOutURL", "NA");
						response.put("Status", 0);
						response.put("Return Time", System.currentTimeMillis());
						response.put("Msg", "Pause failed. Please contact support.");
						return response;
					}
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Failed to update subscription");
			log.error("Error updateproductsubV2:" + e.getMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	public boolean updateFlexiPlanRecuring(UserV4 user, String timeZone, String subscriptionId) {
		log.info("Entered into updateFlexiPlanRecuring :: subscriptionId : "+ subscriptionId);
		try {
			
			if( subscriptionId != null ) {
				
				List<Date> dateList = new ArrayList<>();
				
				Date conDate = new Date(System.currentTimeMillis() + (30L * 24 * 60 * 60 * 1000));
				dateList.add( conDate );
					
				String jobName = user.getId()+"_"+subscriptionId;
				String groupName = "FLEXI_PLAN_RENEWAL";
				
				HashMap<String, Object> userDetails = new HashMap<>();
				userDetails.put("user_id", user.getId());
				userDetails.put("email", user.getEmail());
				userDetails.put("auth", user.getAuthKey());
				userDetails.put("subscriptionId", subscriptionId);
			
				boolean jobCreatedStatus = jobService.scheduleRepeatJobWithMultipleTriggers(jobName, FlexiPlanRenewal.class, dateList, groupName, user.getId()+"", groupName, 0, userDetails);
				
				log.info("updateFlexiPlanRecuring job created status : "+ jobCreatedStatus);
				
				
			} else {
				log.info("Not a found subscriptionId");
			}
			
		} catch (Exception e) {
			log.info("Error in updateFlexiPlanRecuring :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}
	
	@RequestMapping(value = "/app/v5.0/getvetplans", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getVetPlans(@RequestHeader HttpHeaders header,Authentication authentication,
			@RequestParam("os") String os,	@RequestParam("app_ver") String app_ver,
			@RequestParam("planid") long planid,@RequestParam("period") long period,
			@RequestParam(value = "plan_ver", defaultValue = "V3", required = false) String plan_ver, 
			@RequestParam(defaultValue = "Vet-Plan", required = false) String plantype) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth").trim();

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);
		} catch (Exception e) {
			log.error("getupgradesubplans:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			
			ArrayList<JVetPlanDetails> vetPlanDetails = crService.getVetPlanTerms(plantype);
			response.put("vetPlanDetails", vetPlanDetails);
			response.put("Status", 1);
			response.put("Msg", "Success");
		} catch (Exception e) {
			log.error("getupgradesubplans :" + e.getMessage());
			response.put("Error", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	
	@RequestMapping(value = "/app/v5.0/getupgradesubplansv3", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getupgradesubplansV3(@RequestHeader HttpHeaders header,Authentication authentication,
			@RequestParam("planid") long planid,@RequestParam("period") long period, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam(value = "plan_ver", defaultValue = "V3", required = false) String plan_ver, 
			@RequestParam("monitortype_id") long monitortype_id,
			@RequestParam(value = "gatewayid", defaultValue = "0", required = false) long gatewayid,
			@RequestParam(defaultValue = "upgrade", required = false) String type,
			@RequestParam(defaultValue = "Data-Plan,Flexi-Plan", required = false) String plantype,
			@RequestParam(defaultValue = "NA") String sub_id,
			@RequestParam(defaultValue = "false") boolean is_flexi) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth").trim();

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);
		} catch (Exception e) {
			log.error("getupgradesubplans:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			if (user != null) {

				String country = user.getCountry();

				country = "US";

				boolean defaultFreePlan = false;
				if(planid == 0) {
					if(planid==0 && (monitortype_id==4 || monitortype_id==1) ) {
						planid = 1;						
					}else {
						planid = crService.getplanByCountryCode(planid, country, monitortype_id);
					}
					period = 1;
					defaultFreePlan = true;
				}

				boolean restrict_flexi_plan_period = false;
				if(is_flexi){
					AllProductSubscription sub = cbService.getProductSubscriptionByGatewayId(gatewayid, user.getChargebeeid(),monitortype_id);
					FlexiPlanHistory flexiPlanDetails = crService.getFlexiplandetailsbySubid(sub_id);
					if(flexiPlanDetails != null){
						long hours = helper.getHoursBetweenDate(helper.getCurrentTimeinUTC(), flexiPlanDetails.getCurrent_cycle_end_at());
						if((flexiPlanDetails.getCurrent_cycle() < 3
								|| (flexiPlanDetails.getCurrent_cycle() == 3 && hours > 0))
						   		&& type.equalsIgnoreCase("upgrade")){
							restrict_flexi_plan_period = true;
						} else if(flexiPlanDetails.getCurrent_cycle() >= 3 && hours < 0
								&& sub.getSubscriptionStatus().equalsIgnoreCase("active")) {
							planid = 1;
							period = 1;
						}
					} else if(sub != null && sub.getPlanId().contains("flexi")
							&& !sub.getSubscriptionStatus().contains("cancel")
							&& type.equalsIgnoreCase("upgrade")) {
						restrict_flexi_plan_period = true;
					}

					if(type.equalsIgnoreCase("downgrade")) {
						type = "cancelall";
					}
				}

				response = crService.getUpgradeSubPlanV7(user, planid, period, monitortype_id, country, defaultFreePlan,gatewayid, type,plantype, sub_id, restrict_flexi_plan_period, is_flexi);
				response.put("monitortype_id", monitortype_id);
			}

		} catch (Exception e) {
			log.error("getupgradesubplans :" + e.getMessage());
			response.put("Error", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "/app/v5.0/getupgradesubplansv4", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getupgradesubplansV4(@RequestHeader HttpHeaders header,Authentication authentication,
										  @RequestParam("planid") long planid,@RequestParam("period") long period, @RequestParam("os") String os,
										  @RequestParam("app_ver") String app_ver,
										  @RequestParam(value = "plan_ver", defaultValue = "V3", required = false) String plan_ver,
										  @RequestParam("monitortype_id") long monitortype_id,
										  @RequestParam(value = "gatewayid", defaultValue = "0", required = false) long gatewayid,
										  @RequestParam(defaultValue = "upgrade", required = false) String type,
										  @RequestParam(defaultValue = "Data-Plan,Flexi-Plan", required = false) String plantype,
										  @RequestParam(defaultValue = "NA") String sub_id,
										  @RequestParam(defaultValue = "false") boolean is_flexi) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth").trim();

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);
		} catch (Exception e) {
			log.error("getupgradesubplans:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			if (user != null) {

				String country = user.getCountry();

				country = "US";

				boolean defaultFreePlan = false;
				if(planid == 0) {
					if(planid==0 && (monitortype_id==4 || monitortype_id==1) ) {
						planid = 1;
					}else {
						planid = crService.getplanByCountryCode(planid, country, monitortype_id);
					}
					period = 1;
					defaultFreePlan = true;
				}

				boolean restrict_flexi_plan_period = false;
				if(is_flexi){
					AllProductSubscription sub = cbService.getProductSubscriptionByGatewayId(gatewayid, user.getChargebeeid(),monitortype_id);
					FlexiPlanHistory flexiPlanDetails = crService.getFlexiplandetailsbySubid(sub_id);
					if(flexiPlanDetails != null){
						long hours = helper.getHoursBetweenDate(helper.getCurrentTimeinUTC(), flexiPlanDetails.getCurrent_cycle_end_at());
						if((flexiPlanDetails.getCurrent_cycle() < 3
								|| (flexiPlanDetails.getCurrent_cycle() == 3 && hours > 0))
								&& type.equalsIgnoreCase("upgrade")){
							restrict_flexi_plan_period = true;
						} else if(flexiPlanDetails.getCurrent_cycle() >= 3 && hours < 0
								&& sub.getSubscriptionStatus().equalsIgnoreCase("active")) {
							planid = 1;
							period = 1;
						}
					} else if(sub != null && sub.getPlanId().contains("flexi")
							&& !sub.getSubscriptionStatus().contains("cancel")
							&& type.equalsIgnoreCase("upgrade")) {
						restrict_flexi_plan_period = true;
					}

					if(type.equalsIgnoreCase("downgrade")) {
						type = "cancelall";
					}
				}

				response = crService.getUpgradeSubPlanV8(user, planid, period, monitortype_id, country, defaultFreePlan,gatewayid, type,plantype, sub_id, restrict_flexi_plan_period, is_flexi);
                if ( allowVetChatFreeTrial) {

                    boolean isEligibleForVetChatFreeTrial=crService.isEligibleForVetChatFreeTrial(user.getChargebeeid());

                    HashMap<String, Object> vetChatFreeTrialObject = new HashMap<>();

                    response.put("isFreeVetChatTrial_Eligible", isEligibleForVetChatFreeTrial);
                    if(isEligibleForVetChatFreeTrial) {

                        response.put("vetChatFreeTrialInfo", crService.setVetChatFreeTrialContent(vetChatFreeTrialObject));

                    }


                }
				response.put("monitortype_id", monitortype_id);
				response.put("flexiPlan_activation_count",flexi_plan_activation_count);
				response.put("comboCompare4g",comboCompareImage4g);
			}

		} catch (Exception e) {
			log.error("getupgradesubplans :" + e.getMessage());
			response.put("Error", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}



    @RequestMapping(value = "/app/v5.0/managecancelplans", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getupgradesubplansV5(@RequestHeader HttpHeaders header,Authentication authentication,
										  @RequestParam("planid") long planid,
										  @RequestParam("period") long period,
										  @RequestParam("os") String os,
										  @RequestParam("app_ver") String app_ver,
										  @RequestParam(value = "plan_ver", defaultValue = "V3", required = false) String plan_ver,
										  @RequestParam("monitortype_id") long monitortype_id,
										  @RequestParam(value = "gatewayid", defaultValue = "0", required = false) long gatewayid,
										  @RequestParam(defaultValue = "upgrade", required = false) String type,
										  @RequestParam(defaultValue = "Data-Plan,Flexi-Plan", required = false) String plantype,
										  @RequestParam(defaultValue = "NA") String sub_id,
										  @RequestParam(defaultValue = "false") boolean is_flexi) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth").trim();

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);
		} catch (Exception e) {
			log.error("getupgradesubplansv5:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			if (user != null) {

				String country = user.getCountry();

				country = country.equalsIgnoreCase("CA") ? "CA" : "US";

				boolean defaultFreePlan = false;
				if(planid == 0) {
					if(planid==0 && (monitortype_id==4 || monitortype_id==1) ) {
						planid = 1;
					}else {
						planid = crService.getplanByCountryCode(planid, country, monitortype_id);
					}
					period = 1;
					defaultFreePlan = true;
				}

				boolean restrict_flexi_plan_period = false;
				if(is_flexi){
					AllProductSubscription sub = cbService.getProductSubscriptionByGatewayId(gatewayid, user.getChargebeeid(),monitortype_id);
					FlexiPlanHistory flexiPlanDetails = crService.getFlexiplandetailsbySubid(sub_id);
					if(flexiPlanDetails != null){
						long hours = helper.getHoursBetweenDate(helper.getCurrentTimeinUTC(), flexiPlanDetails.getCurrent_cycle_end_at());
						if((flexiPlanDetails.getCurrent_cycle() < 3
								|| (flexiPlanDetails.getCurrent_cycle() == 3 && hours > 0))
								&& type.equalsIgnoreCase("upgrade")){
							restrict_flexi_plan_period = true;
						} else if(flexiPlanDetails.getCurrent_cycle() >= 3 && hours < 0
								&& sub.getSubscriptionStatus().equalsIgnoreCase("active")) {
							planid = 1;
							period = 1;
						}
					} else if(sub != null && sub.getPlanId().contains("flexi")
							&& !sub.getSubscriptionStatus().contains("cancel")
							&& type.equalsIgnoreCase("upgrade")) {
						restrict_flexi_plan_period = true;
					}

					if(type.equalsIgnoreCase("downgrade")) {
						type = "cancelall";
					}
				}
				List<ManageList> sub = cbService.getManageList();
				boolean showContact;
				if(gatewayid == 0){
					int deviceCnt = crService.getMaxDeviceCount(planid);
					if(deviceCnt > 1){
						showContact = true;
					} else {
                        showContact = false;
                    }
                    planid = 1;
					period = 1;
				} else {
                    showContact = false;
                }

                response = crService.getUpgradeSubPlanV9(user, planid, period, monitortype_id, country, defaultFreePlan,gatewayid, type,plantype, sub_id, restrict_flexi_plan_period, is_flexi);
				JSONObject jsonResponse = new JSONObject(response).getJSONObject("response");
				JSONArray planList = jsonResponse.getJSONArray("planlist");
				sub.stream().forEach(u -> {
					boolean found = false;
					for (int i = 0; i < planList.length(); i++) {
						JSONObject plan = planList.getJSONObject(i);
						if (plan.has("managePlanId") && plan.getInt("managePlanId") == u.getId()) {
							found = true;
							break;
						}
					}
					u.setIs_listAvailable(showContact ? false : found);
				});
				response.put("manage_plan_list", sub);
				boolean isvetplanAvail = cbService.isVetPlanAvailable(user.getChargebeeid(),11);
				response.put("isvetplanAvail", isvetplanAvail);
				response.put("is_vetchat_user", isvetplanAvail);
				response.put("monitortype_id", monitortype_id);
				response.put("show_contact_support", showContact);
				response.put("comboCompare4g",comboCompareImage4g);
			}

		} catch (Exception e) {
			log.error("getupgradesubplansv5 :" + e.getMessage());
			response.put("Error", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}


	@RequestMapping(value = "/app/v5.0/getproductsubscriptionplanV3", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getproductsubscriptionplanV3(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
												  @RequestParam("app_ver") String app_ver, Authentication authentication,
												  @RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
												  @RequestParam(value = "timezone", defaultValue = "", required = false) String timezone,
												  @RequestParam(value = "monitor_id", defaultValue = "0", required = false) long monitorTypeId,
												  @RequestParam(value = "gateway_id", defaultValue = "0", required = false) long gateWayIdParam) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		Timestamp start = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("start : " + start + " getproductsubscriptionplanV3: " + auth);

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);

		} catch (Exception e) {
			log.error("getproductsubscriptionplanV3: user by authkey : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		try {
			if (user != null) {
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				if (user.isEnable()) {
					int inapp_redirect = 1;
					boolean cb_checkout = true;

					if (timezone == null || timezone.isEmpty() || timezone.equalsIgnoreCase("NA"))
						timezone = "-08:00";

					if (!timezone.contains("-") && !timezone.contains("+"))
						timezone = "+" + timezone.trim();
					List<JProductSubResV2> subGatewayList = new ArrayList<>();
					//When monitorTypeId and gatewayId is not passed add all the subscription plan
					if(monitorTypeId == 0 && gateWayIdParam == 0) {

						subGatewayList = getProductSubscriptionForGateway(user, os, app_ver, timezone, monitorTypeId, gateWayIdParam);
                        if (cbService.getVetchatSetUpActivate(user.getEmail())) {
                            JProductSubResV2 response1 = getProductSubscriptionForVetChat(user, os, app_ver, timezone);
                            subGatewayList.add(response1);
                        }
                        response.put("gatewaySubList", subGatewayList);
					} else {

						if(monitorTypeId != 11) {
							subGatewayList = getProductSubscriptionForGateway(user, os, app_ver, timezone, monitorTypeId, gateWayIdParam);
							response.put("gatewaySubList", subGatewayList);
						} else {
							JProductSubResV2 response1 = getProductSubscriptionForVetChat(user, os, app_ver, timezone);
							subGatewayList.add(response1);
							response.put("gatewaySubList", subGatewayList);
						}
					}

					gatewayServiceV4.populateShowBillingPopupKeyForGateways(subGatewayList);

					String allProd = cbService.getProductSubscriptionByChargebee(user.getChargebeeid());
					boolean isActiveyearplan = false;
					if(allProd != null) {
						isActiveyearplan = true;
					}
					SubscriptionWithoutDevice subscriptionWithoutDevice = cbService.getSubscriptionWithoutDevice(user.getId(), user.getEmail());
					response.put("subscriptionWithoutDevice", subscriptionWithoutDevice);
					response.put("is_priorityuser", isActiveyearplan);
					response.put("priorityemail", priorityemail);
					response.put("priorityphone", priorityphone);
					response.put("Status", 1);
					response.put("Msg", "Success");
				} else {
					response.put("Status", 0);
					response.put("Msg", "User not enabled");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Please try again.");
			log.error("getproductsubscriptionplanV3:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	/**
	 * The method is used to get the upgrade subscription plan for the user.
	 * @param header The HTTP headers containing the request information.
	 * @param authentication The authentication information of the user.
	 * @param planid The current plan id of the user.
	 * @param period The current period id of the user.
	 * @param os The operating system of the user's device.
	 * @param app_ver The version of the user's app.
	 * @param plan_ver The version of the plan.
	 * @param monitortype_id The monitor type id of the user.
	 * @param gatewayid The gateway id of the user.
	 * @param type The type of upgrade.
	 * @param plantype The type of plan.
	 * @param sub_id The subscription id of the user.
	 * @param is_flexi A flag indicating whether the user is on a flexi plan.
	 *
	 * @return A JSON response containing the upgrade subscription plan information.
	 */
	@GetMapping(value = "/app/v6.0/getupgradesubplans", headers = "Accept=application/json")
	@ResponseBody
	public ResponseEntity<JResponse> getupgradesubplansV6(@RequestHeader HttpHeaders header,Authentication authentication,
										  @RequestParam("planid") long planid,@RequestParam("period") long period, @RequestParam("os") String os,
										  @RequestParam("app_ver") String app_ver,
										  @RequestParam(value = "plan_ver", defaultValue = "V3", required = false) String plan_ver,
										  @RequestParam("monitortype_id") long monitortype_id,
										  @RequestParam(value = "gatewayid", defaultValue = "0", required = false) long gatewayid,
										  @RequestParam(defaultValue = "upgrade", required = false) String type,
										  @RequestParam(defaultValue = "Data-Plan,Flexi-Plan", required = false) String plantype,
										  @RequestParam(defaultValue = "NA") String sub_id,
										  @RequestParam(defaultValue = "false") boolean is_flexi) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth").trim();

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);
		} catch (Exception e) {
			log.error("getupgradesubplans:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
			return new ResponseEntity<>(response, HttpStatus.UNAUTHORIZED);
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return new ResponseEntity<>(errResponse, HttpStatus.UNAUTHORIZED);
		}

		try {
			if (user != null) {

				String country = "US";

				boolean defaultFreePlan = false;
				if(planid == 0) {
					if(planid==0 && (monitortype_id==4 || monitortype_id==1) ) {
						planid = 1;
					}else {
						planid = crService.getplanByCountryCode(planid, country, monitortype_id);
					}
					period = 1;
					defaultFreePlan = true;
				}

				boolean restrict_flexi_plan_period = false;
				if(is_flexi){
					AllProductSubscription sub = cbService.getProductSubscriptionByGatewayId(gatewayid, user.getChargebeeid(),monitortype_id);
					FlexiPlanHistory flexiPlanDetails = crService.getFlexiplandetailsbySubid(sub_id);
					if(flexiPlanDetails != null){
						long hours = helper.getHoursBetweenDate(helper.getCurrentTimeinUTC(), flexiPlanDetails.getCurrent_cycle_end_at());
						if((flexiPlanDetails.getCurrent_cycle() < 3
								|| (flexiPlanDetails.getCurrent_cycle() == 3 && hours > 0))
								&& type.equalsIgnoreCase("upgrade")){
							restrict_flexi_plan_period = true;
						} else if(flexiPlanDetails.getCurrent_cycle() >= 3 && hours < 0
								&& sub.getSubscriptionStatus().equalsIgnoreCase("active")) {
							planid = 1;
							period = 1;
						}
					} else if(sub != null && sub.getPlanId().contains("flexi")
							&& !sub.getSubscriptionStatus().contains("cancel")
							&& type.equalsIgnoreCase("upgrade")) {
						restrict_flexi_plan_period = true;
					}

					if(type.equalsIgnoreCase("downgrade")) {
						type = "cancelall";
					}
				}

				response = crService.getUpgradeSubPlanV10(user, planid, period, monitortype_id, country, defaultFreePlan,gatewayid, type,plantype, sub_id, restrict_flexi_plan_period, is_flexi);

                if ( allowVetChatFreeTrial) {

                    boolean isEligibleForVetChatFreeTrial=crService.isEligibleForVetChatFreeTrial(user.getChargebeeid());

                    HashMap<String, Object> vetChatFreeTrialObject = new HashMap<>();

                    response.put("isFreeVetChatTrial_Eligible", isEligibleForVetChatFreeTrial);
                    if(isEligibleForVetChatFreeTrial) {

                        response.put("vetChatFreeTrialInfo", crService.setVetChatFreeTrialContent(vetChatFreeTrialObject));

                    }


                }
				response.put("monitortype_id", monitortype_id);
				response.put("flexiPlan_activation_count",flexi_plan_activation_count);
				response.put("comboCompare4g",comboCompareImage4g);
			}

		} catch (Exception e) {
			log.error("getupgradesubplans :" + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");

		}
		response.put("Return Time", System.currentTimeMillis());
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	private List<JProductSubResV2> getProductSubscriptionForGateway(UserV4 user, String os, String app_ver, String timezone, long monitorTypeId, long gateWayId) {
		List<JGatewayInfo> gatewayList = gatewayServiceV4.getJGatewayInfoV1(user.getId(), monitorTypeId, gateWayId);
		List<JProductSubResV2> subGatewayList = new ArrayList<>();
		//Add all the gateways to the list
		if (gatewayList != null) {
			for (JGatewayInfo gateway : gatewayList) {

				JProductSubResV2 response1 = cbService.getProductSubscriptionplanV2FromDB(user, os,
						app_ver, timezone, gateway.getGateway_id(), gateway.getMonitorTypeId());
				response1.setImg_url(gateway.getImage_url());
				subGatewayList.add(response1);
			}
		}

		return subGatewayList;
	}

	private JProductSubResV2 getProductSubscriptionForVetChat(UserV4 user, String os, String app_ver, String timezone) {
		JProductSubResV2 response1 = cbService.getProductSubscriptionplanV2FromDB(user, os,
				app_ver, timezone, 0L, 11L);
		response1.setImg_url(vetChatImageUrl);
		response1.setName("Unlimited 24/7 VetChat");
		return response1;
	}
}
