package com.nimble.irisservices.appcontroller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.google.gson.Gson;
import com.nimble.irisservices.dto.JTerms;
import com.nimble.irisservices.dto.JVetPlanDetails;
import com.nimble.irisservices.entity.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAdvertisementService;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGeneralConfigService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class GeneralControllerV4App {

	private static final Logger log = LogManager.getLogger(GeneralControllerV4App.class);
	
	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IGeneralConfigService generalConfigService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Autowired
	@Lazy
	IAdvertisementService advService;

	@Autowired
	IUserServiceV4 userServiceV4;
	
	@Autowired
	@Lazy
	IChargebeeService chargebeeService;

	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportContactNumber;

	@Value("#{${supportemail}}")
	private Map<String,String> supportContactEmail;

	@Value("${petguideurl}")
	private String petGuideURL;

	@Value("${mapboxtoken}")
	private String mapBoxKeyToken;

	@Value("${nimbleauthkey}")
	private String nimbleAuthKey;

	@Value("${Facebook}")
	private String facebook;

	@Value("${Twitter}")
	private String twitter;

	@Value("${Instagram}")
	private String instagram;

	@Value("${Pinterest}")
	private String pinterest;

	@Value("${support}")
	private String support;

	@Value("${Support_Appstore}")
	private String Support_Appstore;

	@Value("#{${Marketing_Appstore}}")
	private Map<String,String> Marketing_Appstore;
	
	@Value("#{${Privacy_policy}}")
	private Map<String,String> privacy_policy;

	@Value("${buynowfurbit}")
	private String buynowFurbit;

	@Value("#{${buynowpetsafety}}")
	private Map<String,String> buynowpetsafety;

	@Value("${blogUrl}")
	private String blogUrl;

	@Value("#{${faqUrl}}")
	private Map<String,String> faqUrl;
	
	@Value("${showpopupregisteration}")
	private boolean showPopUpRegisteration;

	@Value("${amplitude_andriod}")
	private String amplitude_andriod;

	@Value("${amplitude_ios}")
	private String amplitude_ios;

	@Value("${hr_rateus}")
	private boolean hr_rateus;

	@Value("${rateuscount}")
	private int rateuscount;

	@Value("#{${terms_conditions}}")
	private Map<String,String>  terms_conditions;

	@Value("${fb_live}")
	private String fb_live;

	@Value("${chatbot}")
	private boolean chatbot;

	@Value("${showamazonrateus}")
	private boolean showamazonrateus;

	@Value("${showfeedback}")
	private boolean showfeedback;

	@Value("${redirectamazon}")
	private boolean redirectamazon;

	@Value("${amazonredirecturl}")
	private String amazonredirecturl;

	private boolean enablegoogle = true;

	private boolean enablefb = true;

	private boolean enableapple = true;

	@Value("${upgrade4G_url}")
	private String upgrade4G_url;

	@Value("${upgrade_msg}")
	private String upgrade_msg;

	private boolean hide_subscription = false;

	@Value("${sub_title}")
	private String sub_title;

	private boolean ios_hide_subscription = false;

	@Value("${ios_sub_title}")
	private String ios_sub_title;

	@Value("${amazon.alexa.app_url}")
	private String alexaAppURL;

	@Value("${amazon.alexa.lwa_fallback_url}")
	private String LWA_fallback_URL;

	@Value("${amazon.alexa.clientid}")
	private String alexaClientId;

	@Value("${amazon.alexa.state}")
	private String alexaState;

	@Value("${waggle.universal_url}")
	private String waggleUniversalUrl;

	@Value("${waggle.redirect_uri}")
	private String waggleRedirectUrl;

	@Value("${show_alexa}")
	private boolean show_alexa = true;

	@Value("${show_user_story}")
	private boolean show_user_story = true;
	
	@Value("${show_inapp_markettingpopup}")
	private boolean show_inapp_markettingpopup;
	
	@Value("${inapp_gif_url}")
	private String inapp_gif_url;
	
	@Value("${event_id}")
	private String event_id;
	
	@Value("${inapp_navigation_url}")
	private String inapp_navigation_url;

	private String petdashboard_img = "NA";
	
	private String petdashboard_img_flutter_light = "NA";
	
	private String petdashboard_img_flutter_dark = "NA";

	@Value("#{${petdashboard_url}}")
	private Map<String,String> petdashboard_url;

	@Value("${enable_reminder}")
	private boolean enable_reminder = true;

	// @Value("${enable_tips}")
	private boolean enable_tips = true;

	private boolean enable_powerloss = true;

	@Value("${enable_event}")
	private boolean enable_event = true;

	private boolean show_orderid = true;

	private String plan_version = "V1";

	@Value("${validation_authkey}")
	private String validation_authkey = "NA";
	
	private boolean enable_appnotify = true;
	
	private boolean enable_getsocial = true;

	@Value("${accessories.url}")
	private String accessoriesURL;
	
	@Value("${enable_callus}")
	private boolean enableCallUs;
	
	private boolean enable_delete = false;
	
	@Value("${purchased_from}")
	private boolean purchased_from;
	
	@Value("${mixpanel_token}")
	private String mixpanel_token;
	
	@Value("${show_ai_popup}")
	private boolean show_ai_popup;
	
	@Value("${ai_content}")
	private String ai_content;
	
	@Value("${textalert_infocontent}")
	private String textalert_infocontent;
	
	@Value("${show_abandoned_cart}")
	private boolean show_abandoned_cart=false;
	
	@Value("${show_ask_feature}")
	private boolean show_ask_feature=false;

	@Value("${show_instant_help}")
	private boolean show_instant_help=false;

	@Value("${vetchat_displayname}")
	private String vetchat_displayname="Vetmeet";

	@Value("${shop_link}")
	private String shop_link;

	@Value("${amazon_order_history}")
	private String amazon_order_history;

	@Value("${enable_call_support}")
	private Boolean enable_call_support;

	@Autowired
	Helper _helper;

	@Value("${start_saving_img_subs}")
	private String start_saving_img_subs;

	@Value("${grab_my_offer_img_subs}")
	private String grab_my_offer_img_subs;
	
	@Value("${coupon_img_subs}")
	private String coupon_img_subs;
	
	@Value("${coupon_desc_subs}")
	private String coupon_desc_subs;
	
	@Value("${coupon_code_subs}")
	private String coupon_code_subs;

	@Value("${show_pet_monitor_location}")
	private boolean showPetMonitor;

	@Value("${ask_feature_header}")
	private String ask_feature_header;

	@Value("${ask_feature_title}")
	private String ask_feature_title;

	@Value("${ask_feature_desc}")
	private String ask_feature_desc;

	@Value("${redirectWebSubs}")
	private boolean redirectWebSubs;

	@Value("${coupon_offer_list}")
	private List<String> coupon_offer_list;
	
	// v5.0/getgeneraldata - Kalai
	@RequestMapping(value = "v5.0/getgeneraldata", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGeneraldataV5(
			@RequestParam(value = "userid", defaultValue = "", required = false) String userid,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		JResponse response = new JResponse();

			Helper _helper = new Helper();

			try {
				String auth = header.getFirst("auth");

				UserV4 user = null;
				try {
					user = userServiceV4.verifyAuthV4("authkey", auth);
				} catch (InvalidAuthoException e) {
					log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "Invalid Authkey");
					return response;
				}

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				ReferEarn latestReferEarn = new ReferEarn();

				latestReferEarn = generalConfigService.getLatestReferEarn();

				String updatepaymentmethod = _helper.getExternalConfigValue("updatepaymentmethod", externalConfigService);

				String country = user.getCountry().toUpperCase();
				if(country == null || country.isEmpty() || country.equalsIgnoreCase("US")|| country.equalsIgnoreCase("NA") 
						|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in") 
						) {
					country = "US";
				}
				
				String supportPhone = supportContactNumber.get(country);
				String supportEmail = supportContactEmail.get(country);
				String guideURL = petGuideURL;
				String mapBoxToken = mapBoxKeyToken;

				if(user.getPassword_ver().equalsIgnoreCase("V3")){
					response.put("showpwdUpdate", false);
				}else{
					response.put("showpwdUpdate", true);
				}

				if (latestReferEarn != null) {

					response.put("referEarn", latestReferEarn);

				} else {

					response.put("referEarn", "NA");
				}

				if (!supportPhone.isEmpty()) {

					response.put("supportPhone", supportPhone);

				} else {

					response.put("supportPhone", "NA");
				}

				if (!supportEmail.isEmpty()) {

					response.put("supportEmail", supportEmail);

				} else {

					response.put("supportEmail", "NA");
				}

				if (!guideURL.isEmpty()) {

					response.put("petGuideURL", guideURL);

				} else {
					
					response.put("petGuideURL", "NA");
				}

				if (!mapBoxToken.isEmpty()) {

					response.put("mapBoxToken", mapBoxToken);

				} else {

					response.put("mapBoxToken",
							"pk.eyJ1IjoibmltYmxld2lyZWxlc3MiLCJhIjoiY2preHBjcnV3MGE0MjNwcjBucmNnZzd5MSJ9.iEpIubbVkS8Wc8t4UlIybQ");
				}

				if (!nimbleAuthKey.isEmpty()) {

					response.put("nimbleAuthKey", nimbleAuthKey);

				} else {

					response.put("nimbleAuthKey",
							"pk.eyJ1IjoibmltYmxld2lyZWxlc3MiLCJhIjoiY2preHBjcnV3MGE0MjNwcjBucmNnZzd5MSJ9.iEpIubbVkS8Wc8t4UlIybQ");
				}

				if (!facebook.isEmpty()) {

					response.put("facebook", facebook);

				} else {

					response.put("facebook", "NA");
				}
				if (!twitter.isEmpty()) {

					response.put("twitter", twitter);

				} else {

					response.put("twitter", "NA");
				}

				if (!instagram.isEmpty()) {

					response.put("instagram", instagram);

				} else {

					response.put("instagram", "NA");
				}
				if (!pinterest.isEmpty()) {

					response.put("pinterest", pinterest);

				} else {

					response.put("pinterest", "NA");
				}

				if (!Support_Appstore.isEmpty()) {

					response.put("support_appstore", Support_Appstore);

				} else {

					response.put("support_appstore", "NA");
				}

				if (!Marketing_Appstore.isEmpty()) {

					response.put("marketing_Appstore", Marketing_Appstore.get(country));

				} else {

					response.put("marketing_Appstore", "NA");
				}

				if (!privacy_policy.isEmpty()) {

					response.put("privacy_policy", privacy_policy.get(country));

				} else {

					response.put("privacy_policy", "NA");
				}

				if (!app_ver.isEmpty() && !os.isEmpty()) {
					VersionMapping verObj = crService.getVersionMapping(app_ver, os);
					if (verObj != null) {
						hide_subscription = verObj.isHide_subscription();
						ios_hide_subscription = verObj.isHide_subscription();
						enableapple = verObj.isEnableapple();
						enablegoogle = verObj.isEnablegoogle();
						enablefb = verObj.isEnablefb();
						enable_powerloss = verObj.isEnable_powerloss();
						show_user_story = verObj.isShow_user_story();
						enable_tips = verObj.isEnable_tips();
						show_orderid = verObj.isShow_orderid();
						plan_version = verObj.getPlan_version();
						enable_appnotify = verObj.isEnable_appnotify();
						enable_getsocial = verObj.isEnable_getsocial();
						enable_delete = verObj.isEnable_delete();
					}
				}

				AppImage appImgObj = advService.getAppImages("common", "petdashboard");
				if (appImgObj != null) {
					petdashboard_img = appImgObj.getImg_path();
					petdashboard_img_flutter_light = appImgObj.getImg_path_flutter_light();
					petdashboard_img_flutter_dark = appImgObj.getImg_path_flutter_dark();
				}
				response.put("buynowfurbit", buynowFurbit);
				response.put("buynowpetsafety", buynowpetsafety.get(country));
				response.put("showPopUpRegisteration", showPopUpRegisteration);
				response.put("blogUrl", blogUrl);
				response.put("faqUrl", faqUrl.get(country));
				response.put("enablegoogle", enablegoogle);
				response.put("enablefb", enablefb);
				response.put("enableapple", enableapple);
				response.put("updatepaymentmethod", updatepaymentmethod);
				response.put("amplitude_andriod", amplitude_andriod);
				response.put("amplitude_ios", amplitude_ios);
				response.put("petservicemsg", "Available in next release");
				response.put("rateuscount", rateuscount);
				response.put("show_inappmarketting_popup", show_inapp_markettingpopup);
				response.put("inapp_gifurl", inapp_gif_url);
				response.put("inapp_redirect_url", inapp_navigation_url);
				response.put("event_id", event_id);
				response.put("hr_rateus", hr_rateus);
				response.put("fb_live", fb_live);
				response.put("chatbot", chatbot);
				response.put("terms_conditions", terms_conditions.get(country));
				response.put("showamazonrateus", showamazonrateus);
				response.put("showfeedback", showfeedback);
				response.put("redirectamazon", redirectamazon);
				response.put("amazonredirecturl", amazonredirecturl);
				response.put("temp_maxinfo",
						"* We recommend setting the max limit atleast <b>2&#176 F/1&#176C</b> above the desired ambient temperature.");
				response.put("temp_mininfo",
						"* We recommend setting the min limit atleast <b>2&#176 F/1&#176C</b> below the desired ambient temperature.");
				response.put("temp_range", 2);
				response.put("upgrade_msg", upgrade_msg);
				response.put("upgrade4g_url", upgrade4G_url);
				response.put("hide_subscription", hide_subscription);
				response.put("sub_title", sub_title);
				response.put("ios_hide_subscription", ios_hide_subscription);
				response.put("ios_sub_title", ios_sub_title);
				response.put("textalert_infocontent", textalert_infocontent);
				response.put("show_instant_help", show_instant_help);
				response.put("vetchat_displayname", vetchat_displayname);

				if (!alexaAppURL.isEmpty()) {
					String alexaAppURL_ = alexaAppURL + "?fragment=skill-account-linking-consent"
					// check the client Id
							+ "&client_id=" + alexaClientId + "&scope=alexa::skills:account_linking" + "&skill_stage="
							+ alexaState + "&response_type=code" + "&redirect_uri=" + waggleUniversalUrl + "&state="
							+ alexaState;
					response.put("alexa_app_url", alexaAppURL_);
				} else {
					response.put("alexa_app_url", "NA");
				}

				if (!LWA_fallback_URL.isEmpty()) {
					String fallbackUrl = LWA_fallback_URL + "?client_id=" + alexaClientId
							+ "&scope=alexa::skills:account_linking" + "&response_type=code" + "&redirect_uri="
							+ waggleUniversalUrl + "&state=" + alexaState;
					response.put("lwa_fallback_url", fallbackUrl);
				} else {
					response.put("lwa_fallback_url", "NA");
				}

				response.put("show_alexa", show_alexa);
				response.put("show_user_story", show_user_story);
				response.put("petdashboard_img", petdashboard_img);
				response.put("petdashboard_img_flutter_light", petdashboard_img_flutter_light);
				response.put("petdashboard_img_flutter_dark", petdashboard_img_flutter_dark);
				response.put("petdashboard_url", petdashboard_url.get(country));
				response.put("enable_reminder", enable_reminder);
				response.put("enable_tips", enable_tips);
				response.put("enable_powerloss", enable_powerloss);
				response.put("enable_event", enable_event);
				response.put("show_orderid", show_orderid);
				response.put("plan_version", plan_version);
				response.put("enable_appnotify", enable_appnotify);
				response.put("enable_getsocial", enable_getsocial);
				response.put("getsocial_msg", "<center><p>coming soon</p></center>");
				response.put("accessories_url", accessoriesURL);
				response.put("enable_callus", enableCallUs);
				response.put("enable_delete", enable_delete);
				response.put("purchased_from", purchased_from);
				response.put("mixpanel_token", mixpanel_token);
				response.put("show_ai_popup", show_ai_popup);
				response.put("ai_content", ai_content);
				response.put("show_abandoned_cart", show_abandoned_cart);
				response.put("show_ask_feature", show_ask_feature);
				response.put("shop_link", shop_link);
				response.put("amazon_order_history", amazon_order_history);
				response.put("enable_call_support", enable_call_support);

			ArrayList<JVetPlanDetails> splan = crService.getVetPlanTerms("Vet-Plan");
			if(splan != null && splan.size() > 0) {
				List<JTerms.Terms> termList = splan.stream()
						.flatMap(details -> details.getTerm_list().stream())
						.collect(Collectors.toList());
				response.put("vet_terms", termList);
			}

			response.put("redirectWebSubs", redirectWebSubs);
			response.put("coupon_img_subs", coupon_img_subs);
			response.put("coupon_desc_subs", coupon_desc_subs);
			response.put("coupon_code_subs", coupon_code_subs);

			response.put("ask_feature_header", ask_feature_header);
			response.put("ask_feature_title", ask_feature_title);
			response.put("ask_feature_desc", ask_feature_desc);
			response.put("start_saving_img_subs", start_saving_img_subs);
			response.put("grab_my_offer_img_subs", grab_my_offer_img_subs);
			response.put("coupon_offer_list", coupon_offer_list);

			response.put("showPMLocation",showPetMonitor);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("Return Time", System.currentTimeMillis());
			return response;

			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Excepitoin while getting general data.");
				return response;
			}
		}

	// v4.0/getgeneraldata - SIV
	@RequestMapping(value = "v4.0/getgeneraldata", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGeneraldataV4(
			@RequestParam(value = "userid", defaultValue = "", required = false) String userid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		JResponse response = new JResponse();

		Helper _helper = new Helper();

		try {
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			ReferEarn latestReferEarn = new ReferEarn();

			latestReferEarn = generalConfigService.getLatestReferEarn();

			String updatepaymentmethod = _helper.getExternalConfigValue("updatepaymentmethod", externalConfigService);

			String country = user.getCountry().toUpperCase();
			if(country == null || country.isEmpty() || country.equalsIgnoreCase("US")|| country.equalsIgnoreCase("NA") 
					|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in") 
					) {
				country = "US";
			}
			
			String supportPhone = supportContactNumber.get(country);
			String supportEmail = supportContactEmail.get(country);
			String guideURL = petGuideURL;
			String mapBoxToken = mapBoxKeyToken;

			if (latestReferEarn != null) {

				response.put("referEarn", latestReferEarn);

			} else {

				response.put("referEarn", "NA");
			}

			if (!supportPhone.isEmpty()) {

				response.put("supportPhone", supportPhone);

			} else {

				response.put("supportPhone", "NA");
			}

			if (!supportEmail.isEmpty()) {

				response.put("supportEmail", supportEmail);

			} else {

				response.put("supportEmail", "NA");
			}

			if (!guideURL.isEmpty()) {

				response.put("petGuideURL", guideURL);

			} else {

				response.put("petGuideURL", "NA");
			}

			if (!mapBoxToken.isEmpty()) {

				response.put("mapBoxToken", mapBoxToken);

			} else {

				response.put("mapBoxToken",
						"pk.eyJ1IjoibmltYmxld2lyZWxlc3MiLCJhIjoiY2preHBjcnV3MGE0MjNwcjBucmNnZzd5MSJ9.iEpIubbVkS8Wc8t4UlIybQ");
			}

			if (!nimbleAuthKey.isEmpty()) {

				response.put("nimbleAuthKey", nimbleAuthKey);

			} else {

				response.put("nimbleAuthKey",
						"pk.eyJ1IjoibmltYmxld2lyZWxlc3MiLCJhIjoiY2preHBjcnV3MGE0MjNwcjBucmNnZzd5MSJ9.iEpIubbVkS8Wc8t4UlIybQ");
			}

			if (!facebook.isEmpty()) {

				response.put("facebook", facebook);

			} else {

				response.put("facebook", "NA");
			}
			if (!twitter.isEmpty()) {

				response.put("twitter", twitter);

			} else {

				response.put("twitter", "NA");
			}

			if (!instagram.isEmpty()) {

				response.put("instagram", instagram);

			} else {

				response.put("instagram", "NA");
			}
			if (!pinterest.isEmpty()) {

				response.put("pinterest", pinterest);

			} else {

				response.put("pinterest", "NA");
			}

			if (!Support_Appstore.isEmpty()) {

				response.put("support_appstore", Support_Appstore);

			} else {

				response.put("support_appstore", "NA");
			}

			if (!Marketing_Appstore.isEmpty()) {

				response.put("marketing_Appstore", Marketing_Appstore.get(country));

			} else {

				response.put("marketing_Appstore", "NA");
			}

			if (!privacy_policy.isEmpty()) {

				response.put("privacy_policy", privacy_policy.get(country));

			} else {

				response.put("privacy_policy", "NA");
			}

			if (!app_ver.isEmpty() && !os.isEmpty()) {
				VersionMapping verObj = crService.getVersionMapping(app_ver, os);
				if (verObj != null) {
					hide_subscription = verObj.isHide_subscription();
					ios_hide_subscription = verObj.isHide_subscription();
					enableapple = verObj.isEnableapple();
					enablegoogle = verObj.isEnablegoogle();
					enablefb = verObj.isEnablefb();
					enable_powerloss = verObj.isEnable_powerloss();
					show_user_story = verObj.isShow_user_story();
					enable_tips = verObj.isEnable_tips();
					show_orderid = verObj.isShow_orderid();
					plan_version = verObj.getPlan_version();
					enable_appnotify = verObj.isEnable_appnotify();
					enable_getsocial = verObj.isEnable_getsocial();
					enable_delete = verObj.isEnable_delete();
				}
			}

			AppImage appImgObj = advService.getAppImages("common", "petdashboard");
			if (appImgObj != null) {
				petdashboard_img = appImgObj.getImg_path();
				petdashboard_img_flutter_light = appImgObj.getImg_path_flutter_light();
				petdashboard_img_flutter_dark = appImgObj.getImg_path_flutter_dark();
			}
			response.put("buynowfurbit", buynowFurbit);
			response.put("buynowpetsafety", buynowpetsafety.get(country));
			response.put("showPopUpRegisteration", showPopUpRegisteration);
			response.put("blogUrl", blogUrl);
			response.put("faqUrl", faqUrl.get(country));
			response.put("enablegoogle", enablegoogle);
			response.put("enablefb", enablefb);
			response.put("enableapple", enableapple);
			response.put("updatepaymentmethod", updatepaymentmethod);
			response.put("amplitude_andriod", amplitude_andriod);
			response.put("amplitude_ios", amplitude_ios);
			response.put("petservicemsg", "Available in next release");
			response.put("rateuscount", rateuscount);
			response.put("hr_rateus", hr_rateus);
			response.put("fb_live", fb_live);
			response.put("chatbot", chatbot);
			response.put("terms_conditions", terms_conditions.get(country));
			response.put("showamazonrateus", showamazonrateus);
			response.put("showfeedback", showfeedback);
			response.put("redirectamazon", redirectamazon);
			response.put("amazonredirecturl", amazonredirecturl);
			response.put("temp_maxinfo",
					"* We recommend setting the max limit atleast <b>2&#176 F/1&#176C</b> above the desired ambient temperature.");
			response.put("temp_mininfo",
					"* We recommend setting the min limit atleast <b>2&#176 F/1&#176C</b> below the desired ambient temperature.");
			response.put("temp_range", 2);
			response.put("upgrade_msg", upgrade_msg);
			response.put("upgrade4g_url", upgrade4G_url);
			response.put("hide_subscription", hide_subscription);
			response.put("sub_title", sub_title);
			response.put("ios_hide_subscription", ios_hide_subscription);
			response.put("ios_sub_title", ios_sub_title);

			if (!alexaAppURL.isEmpty()) {
				String alexaAppURL_ = alexaAppURL + "?fragment=skill-account-linking-consent"
				// check the client Id
						+ "&client_id=" + alexaClientId + "&scope=alexa::skills:account_linking" + "&skill_stage="
						+ alexaState + "&response_type=code" + "&redirect_uri=" + waggleUniversalUrl + "&state="
						+ alexaState;
				response.put("alexa_app_url", alexaAppURL_);
			} else {
				response.put("alexa_app_url", "NA");
			}

			if (!LWA_fallback_URL.isEmpty()) {
				String fallbackUrl = LWA_fallback_URL + "?client_id=" + alexaClientId
						+ "&scope=alexa::skills:account_linking" + "&response_type=code" + "&redirect_uri="
						+ waggleUniversalUrl + "&state=" + alexaState;
				response.put("lwa_fallback_url", fallbackUrl);
			} else {
				response.put("lwa_fallback_url", "NA");
			}

			response.put("show_alexa", show_alexa);
			response.put("show_user_story", show_user_story);
			response.put("petdashboard_img", petdashboard_img);
			response.put("petdashboard_img_flutter_light", petdashboard_img_flutter_light);
			response.put("petdashboard_img_flutter_dark", petdashboard_img_flutter_dark);
			response.put("petdashboard_url", petdashboard_url.get(country));
			response.put("enable_reminder", enable_reminder);
			response.put("enable_tips", enable_tips);
			response.put("enable_powerloss", enable_powerloss);
			response.put("enable_event", enable_event);
			response.put("show_orderid", show_orderid);
			response.put("plan_version", plan_version);
			response.put("enable_appnotify", enable_appnotify);
			response.put("enable_getsocial", enable_getsocial);
			response.put("getsocial_msg", "<center><p>coming soon</p></center>");
			response.put("accessories_url", accessoriesURL);
			response.put("enable_callus", enableCallUs);
			response.put("enable_delete", enable_delete);
			response.put("purchased_from", purchased_from);
			response.put("mixpanel_token", mixpanel_token);

			response.put("showPMLocation",showPetMonitor);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("Return Time", System.currentTimeMillis());
			return response;

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while getting general data.");
			return response;
		}
	}
	
	// v5.0/getuserplanversion - SIV Arun
		@RequestMapping(value = "v5.0/getuserplanversion", method = RequestMethod.GET, headers = "Accept=application/json")
		@ResponseBody
		public JResponse getuserplanversion(
				@RequestParam(value = "os", defaultValue = "", required = false) String os,
				@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
				Authentication authentication, @RequestHeader HttpHeaders header) {
			JResponse response = new JResponse();

			Helper _helper = new Helper();

			try {
				String auth = header.getFirst("auth");

				UserV4 user = null;
				try {
					user = userServiceV4.verifyAuthV4("authkey", auth);
				} catch (InvalidAuthoException e) {
					log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "Invalid Authkey");
					return response;
				}

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
				boolean oldSubAvail = false;
				List<AllSubscription> subscription = chargebeeService.getSubscriptionByChargebeeId(user.getChargebeeid());
				if (subscription != null) {
					oldSubAvail = true;
				}
				
				response.put("plan_ver", oldSubAvail ? "V2" : "V3");
				response.put("old_sub_user", oldSubAvail);
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("Return Time", System.currentTimeMillis());
				
				return response;
				
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Excepitoin while getting get userplan version.");
				return response;
			}
		}
}
