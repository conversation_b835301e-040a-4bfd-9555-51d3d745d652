package com.nimble.irisservices.dao.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

import com.nimble.irisservices.dto.*;
import com.nimble.irisservices.entity.*;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.niom.entity.Order_account;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;
import org.hibernate.transform.AliasToBeanResultTransformer;
import org.hibernate.type.StringType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import com.chargebee.ListResult;
import com.chargebee.filters.enums.SortOrder;
import com.chargebee.models.Customer;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.dao.IReportDaoV4;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IPetSpeciesServices;
import com.nimble.irisservices.service.IPetSpeciesServicesV4;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.IWifiInfoService;
import com.nimble.irisservices.service.IWifiInfoServiceV4;

@Repository
public class ReportDaoImplV4 implements IReportDaoV4 {

	private static final Logger log = LogManager.getLogger(ReportDaoImplV4.class);

	@Autowired
	private SessionFactory slave4SessionFactory;

	@Autowired
	@Lazy
	ICreditSystemService crService;
	
	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;
	
	@Autowired
	@Lazy
	IChargebeeService cbService;
	
	@Value("${freeplan}")
	private String freeplan;

	@Value("${omitplan}")
	private String omitplan;

	@Value("${vpmplan}")
	private String vpmplan;

	@Value("${addonplan}")
	private String addonplan;
	
	@Value("${lowbatteryvalue}")
	private int lowbatteryvalue;
	
	
	@Autowired
	@Lazy
	IWifiInfoService wifiService;
	
	@Autowired
	Helper _helper;
	
	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;
	
	@Autowired
	IWifiInfoServiceV4 wifiInfoServiceV4;
	
	@Autowired
	@Lazy
	IPetSpeciesServices petSpeciesServices;
	
	@Autowired
	@Lazy
	IPetSpeciesServicesV4 petSpeciesServicesv4;
	
	@Value("${petbowl_shop_url}")
	private String shop_url;
	
	@Value("#{${ev3_devices}}")
	private List<String> ev3_devices;

	@Value("${show_nextrenewal_popup}")
	private boolean show_nextrenewal_popup;

	@Value("${days_tohandle_nextrenewal}")
	private int days_tohandle_nextrenewal;

	@Value("${hide_warranty_popup}")
	private boolean hide_warranty_popup;
	
	@Override
	public List<JAssetLastReportV4> getLastGatewayReportV4(String groupid, String subgroupid, String assetgroupid,
			String assetid, long userid, String offset, String limit, String tempunit, String country) {
		log.info("Entered ReportDaoImplV4 :: getLastGatewayReportV4 ");
		List<JAssetLastReportV4> jgatewayandrpt = getLstGatrptV4(groupid, subgroupid, assetgroupid, assetid, userid,offset, limit, tempunit, country);
		return jgatewayandrpt;
	}
	
	@Override
	public List<JAssetLastReportWatch> getLastGatewayReportWatch( long userid,  String tempunit,List<JGatewaySubSetup> gateway_setup) {
		
		log.info(" Entered getLastGatewayReportWatch :: userid : " + userid);
		List<JAssetLastReportWatch> lgrList = new ArrayList<JAssetLastReportWatch>();
		try {

			String query = "SELECT G.id,G.name,LGR.humidity,LGR.temperature,LGR.updatedon,LGR.defalut_rpt,LGR.heat_index,"
					+ " AG.minval,AG.maxval,LGR.datetime,LGR.timezone,LGR.battery,AM.model,LGR.aqi  FROM lastgatewayreport LGR  JOIN  gateway G ON G.id = LGR.gateway_id "
					+ " JOIN usergateway UG ON G.id = UG.gatewayid JOIN assetmodel AM  ON G.model_id=AM.id "
					+ " JOIN alertcfg AG JOIN `alertcfg_to_asset` AA  ON AA.alertcfg_id = AG.id AND AA.asset_id=UG.gatewayid"
					+ " WHERE  AM.monitor_type_id=1 AND AG.alerttype_id=1 AND UG.userid =" + userid ;

			Query qry = slave4SessionFactory.getCurrentSession().createSQLQuery(query);

			List res = qry.list();
			HashMap<Long, Boolean> gateway_setupactivate = new HashMap<Long, Boolean>();
			if (!res.isEmpty() && res.size() > 0) {

				for (JGatewaySubSetup gs : gateway_setup ) {
					gateway_setupactivate.put(gs.getGateway_id(), gs.isSetupActivate());
				}
				
				for (int i = 0; i < res.size(); i++) {
					Object[] lastReport = (Object[]) res.get(i);
				
					JAssetLastReportWatch assetRpt = new JAssetLastReportWatch();
					long gatewayid = ((BigInteger) lastReport[0]).longValue();
					assetRpt.setAssetid(gatewayid);
					assetRpt.setAssetname((String) lastReport[1]);
					
					DecimalFormat df = new DecimalFormat("0.00");
					float tempval = (float) lastReport[3];
					float tmin = (float)lastReport[7];
					float tmax = (float)lastReport[8];
					if (tempunit.equalsIgnoreCase("F") && tempval > -200) {
						tempval = CelsiusToFahrenheit(tempval);	
						tmin =  CelsiusToFahrenheit(tmin);
						tmax =  CelsiusToFahrenheit(tmax);
					}
					String temp = df.format(tempval);
					
					float heatIndexVal = (float) lastReport[6];

					if (tempunit.equalsIgnoreCase("C")) {
						heatIndexVal = FahrenheitToCelsius(heatIndexVal);
					}
					String heatIndex = df.format(heatIndexVal);					
					String humidity = (df.format((float) lastReport[2]));
												
					assetRpt.setHumidity(humidity);
					assetRpt.setTemperature(temp);	
					assetRpt.setHeat_index(heatIndex);
					assetRpt.setUpdatedon(changeDateFormat((Timestamp)lastReport[9]));
					
					String tempmin = df.format(tmin);
					String tempmax = df.format(tmax);
					String title="";
					assetRpt.setTemp_min(tempmin);
					assetRpt.setTemp_max(tempmax);

					int default_rpt = 0;
					String default_rpt_msg = "NA";
					int batteryVal = (int) lastReport[11];
					String deviceModel = (String) lastReport[12];
					boolean isN13 = false;
					if (deviceModel.contains("N13"))
						isN13 = true;
					
					SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);//default system tz
					
					try {
						if (gateway_setupactivate.get(gatewayid)) {
							default_rpt = 2; // DNR
							title = "Activate Now";
							default_rpt_msg = "Please activate your plan in the app";
						} else {
							if ((boolean) lastReport[5]) {

								default_rpt = 1; // FDNR
								title = "Whoops";
								default_rpt_msg = "Please check your app";

							} else {
								try {
									if(isN13 && (batteryVal <= lowbatteryvalue && batteryVal > 0)) {
										default_rpt = 4;
										title = "Whoops";
										default_rpt_msg = "Please check your app";
									}
									
								}catch (Exception e) {
									log.info("getBatteryValfailed:"+ e.getLocalizedMessage());
								}
								
								Date userTime = formatter.parse(lastReport[9].toString());
								String tz = lastReport[10].toString();
								Date d = Calendar.getInstance().getTime();

								SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // user timezone
								df1.setTimeZone(TimeZone.getTimeZone("GMT" + tz.trim()));
								String dtt = df1.format(d);
								Date curTime = formatter.parse(dtt);

								float diffInHrs = ((curTime.getTime() - userTime.getTime()) / 3600000);


//								if (diffInHrs >= 1) {
//									default_rpt = 1; // DNR
//									title = "Whoops";
//									default_rpt_msg = "please check your app";
//								}
								if (diffInHrs >= 1 && (batteryVal ==-1 )) {
									default_rpt = 1;
									title = "Whoops";
									default_rpt_msg = "Please check your app";
								}else if (diffInHrs >= 1 && ((batteryVal <=lowbatteryvalue  && batteryVal >=0 ) || batteryVal ==-2  )) {
									default_rpt = 1;
									title = "Whoops";
									default_rpt_msg = "Please check your app";
								}else if (diffInHrs >= 1 && (batteryVal >lowbatteryvalue || batteryVal ==-3 || batteryVal ==-4)) {
									default_rpt = 3; // during DNR state- play video to reset device
									title = "Whoops";
									default_rpt_msg = "Please check your app";
								}
							}
						}

					}catch (Exception e) {
						log.info("getLastGatewayReportWatch:"+ e.getLocalizedMessage());
					}					
					String airQuality = getAirQualityInfo((int) lastReport[13]);

					assetRpt.setDefault_report(default_rpt);
					assetRpt.setDefault_rpt_msg(default_rpt_msg);
					assetRpt.setTitle(title);
					assetRpt.setTempunit(tempunit);
					assetRpt.setAir_quality(airQuality);
					lgrList.add(assetRpt);
				}

			} else {
				log.info("getJPetprofilesForWatch: No gateways are mapped to this user id- " + userid);
			}

		} catch (Exception e) {
			//e.printStackTrace();
			log.error("Error in getJPetprofilesForWatch :error : " + e.getLocalizedMessage());
		}
		
		return lgrList;
	}
	
	public List<JAssetLastReportV4> getLstGatrptV4(String groupid, String subgroupid, String assetgroupid,
			String assetid, long userid, String offset, String limit, String tempunit, String country) {
		log.info("Entered ReportDaoImplV4 ::  ");
		List<Object[]> greports = this.getLastGatewayReportsV4(groupid, subgroupid, assetgroupid, assetid, userid, "",
				offset, limit);

		List<JAssetLastReportV4> lastReport = new ArrayList<JAssetLastReportV4>();
		for (Object[] report : greports) {
			lastReport.add(convertGatewayLstRpttoAssetRptV4(report, tempunit, country,userid));
		}
		return lastReport;
	}
	
	
	public List<sensorReportData> getSensorReports(long assetid, String date, String zone) {
		log.info("Entered ReportDaoImplV4 ::  ");
		List<sensorReportData> greports = new ArrayList<sensorReportData>();
		try {
			String startTime = date + " 00:00:00";
			String endTime = date + " 23:59:59";
			
			String qry = "SELECT s.datetime AS reportdatetime, em.event_name ,color_code,connected_sensor, G.sensor_type_id,em.status_key "
					+ "FROM iris.sensorreport s "
					+ "JOIN gateway G ON G.id = s.gateway_id  JOIN sensortype S ON G.sensor_type_id = S.id "
					+ "JOIN iris.eventid_mapping em ON s.eventid = em.event_id "
					+ "WHERE s.gateway_id = '" + assetid +"' AND connected_sensor = S.sensorcode "
					+ "AND s.datetime BETWEEN '" + startTime + "' AND '" + endTime + "' ORDER BY s.id DESC";
			if (assetid>0) {
				Query query = this.slave4SessionFactory.getCurrentSession()
						.createSQLQuery(qry);
				List<Object[]> reportsList = (List<Object[]>) query.list();
				if (!query.list().isEmpty()) {
					String sensor = reportsList.get(0)[3].toString();
					String sensorId = reportsList.get(0)[4].toString();
					String sensorname = "";
					
					if(sensorId.equalsIgnoreCase("1"))
						sensorname = "Door Status";
					else if(sensorId.equalsIgnoreCase("2"))
						sensorname = "Leak Status";
					else if(sensorId.equalsIgnoreCase("3"))
						sensorname = "Water Level Sensor";
					
					for (Object[] gat : reportsList) {
						sensorReportData rpt = new sensorReportData();
						SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						rpt.setReportdatetime(sdf.format((Timestamp)gat[0]));
						rpt.setStatus(gat[1].toString());
						rpt.setColor_code(gat[2].toString());
						rpt.setSensor_name(sensorname);
						rpt.setCurr_status(Integer.valueOf(gat[5].toString()));
						greports.add(rpt);
					}
				}
			}
		}catch (Exception e) {
			log.info("getSensorReports:"+ e.getLocalizedMessage());
			
		}
		return greports;
	}
	
	
	public List<sensorReportData> getSensorGroupReports(long assetid, String startdate, String endDate, String timezone) {
		log.info("Entered ReportDaoImplV4 ::  ");
		List<sensorReportData> greports = new ArrayList<sensorReportData>();
		try {
			String formdate = startdate + " 00:00:00";
			String todate = endDate + " 23:59:59";
			String qry = "SELECT s.datetime AS reportdatetime, em.event_name AS status \n"
		             + "FROM sensorreport s\n"
		             + "JOIN eventid_mapping em\n"
		             + "ON s.eventid = em.event_id\n"
		             + "WHERE s.gateway_id = '"+assetid+"'\n"
		             + "AND s.datetime BETWEEN '" + formdate + "' AND '" + todate + "' ORDER BY s.`datetime` DESC;";


			if (assetid>0) {
				Query query = this.slave4SessionFactory.getCurrentSession()
						.createSQLQuery(qry);
				query.setResultTransformer(new AliasToBeanResultTransformer(sensorReportData.class));
				((SQLQuery) query).addScalar("reportdatetime", new StringType())
				.addScalar("status", new StringType());
				if (!query.list().isEmpty()) {
					greports = (List<sensorReportData> )query.list();
				}
			}
		}catch (Exception e) {
			log.info("exportSensorReports:"+ e.getLocalizedMessage());
		}
		return greports;
	}
	
	
	
	public List<Object[]> getLastGatewayReportsV4(String groupid, String subgroupid, String assetgroupid,
			String assetid, long userid, String deliquencyStatus, String offset, String limit) {
		log.info("Entered :: OptimizedDoaImpl :: getLastGatewayReportsV4:: ");
		// List<LastGatewayRpt> greports = new ArrayList<LastGatewayRpt>();

		// last used index 51 - LGR.default_report
		String qry = "SELECT G.id,G.name,GRP.groupname,LGR.datetime,LGR.lat,LGR.latdir,LGR.lon,LGR.londir,LGR.speed,LGR.gpsstatus,"
				+ "LGR.gpsinfo,LGR.gpsmode,LGR.battery,LGR.distance,LGR.heading,LGR.eventid,LGR.nmeventid,LGR.iostatus,LGR.externalsensor,"
				+ "G.extsensortype,LGR.humidity,LGR.temperature,LGR.tempseverity,LGR.light,LGR.pressure,LGR.motion,LGR.rssi,LGR.address,"
				+ "G.isalive,LGR.lastvalidlat,LGR.lastvalidlatdir,LGR.lastvalidlon,LGR.lastvalidlondir,LGR.lastvaliddatetime,GRP.id as grpId,"
				+ "LGR.cellidlat,LGR.cellidlon,LGR.cellidacc,LGR.version,LGR.lastvalidaddress,LGR.rawrssi,LGR.lastvalidtemp,"
				+ "LGR.timezone,LGR.heat_index,AM.isgps,P.type,AM.model,G.onoffstatus,LGR.updatedon,AM.is_upgrade,AM.ishumidity,LGR.defalut_rpt,"
				+ "G.show_temp_video,LGR.power_cmd,LGR.aqi,LGR.voc,LGR.co2,AM.is_aqi FROM lastgatewayreport LGR, gateway G, usergateway UG,  `groups` GRP, assetmodel AM, "
				+ "probecategory P WHERE G.id = UG.gatewayid AND G.id = LGR.gateway_id AND GRP.id = G.groups_id "
				+ "AND  G.model_id=AM.id AND G.model_id=P.model_id AND AM.monitor_type_id=1 AND UG.userid = '" + userid + "'";
		if (!assetid.isEmpty()) {
			log.info("get last gateway report for asset, id: " + assetid);
			qry = qry + " and LGR.gateway_id = '" + assetid + "'";
		} else if (!assetgroupid.isEmpty()) {
			log.info("get last gateway report for assetgroup, id: " + assetgroupid);
			qry = qry + " and G.assetgroup_id = '" + assetgroupid + "'";
		} else if (!groupid.isEmpty()) {
			log.info("get last gateway report for group, id: " + groupid);
			qry = qry + " and G.groups_id = '" + groupid + "'";
		}
		Query query = this.slave4SessionFactory.getCurrentSession()
				.createSQLQuery(qry + " GROUP BY G.id order by LGR.id");

		if (!offset.isEmpty() && !limit.isEmpty()) {
			log.info("get last gateway report for offset & limit: " + offset + "," + limit);
			query.setFirstResult(Integer.parseInt(offset)).setMaxResults(Integer.parseInt(limit));
		}

		List<Object[]> greports = query.list();
		
		return greports;
	}
	
	@Override
	public String getLastGatewayReporttime(long gatewayid) {
		log.info("Entered getLastGatewayReporttime for gateway id : "+gatewayid);
		
		String datetime = "";
		String qry = "SELECT CONVERT_TZ(`datetime`,timezone,'+00:00') AS `datetime` FROM `lastgatewayreport` WHERE"
				+ " gateway_id = " + gatewayid + " ;";
		
		try {
			Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();
			if (!res.isEmpty()) {
				datetime = ((Timestamp) res.get(0)).toString();
			}
		} catch (Exception e) {
			log.error("get LastGatewayReporttime : ", e.getLocalizedMessage());
		}
		return datetime;
	}

	public int getGatewayReportCount(long gatewayid) {
		log.info("Entered getGatewayReportCount for gateway id : "+gatewayid);
		
		int count = 0;
		String qry = "SELECT COUNT(id) AS cnt FROM gatewayreport WHERE gateway_id =" + gatewayid + ";";
		try {
			Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();
			if (!res.isEmpty()) {
				count = ((BigInteger) res.get(0)).intValue();
			}
		} catch (Exception e) {
			log.error("get GatewayReportCount : ", e.getLocalizedMessage());
		}
		return count;
	}

	private JAssetLastReportV4 convertGatewayLstRpttoAssetRptV4(Object[] lastReport, String tempunit, String country, long userid) {
		DecimalFormat df = new DecimalFormat("0.00");
		boolean show_battery = false;
		String bat_info="NA";
		boolean isN13 = false;
		float tempval = (float) lastReport[21];
		float lastValidTemp = (float) lastReport[41];
		if (tempunit.equalsIgnoreCase("F") && tempval > -200)
			tempval = CelsiusToFahrenheit(tempval);
		if (tempunit.equalsIgnoreCase("F") && lastValidTemp > -200)
			lastValidTemp = CelsiusToFahrenheit(lastValidTemp);
		float heatIndexVal = (float) lastReport[43];
		if (tempunit.equalsIgnoreCase("C")) {
			heatIndexVal = FahrenheitToCelsius(heatIndexVal);
		}
		tempval = Float.parseFloat(df.format(tempval));
		heatIndexVal = Float.parseFloat(df.format(heatIndexVal));
		float humidity = Float.parseFloat(df.format((float) lastReport[20]));
		
		int batteryVal = (int) lastReport[12];
		String devicemodel = (String) lastReport[46];
		boolean show_aqi = false;
		if (devicemodel.contains("N13"))
		{
			isN13 = true;
			show_aqi=true;
		}
		
		if ((devicemodel.equalsIgnoreCase("N1-503 NT3D") || devicemodel.equalsIgnoreCase("N1A-503 NT3D")
				|| devicemodel.equalsIgnoreCase("N1-503 NT3B")) && (batteryVal > 1 && batteryVal <= 5)) {
			batteryVal = 1; // from 1-5 = display as 1 for the above model oly
		}

		if ((devicemodel.equalsIgnoreCase("N1A-503 NT3D")) && (batteryVal == -3)) {
			String[] gpsDetails = null;
			String gpsInfo = (String) lastReport[10];
			gpsDetails = gpsInfo.split(":");
			int battery = 0;

			if (gpsDetails.length > 11) {
				battery = Integer.valueOf(gpsDetails[11]);

				if (battery >= 96)
					batteryVal = -4; // fully charged
			}
		}

		JAssetLastReportV4 assetRpt = new JAssetLastReportV4();
		assetRpt.setAssetid(((BigInteger) lastReport[0]).longValue());
		assetRpt.setAssetname((String) lastReport[1]);
		assetRpt.setGroupname((String) lastReport[2]);
		assetRpt.setDatetime(changeDateFormat((Timestamp) lastReport[3]));
		assetRpt.setLat(Double.parseDouble(lastReport[4] + ""));
		assetRpt.setLatdir((String) lastReport[5]);
		assetRpt.setLon(Double.parseDouble(lastReport[6] + ""));
		assetRpt.setLondir((String) lastReport[7]);
		assetRpt.setSpeed(Float.parseFloat(lastReport[8] + ""));
		assetRpt.setGpsstatus((String) lastReport[9]);
		assetRpt.setGpsinfo((String) lastReport[10]);
		assetRpt.setGpsmode((int) lastReport[11]);
		assetRpt.setBatt(batteryVal);
		assetRpt.setDistance(Float.parseFloat(lastReport[13] + ""));
		assetRpt.setHeading((String) lastReport[14]);
		assetRpt.setEventid((String) lastReport[15]);

		assetRpt.setNmeventid((String) lastReport[16]);
		assetRpt.setIostatus((int) lastReport[17]);
		assetRpt.setExtsensor(Float.parseFloat(lastReport[18] + ""));
		assetRpt.setExtsensortype((String) lastReport[19]);
		assetRpt.setHumidity(humidity);
		assetRpt.setTemperature(tempval);
		assetRpt.setTempseverity((int) lastReport[22]);
		assetRpt.setLight(Float.parseFloat(lastReport[23] + ""));
		assetRpt.setPressure(Float.parseFloat(lastReport[24] + ""));
		assetRpt.setMotion((String) lastReport[25]);
		assetRpt.setRssi((String) lastReport[26]);
		assetRpt.setAddress((String) lastReport[27]);
		assetRpt.setAlive((boolean) lastReport[28]);
		assetRpt.setLastvalidlat((double) lastReport[29]);
		assetRpt.setLastvalidlatdir((String) lastReport[30]);
		assetRpt.setLastvalidlon((double) lastReport[31]);
		assetRpt.setLastvalidlondir((String) lastReport[32]);
		assetRpt.setLastvaliddatetime(changeDateFormat((Timestamp) lastReport[33]));
		assetRpt.setGroupid(((BigInteger) lastReport[34]).longValue());
		assetRpt.setCellidlat((double) lastReport[35]);
		assetRpt.setCellidlon((double) lastReport[36]);
		assetRpt.setCellidacc(Float.parseFloat(lastReport[37] + ""));
		
		String version = (String) lastReport[38];
		String eventname = getEventsName((String) lastReport[15], version, true, true); /* Parse Eventid */
		String nmeventname = "";
		
		if(assetRpt.getAssetid()>0) {
		
		String cur_date = IrisservicesUtil.getUtcDate();
		// last used index 51 - LGR.default_report
		String qry = "SELECT G.id,G.name,GRP.groupname,LGR.datetime,LGR.weight,SUM(LGR.intake_weight),LGR.battery,LGR.rawrssi,PFD.req_calories,PF.calories,MT.name AS devicetype " +
				"FROM sensorreport LGR, gateway G, usergateway UG,  `groups` GRP, assetmodel AM," + 
				"monitortype MT, probecategory P,pet_feed_details PFD,pet_food PF WHERE G.id = UG.gatewayid AND G.id = LGR.gateway_id AND GRP.id = G.groups_id " + 
				"AND G.model_id=AM.id AND G.model_id=P.model_id AND MT.id=AM.monitor_type_id AND PFD.gateway_id = G.id AND PF.id = PFD.pet_food_id " +
				"AND MT.enable=1 AND MT.id=3 AND G.id = '" + assetRpt.getAssetid() + "' AND DATE(LGR.datetime) = '"+ cur_date +"' AND (LGR.eventid LIKE '%800000' OR LGR.eventid LIKE '%200' OR LGR.eventid LIKE '%1000000')";
		
		Query query = this.slave4SessionFactory.getCurrentSession()
				.createSQLQuery(qry + " GROUP BY G.id order by LGR.id DESC LIMIT 1");

		List res1 = query.list();
		 if (!res1.isEmpty() && res1.size() > 0) {
			 Object[] row = (Object[]) res1.get(0);  
		 
			 assetRpt.setFoodIntake(((BigDecimal) row[5]).floatValue());
				assetRpt.setBattery((Integer) row[6]);
				assetRpt.setWifiRange((Integer) row[7]);
				assetRpt.setActualCalories((Double) row[8]);
				assetRpt.setFoodCal((Double) row[9]);
				assetRpt.setDevice_type((String) row[10]);
				
				double caloriesIntake = 0d;
				if(assetRpt.getFoodCal() != null && assetRpt.getFoodCal()>0) {
					caloriesIntake = (assetRpt.getFoodCal() / 1000) * assetRpt.getFoodIntake();
				}
				assetRpt.setCaloriesConsumed(caloriesIntake);
				
				
				
		 }
		 List<BluetoothDeviceList> ble_device_list = wifiService.getWifiList(assetRpt.getAssetid(),userid);
		 assetRpt.setBledevicelist(ble_device_list);
		}
		

		if (version.equalsIgnoreCase("004") || version.equalsIgnoreCase("005") || version.equalsIgnoreCase("HD06")
				|| version.equalsIgnoreCase("HD07")|| version.equalsIgnoreCase("N701")|| version.equalsIgnoreCase("N702")
				|| version.equalsIgnoreCase("N7G1"))
			nmeventname = getEventsName((String) lastReport[16], version, false, true); /* Parse NMEventid */

		assetRpt.setEventname(getLastEventName(eventname, nmeventname));
		assetRpt.setLastvalidaddress((String) lastReport[39]);
		assetRpt.setRawrssi((int) lastReport[40]);
		double lat_d = getLatAndLon((Double) lastReport[4], (String) lastReport[5]);
		double lon_d = getLatAndLon((Double) lastReport[6], (String) lastReport[7]);
		double lastvalidlat_d = getLatAndLon((Double) lastReport[29], (String) lastReport[30]);
		double lastvalidlon_d = getLatAndLon((Double) lastReport[31], (String) lastReport[32]);

		assetRpt.setLat_d(lat_d);
		assetRpt.setLon_d(lon_d);

		assetRpt.setLastvalidlat_d(lastvalidlat_d);
		assetRpt.setLastvalidlon_d(lastvalidlon_d);

		assetRpt.setLastvalidtemp(lastValidTemp);
		String tz = ((String) lastReport[42]).trim();
		assetRpt.setRpt_timezone(tz);
		assetRpt.setHeat_index(heatIndexVal);

		boolean enableLoc = false;
		boolean onoffstatus = false;
		if ((lastReport[44].toString()).equalsIgnoreCase("1") || (lastReport[44].toString()).equalsIgnoreCase("true"))
			enableLoc = true;

		assetRpt.setEnableLocation(enableLoc);

		assetRpt.setProbeType((String) lastReport[45]);

		if ((lastReport[47].toString()).equalsIgnoreCase("1") || (lastReport[47].toString()).equalsIgnoreCase("true"))
			onoffstatus = true;

		assetRpt.setOnoffstatus(onoffstatus);
		
		assetRpt.setUpdatedon(changeDateFormat((Timestamp) lastReport[48]));
		assetRpt.setIs_upgrade((boolean) lastReport[49]);
		
		assetRpt.setIshumidity((boolean) lastReport[50]);
		int default_rpt = 0;
		String default_rpt_msg = "NA";
		String default_rpt_label = "NA";
		String default_rpt_msg_watch = "NA";
		boolean show_N13Poff = false;
		
		boolean show_temp_video = false;
		
		if ((lastReport[52].toString()).equalsIgnoreCase("1") || (lastReport[52].toString()).equalsIgnoreCase("true"))
			show_temp_video = true;
			
		assetRpt.setShow_temp_video(show_temp_video);
		
		SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);//default system tz
		
		//check for battery below lowbatteryvalue config value........
		try {
			if(isN13 && (batteryVal <= lowbatteryvalue && batteryVal > 0 )) {
				default_rpt = 4;
				default_rpt_label = "Low Battery..";
				default_rpt_msg = "<center><p>Wait to press the ON/OFF button until you can view the temperature data here.</p></center>";
				default_rpt_msg_watch = "Low battery, Please check on your app";
			}
			
		}catch (Exception e) {
			log.info("getBatteryValfailed:"+ e.getLocalizedMessage());
		}
		try {
			if ((boolean) lastReport[51]) {
				default_rpt = 2;
				default_rpt_msg = "Contact our support";

				float diffInHrs = 0;
				Date curr = formatter.parse(IrisservicesUtil.getCurrentTimeUTC());

				Date pre = formatter.parse(lastReport[48].toString()); // 48 index - updatedon date

				diffInHrs = ((curr.getTime() - pre.getTime()) / 3600000);

				if (diffInHrs >= 24) {
					String subPurchaseTime = getSubUpdateTime(userid,assetRpt.getAssetid());
					
					pre = formatter.parse(subPurchaseTime); // read from all_chargebee_subscription table

					diffInHrs = ((curr.getTime() - pre.getTime()) / 3600000);
					
					if (diffInHrs <= 24) {
						default_rpt = 1;
						default_rpt_msg = "<center><p>Your Pet Monitor will take up to 24 hrs to connect with Cellular Network.</p></center>";
						default_rpt_label = "Hang on..";
						default_rpt_msg_watch = "FDNR Please check on your app";
					}else {
						default_rpt = 2;
						if (country.equalsIgnoreCase("AU"))
							default_rpt_msg = "<center><p style=\"font-weight: 600; font-size: 18px; margin-bottom: -10px;\">Please contact our customer support.</p>"
									+ "<p>7 AM-6 PM AEDT [Mon-Fri]</p></center>";
						else
							default_rpt_msg = "<center><p style=\"font-weight: 600; font-size: 18px; margin-bottom: -10px;\">Please contact our customer support.</p>"
									+ "<p>10 AM-8 PM EST [Mon-Fri]</p></center>";
						default_rpt_label = "Still Connecting?";
						default_rpt_msg_watch = "DNR Please check on your app";
					}
				} else {
					default_rpt = 1;
					default_rpt_msg = "<center><p>Your Pet Monitor will take up to 24 hrs to connect with Cellular Network.</p></center>";
					default_rpt_label = "Hang on..";
					default_rpt_msg_watch = "FDNR Please check on your app";
				}

			} else {
				Date userTime = formatter.parse(lastReport[3].toString());
				Date d = Calendar.getInstance().getTime();

				SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // user timezone
				df1.setTimeZone(TimeZone.getTimeZone("GMT"+tz.trim()));
				String dtt =  df1.format(d);
				Date curTime = formatter.parse(dtt);
				
				float diffInHrs = ((curTime.getTime() - userTime.getTime()) / 3600000);
				
				if (diffInHrs >= 1 && (batteryVal ==-1 )) {
					show_battery = true;
					bat_info="Please contact support.";
				}else if (diffInHrs >= 1 && ((batteryVal <=20  && batteryVal >=0) || batteryVal ==-2 ) && devicemodel.contains("N13")) {
					show_battery = true;
					bat_info="Press Turn ON button after 45 mins of charging.";
				}else if (diffInHrs >= 1 && ((batteryVal <=10  && batteryVal >=0 ) || batteryVal ==-2  )) {
					show_battery = true;
					bat_info="Charge Now!";
				}else if (diffInHrs >= 1 && (batteryVal >10 || batteryVal ==-3 || batteryVal ==-4)) {
					default_rpt = 3; // during DNR state- play video to reset device
					default_rpt_msg_watch = "Please watch on your app";
				}
					
			}
			
			if ((lastReport[53].toString()).equalsIgnoreCase("POWEROFF") && isN13) {
				bat_info="Monitor: Powered OFF";
				show_N13Poff=true;
			}
			//LGR.aqi,LGR.voc,LGR.co2 
			int aqi = (int) lastReport[54];
			int co2 =(int) lastReport[56];
			int voc = (int) lastReport[55];
			show_aqi = (boolean)lastReport[57];
			String device_type = (String) lastReport[58];
			assetRpt.setProfileId(((BigInteger) lastReport[59]).longValue());		
			assetRpt.setImgUrl((String) lastReport[60]);
			String co2_info = "";
			String aqi_info = "";
			String aqi_desc = "";
			String voc_info = "";

			if(co2>=400 && co2<=600)
				co2_info = "Excellent";
			else if(co2>=601 && co2<=800)
				co2_info = "Good";
			else if(co2>=801 && co2<=1000)
				co2_info = "Fair";
			else if(co2>=1001 && co2<=1500)
				co2_info = "Poor";
			else
				co2_info = "Bad";
			
			if(aqi==1 ) {
				aqi_info = "Excellent";
				aqi_desc = "The air is fresh and free from toxins. Pets are not exposed to any health risk.";	
			}
			else if(aqi==2 ) {
				aqi_info = "Good";
				aqi_desc = "Air quality is acceptable. There may be a risk for some sensitive pets.";	
			}
			else if(aqi==3 ) {
				aqi_info = "Fair";
				aqi_desc = "Inhaling this air can cause slight discomfort and difficulty in breathing.";	
			}
			else if(aqi==4 ) {
				aqi_info = "Poor";
				aqi_desc = "Typically problematic for puppies, pregnant dogs and senior dogs.";	
			}
			else {
				aqi=5;
				aqi_info = "Bad";
				aqi_desc = "Health alert! The risk of health effects is increased for everyone.";	
			}
			
			if(voc>0 && voc<=220) {
				voc_info = "Good";
			}else if(voc>221 && voc<=660) {
				voc_info = "Moderate";
			}else if(voc>661 && voc<=1430) {
				voc_info = "High";
			}else if(voc>1431 && voc<=5500) {
				voc_info = "Very High";
			}else
				voc_info = "Poor";
			
			assetRpt.setAqi(aqi);
			assetRpt.setAqi_info(aqi_info);
			assetRpt.setAqi_desc(aqi_desc);
			assetRpt.setVoc(voc);
			assetRpt.setVoc_info(voc_info);
			assetRpt.setCo2(co2);
			assetRpt.setCo2_info(co2_info);
			assetRpt.setShow_aqi(show_aqi);
			assetRpt.setDevice_type(device_type);
			
		}catch (Exception e) {
			log.info("convertGatewayLstRpttoAssetRptV4:"+ e.getLocalizedMessage());
		}
		assetRpt.setDefault_report(default_rpt);
		assetRpt.setDefault_rpt_msg(default_rpt_msg);
		assetRpt.setDefault_rpt_label(default_rpt_label);
		assetRpt.setShow_battery(show_battery);
		assetRpt.setBat_info(bat_info);
		assetRpt.setN13(isN13);
		assetRpt.setShow_N13Poff(show_N13Poff);
		assetRpt.setDefault_rpt_msg_watch(default_rpt_msg_watch);
		
		return assetRpt;
	}

	@Override
	public String getSubUpdateTime(long userid, long gatewayid) {
		log.info("Entered getSubUpdateTime for user id : "+userid);
		
		String datetime = "1753-01-01 00:00:00";
		String qry = "(SELECT updated_indb FROM `all_chargebee_subscription` WHERE is_deleted = 0 AND subscription_status IN ('IN_TRIAL','Active','non_renewing') AND "
				+ " chargebee_id = (SELECT chargebeeid FROM `user` WHERE id=" + userid + ")) "
				+ " UNION( SELECT updated_indb FROM `all_product_subscription` WHERE is_deleted = 0 "
				+ " AND subscription_status IN ('IN_TRIAL', 'Active', 'non_renewing') AND monitor_type = 1 AND gateway_id = " +gatewayid+ " ) ;";
		
		try {
			Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();
			if (!res.isEmpty()) {
				datetime = ((Timestamp) res.get(0)).toString();
			}
		} catch (Exception e) {
			log.error("getSubUpdateTime EXP : ", e.getLocalizedMessage());
		}
		return datetime;
	}
	public static String getEventsName(String eventid, String pktVer, boolean isEventid, boolean isLastRpt) {
		log.info("Entered ReportDaoImplV4 :: getEventsName ");
		if (eventid.equalsIgnoreCase("NA"))
			return "";

		ArrayList<String> eventIdBit = parseEventsHex(eventid);
		String event_Headers[] = getEventHeaders(pktVer, isEventid);

		if (isLastRpt)
			event_Headers = getEventHeaders_LastRpt(pktVer, isEventid);

		String eventsName = "";

		for (int i = 0; i < eventIdBit.size(); i++) {
			if (eventIdBit.get(i).equalsIgnoreCase("X")) {
				eventsName = eventsName + event_Headers[i] + ",";
			}
		}
		return eventsName.replaceAll(",$", "");
	}

	public double getLatAndLon(double latlon, String latdlond) {
		/*
		 * South (negative latitude), West (negative longitude), North (positive
		 * latitude), East (positive longitude)
		 */

		if (latdlond.equals("S"))
			latlon *= -1;
		else if (latdlond.equals("W"))
			latlon *= -1;
		return latlon;
	}

	/* Read LastReports Eventnames */
	public String getLastEventName(String eventname, String nmeventname) {
		log.info("Entered ReportDaoImplV4 :: getLastEventName ");
		String evtnames = "";

		if (!eventname.isEmpty() && !nmeventname.isEmpty())
			eventname += "," + nmeventname;
		else if (!nmeventname.isEmpty())
			eventname = nmeventname;

		for (String evtname : eventname.split(",")) {
			if (!evtname.equalsIgnoreCase("NA") && !evtname.isEmpty()) {
				evtnames += evtname + ",";
			}
		}
		if (evtnames.isEmpty())
			return "NONE";
		return evtnames.replaceAll(",$", "");
	}

	private float CelsiusToFahrenheit(float tempmvalIndegreeCelsius) {

		double degreeFahrenheit = Double.valueOf(tempmvalIndegreeCelsius).floatValue() * 9 / 5 + 32;
		double roundvalues = Math.round(degreeFahrenheit * 100.0) / 100.0;
		return (float) roundvalues;
	}

	private float FahrenheitToCelsius(float tempmvalIndegreeFahrenheit) {
		double degreeCelcius = (Double.valueOf(tempmvalIndegreeFahrenheit).floatValue() - 32) * 5 / 9;
		double roundvalues = Math.round(degreeCelcius * 100.0) / 100.0;
		return (float) roundvalues;
	}

	private String changeDateFormat(Timestamp myTimestamp) {
		return new SimpleDateFormat("MM/dd/yyyy HH:mm:ss").format(myTimestamp);
	}

	private static ArrayList<String> parseEventsHex(String hexValue) {
		ArrayList<String> events = new ArrayList<String>();
		String binValue = hexToBinary(hexValue);

		for (int i = 0; i < 32; i++) {
			events.add(binValue.charAt(i) == '0' ? "-" : "X");
		}
		return events;
	}

	/* Read Reports EventHeaders */
	public static String[] getEventHeaders(String pktVersion, boolean eventid) {
		log.info("Entered ReportDaoImplV4 :: getEventHeaders ");
		String events_003[] = { "IGN_ON", "IGN_OFF", "IO1_ON", "IO1_OFF", "EVENT_IO2_ON", "IO2_OFF", "IO3_ON",
				"IO3_OFF", "IO4_ON", "IO4_OFF", "IO5_ON", "IO5_OFF", "IO6_ON", "IO6_OFF", "IO7_ON", "IO7_OFF", "IO8_ON",
				"IO8_OFF", "Stoppage", "Heading Exceed", "ReportInterval Changed", "Ping3", "Fall Detect", "Normal",
				"ADCTHRES", "IDLE", "Overspeed", "Panic Detect", "Motion Detect", "SAND", "LOGGED_80", "LOGGED_90" };

		String events_004[] = { "Door Open", "Door Close", "IO1_ON", "IO1_OFF", "IO2_ON", "IO2_OFF", "Light Detect",
				"Dark Detect", "LCDButtonPress", "Reserved_2", "Reserved_3", "Reserved_4", "IO6_ON", "IO6_OFF",
				"Tamper Activate", "Shock Detect", "IO8_ON", "IO8_OFF", "Stoppage", "Heading Exceed",
				"ReportInterval Changed", "Ping3", "Fall Detect", "Normal", "Charger Removed", "Idle", "Overspeed",
				"Panic Detect", "Motion Detect", "SAND", "LOGGED_80", "LOGGED_90" };

		String nmeventid[] = { "RST_POR", "RST_EXTR", "RST_WDTR", "RST_BODR", "Reserved_1", "Reserved_2", "Reserved_3",
				"Reserved_4", "SMS Configured", "ADC0_LOW", "ADC0_HIGH", "ADC0_NORMAL", "ADC1_LOW", "ADC1_HIGH",
				"ADC1_NORMAL", "ADC2_LOW", "ADC2_HIGH", "ADC2_NORMAL", "ADC3_LOW", "ADC3_HIGH", "ADC3_NORMAL",
				"Tilt detect", "Tilt detect", "Tilt detect", "Tilt detect", "Tilt detect", "Tilt detect", "Reserved_5",
				"Reserved_6", "Reserved_7", "Reserved_8", "Reserved_9" };

		if ((pktVersion.equalsIgnoreCase("004") || pktVersion.equalsIgnoreCase("005")
				|| pktVersion.equalsIgnoreCase("HD06")|| pktVersion.equalsIgnoreCase("HD07")
				|| pktVersion.equalsIgnoreCase("N701") || pktVersion.equalsIgnoreCase("N702")
				|| pktVersion.equalsIgnoreCase("N7G1")) && eventid)
			return events_004;
		else if ((pktVersion.equalsIgnoreCase("004") || pktVersion.equalsIgnoreCase("005")
				|| pktVersion.equalsIgnoreCase("HD06")|| pktVersion.equalsIgnoreCase("HD07")
				|| pktVersion.equalsIgnoreCase("N701") || pktVersion.equalsIgnoreCase("N702")
				|| pktVersion.equalsIgnoreCase("N7G1")) && eventid == false)
			return nmeventid;

		return events_003;
	}

	/* Read LastReports EventHeaders */
	public static String[] getEventHeaders_LastRpt(String pktVersion, boolean eventid) {
		log.info("Entered ReportDaoImplV4 :: getEventHeaders_LastRpt ");
		String events_003[] = { "NA", "NA", "NA", "NA", "NA", "NA", "NA", "NA", "NA", "NA", "NA", "NA", "NA", "NA",
				"NA", "NA", "NA", "NA", "Stoppage", "Heading Exceed", "ReportInterval Changed", "Ping3", "Fall Detect",
				"Normal", "NA", "IDLE", "Overspeed", "Panic Detect", "Motion Detect", "NA", "NA", "NA" };

		String events_004[] = { "Door Open", "Door Close", "NA", "NA", "NA", "NA", "Light Detect", "Dark Detect", "NA",
				"NA", "NA", "NA", "NA", "NA", "Tamper Activate", "Shock Detect", "NA", "NA", "Stoppage",
				"Heading Exceed", "ReportInterval Changed", "Ping3", "Fall Detect", "Normal", "Charger Removed", "Idle",
				"Overspeed", "Panic Detect", "Motion Detect", "NA", "NA", "NA" };

		String nmeventid[] = { "NA", "NA", "NA", "NA", "NA", "NA", "NA", "NA", "NA", "NA", "NA", "NA", "NA", "NA", "NA",
				"NA", "NA", "NA", "NA", "NA", "NA", "Tilt detect", "Tilt detect", "Tilt detect", "Tilt detect",
				"Tilt detect", "Tilt detect", "NA", "NA", "NA", "NA", "NA" };

		if ((pktVersion.equalsIgnoreCase("004") || pktVersion.equalsIgnoreCase("005")
				|| pktVersion.equalsIgnoreCase("HD06")|| pktVersion.equalsIgnoreCase("HD07")
				|| pktVersion.equalsIgnoreCase("N701") || pktVersion.equalsIgnoreCase("N702")
				|| pktVersion.equalsIgnoreCase("N7G1")) && eventid)
			return events_004;
		else if ((pktVersion.equalsIgnoreCase("004") || pktVersion.equalsIgnoreCase("005")
				|| pktVersion.equalsIgnoreCase("HD06")|| pktVersion.equalsIgnoreCase("HD07")
				|| pktVersion.equalsIgnoreCase("N701") || pktVersion.equalsIgnoreCase("N702")
				|| pktVersion.equalsIgnoreCase("N7G1")) && eventid == false)
			return nmeventid;

		return events_003;
	}

	private static String hexToBinary(String hexValue) {
		String bin = "";
		String binFragment = "";
		int iHex;
		hexValue = hexValue.trim();

		int ioLength = 8 - hexValue.length();

		for (int i = 0; i < ioLength; i++) {
			hexValue = "0" + hexValue;
		}

		for (int i = 0; i < hexValue.length(); i++) {
			iHex = Integer.parseInt("" + hexValue.charAt(i), 16);
			binFragment = Integer.toBinaryString(iHex);

			while (binFragment.length() < 4) {
				binFragment = "0" + binFragment;
			}
			bin += binFragment;
		}
		// Need to reverse the binary so that charAt(0) will return 0 th
		// position otherwise will return 31st data
		return new StringBuffer(bin).reverse().toString();
	}
	
	@Override
	public List<JGatewaySubSetup> getSubscriptionFromDB(UserV4 user) {
		List<JGatewaySubSetup> setupList = new ArrayList<JGatewaySubSetup>();
		try {
			String cbId = "NA";
			if (user.getChargebeeid().equalsIgnoreCase("NA")) {
				ListResult rs = null;
				int i = 1;
				Loop: while (rs == null || i <= 3) {
					i = i + 1;
					Thread.sleep(2000);
					rs = Customer.list().email().is(user.getEmail()).sortByCreatedAt(SortOrder.ASC).request();

					if (!rs.isEmpty()) {
						for (ListResult.Entry entry : rs) {
							Customer customer = entry.customer();
							user.setChargebeeid(customer.id());
						}
						break Loop;
					}
				}
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
							user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}
			}

			AllSubscription subscrip = null;
			boolean isPaidPlan = false;
			if (!user.getChargebeeid().equalsIgnoreCase("NA")) {

				List<AllSubscription> allSubscription = cbService.getSubscriptionByChargebeeId(user.getChargebeeid());
				int ssize = 0;

				if (allSubscription != null) {
					ssize = allSubscription.size();
					for (AllSubscription subs : allSubscription) {
						String subs_planid = subs.getPlanId();
						if (ssize == 1 && !omitplan.contains(subs_planid) && !vpmplan.contains(subs_planid)
								&& !addonplan.contains(subs_planid)) {
							subscrip = subs;
							if (!freeplan.contains(subs_planid)) {
								isPaidPlan = true;
								break;
							}

						} else if (!freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
								&& !vpmplan.contains(subs_planid) && !addonplan.contains(subs_planid)) {
							subscrip = subs;
							break;
						}
					}

					if (subscrip == null) {
						for (AllSubscription subs : allSubscription) {
							if (freeplan.contains(subs.getPlanId())) {
								subscrip = subs;
								break;
							}
						}
					}
				}

			}

			String planid = "chum";
			String status = "ACTIVE";
			int daysBetween = -1;
			int days_remaining = -1;
			String nextPaymentDate = "NA";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date dateobj = new Date();
			Date nextpaymentTS = new Date();

			int iris_splan = 1;
			if (subscrip != null) {

				// JPlan jPlan = cbService.getPlanDesc(subscrip.getPlanId());
				planid = subscrip.getPlanId();
				status = subscrip.getSubscriptionStatus().toUpperCase();

				if ((status.equalsIgnoreCase("ACTIVE")) || (status.equalsIgnoreCase("IN_TRIAL"))) {
					sdf.setTimeZone(TimeZone.getTimeZone("UTC"));

					nextpaymentTS = sdf.parse(subscrip.getNextBillingAt());
					nextPaymentDate = sdf.format(nextpaymentTS.getTime());

					Date nextPaymentDate1 = sdf.parse(subscrip.getNextBillingAt());
					Date todayDate = sdf.parse(sdf.format(dateobj));
					Date nextPaymentDateNew = sdf.parse(subscrip.getNextBillingAt());

					long difference = nextPaymentDate1.getTime() - todayDate.getTime();
					daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
					days_remaining = daysBetween;

					sdf = new SimpleDateFormat("yyyy-MM-dd");
					// sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
					nextPaymentDate = sdf.format(nextPaymentDateNew.getTime());
					status = "ACTIVE";

					if (freeplan.contains(subscrip.getPlanId())) {
						nextPaymentDate = "NA";
						days_remaining = -1;
					}

				} else if (status.equalsIgnoreCase("NON_RENEWING")) {
					sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
					status = "ACTIVE";
					nextPaymentDate = sdf.format(sdf.parse(subscrip.getSubscriptionCancelledAt()).getTime());
					Date cancelledAt = sdf.parse(nextPaymentDate);
					Date todayDate = sdf.parse(sdf.format(dateobj));

					long difference = cancelledAt.getTime() - todayDate.getTime();
					daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
					days_remaining = daysBetween;

					sdf = new SimpleDateFormat("yyyy-MM-dd");
					sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
					nextPaymentDate = sdf.format(sdf.parse(subscrip.getSubscriptionCancelledAt()).getTime());
				}

				if (daysBetween < 0)
					days_remaining = -1;

				ArrayList<Integer> ids = crService.getPlanAndPeriod(planid);
				if (!ids.isEmpty()) {
					iris_splan = ids.get(0);
				}

			}
			setupList = crService.checkDeviceConfigStatusV2(iris_splan, user.getId(), days_remaining);

		} catch (Exception e) {

			log.error("getSubscription:" + e.getMessage());
		}

		return setupList;
	}

	@Override
	public List<LastGatewayReport> getjGatewayReport(long userid, String tempunit, List<JGatewaySubSetup> gateway_setup) {
		
		log.info(" Entered getLastGatewayReportWatch :: userid : " + userid);
		List<LastGatewayReport> lgrList = new ArrayList<LastGatewayReport>();
		try {

			String query = "SELECT G.id,G.name,LGR.humidity,"
					+ " LGR.datetime,LGR.timezone,LGR.speed,LGR.date,LGR.time,LGR.eventid,LGR.battery,LGR.temperature  FROM lastgatewayreport LGR  JOIN  gateway G ON G.id = LGR.gateway_id "
					+ " JOIN usergateway UG ON G.id = UG.gatewayid"
					+ " JOIN alertcfg AG JOIN `alertcfg_to_asset` AA  ON AA.alertcfg_id = AG.id AND AA.asset_id=UG.gatewayid"
					+ " WHERE  AG.alerttype_id=1 AND UG.userid =" + userid ;

			Query qry = slave4SessionFactory.getCurrentSession().createSQLQuery(query);

			List res = qry.list();
			HashMap<Long, Boolean> gateway_setupactivate = new HashMap<Long, Boolean>();
			if (!res.isEmpty() && res.size() > 0) {

				for (JGatewaySubSetup gs : gateway_setup ) {
					gateway_setupactivate.put(gs.getGateway_id(), gs.isSetupActivate());
				}
				
				for (int i = 0; i < res.size(); i++) {
					Object[] lastReport = (Object[]) res.get(i);
				
					LastGatewayReport assetRpt = new LastGatewayReport();
					long gatewayid = ((BigInteger) lastReport[0]).longValue();
					assetRpt.setAssetid(String.valueOf(gatewayid));
					
					DecimalFormat df = new DecimalFormat("0.00");
					float tempval = (float) lastReport[3];

					if (tempunit.equalsIgnoreCase("F") && tempval > -200) {
						tempval = CelsiusToFahrenheit(tempval);	
					}
					String temp = df.format(tempval);
									
					String humidity = (df.format((float) lastReport[2]));
												
					assetRpt.setTemperature(temp);	
					
					String title="";

					int default_rpt = 0;
					String default_rpt_msg = "NA";
					
					SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);//default system tz
					
					try {
						if (gateway_setupactivate.get(gatewayid)) {
							default_rpt = 2; // DNR
							title = "Activate Now";
							default_rpt_msg = "Please activate your plan in the app";
						} else {
							assetRpt.setReportdate(String.valueOf(lastReport[12].toString()));
							assetRpt.setReporttime(lastReport[13].toString());
							assetRpt.setEventid(lastReport[14].toString());
							assetRpt.setBattery(lastReport[15].toString());
							assetRpt.setTdisplayunit(tempunit);
							float spd = (float) lastReport[11];
							assetRpt.setSpeed(spd);
						}

					}catch (Exception e) {
						log.info("getLastGatewayReportWatch:"+ e.getLocalizedMessage());
					}					
					
					
					lgrList.add(assetRpt);
				}

			} else {
				log.info("getJPetprofilesForWatch: No gateways are mapped to this user id- " + userid);
			}

		} catch (Exception e) {
			log.error("Error in getJPetprofilesForWatch :error : " + e.getLocalizedMessage());
		}
		
		return lgrList;
	}
	
	@Override
	public JGatewayGeneralInfo getGatewayGeneralInfo(long gatewayid,String date,String timeZone) {
		JGatewayGeneralInfo jGeninfo = new JGatewayGeneralInfo();
		try {
			
		String query = null;
		String cur_date = IrisservicesUtil.getUtcDate();
		if(date != null && !date.equals("")) {
		 query = "SELECT SUM(LGR.intake_weight),Max(LGR.datetime),IFNULL(PF.calories,0) FROM sensorreport LGR  JOIN  gateway G ON G.id = LGR.gateway_id "
				+ " JOIN assetmodel AM on G.model_id = AM.id"
				+ " LEFT JOIN pet_food PF ON PF.id = LGR.petfood_id"
				+ " WHERE AM.monitor_type_id = 3 AND LGR.gateway_id =" + gatewayid  + " AND DATE(LGR.datetime) = '" + date  +"' AND (LGR.eventid LIKE '%800000' OR LGR.eventid LIKE '%200' OR LGR.eventid LIKE '%1000000' OR LGR.eventid = '111111')  GROUP BY LGR.petfood_id ORDER BY LGR.id DESC";
		}
		
		Query qry = slave4SessionFactory.getCurrentSession().createSQLQuery(query);
		
		
		 String query1 = null;
		 if(date != null && !date.equals("")) {
			  query1 = "SELECT IFNULL(SUM(LGR.weight),0),MAX(LGR.datetime),IFNULL(PF.calories,0) FROM sensorreport LGR"
			  		+ " LEFT JOIN pet_food PF ON PF.id = LGR.petfood_id"
			  		+ " WHERE LGR.gateway_id ="+ gatewayid +" AND (LGR.eventid LIKE '%100' OR LGR.eventid = '20800000' OR LGR.eventid = '111111') AND DATE(LGR.datetime) = '" + date  +"' AND LGR.weight > 0 GROUP BY LGR.petfood_id ORDER BY LGR.id ASC ";
		 }
		 
		 Query qry1 = slave4SessionFactory.getCurrentSession().createSQLQuery(query1);
		 
		 List res = qry1.list();
		 if (!res.isEmpty() && res.size() > 0) {
			 
			 float totWeight = 0;
			 double equvalentCal = 0d;
			 for (int i = 0; i < res.size(); i++) {
				 Object[] row1 = (Object[]) res.get(i);
				 
				 totWeight += ((BigDecimal) row1[0]).floatValue();
				 Double foodCal = (Double) row1[2] != null && (Double) row1[2] > 0 ? (Double) row1[2] : jGeninfo.getFoodCal();
				 
				 equvalentCal += (foodCal / 1000) * ((BigDecimal) row1[0]).floatValue();
				 
				 DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
				 dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"+timeZone));
				 if((Date) row1[1] != null) {
					 jGeninfo.setLastUpdated(dateFormat.format((Date) row1[1]));
				 }
				 
			 }
			jGeninfo.setInitialPortion(totWeight);
			double equvalentkcal = (int)equvalentCal; 
			jGeninfo.setEquvalentCalory(equvalentkcal);
			jGeninfo.setCurrentWeight(totWeight);
		 }
		 
		 
		 String query2 = "SELECT IFNULL(GR.weight,0) as 'curwgt',IFNULL(GR.battery,0),IFNULL(GR.rawrssi,0),PFD.req_calories,PF.calories,GR.datetime as 'lastdate' FROM lastsensorreport GR "
		 		+ " RIGHT JOIN pet_feed_details PFD ON PFD.gateway_id = GR.gateway_id "
		 		+ " JOIN pet_food PF ON PF.id = PFD.pet_food_id WHERE PFD.gateway_id ="+ gatewayid +";";
		 
		 Query qry2 = slave4SessionFactory.getCurrentSession().createSQLQuery(query2);
		 
		 List res2 = qry2.list();
		 
		 if (!res2.isEmpty() && res2.size() > 0) {
			 Object[] row = (Object[]) res2.get(0);
			 
			 DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			 dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"+timeZone));
			 DateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
			 if(date.equalsIgnoreCase(dateFormat1.format((Date) row[5])) && cur_date.equalsIgnoreCase(date)) {
				 jGeninfo.setCurrentWeight(((BigInteger) row[0]).floatValue());
				 jGeninfo.setLastUpdated(dateFormat.format((Date) row[5]));
			 }
			 jGeninfo.setBattery(((BigInteger) row[1]).intValue());
			 jGeninfo.setWifiRange(((BigInteger) row[2]).intValue());
			 double ActKcalVal = (Double) row[3];
			 double kcal = (int)ActKcalVal; 
			 jGeninfo.setActualCalories(kcal);
			 jGeninfo.setFoodCal((Double) row[4]);
			 
		 }
		 
		 jGeninfo.setGatewayId(gatewayid);
		 
		 List res1 = qry.list();
		 if (!res1.isEmpty() && res1.size() > 0) {
			double caloriesIntake = 0d;
			BigDecimal totIntak = new BigDecimal(0);
			for (int i = 0; i < res1.size(); i++) {
			Object[] rowS = (Object[]) res1.get(i);
			 	BigDecimal lastReport = (BigDecimal) rowS[0]; 
			 	Double foodCal = (Double) rowS[2] != null && (Double) rowS[2] > 0 ? (Double) rowS[2] : jGeninfo.getFoodCal();
		 
				
				totIntak = totIntak.add(lastReport);;
				jGeninfo.setFoodCal(foodCal);
				if(jGeninfo.getFoodCal() != null && jGeninfo.getFoodCal()>0) {
					
					caloriesIntake += (jGeninfo.getFoodCal() / 1000) * lastReport.floatValue();
					
				}
				DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
				dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"+timeZone));
				if(StringUtils.isEmpty(jGeninfo.getLastUpdated()) && (Date) rowS[1] != null) {
				  jGeninfo.setLastUpdated(dateFormat.format((Date) rowS[1]));
				}
				
			 }
			
			double consumedcal = (int)caloriesIntake;
			jGeninfo.setCaloriesConsumed(consumedcal);
			
			jGeninfo.setFoodIntake(totIntak != null ? (totIntak).floatValue():0);
		 }
		
		jGeninfo.setContent_1("Food Logging");
		jGeninfo.setContent_2("View Detailed Report");
		jGeninfo.setContent_3("Food Intake");
		jGeninfo.setContent_4("Calories Consumed");
		jGeninfo.setContent_5("Current Weight");
		jGeninfo.setContent_6("Last Updated");
		jGeninfo.setContent_7("Initial Portion");
		jGeninfo.setContent_8("Equivalent Calories");
		jGeninfo.setContent_9("grams");
		jGeninfo.setContent_10("kcal");
	} catch (Exception e) {
		//e.printStackTrace();
		log.error("Error in getGatewayGeneralInfo :error : " + e.getLocalizedMessage());
	}
		
		return jGeninfo;
	}

	@Override
	public PetProfile getGatewayGeneralInfo(long userId) {
		
		String qry = "SELECT * FROM pet_profile pp JOIN  gateway G ON G.id = pp.gateway_id "
				+" JOIN assetmodel AM ON G.model_id = AM.id "
				+" WHERE AM.monitor_type_id = 3 AND pp.user_id =:userId ";
		Query query = slave4SessionFactory.getCurrentSession().createSQLQuery(qry).addEntity(PetProfile.class);
		query.setParameter("userId", userId);
		
		PetProfile petProf = (PetProfile) query.list().get(0); 
		return petProf;
		
		
	}

	@Override
	public List<JGatewayWeekList> getGatewayGenerallist(long gatewayId,String date, String timeZone, boolean isFreePlan) {
		List<JGatewayWeekList> infoList = new ArrayList<JGatewayWeekList>();
		try {
			LocalDate userDate = LocalDate.parse(date);
			
			LocalDate startDate = userDate.with(TemporalAdjusters.previous(DayOfWeek.SATURDAY)).plusDays(1);
			
			LocalDate endDate = userDate.with(TemporalAdjusters.previous(DayOfWeek.SATURDAY)).plusDays(7);
			
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
			dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"+timeZone));
	        
			
			Map<String,JGatewayWeekList> weekMap = new LinkedHashMap<String,JGatewayWeekList>();
			
			Date currDate = _helper.timeZoneConverterfromutc( "yyyy-MM-dd HH:mm:ss","+00:00", timeZone, IrisservicesUtil.getCurrentTimeUTC());
			
			LocalDateTime curlocalDateTime = LocalDateTime.parse(dateFormat.format(currDate), formatter);
	        
	        LocalDate curuserDate1 = curlocalDateTime.toLocalDate();
			
			for(int i = 1;i<=7;i++) {
				JGatewayWeekList assetRpt1 = new JGatewayWeekList();
				LocalDate addDate = userDate.with(TemporalAdjusters.previous(DayOfWeek.SATURDAY)).plusDays(i);
				
				if(addDate.isAfter(curuserDate1.minusDays(91)) && !isFreePlan) {
					assetRpt1.setShow_activate(false);
				}else if (addDate.isAfter(curuserDate1.minusDays(30)) && addDate.isBefore(curuserDate1.plusDays(1)) && isFreePlan) {
					assetRpt1.setShow_activate(false);
				}else {
					assetRpt1.setShow_activate(false);
				}
				
				assetRpt1.setDate(addDate.toString());
				assetRpt1.setDay(addDate.format(DateTimeFormatter.ofPattern("EEEE")).substring(0, 3));
				weekMap.put(addDate.toString(), assetRpt1);
			}
			
			
			
			String query1 = "SELECT COALESCE(PFD.req_calories, 0) AS req_calories, COALESCE(PF.calories, 0) AS calories, COALESCE(SUM(LGR.intake_weight), 0) AS total_intake_weight,LGR.updatedon,COALESCE(PFD.req_grams, 0) AS req_grams "
            		+ "FROM sensorreport LGR LEFT JOIN pet_feed_details PFD ON PFD.gateway_id = LGR.gateway_id LEFT JOIN pet_food PF ON PF.id = LGR.petfood_id "
            		+ "WHERE LGR.gateway_id =:gatewayId AND (LGR.eventid LIKE '%800000' OR LGR.eventid LIKE '%200' OR LGR.eventid LIKE '%1000000' OR LGR.eventid = '111111') AND DATE(LGR.updatedon) between :startDate AND :endDate GROUP BY LGR.petfood_id,DATE(LGR.updatedon) ORDER BY LGR.updatedon;";
           
			Query qry = slave4SessionFactory.getCurrentSession().createSQLQuery(query1);

			qry.setParameter("gatewayId", gatewayId);
			qry.setParameter("startDate", startDate.toString());
			qry.setParameter("endDate", endDate.toString());
   			List res = qry.list();
		        
	   			if (!res.isEmpty() && res.size() > 0) {
	   				
	   				for (int i = 0; i < res.size(); i++) {
					
						double caloriesIntake = 0;
						Object[] lastReport = (Object[]) res.get(i);
						
						JGatewayWeekList assetRpt = new JGatewayWeekList();
			            
			            SimpleDateFormat outputDateFormat = new SimpleDateFormat("EEEE, yyyy-MM-dd");

			            String formattedDate = outputDateFormat.format((Date) lastReport[3]);
			            
			            String[] dateStringList = formattedDate.split(",");
			            
			            double actualCal = (double) lastReport[0];
					    double kcal = (int)actualCal; 
			            assetRpt.setReqCalories(kcal);
			       
			            assetRpt.setReqGrams((double) lastReport[4]);
			            
			            if(weekMap.containsKey(dateStringList[1].trim())){
							BigDecimal bigDecimalValue = (BigDecimal) lastReport[2];
							float foodint = bigDecimalValue.floatValue();
							Double foodCal = ((Double) lastReport[1]);
							caloriesIntake = weekMap.get(dateStringList[1].trim()).getTotalCal() + (foodCal / 1000) * foodint;
							assetRpt.setWeight(weekMap.get(dateStringList[1].trim()).getWeight() + foodint);
							assetRpt.setShow_activate(weekMap.get(dateStringList[1].trim()).isShow_activate());
							assetRpt.setTotalCal(caloriesIntake);
							assetRpt.setDate(dateStringList[1].trim());
							assetRpt.setDay(dateStringList[0].substring(0, 3));
							
							weekMap.put(dateStringList[1].trim(), assetRpt);
						}
			           
					}
				
	        }
	   			
	   		infoList = new ArrayList<JGatewayWeekList>(weekMap.values());	
	} catch (Exception e) {
		log.error("Error in getJWeeklycalories api :error : " + e.getLocalizedMessage());
	}
		return infoList;
	}
	
	@Override
	public List<JGatewayTimeList> getGatewayGeneralTimelist(long gatewayId,String date,String timeZone) {
		// TODO Auto-generated method stub
		List<JGatewayTimeList> infoList = new ArrayList<JGatewayTimeList>();
		try {
			
			 String query = null;
		       
			 if(date != null && !date.equals("")) {
				 	query = "SELECT LGR.pkttime_utc_datetime AS hour_of_day,LGR.intake_weight AS total_speed FROM sensorreport LGR "
				 			+ " WHERE LGR.gateway_id =:gatewayId AND (LGR.eventid LIKE '%800000' OR LGR.eventid LIKE '%200' OR LGR.eventid LIKE '%1000000' OR LGR.eventid = '111111') AND DATE(LGR.pkttime_utc_datetime) =:date ;";
			 }
		 
		 Query qry = slave4SessionFactory.getCurrentSession().createSQLQuery(query);
		 qry.setParameter("gatewayId", gatewayId);
		 qry.setParameter("date", date);

			List res = qry.list();
			if (!res.isEmpty() && res.size() > 0) {
				
				for (int i = 0; i < res.size(); i++) {
					JGatewayTimeList assetRpt = new JGatewayTimeList();
					Object[] lastReport = (Object[]) res.get(i);
					float Weight = ((Integer) lastReport[1]);
					
					SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
					
					assetRpt.setWeight(Weight);
					assetRpt.setTime(sdf.format((Date) lastReport[0]));
					
					infoList.add(assetRpt);
				}
			}
	} catch (Exception e) {
		log.error("Error in getJWeeklycalories api :error : " + e.getLocalizedMessage());
	}
		return infoList;
	}
	
	@Override
	public List<MonitorType> getAllmonitorType() {
		log.info("Entered into getmonitortype");
		List<MonitorType> monitorTypeList = new ArrayList<>();
		try {
			String qry = " SELECT * FROM monitortype  WHERE `enable`=1 ORDER BY `order`;";
			Query query = slave4SessionFactory.getCurrentSession().createSQLQuery(qry).addEntity(MonitorType.class);
			monitorTypeList =  query.list(); 
			
			if( monitorTypeList.isEmpty() ) 
				log.info("Monitortype is empty");
				
			return monitorTypeList;
		} catch (Exception e) {
			log.error("Error in getmonitortype :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public Map<String,List<Object>> getLastGatewayReportV5( long userid, 
			 String tempunit, String country, String category,String os) {
		log.info("Entered ReportDaoImplV4 :: getLastGatewayReportV5 :: user_id : "+ userid+" :: category : "+ category);
		List<MonitorType> monitorTypelist = getAllmonitorType();
		Map<String,List<Object>> lastReport1 = new HashMap<String,List<Object>>();

		monitorTypelist.stream().filter(u->u.getCategory().equals(category)).forEach(category1 ->{
			//long gatewayId = 0L;//(assetid == null || assetid.isEmpty()) ? 0L : Long.parseLong( assetid );
			if(category1.getName().equalsIgnoreCase( IrisservicesConstants.WAGGLE )){
				List<Object[]> greports = this.getLastGatewayReportsV5( userid, category,category1.getName());
				List<Object> lastReport = new ArrayList<Object>();
				for (Object[] report : greports) {

					lastReport.add(convertGatewayLstRpttoAssetRptV5(report, tempunit, country,userid));
				}

				lastReport1.put(category1.getName(), lastReport);
			}else if( category1.getName().equalsIgnoreCase( IrisservicesConstants.WAGGLECAM ) ) {
				try {
					List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, 0, category1.getId());

					lastReport1.put(category1.getName(), wcDeviceLists);

				} catch (Exception e) {
					log.error(e.getLocalizedMessage());
				}


			}else if(category1.getName().equalsIgnoreCase( IrisservicesConstants.SMARTBOWL )) {
				try {
					List<Object> smBowlLists = getPetBowlDeviceList( userid,os, 0);

					lastReport1.put(category1.getName(), smBowlLists);
				} catch (Exception e) {
					log.error(e.getLocalizedMessage());
				}
			}else if(category1.getName().equalsIgnoreCase( IrisservicesConstants.MINICAM )) {
				List<Object> minicams = userServiceV4.listUserMiniCam(userid);

				lastReport1.put(category1.getName(), minicams);
			}else if( category1.getName().equalsIgnoreCase( IrisservicesConstants.WAGGLECAMPRO )) {
				List<Object> petcams = userServiceV4.listUserPetCam(userid);

				lastReport1.put(category1.getName(), petcams);
			}

			if((lastReport1.get("MiniCam")==null) && (category.equalsIgnoreCase("RvHub")))
				lastReport1.put("MiniCam", new ArrayList<Object>());

			if((lastReport1.get("WaggleCam")==null) && (category.equalsIgnoreCase("PetHub")))
				lastReport1.put("WaggleCam", new ArrayList<Object>());

			if((lastReport1.get("WaggleCamPro") == null) && (category.equalsIgnoreCase("PetHub")))
				lastReport1.put("WaggleCamPro", new ArrayList<Object>());

			if((lastReport1.get("SmartBowl")==null) && (category.equalsIgnoreCase("PetHub")))
				lastReport1.put("SmartBowl", new ArrayList<Object>());

			if((lastReport1.get("PetMonitor")==null) && (category.equalsIgnoreCase("RvHub")))
				lastReport1.put("PetMonitor", new ArrayList<Object>());


		});
		//Map<String,List<Object>> jgatewayandrpt = getLstGatrptV5(userid,tempunit, country, category);
		return lastReport1;		
	}
	
	public List<Object[]> getLastGatewayReportsV5( long userid,String category,String catName) {
		log.info("Entered :: OptimizedDoaImpl :: getLastGatewayReportsV4:: ");

		// last used index 51 - LGR.default_report
		String qry = "SELECT "
				+ " G.id,"
				+ " G.name,"
				+ " GRP.groupname,"
				+ " LGR.datetime,"
				+ " LGR.lat,"
				+ " LGR.latdir,"
				+ " LGR.lon,"
				+ " LGR.londir,"
				+ " LGR.speed,"
				+ " LGR.gpsstatus,"
				+ " LGR.gpsinfo,"
				+ " LGR.gpsmode,"
				+ " LGR.battery,"
				+ " LGR.distance,"
				+ " LGR.heading,"
				+ " LGR.eventid,"
				+ " LGR.nmeventid,"
				+ " LGR.iostatus,"
				+ " LGR.externalsensor," 
				+ " G.extsensortype,"
				+ " LGR.humidity,"
				+ " LGR.temperature,"
				+ " LGR.tempseverity,"
				+ " LGR.light,"
				+ " LGR.pressure,"
				+ " LGR.motion,"
				+ " LGR.rssi,"
				+ " LGR.address," 
				+ " G.isalive,"
				+ " LGR.lastvalidlat,"
				+ " LGR.lastvalidlatdir,"
				+ " LGR.lastvalidlon,"
				+ " LGR.lastvalidlondir,"
				+ " LGR.lastvaliddatetime,"
				+ " GRP.id AS grpId," 
				+ " LGR.cellidlat,"
				+ " LGR.cellidlon,"
				+ " LGR.cellidacc,"
				+ " LGR.version,"
				+ " LGR.lastvalidaddress,"
				+ " LGR.rawrssi,"
				+ " LGR.lastvalidtemp,"
				+ " LGR.timezone,"
				+ " LGR.heat_index,"
				+ " AM.isgps,"
				+ " P.type,"
				+ " AM.model,"
				+ " G.onoffstatus,"
				+ " LGR.updatedon,"
				+ " AM.is_upgrade,"
				+ " AM.ishumidity,"
				+ " LGR.defalut_rpt," 
				+ " G.show_temp_video,"
				+ " LGR.power_cmd,"
				+ " LGR.aqi,"
				+ " LGR.voc,"
				+ " LGR.co2,"
				+ " AM.is_aqi,"
				+ " MT.name AS devicetype,"
				+ " G.qrcode, "
				+ " MT.id AS monitor_type_id,"//60
				+ " GP.radius,"
				+ " GP.enable,"
				+ " GP.last_lat,"
				+ " GP.last_lon,"		//64
				+ " GP.gps_address,"
				+ " AM.model as devicemodel," + //66
				" APS.plan_id," +      // 67
				" FPH.is_paused," +    // 68
				" FPH.current_cycle_end_at," + //69
				" APS.subscription_status," + // 70
				" FPH.current_cycle,"+        // 71
				" G.show_order_id, "           //72
				+ " UG.userId, "           	//73
				+ " G.purchased_from_others, "           	//74
				+ " G.iswithoutsub, "           	//75
				+ " G.isnewverdevice, "           	//76
				+ " G.meid, "					//77
				+ " G.warranty_skipped, "					//78
				+ " G.order_channel "             // 79 OrderChannel to show Purchased From
				+ " FROM lastgatewayreport LGR, usergateway UG,  `groups` GRP, assetmodel AM," + 
				" monitortype MT, probecategory P, gateway G LEFT JOIN `gateway_parksafe` GP ON G.id = GP.gateway_id" +
				" LEFT JOIN all_product_subscription APS ON APS.gateway_id=G.id " +
				" LEFT JOIN flexi_plan_history FPH ON FPH.gateway_id=G.id "+
				" WHERE G.id = UG.gatewayid AND G.id = LGR.gateway_id AND GRP.id = G.groups_id " + 
				" AND G.model_id=AM.id AND G.model_id=P.model_id AND MT.id=AM.monitor_type_id " +
				" AND MT.category=:category AND UG.userid =:userid AND MT.enable=1 AND MT.name =:catName ";

		Query query = this.slave4SessionFactory.getCurrentSession()
				.createSQLQuery(qry + " GROUP BY G.id order by LGR.id");
		query.setParameter("userid", userid);
		query.setParameter("category", category);
		query.setParameter("catName", catName);
		


		List<Object[]> greports = query.list();
		return greports;
	}
	
	private JAssetLastReportV5 convertGatewayLstRpttoAssetRptV5(Object[] lastReport, String tempunit, String country, long userid) {
		DecimalFormat df = new DecimalFormat("0.0");
		boolean show_battery = false;
		String bat_info="NA";
		boolean isN13 = false;
		float tempval = (float) lastReport[21];
		float lastValidTemp = (float) lastReport[41];
		if (tempunit.equalsIgnoreCase("F") && tempval > -200)
			tempval = CelsiusToFahrenheit(tempval);
		if (tempunit.equalsIgnoreCase("F") && lastValidTemp > -200)
			lastValidTemp = CelsiusToFahrenheit(lastValidTemp);
		float heatIndexVal = (float) lastReport[43];
		if (tempunit.equalsIgnoreCase("C")) {
			heatIndexVal = FahrenheitToCelsius(heatIndexVal);
		}
		tempval = Float.parseFloat(df.format(tempval));
		heatIndexVal = Float.parseFloat(df.format(heatIndexVal));
		float humidity = Float.parseFloat(df.format((float) lastReport[20]));
		
		int batteryVal = (int) lastReport[12];
		String devicemodel = (String) lastReport[46];
		boolean show_aqi = false;
		boolean isN12_5 = false;
		String bleId = "";

		if (devicemodel.contains("N13"))
		{
			isN13 = true;
			show_aqi=true;
		}
		if(devicemodel.contains("N12.5"))
		{
			bleId = "N12_" + (String) lastReport[77];
			isN12_5 = true;
		}
		if ((devicemodel.equalsIgnoreCase("N1-503 NT3D") || devicemodel.equalsIgnoreCase("N1A-503 NT3D")
				|| devicemodel.equalsIgnoreCase("N1-503 NT3B")) && (batteryVal > 1 && batteryVal <= 5)) {
			batteryVal = 1; // from 1-5 = display as 1 for the above model oly
		}

		if ((devicemodel.equalsIgnoreCase("N1A-503 NT3D")) && (batteryVal == -3)) {
			String[] gpsDetails = null;
			String gpsInfo = (String) lastReport[10];
			gpsDetails = gpsInfo.split(":");
			int battery = 0;

			if (gpsDetails.length > 11) {
				battery = Integer.valueOf(gpsDetails[11]);

				if (battery >= 96)
					batteryVal = -4; // fully charged
			}
		}

		JAssetLastReportV5 assetRpt = new JAssetLastReportV5();
		assetRpt.setBle_id(bleId);
		assetRpt.setAssetid(((BigInteger) lastReport[0]).longValue());
		assetRpt.setAssetname((String) lastReport[1]);
		assetRpt.setGroupname((String) lastReport[2]);
		assetRpt.setDatetime(changeDateFormat((Timestamp) lastReport[3]));
		assetRpt.setLat(Double.parseDouble(lastReport[4] + ""));
		assetRpt.setLatdir((String) lastReport[5]);
		assetRpt.setLon(Double.parseDouble(lastReport[6] + ""));
		assetRpt.setLondir((String) lastReport[7]);
		assetRpt.setSpeed(Float.parseFloat(lastReport[8] + ""));
		assetRpt.setGpsstatus((String) lastReport[9]);
		assetRpt.setGpsinfo((String) lastReport[10]);
		assetRpt.setGpsmode((int) lastReport[11]);
		assetRpt.setBatt(batteryVal);
		assetRpt.setDistance(Float.parseFloat(lastReport[13] + ""));
		assetRpt.setHeading((String) lastReport[14]);
		assetRpt.setEventid((String) lastReport[15]);

		assetRpt.setNmeventid((String) lastReport[16]);
		assetRpt.setIostatus((int) lastReport[17]);
		assetRpt.setExtsensor(Float.parseFloat(lastReport[18] + ""));
		assetRpt.setExtsensortype((String) lastReport[19]);
		assetRpt.setHumidity(humidity);
		assetRpt.setTemperature(tempval);
		assetRpt.setTempseverity((int) lastReport[22]);
		assetRpt.setLight(Float.parseFloat(lastReport[23] + ""));
		assetRpt.setPressure(Float.parseFloat(lastReport[24] + ""));
		assetRpt.setMotion((String) lastReport[25]);
		assetRpt.setRssi((String) lastReport[26]);
		assetRpt.setAddress((String) lastReport[27]);
		assetRpt.setAlive((boolean) lastReport[28]);
		assetRpt.setLastvalidlat((double) lastReport[29]);
		assetRpt.setLastvalidlatdir((String) lastReport[30]);
		assetRpt.setLastvalidlon((double) lastReport[31]);
		assetRpt.setLastvalidlondir((String) lastReport[32]);
		assetRpt.setLastvaliddatetime(changeDateFormat((Timestamp) lastReport[33]));
		assetRpt.setGroupid(((BigInteger) lastReport[34]).longValue());
		assetRpt.setCellidlat((double) lastReport[35]);
		assetRpt.setCellidlon((double) lastReport[36]);
		assetRpt.setCellidacc(Float.parseFloat(lastReport[37] + ""));
		assetRpt.setN12_5(isN12_5);
		String version = (String) lastReport[38];
		String eventname = getEventsName((String) lastReport[15], version, true, true); /* Parse Eventid */
		String nmeventname = "";

		if (version.equalsIgnoreCase("004") || version.equalsIgnoreCase("005") || version.equalsIgnoreCase("HD06")
				|| version.equalsIgnoreCase("HD07")|| version.equalsIgnoreCase("N701")|| version.equalsIgnoreCase("N702")
				|| version.equalsIgnoreCase("N7G1"))
			nmeventname = getEventsName((String) lastReport[16], version, false, true); /* Parse NMEventid */

		assetRpt.setEventname(getLastEventName(eventname, nmeventname));
		assetRpt.setLastvalidaddress((String) lastReport[39]);
		assetRpt.setRawrssi((int) lastReport[40]);
		double lat_d = getLatAndLon((Double) lastReport[4], (String) lastReport[5]);
		double lon_d = getLatAndLon((Double) lastReport[6], (String) lastReport[7]);
		double lastvalidlat_d = getLatAndLon((Double) lastReport[29], (String) lastReport[30]);
		double lastvalidlon_d = getLatAndLon((Double) lastReport[31], (String) lastReport[32]);

		assetRpt.setLat_d(lat_d);
		assetRpt.setLon_d(lon_d);

		assetRpt.setLastvalidlat_d(lastvalidlat_d);
		assetRpt.setLastvalidlon_d(lastvalidlon_d);

		assetRpt.setLastvalidtemp(lastValidTemp);
		String tz = ((String) lastReport[42]).trim();
		assetRpt.setRpt_timezone(tz);
		assetRpt.setHeat_index(heatIndexVal);

		boolean enableLoc = false;
		boolean onoffstatus = false;
		if ((lastReport[44].toString()).equalsIgnoreCase("1") || (lastReport[44].toString()).equalsIgnoreCase("true"))
			enableLoc = true;

		assetRpt.setEnableLocation(enableLoc);

		assetRpt.setProbeType((String) lastReport[45]);

		if ((lastReport[47].toString()).equalsIgnoreCase("1") || (lastReport[47].toString()).equalsIgnoreCase("true"))
			onoffstatus = true;

		assetRpt.setOnoffstatus(onoffstatus);
		
		assetRpt.setUpdatedon(changeDateFormat((Timestamp) lastReport[48]));
		assetRpt.setIs_upgrade((boolean) lastReport[49]);
		
		assetRpt.setIshumidity((boolean) lastReport[50]);
		int default_rpt = 0;
		String default_rpt_msg = "NA";
		String default_rpt_label = "NA";
		String default_rpt_msg_watch = "NA";
		boolean show_N13Poff = false;
		
		boolean show_temp_video = false;
		
		if ((lastReport[52].toString()).equalsIgnoreCase("1") || (lastReport[52].toString()).equalsIgnoreCase("true"))
			show_temp_video = true;
			
		assetRpt.setShow_temp_video(show_temp_video);
		
		SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);//default system tz
		
		//check for battery below lowbatteryvalue config value........
		try {
			if(isN13 && (batteryVal <= lowbatteryvalue && batteryVal > 0 )) {
				default_rpt = 4;
				default_rpt_label = "Oops! No data found!";
				default_rpt_msg = "A quick charge or restart should help fix this";
				default_rpt_msg_watch = "Low battery, Please check on your app";
			}
			
		}catch (Exception e) {
			log.info("getBatteryValfailed:"+ e.getLocalizedMessage());
		}
		try {
			boolean flexiPlan = lastReport[67]!= null && ((String) lastReport[67]).contains("flexi");
			if ((boolean) lastReport[51]) {
				default_rpt = 2;
				default_rpt_msg = "Contact our support";

				float diffInHrs = 0;
				Date curr = formatter.parse(IrisservicesUtil.getCurrentTimeUTC());

				Date pre = formatter.parse(lastReport[48].toString()); // 48 index - updatedon date

				diffInHrs = ((curr.getTime() - pre.getTime()) / 3600000);
				if (diffInHrs >= 24) {
					String subPurchaseTime = getSubUpdateTime(userid,assetRpt.getAssetid());

					pre = formatter.parse(subPurchaseTime); // read from all_chargebee_subscription table

					diffInHrs = ((curr.getTime() - pre.getTime()) / 3600000);

					if (diffInHrs <= 24) {
						default_rpt = 1;
						default_rpt_msg = "Your Pet Monitor may take up to 24 hrs to connect with carrier network. Sit back-we've got it covered!";
						default_rpt_label = "Network Setup in Process!";
						default_rpt_msg_watch = "FDNR Please check on your app";
					}else {
						default_rpt = 2;
						if (country.equalsIgnoreCase("AU"))
							default_rpt_msg = "Try a quick reset - it should be available soon";
						else
							default_rpt_msg = "Try a quick reset - it should be available soon";
						default_rpt_label = "Data isn't visible yet?";
						default_rpt_msg_watch = "DNR Please check on your app";
					}
				} else {
					default_rpt = 1;
					default_rpt_msg = "Your Pet Monitor may take up to 24 hrs to connect with carrier network. Sit back-we've got it covered!";
					default_rpt_label = "Network Setup in Process!";
					default_rpt_msg_watch = "FDNR Please check on your app";

				}


			} else {
				Date userTime = formatter.parse(lastReport[3].toString());
				Date d = Calendar.getInstance().getTime();

				SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // user timezone
				df1.setTimeZone(TimeZone.getTimeZone("GMT"+tz.trim()));
				String dtt =  df1.format(d);
				Date curTime = formatter.parse(dtt);
				
				float diffInHrs = ((curTime.getTime() - userTime.getTime()) / 3600000);
				
				if (diffInHrs >= 1 && (batteryVal ==-1 )) {
					default_rpt = 3;
					show_battery = true;
					bat_info="Please contact support";
				}
//				else if (diffInHrs >= 1 && ((batteryVal <=20  && batteryVal >=11) || batteryVal ==-2 ) && devicemodel.contains("N13")) {
//					show_battery = true;
//					bat_info="Charge for at least 45 minutes before turning ON";
//				}
				else if (diffInHrs >= 1 && devicemodel.contains("N13") && batteryVal > lowbatteryvalue) {
					default_rpt = 3;
					default_rpt_msg = "A quick reset should sort it out";
					default_rpt_label = "We're missing updates from the Pet Monitor";
				}
				else if (diffInHrs >= 1 && ((batteryVal <=lowbatteryvalue  && batteryVal >=0 ) || batteryVal ==-2  )) {
					default_rpt = 3;
					show_battery = true;
					bat_info="Charge and reset";
				}else if (diffInHrs >= 1 && (batteryVal >lowbatteryvalue || batteryVal ==-3 || batteryVal ==-4)) {
					default_rpt = 3; // during DNR state- play video to reset device
					default_rpt_msg_watch = "Please watch on your app";
				}
			}

			if(flexiPlan){
				if(((String) lastReport[70]).equalsIgnoreCase("cancelled")){
					default_rpt = 2;
					default_rpt_msg = "Contact our support";
				} else if(lastReport[68] != null) {
					long currMonthDaysLeft = _helper.getDaysBetweenDate(_helper.getCurrentTimeinUTC(),((Timestamp) lastReport[69]).toString());
					if(currMonthDaysLeft <= 0){
						if ((int) lastReport[71] == 3) {
							default_rpt = 6;
							default_rpt_label = "3 months completed!";
							default_rpt_msg = "Upgrade now to keep going.";
							default_rpt_msg_watch = "3 months completed!";
						} else if(Boolean.parseBoolean(lastReport[68].toString())) {
							default_rpt = 5;
							default_rpt_label = "Temporarily paused!";
							default_rpt_msg = "Resume to keep track.";
							default_rpt_msg_watch = "Temporarily paused!";
						}
					}
				} else {
					default_rpt = 1;
					default_rpt_label = "Plan purchased!";
					default_rpt_msg = "Activate anytime to start monitoring.";
					default_rpt_msg_watch = "Plan purchased!";
				}
			}
			
			if ((lastReport[53].toString()).equalsIgnoreCase("POWEROFF") && isN13) {
				bat_info="Monitor: Powered OFF";
				show_N13Poff=true;
			}
			//LGR.aqi,LGR.voc,LGR.co2 
			int aqi = (int) lastReport[54];
			int co2 =(int) lastReport[56];
			int voc = (int) lastReport[55];
			show_aqi = (boolean)lastReport[57];
			String device_type = (String) lastReport[58];
//			assetRpt.setProfileId(((BigInteger) lastReport[59]).longValue());		
//			assetRpt.setImgUrl((String) lastReport[60]);
			assetRpt.setQrcode((String) lastReport[59]);
			assetRpt.setMonitortype(((BigInteger) lastReport[60]).longValue());
			
			//parksafe
			boolean is_parksafe = false;
			double last_lat=0;
			double last_lon=0;
			double radius=0;
			
			if(lastReport[61]!= null) {
				radius = (Double) lastReport[61];
				assetRpt.setRadius(radius);
			}
			if(lastReport[62]!= null) {
				is_parksafe = (boolean) lastReport[62];
				assetRpt.setIs_parksafe(is_parksafe);				
			}
			if(lastReport[63]!= null) {
				last_lat= (Double) lastReport[63];	
				assetRpt.setLast_lat(last_lat);
			}
			if(lastReport[64]!= null) {
				last_lon = (Double) lastReport[64];
				assetRpt.setLast_lon(last_lon);
			}
			if(lastReport[65]!= null) {
				String gps_address = (String) lastReport[65];
				assetRpt.setGps_address(gps_address);
			}
			if(lastReport[66]!= null) {
				String deviceModel = (String) lastReport[66];
				assetRpt.setIs_ev3_device(ev3_devices.contains(deviceModel));
			}
			String co2_info = "";
			String aqi_info = "";
			String aqi_desc = "";
			String voc_info = "";

			if(co2>=400 && co2<=600)
				co2_info = "Excellent";
			else if(co2>=601 && co2<=800)
				co2_info = "Good";
			else if(co2>=801 && co2<=1000)
				co2_info = "Fair";
			else if(co2>=1001)
				co2_info = "Poor";
			else
				co2_info = "Bad";
			
			if(aqi==1 ) {
				aqi_info = "Excellent";
				aqi_desc = "Fresh air, no health risks for pets.";	
			}
			else if(aqi==2 ) {
				aqi_info = "Good";
				aqi_desc = "Air quality is safe for most, but sensitive pets may experience mild effects.";	
			}
			else if(aqi==3 ) {
				aqi_info = "Moderate";
				aqi_desc = "Pets may experience slight breathing discomfort.";	
			}
			else if(aqi==4 ) {
				aqi_info = "Poor";
				aqi_desc = "Unsafe for puppies, senior pets, and pregnant dogs.";	
			}
			else {
				aqi=5;
				aqi_info = "Unhealthy";
				aqi_desc = "Health alert! Seek veterinary advice if symptoms appear.";	
			}
			
			if(voc>0 && voc<=220) {
				voc_info = "Good";
			}else if(voc>221 && voc<=660) {
				voc_info = "Moderate";
			}else if(voc>661 && voc<=1430) {
				voc_info = "High";
			}else if(voc>1431) {
				voc_info = "Very High";
			}else
				voc_info = "Bad";
			
			assetRpt.setAqi(aqi);
			assetRpt.setAqi_info(aqi_info);
			assetRpt.setAqi_desc(aqi_desc);
			assetRpt.setVoc(voc);
			assetRpt.setVoc_info(voc_info);
			assetRpt.setCo2(co2);
			assetRpt.setCo2_info(co2_info);
			assetRpt.setShow_aqi(show_aqi);
			assetRpt.setDevice_type(device_type);

			if(tempunit.equalsIgnoreCase("C")) {
				tempval = CelsiusToFahrenheit(tempval);
			}

            int environmentScorePercentage = getEnvironmentScorePercentage((int) tempval, (int) humidity, aqi);
			int environmentScore = getEnvironmentScore(environmentScorePercentage);

            assetRpt.setEnv_score(environmentScore);
			assetRpt.setEnv_score_desc(getEnvironmentScoreDescription(environmentScore));
        }catch (Exception e) {
			log.info("convertGatewayLstRpttoAssetRptV5:"+ e.getLocalizedMessage());
		}
		assetRpt.setDefault_report(default_rpt);
		assetRpt.setDefault_rpt_msg(default_rpt_msg);
		assetRpt.setDefault_rpt_label(default_rpt_label);
		assetRpt.setShow_battery(show_battery);
		assetRpt.setBat_info(bat_info);
		assetRpt.setN13(isN13);
		assetRpt.setShow_N13Poff(show_N13Poff);
		assetRpt.setDefault_rpt_msg_watch(default_rpt_msg_watch);
		
		JPetprofileFlutter pet_profile = petSpeciesServices.getJPetprofiles( assetRpt.getAssetid() );
		assetRpt.setPet_profile(pet_profile);

		assetRpt.setOrdermapped((boolean) lastReport[72]);
		if(hide_warranty_popup || (boolean) lastReport[78]){
			assetRpt.setOrdermapped(true);
		}
		if ((boolean) lastReport[76]) {
			if (pet_profile != null) {
				if (pet_profile.getSex() == null || pet_profile.getSex().equalsIgnoreCase("null") || pet_profile.getBreed() == null || pet_profile.getBreed().equalsIgnoreCase("null") || (pet_profile.getAgeMonth().equals("00") && pet_profile.getAgeYr().equals("00"))) {
					assetRpt.setProfileStep(0);
				}
			}

			if (assetRpt.getProfileStep() != 0) {
				UserV4 user = null;
				try {
					long userId = ((BigInteger) lastReport[73]).longValue();
					user = userServiceV4.verifyAuthV3("id", String.valueOf(userId));
					if (user.getMobileno() == null || user.getMobileno().trim().equalsIgnoreCase("")) {
						assetRpt.setProfileStep(1);
					}
				} catch (InvalidAuthoException ex) {
					log.info("convertGatewayLstRpttoAssetRptV5 user check:" + ex.getLocalizedMessage());
				}
				if (assetRpt.getProfileStep() != 1) {
					boolean userDevice = gatewayServiceV4.getUserDeviceSpotByUserId(user.getId(), assetRpt.getAssetid());
					if (!userDevice) {
						assetRpt.setProfileStep(2);
					}
					if (assetRpt.getProfileStep() != 2) {
						if (!assetRpt.isOrdermapped() && (boolean) lastReport[75]) {
							assetRpt.setProfileStep(3);
						}
					}
				}
			}
		}

		long orderChannelId =  ((BigInteger) lastReport[79]).longValue();
		if(orderChannelId>0){
			assetRpt.setPurchased_from(getOrderChannelName(orderChannelId));
		}


		return assetRpt;
	}

	private String getOrderChannelName(long orderChannelId) {

		log.info("Entered :: OptimizedDoaImpl :: getOrderChannelName:: ");

		String orderChannel="NA";

		String query1 = null;

		query1 = "SELECT orderchannel FROM order_channel WHERE id = :id";

		Query qry1 = slave4SessionFactory.getCurrentSession().createSQLQuery(query1).setParameter("id",orderChannelId);

		try{
			List res = qry1.list();
			if (res.size() != 0) {
				orderChannel= (String) res.get(0);
			}

		} catch (Exception e) {
			log.error("Error getOrderChannelName : " + e.getMessage());

		}
		finally {
			return orderChannel;
		}

	}

	@Override
	public List<Object> getPetBowlDeviceList(long userid,String os, long gatewayId) {
		log.info("Entered :: OptimizedDoaImpl :: getPetBowlDeviceList:: ");

		List<Object> smDeviceList = new ArrayList<Object>();
		List<JAssetLastSensorReport> smDeviceList1 = new ArrayList<>();

		String timeZone = "+00:00";

		String query1 = null;

		query1 = "SELECT timezone FROM user_timezone WHERE user_id = :userid ";

		Query qry1 = slave4SessionFactory.getCurrentSession().createSQLQuery(query1);
		qry1.setParameter("userid", userid);

		if (qry1.list().size() > 0) {
			timeZone = (String) qry1.list().get(0);  
		}

		String cur_date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT,timeZone);

		Query query = null;
		
		String qry = "SELECT "
				+ "G.id, "													//0
				+ "G.name, " 												//1
				+ "MAX(LGR.updatedon), "								//2
				+ "LGR.weight, "											//3	
				+ "SUM(LGR.intake_weight), "								//4
				+ "LGR.battery, "											//5
				+ "LGR.rawrssi, "											//6
				+ "IF (PFD.req_calories IS NULL,0, PFD.req_calories), "		//7
				+ "IF (PF.calories IS NULL,0, PF.calories), "				//8
				+ "MT.name AS devicetype, "									//9
				+ "IF (PP.id IS NULL,0, PP.id) AS petId, "					//10
				+ "IF (PP.imageurl IS NULL,'NA', PP.imageurl), "			//11
				+ "G.qrcode, "												//12
				+ "LGR.petfood_id, "										//13
				+ "MT.id AS monitore_type, "								//14
				+ "G.add_on, "												//15
				+ "RPN.device_notification, "								//16
				+ "IF (PFD.req_grams IS NULL,0, PFD.req_grams), "			//17
				+ "PL.id AS plan_id, "										//18
				+ "PL.is_freeplan, "										//19
				+ "RPN.habit_alert_notification, "							//20
				+ "GF.sub_id, "												//21
				+ "GF.period_id AS periodid "								//22
				+ "FROM gateway G "
				+ "LEFT JOIN sensorreport LGR ON G.id = LGR.gateway_id AND (LGR.eventid LIKE '%800000' OR LGR.eventid LIKE '%200' OR LGR.eventid LIKE '%1000000') AND DATE(LGR.updatedon) = '"+cur_date+"' "
				+ "LEFT JOIN usergateway UG ON G.id = UG.gatewayid "
				+ "LEFT JOIN assetmodel AM ON G.model_id = AM.id "
				+ "LEFT JOIN monitortype MT ON MT.id = AM.monitor_type_id "
				+ "LEFT JOIN pet_feed_details PFD ON PFD.gateway_id = G.id "
				+ "LEFT JOIN pet_food PF ON PF.id = LGR.petfood_id "
				+ "LEFT JOIN pet_profile PP ON PP.gateway_id = LGR.gateway_id "
				+ "LEFT JOIN restrict_push_notification RPN ON RPN.gateway_id = G.id "
				+ "LEFT JOIN gateway_feature GF ON GF.gateway_id = G.id AND GF.enable=1 "
				+ "LEFT JOIN plan PL ON PL.id = GF.plan_id "
				+ "WHERE MT.id=3  AND MT.enable=1 AND UG.userid =:userid";

		if (gatewayId != 0) {
			qry += " AND UG.gatewayId=:gatewayid";
			query = this.slave4SessionFactory.getCurrentSession()
					.createSQLQuery(qry + " GROUP BY G.id,LGR.petfood_id,DATE(LGR.updatedon) order by LGR.updatedon ASC");
			query.setParameter("userid", userid);
			query.setParameter("gatewayid", gatewayId);
		} else {
			query = this.slave4SessionFactory.getCurrentSession()
					.createSQLQuery(qry + " GROUP BY G.id,LGR.petfood_id,DATE(LGR.updatedon) order by LGR.updatedon ASC");
			query.setParameter("userid", userid);
		}
		
		List<Object[]> greports = query.list();

		if (greports.size() > 0) {
			Map<Long,Double> existGateway = new HashMap<Long,Double>();
			float foodInt = 0;
			double caloriesIntake = 0d;
			
			int default_rpt = 0;
			String default_rpt_msg = "NA";
			String default_rpt_label = "NA";
			
			 DateFormat dateFormat = new SimpleDateFormat("MMM dd, hh:mm a");  
			 dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"+timeZone));
			 
			 SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
			 SimpleDateFormat dateformatter = new SimpleDateFormat(IrisservicesConstants.DATEFORMAT);

			try {
				for (Object[] result : greports) {
				    
				    JAssetLastSensorReport sbDevice;
				    long assetId = ((BigInteger) result[0]).longValue();
				    
				    if (existGateway.containsKey(assetId)) {
				    	
				        sbDevice = (JAssetLastSensorReport) smDeviceList1.stream()
				                .filter(device -> device.getAssetid() == assetId)
				                .findFirst()
				                .orElse(null);
				                
				        if (sbDevice == null) {
				            continue;
				        }
				    } else {
				        
				        sbDevice = new JAssetLastSensorReport();
				         foodInt = 0;
					     caloriesIntake = 0d;
				        existGateway.put(assetId, 0d);
				        smDeviceList1.add(sbDevice);
				    }

				    sbDevice.setAssetid(assetId);
				    sbDevice.setAssetname((String) result[1]);
				    DateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
				    sbDevice.setDatetime((Date) result[2] != null ? dateFormat1.format((Date) result[2]) : "");

				   
				    sbDevice.setBattery((Integer) result[5] != null ? (Integer) result[5] : 0);
				    sbDevice.setWifiRange((Integer) result[6] != null ? (Integer) result[6] : 0);
				    
				    if((Date) result[2] != null) {

						if (cur_date.equals(dateFormat1.format((Date) result[2]))) {

				    		if (result[4] != null) {
				    			foodInt += ((BigDecimal) result[4]).floatValue();
				    		}
				    		sbDevice.setFoodCal((Double) result[8]);
				    		if (sbDevice.getFoodCal() != null && sbDevice.getFoodCal() > 0 && result[4] != null) {
				    			caloriesIntake += (sbDevice.getFoodCal() / 1000) * ((BigDecimal) result[4]).floatValue();
				    		}
				    		sbDevice.setFoodIntake(foodInt);
				    		sbDevice.setCaloriesConsumed(caloriesIntake);

				    		Date curr = formatter.parse(IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATETIMEFORMAT,"+00:00"));
				    		float diffInHrs = 0;
				    		Date pre = formatter.parse(formatter.format((Date) result[2]).toString());
				    		diffInHrs = ((curr.getTime() - pre.getTime()) / 3600000);
				    		if (diffInHrs >= 3 && cur_date.equals(dateformatter.format(curr))) {
				    			default_rpt = 1;
				    			default_rpt_msg = "No Internet connection found. \n Check your connection or try again";
				    			default_rpt_label = "Whoops!";
				    		}else {
				    			default_rpt = 0;
				    			default_rpt_msg = "NA";
				    			default_rpt_label = "NA";
				    		}
				    		sbDevice.setDefault_report(default_rpt);
				    		sbDevice.setDefault_rpt_msg(default_rpt_msg);
				    		sbDevice.setDefault_rpt_label(default_rpt_label);

				    		if(dateformatter.format((Date) result[2]).equals(dateformatter.format(curr)) && cur_date.equals(dateformatter.format(curr))) {
				    			Date convertedDate = _helper.timeZoneConverterfromutc( "yyyy-MM-dd HH:mm:ss","+00:00", timeZone, ((Date) result[2]).toString());
				    			sbDevice.setLastUpdated(dateFormat.format(convertedDate));
				    		}
				    	}
				    } else {
				    	
				    	String query2 = "SELECT LGR.updatedon as 'lastdate' FROM lastsensorreport LGR WHERE LGR.gateway_id =:gatewayId ;";
						 
						 Query qry2 = slave4SessionFactory.getCurrentSession().createSQLQuery(query2);
						 qry2.setParameter("gatewayId", assetId);
						 List res2 = qry2.list();
						 if (!res2.isEmpty() && res2.size() > 0) {
							 Date curr = formatter.parse(IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATETIMEFORMAT,"+00:00"));
							 float diffInHrs = 0;
								
								Date pre = formatter.parse(formatter.format((Date) res2.get(0)).toString()); 

								diffInHrs = ((curr.getTime() - pre.getTime()) / 3600000);

								if (diffInHrs >= 3 && cur_date.equals(dateformatter.format(curr))) {
									default_rpt = 1;
									default_rpt_msg = "No Internet connection found. \n Check your connection or try again";
									default_rpt_label = "Whoops!";
								}else {
					    			default_rpt = 0;
					    			default_rpt_msg = "NA";
					    			default_rpt_label = "NA";
					    		}
								
							} else {
								default_rpt = 1;
								default_rpt_msg = "No Internet connection found. \n Check your connection or try again";
								default_rpt_label = "Whoops!";
							}
		    			
		    			sbDevice.setDefault_report(default_rpt);
			    		sbDevice.setDefault_rpt_msg(default_rpt_msg);
			    		sbDevice.setDefault_rpt_label(default_rpt_label);
		    		}
		    		
				    double actualKcal = (Double) result[7];
				    
				    double kcal = (int)actualKcal; 
				    sbDevice.setDevice_type((String) result[9]);
				    sbDevice.setActualCalories(kcal);
				    sbDevice.setProfileId(((BigInteger) result[10]).longValue());
				    sbDevice.setImgUrl((String) result[11]);
				    sbDevice.setQrcode((String) result[12]);
				    sbDevice.setMonitortype(((BigInteger) result[14]).longValue());
				    sbDevice.setAdd_on((boolean) result[15]);
				    if(result[16] != null) {
				    	sbDevice.setPb_notification((boolean) result[16]);
				    }else {
				    	sbDevice.setPb_notification(false);
				    }
				    sbDevice.setReqGrams((Double) result[17]);
				    JPetprofileFlutter pet_profile = petSpeciesServices.getJPetprofiles(sbDevice.getAssetid());
				    sbDevice.setPet_profile(pet_profile);
				    
				    Double CalPer =  0d;
					CalPer =  (sbDevice.getCaloriesConsumed() / sbDevice.getActualCalories()) * 100;
					int PerVal = 1;
					if (CalPer >= 0 && CalPer <= 20) {
						PerVal = 1;
			        } else if (CalPer > 20 && CalPer <= 40) {
			        	PerVal = 2;
			        }  else if (CalPer > 40 && CalPer <= 60) {
			        	PerVal = 3;
			        }  else if (CalPer > 60 && CalPer <= 80) {
			        	PerVal = 4;
			        }  else if (CalPer > 80 && CalPer <= 100) {
			        	PerVal = 5;
			        } else if ( CalPer > 100) {
			        	PerVal = 5;
			        }
					
					sbDevice.setPerVal(PerVal);
					List<BluetoothDeviceList> ble_device_list = wifiInfoServiceV4.getWifiList(sbDevice.getAssetid(), userid,os);
					sbDevice.setBledevicelist(ble_device_list.isEmpty() ? null : ble_device_list.get(0));
					if (result[18] != null) {
						sbDevice.setPlanId(((BigInteger) result[18]).longValue());
					}
					if (result[19] == null) {
						sbDevice.setShow_activate(false);
						sbDevice.setIs_freePlan(false);
					} else {
						sbDevice.setShow_activate(false);
						sbDevice.setIs_freePlan(false);
					}

					if (result[20] != null) {
						sbDevice.setPb_habit_notification((boolean) result[20]);
					}

					if (result[21] != null) {
						sbDevice.setSub_id((String) result[21]);
					}

					if (result[22] != null) {
						sbDevice.setPeriodId(((BigInteger) result[22]).longValue());
					}

				}
			    }catch(Exception e) {
			    	
					log.error("Error in getPetBowlDeviceList api :error : " + e.getLocalizedMessage());
			    }
		}
		smDeviceList.addAll(smDeviceList1);
		return smDeviceList;
	}
	
	@Override
	public List<JGatewayMonthList> getGatewayMonthlist(long gatewayId,String date, String timeZone, boolean isFreePlan) {
		List<JGatewayMonthList> infoList = new ArrayList<JGatewayMonthList>();
		try {
			LocalDate userDate = LocalDate.parse(date);
			
			LocalDate startDate = userDate.minusDays(90);
			
			LocalDate endDate = userDate.minusDays(0);
			
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
			dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"+timeZone)); 
	        
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			 
			Map<String,JGatewayMonthList> monthMap = new LinkedHashMap<String,JGatewayMonthList>();
			
			Date currDate = _helper.timeZoneConverterfromutc( "yyyy-MM-dd HH:mm:ss","+00:00", timeZone, IrisservicesUtil.getCurrentTimeUTC());
			
			LocalDateTime curlocalDateTime = LocalDateTime.parse(dateFormat.format(currDate), formatter);
	        
	        LocalDate curuserDate1 = curlocalDateTime.toLocalDate();
			
			for (int i = 0; i < 90; i++) {
				JGatewayMonthList monthRpt = new JGatewayMonthList();
				LocalDate currentDate = userDate.minusDays(i);
				if(!isFreePlan) {
					 monthRpt.setShow_activate(false);
				}else if (currentDate.isAfter(curuserDate1.minusDays(30)) && isFreePlan) {
					 monthRpt.setShow_activate(false);
				}else {
					 monthRpt.setShow_activate(true);
				}
				
				monthRpt.setDate(currentDate.toString());
				monthRpt.setDay(currentDate.format(DateTimeFormatter.ofPattern("EEEE")).substring(0, 3));
				monthMap.put(currentDate.toString(), monthRpt);
			}
			
			String query1 = "SELECT COALESCE(PFD.req_calories, 0) AS req_calories, COALESCE(PF.calories, 0) AS calories, COALESCE(SUM(LGR.intake_weight), 0) AS total_intake_weight,LGR.pkttime_utc_datetime,COALESCE(PFD.req_grams, 0) AS req_grams "
            		+ "FROM sensorreport LGR LEFT JOIN pet_feed_details PFD ON PFD.gateway_id = LGR.gateway_id LEFT JOIN pet_food PF ON PF.id = LGR.petfood_id "
            		+ "WHERE LGR.gateway_id =:gatewayId AND (LGR.eventid LIKE '%800000' OR LGR.eventid LIKE '%200' OR LGR.eventid LIKE '%1000000' OR LGR.eventid = '111111') AND DATE(LGR.pkttime_utc_datetime) between :startDate AND :endDate GROUP BY LGR.petfood_id,DATE(LGR.pkttime_utc_datetime) ORDER BY LGR.pkttime_utc_datetime;";
           
			Query qry = slave4SessionFactory.getCurrentSession().createSQLQuery(query1);
            
			qry.setParameter("gatewayId", gatewayId);
			qry.setParameter("startDate", startDate.toString());
			qry.setParameter("endDate", endDate.toString());
			
   			List res = qry.list();
		        
	   			if (!res.isEmpty() && res.size() > 0) {
	   				
	   				for (int i = 0; i < res.size(); i++) {
					
						double caloriesIntake = 0;
						Object[] lastReport = (Object[]) res.get(i);
						
						JGatewayMonthList assetRpt = new JGatewayMonthList();			           
			            
			            SimpleDateFormat outputDateFormat = new SimpleDateFormat("EEEE, yyyy-MM-dd");

			            String formattedDate = outputDateFormat.format((Date) lastReport[3]);
			            
			            String[] dateStringList = formattedDate.split(",");
			            
			            if(monthMap.containsKey(dateStringList[1].trim())){
							BigDecimal bigDecimalValue = (BigDecimal) lastReport[2];
							float foodint = bigDecimalValue.floatValue();
							Double foodCal = ((Double) lastReport[1]);
							caloriesIntake = monthMap.get(dateStringList[1].trim()).getTotalCal() + (foodCal / 1000) * foodint;
							assetRpt.setWeight(monthMap.get(dateStringList[1].trim()).getWeight() + foodint);
							assetRpt.setTotalCal(caloriesIntake);
							assetRpt.setDate(dateStringList[1].trim());
							assetRpt.setDay(dateStringList[0].substring(0, 3));
							assetRpt.setShow_activate(monthMap.get(dateStringList[1].trim()).isShow_activate());
							assetRpt.setReqCalories((double) lastReport[0]);
				            assetRpt.setReqWeight((double) lastReport[4]);
				            
							monthMap.put(dateStringList[1].trim(), assetRpt);
						}
			            
			           
					}
				
	        }
	   			
	   		infoList = new ArrayList<JGatewayMonthList>(monthMap.values());	
	} catch (Exception e) {
		log.error("Error in getJWeeklycalories api :error : " + e.getLocalizedMessage());
	}
		return infoList;
	}
	
	
	@Override
	public JGatewayGeneralInfoV5 getGatewayGeneralInfoV5(long gatewayid,String date,String timeZone) {
		JGatewayGeneralInfoV5 jGeninfo = new JGatewayGeneralInfoV5();
		try {
			
		String query = null;
        
		if(date != null && !date.equals("")) {
		 query = "SELECT SUM(LGR.intake_weight),Max(LGR.pkttime_utc_datetime),IFNULL(PF.calories,0) FROM sensorreport LGR  JOIN  gateway G ON G.id = LGR.gateway_id "
				+ " JOIN assetmodel AM on G.model_id = AM.id"
				+ " LEFT JOIN pet_food PF ON PF.id = LGR.petfood_id"
				+ " WHERE AM.monitor_type_id = 3 AND LGR.gateway_id =:gatewayId AND DATE(LGR.pkttime_utc_datetime) =:date AND (LGR.eventid LIKE '%800000' OR LGR.eventid LIKE '%200' OR LGR.eventid LIKE '%1000000' OR LGR.eventid = '111111')  GROUP BY LGR.petfood_id ORDER BY LGR.id DESC";
		}
		
		
		Query qry = slave4SessionFactory.getCurrentSession().createSQLQuery(query);
		qry.setParameter("gatewayId", gatewayid);
		qry.setParameter("date", date);
		
		
		 String query1 = null;
		 if(date != null && !date.equals("")) {
			  query1 = "SELECT IFNULL(SI.last_weight,0),MAX(SI.date),IFNULL(PF.calories,0),IFNULL(SI.total_intake,0) FROM sensor_intake_report SI"
			  		+ " LEFT JOIN pet_food PF ON PF.id = SI.petfood_id"
			  		+ " WHERE SI.gateway_id =:gatewayId AND SI.date =:date GROUP BY SI.petfood_id ORDER BY SI.id ASC ";
		 }
		 
		Query qry1 = slave4SessionFactory.getCurrentSession().createSQLQuery(query1);
		qry1.setParameter("gatewayId", gatewayid);
		qry1.setParameter("date", date);
		 
		 List res = qry1.list();
		 if (!res.isEmpty() && res.size() > 0) {
			 
			 float totWeight = 0;
			 float totIntWeight = 0;
			 double equvalentCal = 0d;
			 for (int i = 0; i < res.size(); i++) {
				 Object[] row1 = (Object[]) res.get(i);
				 totIntWeight += ((BigInteger) row1[3]).floatValue();
				 totWeight += ((BigInteger) row1[0]).floatValue();
				 Double foodCal = (Double) row1[2] != null && (Double) row1[2] > 0 ? (Double) row1[2] : jGeninfo.getFoodCal();
				 float totCalVal = ((BigInteger) row1[0]).floatValue()+((BigInteger) row1[3]).floatValue();
				 equvalentCal += (foodCal / 1000) * (totCalVal);
				 
			 }
			jGeninfo.setInitialPortion(totWeight+totIntWeight);
			double equvalentkcal = (int)equvalentCal; 
			jGeninfo.setEquvalentCalory(equvalentkcal);
			jGeninfo.setCurrentWeight(totWeight+totIntWeight);
		 }
		 
		 
		 String query2 = "SELECT IFNULL(GR.weight,0) as 'curwgt',IFNULL(GR.battery,0),IFNULL(GR.rawrssi,0),PFD.req_calories,PF.calories,GR.updatedon as 'lastdate',"
		 		+ "PF.name as 'petname',G.name,PFD.req_grams FROM gateway G "
		 		+ " RIGHT JOIN pet_feed_details PFD ON PFD.gateway_id = G.id "
		 		+ " LEFT JOIN lastsensorreport GR ON G.id = GR.gateway_id"
		 		+ " JOIN pet_food PF ON PF.id = PFD.pet_food_id "
		 		+ " WHERE PFD.gateway_id =:gatewayId ;";
		 
		 Query qry2 = slave4SessionFactory.getCurrentSession().createSQLQuery(query2);
			qry2.setParameter("gatewayId", gatewayid);
			 
		 
		 List res2 = qry2.list();
		 
		int default_rpt = 0;
		String default_rpt_msg = "NA";
		String default_rpt_label = "NA";
		
		 DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
		 dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"+timeZone));
		 
		 
		 if (!res2.isEmpty() && res2.size() > 0) {
			 Object[] row = (Object[]) res2.get(0);
			 
			 SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
			 
			 SimpleDateFormat dateformatter = new SimpleDateFormat(IrisservicesConstants.DATEFORMAT);
			 
			 Date curr = formatter.parse(IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATETIMEFORMAT,"+00:00"));
				
			
			 if(row[5] != null) {
			 
			 if(dateformatter.format((Date) row[5]).equals(dateformatter.format(curr)) && date.equals(dateformatter.format(curr))) {
				 jGeninfo.setCurrentWeight(((BigInteger) row[0]).floatValue());
				 Date convertedDate = _helper.timeZoneConverterfromutc( "yyyy-MM-dd HH:mm:ss","+00:00", timeZone, ((Date) row[5]).toString());
				 jGeninfo.setLastUpdated(dateFormat.format(convertedDate));
			 }
			 }
			 jGeninfo.setFoodName((String) row[6]);
			 jGeninfo.setPetName((String) row[7]);
			 jGeninfo.setReqGrams((Double) row[8]);
			 jGeninfo.setBattery(((BigInteger) row[1]).intValue());
			 jGeninfo.setWifiRange(((BigInteger) row[2]).intValue());
			 double ActKcalVal = (Double) row[3];
			 double kcal = (int)ActKcalVal; 
			 jGeninfo.setActualCalories(kcal);
			 jGeninfo.setFoodCal((Double) row[4]);
			 
//			 if (row[9] != null) {
//				 jGeninfo.setPlanId(((BigInteger) row[9]).longValue());
//				}
//				if (row[10] == null) {
//					jGeninfo.setShow_activate(true);
//					jGeninfo.setIs_freePlan(false);
//				}else {
//					jGeninfo.setShow_activate(false);
//					jGeninfo.setIs_freePlan((boolean) row[10]);
//				}
//			 
			 float diffInHrs = 0;
				
				Date pre = formatter.parse(formatter.format((Date) row[5]).toString()); 

				diffInHrs = ((curr.getTime() - pre.getTime()) / 3600000);

				if (diffInHrs >= 3 && date.equals(dateformatter.format(curr))) {
					default_rpt = 1;
					default_rpt_msg = "No Internet connection found. \n Check your connection or try again";
					default_rpt_label = "Whoops!";
				}
				
				
		 }
		 
		
		 
		 List res1 = qry.list();
		 if (!res1.isEmpty() && res1.size() > 0) {
			double caloriesIntake = 0d;
			BigDecimal totIntak = new BigDecimal(0);
			for (int i = 0; i < res1.size(); i++) {
			Object[] rowS = (Object[]) res1.get(i);
			 	BigDecimal lastReport = (BigDecimal) rowS[0]; 
			 	Double foodCal = (Double) rowS[2] != null && (Double) rowS[2] > 0 ? (Double) rowS[2] : jGeninfo.getFoodCal();
		 
				
				totIntak = totIntak.add(lastReport);
				jGeninfo.setFoodCal(foodCal);
				if(jGeninfo.getFoodCal() != null && jGeninfo.getFoodCal()>0) {
					
					caloriesIntake += (jGeninfo.getFoodCal() / 1000) * lastReport.floatValue();
					
				}

				if(StringUtils.isEmpty(jGeninfo.getLastUpdated()) && (Date) rowS[1] != null) {
				  jGeninfo.setLastUpdated(((Date) rowS[1]).toString());
				}
				
			 }
			
			double consumedcal = (int)caloriesIntake;
			jGeninfo.setCaloriesConsumed(consumedcal);
			
			jGeninfo.setFoodIntake(totIntak != null ? (totIntak).floatValue():0);
		 }
		
		Double CalPer =  0d;
		CalPer =  (jGeninfo.getCaloriesConsumed() / jGeninfo.getActualCalories()) * 100;
		int PerVal = 1;
		if (CalPer >= 0 && CalPer <= 20) {
			PerVal = 1;
        } else if (CalPer > 20 && CalPer <= 40) {
        	PerVal = 2;
        }  else if (CalPer > 40 && CalPer <= 60) {
        	PerVal = 3;
        }  else if (CalPer > 60 && CalPer <= 80) {
        	PerVal = 4;
        }  else if (CalPer > 80 && CalPer <= 100) {
        	PerVal = 5;
        } else if ( CalPer > 100) {
        	PerVal = 5;
        }
		
		
		
		 jGeninfo.setDefault_report(default_rpt);
		 jGeninfo.setDefault_rpt_label(default_rpt_label);
		 jGeninfo.setDefault_rpt_msg(default_rpt_msg);
		 jGeninfo.setGatewayId(gatewayid);
		
		jGeninfo.setPerVal(PerVal);
		jGeninfo.setContent_1("Food Logging");
		jGeninfo.setContent_2("View Detailed Report");
		jGeninfo.setContent_3("Food Intake");
		jGeninfo.setContent_4("Calories Consumed");
		jGeninfo.setContent_5("Current Weight");
		jGeninfo.setContent_6("Last Updated");
		jGeninfo.setContent_7("Initial Portion");
		jGeninfo.setContent_8("Equivalent Calories");
		jGeninfo.setContent_9("grams");
		jGeninfo.setContent_10("kcal");
	} catch (Exception e) {
		//e.printStackTrace();
		log.error("Error in getGatewayGeneralInfo :error : " + e.getLocalizedMessage());
	}
		
		return jGeninfo;
	}
	
	@Override
	public Map<String,List<Object>> getLastGatewayReportV6( long userid, 
			 String tempunit, String country, String category,String os, String req_ver) {
		log.info("Entered ReportDaoImplV4 :: getLastGatewayReportV6 :: user_id : "+ userid+" :: category : "+ category);
		List<MonitorType> monitorTypelist = getAllmonitorType();
		Map<String,List<Object>> lastReport1 = new HashMap<String,List<Object>>();
		
		monitorTypelist.stream().filter(u->u.getCategory().equals(category)).forEach(category1 ->{
			//long gatewayId = 0L;//(assetid == null || assetid.isEmpty()) ? 0L : Long.parseLong( assetid );
			if(category1.getName().equalsIgnoreCase( IrisservicesConstants.WAGGLE )){
				List<Object[]> greports = this.getLastGatewayReportsV5( userid, category,category1.getName());
				List<Object> lastReport = new ArrayList<Object>();
				for (Object[] report : greports) {

					lastReport.add(convertGatewayLstRpttoAssetRptV5(report, tempunit, country,userid));
				}
				
				lastReport1.put(category1.getName(), lastReport);
			}else if( category1.getName().equalsIgnoreCase( IrisservicesConstants.WAGGLECAM ) ) {
				try {
					List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, 0, category1.getId() );
					
					lastReport1.put(category1.getName(), wcDeviceLists);
					
				} catch (Exception e) {
					log.error(e.getLocalizedMessage());
				}
				
				
			}else if(category1.getName().equalsIgnoreCase( IrisservicesConstants.SMARTBOWL )) {
				try {
					List<Object> smBowlLists = getPetBowlDeviceList( userid,os, 0);
					lastReport1.put(category1.getName(), smBowlLists);
					
					if(smBowlLists.size()<=0) {
						List<Object> smBowlPetCalLists = getPetBowlCaloriesList( userid,os, 0);
						
						lastReport1.put("PetCalories", smBowlPetCalLists);
					}
					
				} catch (Exception e) {
					log.error(e.getLocalizedMessage());
				}
			}else if(category1.getName().equalsIgnoreCase( IrisservicesConstants.MINICAM )) {
				
				if( req_ver.equalsIgnoreCase("V2") ) {
					List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, 0, category1.getId());
					lastReport1.put(category1.getName(), wcDeviceLists);
				} else {
					List<Object> minicams = userServiceV4.listUserMiniCam(userid);
					lastReport1.put(category1.getName(), minicams);
				}
				
				
			}else if( category1.getName().equalsIgnoreCase( IrisservicesConstants.WAGGLECAMPRO )) {
				List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, 0, category1.getId());
				
				lastReport1.put(category1.getName(), wcDeviceLists);
			}
			
			if((lastReport1.get("MiniCam")==null) && (category.equalsIgnoreCase("RvHub"))) 
				lastReport1.put("MiniCam", new ArrayList<Object>());
			
			if((lastReport1.get("WaggleCam")==null) && (category.equalsIgnoreCase("PetHub"))) 
				lastReport1.put("WaggleCam", new ArrayList<Object>());
			
			if((lastReport1.get("WaggleCamPro") == null) && (category.equalsIgnoreCase("PetHub"))) 
				lastReport1.put("WaggleCamPro", new ArrayList<Object>());
			
			if((lastReport1.get("SmartBowl")==null) && (category.equalsIgnoreCase("PetHub"))) {
				lastReport1.put("SmartBowl", new ArrayList<Object>());
				
			}
			
			if((lastReport1.get("PetCalories")==null) && (category.equalsIgnoreCase("PetHub"))) {
				lastReport1.put("PetCalories", new ArrayList<Object>());
				
			}
			
			if((lastReport1.get("PetMonitor")==null) && (category.equalsIgnoreCase("RvHub"))) 
				lastReport1.put("PetMonitor", new ArrayList<Object>());
			
			
		});
		//Map<String,List<Object>> jgatewayandrpt = getLstGatrptV5(userid,tempunit, country, category);
		return lastReport1;		
	}
	
	@Override
	public List<Object> getPetBowlCaloriesList(long userid,String os, long gatewayId) {
		log.info("Entered :: OptimizedDoaImpl :: getPetBowlCaloriesList:: ");

		List<Object> smDeviceList = new ArrayList<Object>();
		List<JAssetPetKcalReport> smDeviceList1 = new ArrayList<>();
		Query query = null;
		String qry = "SELECT "
				+ "IF (PFD.req_calories IS NULL,0, PFD.req_calories), "			
				+ "IF (PP.id IS NULL,0, PP.id) AS petId, "					
				+ "IF (PP.imageurl IS NULL,'NA', PP.imageurl),PP.weight,PP.speciesid,PP.structure,PP.intact,PP.activitylevel,"
				+ "M.product_link "							
				+ "FROM pet_profile PP "
				+ "LEFT JOIN monitortype M ON M.id=3 "
				+ "LEFT JOIN pet_feed_details PFD ON PFD.gateway_id = PP.gateway_id "
				+" WHERE PP.user_id = :userid AND PP.find_now ";

		try {
			if (gatewayId != 0) {
				qry += "AND PP.gateway_id=:gatewayid ";
				query = this.slave4SessionFactory.getCurrentSession()
						.createSQLQuery(qry);
				query.setParameter("userid", userid);
				query.setParameter("gatewayid", gatewayId);
			} else {
				query = this.slave4SessionFactory.getCurrentSession()
						.createSQLQuery(qry);
				query.setParameter("userid", userid);
			}
			qry += "ORDER BY PP.updated_on DESC LIMIT 1";


			List<Object[]> greports = query.list();

			if (greports.size() > 0) {

				for (Object[] result : greports) {

					JAssetPetKcalReport sbDevice = new JAssetPetKcalReport();

					double actualKcal = (Double) result[0];
					double kcal = (int)actualKcal; 
					if(kcal >0) {
						sbDevice.setActualCalories(kcal);
					}else {
						double MER = 0;

						double weight = Double.parseDouble( (String) result[3] );
						double weightPounds = weight * 0.453592;
						double RER = 70 * Math.pow(weightPounds, 0.75);
						BigInteger speciesId = (BigInteger) result[4]; 
						double signalment = petSpeciesServicesv4.getSignalment( speciesId.longValue() , (boolean) result[6]);		
						double BCS = petSpeciesServicesv4.getBCS( (String) result[5] != null && (String) result[5] != "" ? (String) result[5] : "ideal");
						double activityLevel = petSpeciesServicesv4.getActivityLevel( (String) result[7]);
						MER = RER * signalment * activityLevel * BCS;
						double kcalVal = (int)MER;
						sbDevice.setActualCalories(kcalVal);
						sbDevice.setProduct_link((String) result[8]);
					}
					sbDevice.setProfileId(((BigInteger) result[1]).longValue());
					sbDevice.setImgUrl((String) result[2]);
					sbDevice.setShop_url(shop_url);
					smDeviceList1.add(sbDevice);
				}
				smDeviceList.addAll(smDeviceList1);
				return smDeviceList;
			}
			
		} catch (Exception e) {
			log.error("Error on getPetBowlCaloriesList : "+e.getLocalizedMessage());
		}
		return null;
	}
	
	public JAssetPetKcalReport getPetBowlCaloriesListV2(long userid,String os, long gatewayId) {
		log.info("Entered :: OptimizedDoaImpl :: getPetBowlCaloriesList:: ");
		List<JAssetPetKcalReport> smDeviceList1 = new ArrayList<>();
		Query query = null;
		String qry = "SELECT "
				+ "IF (PFD.req_calories IS NULL,0, PFD.req_calories), "			
				+ "IF (PP.id IS NULL,0, PP.id) AS petId, "					
				+ "IF (PP.imageurl IS NULL,'NA', PP.imageurl),PP.weight,PP.speciesid,PP.structure,PP.intact,PP.activitylevel,"
				+ "M.product_link, "
				+ "PP.`name` "
				+ "FROM pet_profile PP "
				+ "LEFT JOIN monitortype M ON M.id=3 "
				+ "LEFT JOIN pet_feed_details PFD ON PFD.gateway_id = PP.gateway_id "
				+" WHERE PP.user_id = :userid AND PP.find_now ";

		try {
			if (gatewayId != 0) {
				qry += "AND PP.gateway_id=:gatewayid ";
				query = this.slave4SessionFactory.getCurrentSession()
						.createSQLQuery(qry);
				query.setParameter("userid", userid);
				query.setParameter("gatewayid", gatewayId);
			} else {
				query = this.slave4SessionFactory.getCurrentSession()
						.createSQLQuery(qry);
				query.setParameter("userid", userid);
			}
			qry += "ORDER BY PP.updated_on DESC LIMIT 1";


			List<Object[]> greports = query.list();

			if (greports.size() > 0) {

				for (Object[] result : greports) {

					JAssetPetKcalReport sbDevice = new JAssetPetKcalReport();

					double actualKcal = (Double) result[0];
					double kcal = (int)actualKcal; 
					if(kcal >0) {
						sbDevice.setActualCalories(kcal);
					}else {
						double MER = 0;

						double weight = Double.parseDouble( (String) result[3] );
						double weightPounds = weight * 0.453592;
						double RER = 70 * Math.pow(weightPounds, 0.75);
						BigInteger speciesId = (BigInteger) result[4]; 
						double signalment = petSpeciesServicesv4.getSignalment( speciesId.longValue() , (boolean) result[6]);		
						double BCS = petSpeciesServicesv4.getBCS( (String) result[5] != null && (String) result[5] != "" ? (String) result[5] : "ideal");
						double activityLevel = petSpeciesServicesv4.getActivityLevel( (String) result[7]);
						MER = RER * signalment * activityLevel * BCS;
						double kcalVal = (int)MER;
						sbDevice.setActualCalories(kcalVal);
						sbDevice.setProduct_link((String) result[8]);
						sbDevice.setProfileName((String) result[9]);
					}
					sbDevice.setProfileId(((BigInteger) result[1]).longValue());
					sbDevice.setImgUrl((String) result[2]);
					sbDevice.setShop_url(shop_url);
					smDeviceList1.add(sbDevice);
				}
				return smDeviceList1.get(0);
			}
			
		} catch (Exception e) {
			log.error("Error on getPetBowlCaloriesList : "+e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public List<DashboardBanner> getMarkettingBannerreport(String os, String app_ver, boolean checkPaidPlan) {
		log.info("Entered :: OptimizedDoaImpl :: getMarkettingBannerList:: ");
		try {
			List bannerDetails = this.slave4SessionFactory.getCurrentSession().createCriteria(DashboardBanner.class)
					.list();

			if (!bannerDetails.isEmpty()) {
				if( checkPaidPlan )
					bannerDetails.remove(0);
				return bannerDetails;
			}

		} catch (Exception e) {
			log.error("checkshipmentDetailCount " + e.getMessage());
		}
		return new ArrayList<>();
	}
	
	@Override
	public Map<String,Object> getLastGatewayReportV6(long gatewayid, long userid, long monitor_id, 
			String country, String os, String req_ver, JResponse response) {
		log.info("Entered ReportDaoImplV4 :: getLastGatewayReportV6 :: user_id : "+ userid+" :: gatewayid : "+ gatewayid);
		Map<String, Object> response1 = new HashMap<>();
		MonitorType monitorType = getmonitorTypeById(monitor_id);
		response1.put(IrisservicesConstants.SMARTBOWL, null);
		response1.put(IrisservicesConstants.MINICAM, null);
		response1.put(IrisservicesConstants.WAGGLECAMPRO, null);
		response1.put(IrisservicesConstants.RVSOLARCAM, null);
		response1.put(IrisservicesConstants.RVSOLARMINI, null);
		
		if(monitorType.getName().equalsIgnoreCase( IrisservicesConstants.SMARTBOWL )) {
			try {
				List<Object> smBowlLists = getPetBowlDeviceList( userid,os, gatewayid);
				response1.put(IrisservicesConstants.SMARTBOWL, smBowlLists.isEmpty() ? null : smBowlLists.get(0));

				if(smBowlLists.size()<=0) {
					List<Object> smBowlPetCalLists = getPetBowlCaloriesList( userid,os, gatewayid);

					response1.put(IrisservicesConstants.SMARTBOWL,  smBowlPetCalLists.isEmpty() ? null : 
						  smBowlPetCalLists.get(0));
				}
			} catch (Exception e) {
				log.error("Error in smart bowl : " + e.getLocalizedMessage());
			}
		} else if(monitorType.getName().equalsIgnoreCase( IrisservicesConstants.MINICAM )) {

			if( req_ver.equalsIgnoreCase("V2") ) {
				List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, gatewayid, monitorType.getId());
				if (wcDeviceLists.size() > 0)
					response1.put(IrisservicesConstants.MINICAM, wcDeviceLists.get(0));
			}
		} else if( monitorType.getName().equalsIgnoreCase( IrisservicesConstants.WAGGLECAMPRO )) {
			List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, gatewayid, monitorType.getId());
			if (wcDeviceLists.size() > 0)
				response1.put(IrisservicesConstants.WAGGLECAMPRO, wcDeviceLists.get(0));
		} else if( monitorType.getName().equalsIgnoreCase( IrisservicesConstants.RVSOLARCAM )) {
			List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, gatewayid, monitorType.getId());
			if (wcDeviceLists.size() > 0)
				response1.put(IrisservicesConstants.RVSOLARCAM, wcDeviceLists.get(0));
		} else if( monitorType.getName().equalsIgnoreCase( IrisservicesConstants.RVSOLARMINI )) {
			List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, gatewayid, monitorType.getId());
			if (wcDeviceLists.size() > 0)
				response1.put(IrisservicesConstants.RVSOLARMINI, wcDeviceLists.get(0));
		}
		return response1;
	}

	public MonitorType getmonitorTypeById(long monitor_id) {
		log.info("Entered into getmonitorTypeById");
		List<MonitorType> monitorTypeList = new ArrayList<>();
		try {
			String qry = " SELECT * FROM monitortype  WHERE enable=1 AND id=:id";
			Query query = slave4SessionFactory.getCurrentSession().createSQLQuery(qry).addEntity(MonitorType.class);
			query.setParameter("id", monitor_id);
			monitorTypeList =  query.list();

			if( monitorTypeList.isEmpty() ) 
				log.info("Monitortype is empty");

			return monitorTypeList.get(0);
		} catch (Exception e) {
			log.error("Error in getmonitortype :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}
	
	@Override
	public Map<String,List<Object>> getLastGatewayReportV7( long userid, 
			 String tempunit, String country, String category,String os, String req_ver) {
		log.info("Entered ReportDaoImplV4 :: getLastGatewayReportV6 :: user_id : "+ userid+" :: category : "+ category);
		List<MonitorType> monitorTypelist = getAllmonitorType();
		Map<String,List<Object>> lastReport1 = new HashMap<String,List<Object>>();
		boolean hasWcUltra = false;
		monitorTypelist.stream().forEach(category1 ->{
			//long gatewayId = 0L;//(assetid == null || assetid.isEmpty()) ? 0L : Long.parseLong( assetid );
			if(category1.getName().equalsIgnoreCase( IrisservicesConstants.WAGGLE )){
				List<Object[]> greports = this.getLastGatewayReportsV5( userid, category,category1.getName());
				List<Object> lastReport = new ArrayList<Object>();
				for (Object[] report : greports) {
					
					lastReport.add(convertGatewayLstRpttoAssetRptV5(report, tempunit, country,userid));
				}
				
				lastReport1.put(category1.getName(), lastReport);
			}else if( category1.getName().equalsIgnoreCase( IrisservicesConstants.WAGGLECAM ) ) {
				try {
					List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, 0, category1.getId() );					
					lastReport1.put(category1.getName(), wcDeviceLists);
					
				} catch (Exception e) {
					log.error(e.getLocalizedMessage());
				}
				
				
			}else if(category1.getName().equalsIgnoreCase( IrisservicesConstants.SMARTBOWL )) {
				try {
					List<Object> smBowlLists = getPetBowlDeviceList( userid,os, 0);
					lastReport1.put(category1.getName(), smBowlLists);
					
					if(smBowlLists.size()<=0) {
						List<Object> smBowlPetCalLists = getPetBowlCaloriesList( userid,os, 0);
						
						lastReport1.put("PetCalories", smBowlPetCalLists);
					}
					
				} catch (Exception e) {
					log.error(e.getLocalizedMessage());
				}
			}else if(category1.getName().equalsIgnoreCase( IrisservicesConstants.MINICAM )) {
				
				if( req_ver.equalsIgnoreCase("V2") ) {
					List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, 0, category1.getId());
					lastReport1.put(category1.getName(), wcDeviceLists);
				} else {
					List<Object> minicams = userServiceV4.listUserMiniCam(userid);
					lastReport1.put(category1.getName(), minicams);
				}
				
				
			}else if( category1.getName().equalsIgnoreCase( IrisservicesConstants.WAGGLECAMPRO )) {
				List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, 0, category1.getId());
				
				lastReport1.put(category1.getName(), wcDeviceLists);
			}else if( category1.getName().equalsIgnoreCase( IrisservicesConstants.NODESENDOR )) {
				List<Object> nodeSensorLists = gatewayServiceV4.getSensorList( userid, 0, category1.getId(), "v4.0");
				
				lastReport1.put(category1.getName(), nodeSensorLists);
			}
			
			if((lastReport1.get("MiniCam")==null) && (category.equalsIgnoreCase("RvHub"))) 
				lastReport1.put("MiniCam", new ArrayList<Object>());
			
			if((lastReport1.get("WaggleCam")==null) && (category.equalsIgnoreCase("RvHub"))) 
				lastReport1.put("WaggleCam", new ArrayList<Object>());
			
			if((lastReport1.get("WaggleCamPro") == null) && (category.equalsIgnoreCase("RvHub"))) 
				lastReport1.put("WaggleCamPro", new ArrayList<Object>());
			
			if((lastReport1.get("SmartBowl")==null) && (category.equalsIgnoreCase("RvHub"))) {
				lastReport1.put("SmartBowl", new ArrayList<Object>());
				
			}
			
			if((lastReport1.get("PetCalories")==null) && (category.equalsIgnoreCase("RvHub"))) {
				lastReport1.put("PetCalories", new ArrayList<Object>());
				
			}
			
			if((lastReport1.get("PetMonitor")==null) && (category.equalsIgnoreCase("RvHub"))) 
				lastReport1.put("PetMonitor", new ArrayList<Object>());
			
			if((lastReport1.get("NodeSensor")==null) && (category.equalsIgnoreCase("RvHub"))) 
				lastReport1.put("NodeSensor", new ArrayList<Object>());
			
			
		});
		//Map<String,List<Object>> jgatewayandrpt = getLstGatrptV5(userid,tempunit, country, category);
		return lastReport1;		
	}
	
	@Override
	public Map<String,Object> getLastGatewayReportV8( long userid, 
			 String tempunit, String country, String category,String os, String req_ver) {
		log.info("Entered ReportDaoImplV4 :: getLastGatewayReportV6 :: user_id : "+ userid+" :: category : "+ category);
		List<MonitorType> monitorTypelist = getAllmonitorType();
		Map<String,Object> lastReport1 = new HashMap<String,Object>();
		boolean hasWcUltra = false;
		monitorTypelist.stream().forEach(category1 ->{
			//long gatewayId = 0L;//(assetid == null || assetid.isEmpty()) ? 0L : Long.parseLong( assetid );
			if(category1.getName().equalsIgnoreCase( IrisservicesConstants.WAGGLE )){
				List<Object[]> greports = this.getLastGatewayReportsV5( userid, category,category1.getName());
				List<Object> lastReport = new ArrayList<Object>();
				for (Object[] report : greports) {
					
					lastReport.add(convertGatewayLstRpttoAssetRptV5(report, tempunit, country,userid));
				}
				
				lastReport1.put(category1.getName(), lastReport);
			}else if( category1.getName().equalsIgnoreCase( IrisservicesConstants.WAGGLECAM ) ) {
				try {
					List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, 0, category1.getId() );
					for (Object obj : wcDeviceLists) {
						WCDeviceList wcDevice = (WCDeviceList) obj;
						if (wcDevice.isTemperature_model()) {
							if (!lastReport1.containsKey("wcultra")) {
								lastReport1.put("wcultra", new ArrayList<>());
							}
						}
					}
					
					lastReport1.put(category1.getName(), wcDeviceLists);
					
				} catch (Exception e) {
					log.error(e.getLocalizedMessage());
				}
				
				
			}else if(category1.getName().equalsIgnoreCase( IrisservicesConstants.SMARTBOWL )) {
				try {
					List<Object> smBowlLists = getPetBowlDeviceList( userid,os, 0);
					lastReport1.put(category1.getName(), smBowlLists);
					
					if(smBowlLists.size()<=0) {
						JAssetPetKcalReport smBowlPetCalLists = getPetBowlCaloriesListV2( userid,os, 0);
						if (smBowlPetCalLists != null) {
							lastReport1.put("PetCalories", smBowlPetCalLists);
						}
					}
					
				} catch (Exception e) {
					log.error(e.getLocalizedMessage());
				}
			}else if(category1.getName().equalsIgnoreCase( IrisservicesConstants.MINICAM )) {
				
				if( req_ver.equalsIgnoreCase("V2") ) {
					List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, 0, category1.getId());
					lastReport1.put(category1.getName(), wcDeviceLists);
				} else {
					List<Object> minicams = userServiceV4.listUserMiniCam(userid);
					lastReport1.put(category1.getName(), minicams);
				}
				
				
			}else if( category1.getName().equalsIgnoreCase( IrisservicesConstants.WAGGLECAMPRO )) {
				List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, 0, category1.getId());
				
				lastReport1.put(category1.getName(), wcDeviceLists);
			}else if( category1.getName().equalsIgnoreCase( IrisservicesConstants.NODESENDOR )) {
				List<Object> nodeSensorLists = gatewayServiceV4.getSensorList( userid, 0, category1.getId(), "v5.0");
				
				lastReport1.put(category1.getName(), nodeSensorLists);
			}else if( category1.getName().equalsIgnoreCase( IrisservicesConstants.RVSOLARCAM )) {
				List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, 0, category1.getId());
				
				lastReport1.put(category1.getName(), wcDeviceLists);
			}else if( category1.getName().equalsIgnoreCase( IrisservicesConstants.RVSOLARMINI )) {
				List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, 0, category1.getId());

				lastReport1.put(category1.getName(), wcDeviceLists);
			}
			
			if((lastReport1.get("MiniCam")==null) && (category.equalsIgnoreCase("RvHub"))) 
				lastReport1.put("MiniCam", new ArrayList<Object>());
			
			if((lastReport1.get("WaggleCam")==null) && (category.equalsIgnoreCase("RvHub"))) 
				lastReport1.put("WaggleCam", new ArrayList<Object>());
			
			if((lastReport1.get("WaggleCamPro") == null) && (category.equalsIgnoreCase("RvHub"))) 
				lastReport1.put("WaggleCamPro", new ArrayList<Object>());
			
			if((lastReport1.get("SmartBowl")==null) && (category.equalsIgnoreCase("RvHub"))) {
				lastReport1.put("SmartBowl", new ArrayList<Object>());
				
			}
			
			if((lastReport1.get("PetCalories")==null) && (category.equalsIgnoreCase("RvHub"))) {
				lastReport1.put("PetCalories", null);
				
			}
			
			if((lastReport1.get("PetMonitor")==null) && (category.equalsIgnoreCase("RvHub"))) 
				lastReport1.put("PetMonitor", new ArrayList<Object>());
			
			if((lastReport1.get("NodeSensor")==null) && (category.equalsIgnoreCase("RvHub"))) 
				lastReport1.put("NodeSensor", new ArrayList<Object>());
			
			if((lastReport1.get("RVSolarCam")==null) && (category.equalsIgnoreCase("RvHub"))) 
				lastReport1.put("RVSolarCam", new ArrayList<Object>());

			if((lastReport1.get("RVSolarMiniCam")==null) && (category.equalsIgnoreCase("RvHub")))
				lastReport1.put("RVSolarMiniCam", new ArrayList<Object>());
			
			
		});
		return lastReport1;		
	}
	
	public HashMap<String, String> getNSInfo(long gatewayid) {
		log.info("Entered getNSInfo :: DAO");
		HashMap<String, String> info = new HashMap<String, String>();
		String qry = "SELECT `name`,sensortype FROM gateway g JOIN `sensortype` s ON g.sensor_type_id = s.id WHERE g.id ="+gatewayid+";";

		Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);
		List res = query.list();
		if (!res.isEmpty()) {
			Object[] tuple = (Object[]) res.get(0);
			info.put("gname", (String) tuple[0]);
			info.put("stype", (String) tuple[1]);

		}
		return info;
	}
	
	public List<JPetmonitorHistory> getPetmonitorGrpRep(long gatewayid, String from, String endDate, String timeZone, String tempunit) {
			log.info("Entered ReportDaoImplV4 ::  ");
			List<JPetmonitorHistory> greports = new ArrayList<JPetmonitorHistory>();
			try {
				String formdate = from + " 00:00:00";
				String todate = endDate + " 23:59:59";
				String qry = "SELECT CONVERT_TZ(s.pkttime_utc,'+00:00','"+timeZone+"') AS 'datetime',s.humidity,s.temperature "
			             + "FROM gatewayreport s "
			             + "WHERE s.gateway_id = '"+gatewayid+"' "
			             + "AND s.pkttime_utc BETWEEN CONVERT_TZ('"+formdate+"','"+timeZone+"','+00:00') AND CONVERT_TZ('" + todate + "','"+timeZone+"','+00:00') ORDER BY s.`pkttime_utc` DESC;";


				if (gatewayid>0) {
					Query query = this.slave4SessionFactory.getCurrentSession()
							.createSQLQuery(qry);
					
					List<Object[]> gname_list = query.list();
					DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					DecimalFormat df = new DecimalFormat("0.00");
					
					for (Object[] object : gname_list) {
						
						JPetmonitorHistory sensCode = new JPetmonitorHistory();
						sensCode.setDatetime(dateFormat.format((Date) object[0]));
						float tempval = (float) object[2];
						if (tempunit.equalsIgnoreCase("F") && tempval > -200)
							tempval = CelsiusToFahrenheit(tempval);
						tempval = Float.parseFloat(df.format(tempval));
						float humidity = Float.parseFloat(df.format((float) object[1]));
						
						sensCode.setHumidity(humidity);
						sensCode.setTemperature(tempval);

						greports.add(sensCode);
						
					}	
					
				}
			}catch (Exception e) {
				log.info("exportSensorReports:"+ e.getLocalizedMessage());
			}
			return greports;
		
	}
	
	public JResponse getLastGatewayReportV8(long gatewayid, long userid, long monitor_id, String country, String os,
			String req_ver, JResponse response) {
		log.info("Entered ReportDaoImplV4 :: getLastGatewayReportV6 :: user_id : "+ userid+" :: gatewayid : "+ gatewayid);
		MonitorType monitorType = getmonitorTypeById(monitor_id);
		response.put(IrisservicesConstants.SMARTBOWL, null);
		response.put(IrisservicesConstants.MINICAM, null);
		response.put(IrisservicesConstants.WAGGLECAMPRO, null);

		if(monitorType.getName().equalsIgnoreCase( IrisservicesConstants.SMARTBOWL )) {
			try {
				List<Object> smBowlLists = getPetBowlDeviceList( userid,os, gatewayid);
				response.put(IrisservicesConstants.SMARTBOWL, smBowlLists.isEmpty() ? null : smBowlLists.get(0));

				if(smBowlLists.size()<=0) {
					List<Object> smBowlPetCalLists = getPetBowlCaloriesList( userid,os, gatewayid);

					response.put(IrisservicesConstants.SMARTBOWL, smBowlPetCalLists.isEmpty() ? null : 
						smBowlPetCalLists.get(0));
				}
			} catch (Exception e) {
				log.error("Error in smart bowl : " + e.getLocalizedMessage());
			}
		} else if(monitorType.getName().equalsIgnoreCase( IrisservicesConstants.MINICAM )) {

			if( req_ver.equalsIgnoreCase("V2") ) {
				List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, gatewayid, monitorType.getId());
				if (wcDeviceLists.size() > 0)
					response.put(IrisservicesConstants.MINICAM, wcDeviceLists.get(0));
			}
		} else if( monitorType.getName().equalsIgnoreCase( IrisservicesConstants.WAGGLECAMPRO )) {
			List<Object> wcDeviceLists = gatewayServiceV4.getWCDeviceList( userid, gatewayid, monitorType.getId());
			if (wcDeviceLists.size() > 0)
				response.put(IrisservicesConstants.WAGGLECAMPRO, wcDeviceLists.get(0));
		}
		return response;
	}

	@Override
	public List<JGatewaySubSetup> getProductSubscriptions(UserV4 user) {
		List<JGatewaySubSetup> setupList = new ArrayList<JGatewaySubSetup>();
		try {
			String status = "ACTIVE";
			int daysBetween = -1;
			int days_remaining = -1;
			String nextPaymentDate = "NA";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date dateobj = new Date();

			LinkedList<JGateway> gatewayList = gatewayServiceV4.getGatewaysByInstalledDate(user.getId());
			if (!gatewayList.isEmpty()) {
				for (JGateway jGateway : gatewayList) {
					int remaindays = -1;
					String showNextRenewal_withContent = "";
					List<AllProductSubscription> allSubscription = cbService.getproductSubscriptions(user.getChargebeeid(), jGateway.getId());
					if(allSubscription != null) {
						AllProductSubscription allProductSubscription = (AllProductSubscription) allSubscription.get(0);
						status = allProductSubscription.getSubscriptionStatus().toUpperCase();

						if ((status.equalsIgnoreCase("ACTIVE")) || (status.equalsIgnoreCase("IN_TRIAL"))) {
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));

							Date nextPaymentDate1 = sdf.parse(allProductSubscription.getNextBillingAt());
							Date todayDate = sdf.parse(sdf.format(dateobj));

							long difference = nextPaymentDate1.getTime() - todayDate.getTime();
							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
							days_remaining = daysBetween;

							sdf = new SimpleDateFormat("yyyy-MM-dd");

							if (freeplan.contains(allProductSubscription.getPlanId())) {
								days_remaining = -1;
							}

						} else if (status.equalsIgnoreCase("NON_RENEWING")) {
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							nextPaymentDate = sdf.format(sdf.parse(allProductSubscription.getSubscriptionCancelledAt()).getTime());
							Date cancelledAt = sdf.parse(nextPaymentDate);
							Date todayDate = sdf.parse(sdf.format(dateobj));

							long difference = cancelledAt.getTime() - todayDate.getTime();
							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
							days_remaining = daysBetween;
						}

						if (daysBetween < 0)
							days_remaining = -1;

                        remaindays = days_remaining;

						if((remaindays < days_tohandle_nextrenewal) && (remaindays > 0) && show_nextrenewal_popup) {
							showNextRenewal_withContent = "Subscription renews in " + remaindays + " days";
						}

						JGatewaySubSetup setup = new JGatewaySubSetup(jGateway.getId(), false, remaindays, jGateway.getMeid(), showNextRenewal_withContent);
						setupList.add(setup);
					} else {
						JGatewaySubSetup setup = new JGatewaySubSetup(jGateway.getId(), true, -1, jGateway.getMeid(), "");
						setupList.add(setup);
					}
				}
			}
		} catch (Exception e) {
			log.error("Error getProductSubscriptions : " + e.getMessage());
		}
		return setupList;
	}

	private String getAirQualityInfo(int aqi) {

		if (aqi == 1) {
			return "Excellent";
		} else if (aqi == 2) {
			return "Good";
		} else if (aqi == 3) {
			return "Fair";
		} else if (aqi == 4) {
			return "Poor";
		} else {
			return "Bad";
		}
	}

	private int getEnvironmentScorePercentage(int temp, int humidity, int aqi) {

		int tempScore = getTempScore(temp);
		int aqiScore = getAQIScore(aqi);
		int humidityScore = getHumidityScore(humidity);

		return (tempScore + humidityScore + aqiScore) / 3;
	}

	private int getTempScore(int actualTemperatureF) {

		int idealTemperatureF = 75;
		int difference = Math.abs(actualTemperatureF - idealTemperatureF);
		int percentageDifference = (int) Math.round(((double) difference / idealTemperatureF) * 100);
		int score = 100 - percentageDifference;

		return Math.max(0, score);
	}


	private int getAQIScore(int aqi) {

		if (aqi == 1) {
			return 100;
		} else if (aqi == 2) {
			return 80;
		} else if (aqi == 3) {
			return 60;
		} else if (aqi == 4) {
			return 40;
		} else {
			return 20;
		}
	}

	private int getEnvironmentScore(int percentage) {

		if(percentage >= 90 && percentage <= 100) {
			return 1;
		} else if(percentage >= 75 && percentage < 90) {
			return 2;
		} else if(percentage >= 50 && percentage < 75) {
			return 3;
		} else if(percentage >= 30 && percentage < 50) {
			return 4;
		} else {
			return 5;
		}
	}

	private String getEnvironmentScoreDescription(int environmentScore) {

		if(environmentScore == 1) {
			return "Optimal environment. Safe and highly comfortable for pets.";
		} else if(environmentScore == 2) {
			return "Minor deviations detected. Mostly safe with minimal discomfort.";
		} else if(environmentScore == 3) {
			return "Noticeable deviations. May cause mild stress or discomfort.";
		} else if(environmentScore == 4) {
			return "Unsafe conditions. Immediate attention required.";
		} else {
			return "Highly unsafe. Severe risk to pet's health and safety.";
		}
	}

	private int getHumidityScore(int actualHumidity) {

		int idealHumidity = 50;
		int difference = Math.abs(actualHumidity - idealHumidity);
		int percentageDifference = (int) Math.round(((double) difference / idealHumidity) * 100);
		int score = 100 - percentageDifference;

		return Math.max(0, score);
	}


	public boolean isSubscribedProductFromDB(String chargebeeId){
		log.info("Entered :: OptimizedDoaImpl :: isSubscribedProductFromDB:: ");


		String query1 = null;

		query1 = "SELECT * FROM all_product_subscription WHERE  subscription_status='active' AND chargebee_id = :chargebeeId";

		Query qry1 = slave4SessionFactory.getCurrentSession().createSQLQuery(query1).setParameter("chargebeeId",chargebeeId);




		try{
		List res = qry1.list();
		if (res.size() != 0) {
			return true;
		}

		} catch (Exception e) {
			log.error("Error isSubscribedProductFromDB : " + e.getMessage());
		}
		return false ;
	}
}
