package com.nimble.irisservices.dao.impl;

import java.io.File;
import java.io.IOException;
import java.nio.file.DirectoryNotEmptyException;
import java.nio.file.Files;
import java.nio.file.NoSuchFileException;
import java.nio.file.Paths;
import java.util.Properties;

import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.InternetHeaders;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import com.nimble.irisservices.Util.SecretManagerService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.nimble.irisservices.controller.WaggleEmailController;
import com.nimble.irisservices.dao.IWaggleEmailDao;
import com.nimble.irisservices.dto.EmailData;

@Repository
public class WaggleEmailDaoImpl implements IWaggleEmailDao{

	@Value("${email_host}")
	private String EMAIL_HOST;	

	@Value("${aws_ses_secret_name}")
	private String SES_SECRET_NAME;

	private String EMAIL_HOST_USER;

	private String EMAIL_HOST_PASSWORD;

	@Autowired
	private SecretManagerService secretManagerService;

	@Autowired
	@Lazy
	private SessionFactory sessionFactory;

	private static final Logger log = LogManager.getLogger(WaggleEmailController.class);


	public boolean sendEmailFromWaggle(EmailData emailData) {
		log.info("Entered to sendEmailFromWaggle Dao Impl : to : "+emailData.getToaddress());
		Transport transport = null;
		try {
			Properties props = new Properties();
			props.put("mail.transport.protocol", "smtps");
			props.put("mail.smtp.auth", "true");
			props.setProperty("mail.smtp.port", "587");	           
			props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");
			props.put("mail.debug",true);

			Session session = Session.getDefaultInstance(props);
			MimeMessage msg = new MimeMessage(session);
			msg.setFrom(new InternetAddress(emailData.getFromaddress()));
			EMAIL_HOST_USER = secretManagerService.getSecretValue(SES_SECRET_NAME, "aws_access_key");
			EMAIL_HOST_PASSWORD = secretManagerService.getSecretValue(SES_SECRET_NAME, "aws_secret_key");

			if (emailData.getToaddress() != null) {
				msg.setRecipients(Message.RecipientType.TO, emailData.getToaddress());
			}			
			if (emailData.getCcaddress() != null) {
				msg.setRecipients(Message.RecipientType.CC, emailData.getCcaddress());
			}
			if (emailData.getBccaddress() != null) {
				msg.setRecipients(Message.RecipientType.BCC, emailData.getBccaddress());
			}
			msg.setSubject(emailData.getSubject());

			InternetHeaders headers = new InternetHeaders();
			headers.addHeader("Content-type", "text/html; charset=UTF-8");

			MimeBodyPart body = new MimeBodyPart(headers, emailData.getMailbody().getBytes("UTF-8"));

			Multipart multipart = new MimeMultipart();
			multipart.addBodyPart(body);

			for(MultipartFile mulPartFile : emailData.getAttachments()) {
				log.info("Adding attachments...");
				MimeBodyPart messageBodyPart2 = new MimeBodyPart();  
				String filename = StringUtils.cleanPath(mulPartFile.getOriginalFilename());
				File convFile = new File(System.getProperty("java.io.tmpdir")+"/"+filename);
				mulPartFile.transferTo(convFile);
				messageBodyPart2.attachFile(convFile);
				multipart.addBodyPart(messageBodyPart2);
			}
			msg.setContent(multipart);
			transport = session.getTransport();
			log.info("sendEmailFromWaggle: Attempting to send an email through the Amazon SES SMTP interface... To : "+emailData.getToaddress());
			transport.connect(EMAIL_HOST, EMAIL_HOST_USER, EMAIL_HOST_PASSWORD);
			transport.sendMessage(msg, msg.getAllRecipients());
			log.info("Email sent!");
			return true;
		} catch (AddressException ex) {
			log.error("Address Exception occured :" + ex.getMessage());
		} catch (MessagingException ex) {
			log.error("Message Exception occured :" + ex.getMessage());			
		} catch (Exception ex) {
			log.error("SendEmail Exception: " + ex.getMessage());
		}

		finally {
			for(MultipartFile mulPartFile : emailData.getAttachments()) {
				try{
					Files.deleteIfExists(Paths.get(System.getProperty("java.io.tmpdir")+"/"+mulPartFile.getOriginalFilename()));
				}catch(NoSuchFileException e){
					log.error("No such file/directory exists");
				}catch(DirectoryNotEmptyException e){
					log.error("Directory is not empty.");
				}catch(IOException e){
					log.error("Invalid permissions.");
				}
				log.info("Local file Deletion successful.");
			}
			try {
				if (null != transport)
					transport.close();
			} catch (Exception e) {
				log.error("SendEmail Exception: " + e.getMessage());
			}
		}
		return false;	
	}
}
