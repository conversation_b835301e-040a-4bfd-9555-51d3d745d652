package com.nimble.irisservices.dao.impl;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;

import com.nimble.irisservices.dto.ComboPlanInfo;
import com.nimble.irisservices.dto.SubscriptionWithoutDevice;
import com.nimble.irisservices.entity.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.LogicalExpression;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dao.IChargebeeDao;
import com.nimble.irisservices.dto.JCancelSubDetail;
import com.nimble.irisservices.dto.JPlan;
import com.nimble.irisservices.entity.AllProductSubscription;
import com.nimble.irisservices.entity.AllSubscription;
import com.nimble.irisservices.entity.CancelsubHistory;
import com.nimble.irisservices.entity.CbCurrentPlanDetails;
import com.nimble.irisservices.entity.Credits;
import com.nimble.irisservices.entity.ProductSubscription;
import com.nimble.irisservices.entity.UnpaidInvoices;
import com.nimble.irisservices.helper.Helper;

@Repository
public class ChargebeeDaoImpl implements IChargebeeDao {

	private static final Logger log = LogManager.getLogger(ChargebeeDaoImpl.class);

	@Autowired
	private SessionFactory sessionFactory;

	@Override
	public List<AllSubscription> getSubscriptionByChargebeeId(String chargebeeid) {
		log.info(" Entered into getSubscriptionByChargebeeId :: chargebeeId : " + chargebeeid);
		try {

			Session session = sessionFactory.getCurrentSession();

			Criteria criteria = session.createCriteria(AllSubscription.class);

			Criterion chargebeeId = Restrictions.eq("customerId", chargebeeid);
			Criterion statusActive = Restrictions.ilike("subscriptionStatus", "active");
			Criterion statusNonRenewing = Restrictions.ilike("subscriptionStatus", "non_renewing");
			Criterion statusInTrial = Restrictions.ilike("subscriptionStatus", "in_trial");
			Criterion statusPaused = Restrictions.ilike("subscriptionStatus", "paused");
			Criterion statusDeleted = Restrictions.eq("isDeleted", 0);

			LogicalExpression status = Restrictions.or(statusNonRenewing, statusInTrial);
			status = Restrictions.or(statusActive, status);
			
			LogicalExpression newStatus = Restrictions.or(status, statusPaused);

			LogicalExpression id = Restrictions.and(chargebeeId, newStatus);
			
			LogicalExpression dbid = Restrictions.and(id, statusDeleted);

			criteria.addOrder(Order.desc("updatedDate"));
			criteria.add(dbid);

			List<AllSubscription> allSubscription = (List<AllSubscription>) criteria.list();

			if (allSubscription.isEmpty()) {
				return null;
			}

			return allSubscription;

		} catch (Exception e) {
			log.error(" Error in getSubscriptionByChargebeeId : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public JPlan getPlanDesc(String planid) {
		try {
			String qry = "SELECT chargebee_planid,period_name,plan_name,plan_to_period.id FROM plan_to_period "
					+ "INNER JOIN sub_period ON plan_to_period.sub_period_id = sub_period.id AND chargebee_planid=:cbplan "
					+ "INNER JOIN plan ON plan_to_period.plan_id = plan.id";
			JPlan jPlan = new JPlan();
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("cbplan", planid);
			List res = query.list();
			if (!res.isEmpty()) {
				for (int i = 0; i < res.size(); i++) {
					Object[] checklistObj = (Object[]) res.get(i);
					jPlan.setPeriodUnit(checklistObj[1].toString());
					jPlan.setPlanName(checklistObj[2].toString());
					jPlan.setPlanToPeriodId( ((BigInteger) checklistObj[3]).intValue() );
				}
			}
			return jPlan;
		} catch (Exception e) {
			log.error("getPlanDesc: " + e.getLocalizedMessage());
			return null;
		}

	}

	@Override
	public Credits getChargebeeUserCredits(String chargebeeId) {
		log.info("Entered in getChargebeeUserCredits :: chargebeeId : " + chargebeeId);
		try {

			Session session = sessionFactory.getCurrentSession();
			Criteria criteria = session.createCriteria(Credits.class).add(Restrictions.eq("chargebee_id", chargebeeId));
			Credits credits = (Credits) criteria.list().get(0);
			return credits;

		} catch (Exception e) {
			return null;
		}

	}

	@Override
	public ProductSubscription getProductSubscription(long user_id) {
		log.info("Entered into getProductSubscription :: user_id : " + user_id);
		try {
			Session session = sessionFactory.getCurrentSession();
			Criteria criteria = session.createCriteria(ProductSubscription.class)
					.add(Restrictions.eq("user_id", user_id)).add(Restrictions.eq("is_subscription_activated", false))
					.add(Restrictions.eq("enable", true)).addOrder(Order.desc("updatedon"));
			List<ProductSubscription> productSubscriptionList = (List<ProductSubscription>) criteria.list();
			if (productSubscriptionList.isEmpty()) {
				log.info("No ProductSubscription found for user_id : "+ user_id);
				return null;
			}
				
			return productSubscriptionList.get(0);
		} catch (Exception e) {
			log.error("Error in getProductSubscription :: error : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public ProductSubscription getProductSubscriptionByOrderId(long order_id) {
		log.info("Entered into getProductSubscriptionByOrderId :: order_id : " + order_id);
		try {
			Session session = sessionFactory.getCurrentSession();
			Criteria criteria = session.createCriteria(ProductSubscription.class)
					.add(Restrictions.eq("order_id", order_id)).add(Restrictions.eq("is_subscription_activated", false))
					.add(Restrictions.eq("enable", true)).addOrder(Order.desc("updatedon"));
			List<ProductSubscription> productSubscriptionList = (List<ProductSubscription>) criteria.list();
			if (productSubscriptionList.isEmpty()) {
				log.info("No ProductSubscription found for order_id : "+ order_id);
				return null;
			}
				
			return productSubscriptionList.get(0);
		} catch (Exception e) {
			log.error("Error in getProductSubscriptionByOrderId :: error : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public List<UnpaidInvoices> getUnpaidInvoice(String type) {
		log.info("Entered in getUnpaidInvoice ::");
		List<UnpaidInvoices> upInvoiceList = new ArrayList<UnpaidInvoices>();

		try {
			String qry = "SELECT UI.id,UI.invoice_id,UI.due_date,UI.unpaid_date,UI.paid_date,UI.planid,UI.subscription_id,UI.chargebee_id,UI.invoicestatus,UI.status,"
					+ " UI.paidstatus,UI.billing_email,UI.updated_date,U.firstname,U.mobileno FROM unpaid_invoices UI"
					+ " INNER JOIN user U ON U.chargebeeid = UI.chargebee_id where UI.invoicestatus =:invoicestatus ORDER BY UI.updated_date DESC ";

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("invoicestatus", type);
			List res = query.list();
			if (!res.isEmpty()) {
				for (int i = 0; i < res.size(); i++) {
					UnpaidInvoices unInvoice = new UnpaidInvoices();
					Object[] checklistObj = (Object[]) res.get(i);
					unInvoice.setId(((Integer) checklistObj[0]).intValue());
					unInvoice.setInvoice_id(checklistObj[1].toString());
					unInvoice.setDue_date(((Date) checklistObj[2]).toString());
					unInvoice.setUnpaid_date(((Date) checklistObj[3]).toString());
					unInvoice.setPaid_date(((Date) checklistObj[4]).toString());
					unInvoice.setPlanid(checklistObj[5].toString());
					unInvoice.setSub_id(checklistObj[6].toString());
					unInvoice.setChargebeeId(checklistObj[7].toString());
					unInvoice.setInvoicestatus(checklistObj[8].toString());
					unInvoice.setStatus(checklistObj[9].toString());
					unInvoice.setPaidstatus(((Boolean) checklistObj[10]) ? 1 : 0);
					unInvoice.setBilling_Email(checklistObj[11].toString());
					unInvoice.setUpdated_date(((Date) checklistObj[12]).toString());
					unInvoice.setName(checklistObj[13].toString());
					unInvoice.setMobileNo(checklistObj[14].toString() != null && !checklistObj[14].toString().trim().equals("") ? checklistObj[14].toString() : "NA");
					upInvoiceList.add(unInvoice);
				}
			}
			return upInvoiceList;
		} catch (Exception e) {
			log.error("getUnpaidInvoice : " + e.getLocalizedMessage());
		}
		return upInvoiceList;
	}

	@Override
	public boolean saveCurrentPlanDetails(CbCurrentPlanDetails cbCurrentPlanDetails) {
		log.info("Entered into saveCurrentPlanDetails :: user_id : "+cbCurrentPlanDetails.getUser_id());
		try {
			Session session = sessionFactory.getCurrentSession();
			cbCurrentPlanDetails = (CbCurrentPlanDetails) session.merge(cbCurrentPlanDetails);
			return true;
		} catch (Exception e) {
			log.error("Error in saveCurrentPlanDetails :: ");
		}
		return false;
	}
	
	@Override
	public void updateSalesChannel(String user_id, String orderChannel, long gatewayId, String order_date,String order_id, int is_bundle) {
		log.info("Entered into update Sales Channel...");
		Helper _helper = new Helper();
		try {
			String qry = "update `device_subscription` set sales_channel='" + orderChannel.toLowerCase() + "', purchase_date='"+order_date+"',`updated_on`= '"+_helper.getCurrentTimeinUTC()+"', order_id = '"+order_id+"', is_bundle='"+is_bundle+"' where user_id='" + user_id +"' and gateway_id="+gatewayId;
			int saveOrUpdate = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			if (saveOrUpdate > 0)
				log.info("Updated SalesChannel and order date for user_id : "+user_id);
			else
				log.info("SalesChannel and order date is not updated for user id : "+user_id);
		} catch (Exception e) {
			log.error("Error in updateSalesChannel and order date : " + e.getMessage());
		}
	}

	@Override
	public boolean saveCancelSubscription(JCancelSubDetail csDetail, long userid, String cancel_type, String cancelled_at, int refund_amount, double exchange_rate, String curr_code) {
		log.info(" Entered into saveRechargeCBSubStatus ");
		
		String curUTC = new Helper().getCurrentTimeinUTC();
		String query = "INSERT INTO `cb_cancelsub_req` (`cb_subid`, `reason_type`, `reason_desc`, `user_id`, `created_on`, cancel_type, cancelled_at, refund_amount, exchange_rate, currency_code) VALUES "
				+ "('"+csDetail.getCb_subid()+"', '"+csDetail.getReason_type()+"', '"+csDetail.getReason_desc()+"', "+userid+", '"+curUTC+"', '"+cancel_type+"', '"+cancelled_at+"', '"+refund_amount+"', '"+exchange_rate+"', '"+curr_code+"');";
		try {
			int update = this.sessionFactory.getCurrentSession().createSQLQuery(query).executeUpdate();
			if (update >= 1) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			//e.printStackTrace();
			log.error("Error in saveCancelSubscription : " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public AllProductSubscription getProductSubscriptionByGatewayId(long gateway_id, String chargebee_id,long monitor_type) {
		log.info("Entered into getProductSubscriptionByGatewayId :: gateway_id : " + gateway_id);
		try {
			Session session = sessionFactory.getCurrentSession();
			Criteria criteria = session.createCriteria(AllProductSubscription.class)
					.add(Restrictions.eq("monitor_type", monitor_type))
					.add(Restrictions.eq("gateway_id", gateway_id))
					.add(Restrictions.eq("isDeleted", 0))
					.add(Restrictions.eq("duplicate_subs", 0))
					.add(Restrictions.eq("customerId", chargebee_id))
					.addOrder(Order.desc("updatedIndb"));
			List<AllProductSubscription> allSubscriptionList = (List<AllProductSubscription>) criteria.list();
			if (allSubscriptionList.isEmpty()) {
				log.info("No ProductSubscription found for user_id : "+ gateway_id);
				return null;
			}

			return allSubscriptionList.get(0);
		} catch (Exception e) {
			log.error("Error in getProductSubscriptionByGatewayId :: error : " + e.getLocalizedMessage());
			return null;
		}
	}
	@Override
	public boolean checkDueInvoice(String cbSubId) {
		log.info("Entered checkDueInvoice : "+cbSubId);
		try {
			String qry = "SELECT invoice_id FROM invoicehistory_new_v2 FORCE INDEX (IDX_status) where subscription_id=:subid "
					+ "AND invoicestatus='payment_due' ORDER BY invoice_date DESC limit 1;";
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("subid", cbSubId);
			List<BigInteger> res = (List<BigInteger>) query.list();
			if (!res.isEmpty()) {
				return true;
			}
		} catch (Exception e) {
			log.error("getPlanDesc: " + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public String getMeariSubCode(long userid, long gatewayid,long period_id, boolean isSolar) {
		String key_name = "";
		
		try {
			String curDt = IrisservicesUtil.getCurrentTimeUTC();
			
			String qry = "SELECT key_name FROM ("
					+ "SELECT key_name,user_id,is_activated FROM `meari_subscription` WHERE period_id=:periodid AND "
					+ "is_activated = 0 AND user_id =0 AND gateway_id =0 AND is_solarcam =:isSolar "
					+ " UNION ALL "
					+ " SELECT key_name,user_id,is_activated FROM `meari_subscription` WHERE period_id=:periodid AND "
					+ " is_activated = 0 AND user_id =:userid AND gateway_id =:gatewayid AND is_solarcam =:isSolar "
					+ " UNION ALL "
					+ "	SELECT key_name,user_id,is_activated FROM `meari_subscription` WHERE period_id=:periodid AND "
					+ "	is_activated = 1 AND expiry_date > UTC_TIMESTAMP() AND user_id =:userid AND gateway_id =:gatewayid AND is_solarcam =:isSolar ) AS A "
					+ " ORDER BY user_id DESC,is_activated DESC LIMIT 1;";

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("periodid", period_id);
			query.setParameter("userid", userid);
			query.setParameter("gatewayid", gatewayid);
			query.setParameter("isSolar", isSolar);
			List<String> res = (List<String>) query.list();
			
			if (!res.isEmpty())
				key_name = res.get(0);
			
			return key_name;
		} catch (Exception e) {
			log.error("getMeariSubCode: " + e.getLocalizedMessage());
			return key_name;
		}
	}

	
	@Override
	public boolean updateMeariSubStatus(long userid, long gatewayid,long per_id,String type,String keyname) {
		log.info("Entered into update Sales Channel...");
		try {
			String qry = "";
			int nMonth = 1;
			
			if(per_id == 4)
				nMonth = 12;
			
			Calendar tdy = Calendar.getInstance();
			Calendar expiry = Calendar.getInstance();
			expiry.add(Calendar.MONTH, nMonth);
			
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String strActivate = sdf.format(tdy.getTime());
			String strExpiry = sdf.format(expiry.getTime());
			
			SQLQuery query = null;
			int cnt = 0;
			if(type.equals("inprogress")) {
				qry = "UPDATE meari_subscription SET key_initiated=:key_initiated, gateway_id=:gatewayid , user_id=:userid "
						+ "WHERE key_name=:keyname AND is_activated=0;";
				query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
				query.setParameter("gatewayid", gatewayid);
				query.setParameter("userid", userid);
				query.setParameter("keyname", keyname);
				query.setParameter("key_initiated", strActivate);

				cnt = query.executeUpdate();
			} else {
				qry = "UPDATE meari_subscription SET is_activated=1,activated_date=:activatedate ,expiry_date=:expirydate '"
						+"' WHERE gateway_id=:gatewayid AND user_id=:userid  AND is_activated=0 AND key_name=:keyname ";
				query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
				query.setParameter("gatewayid", gatewayid);
				query.setParameter("userid", userid);
				query.setParameter("keyname", keyname);
				query.setParameter("activatedate", strActivate);
				query.setParameter("expirydate", strExpiry);
				cnt = query.executeUpdate();
			}
			
			log.info("updateMeariSubStatus user :"+userid+", gateway="+gatewayid+", status: "+cnt);
			return true;
		} catch (Exception e) {
			log.error("Error in updateMeariSubStatus : " + e.getMessage());
			return false;
		}
	}
	
	@Override
	public boolean saveTemp_sub_purchase( long userid,long gatewayid, long monitortype_id, String  hp_id) {
		log.info(" Entered into saveTemp_sub_purchase ");
		try {
			log.info("Entered into update saveTemp_sub_purchase ::" + gatewayid);
			String curUTC = new Helper().getCurrentTimeinUTC();
			String qry = "INSERT INTO `temp_sub_purchase` (`userid`, `gatewayid`, `monitortype_id`, `hp_id`,updated_date) "
					+ " VALUES (:userid , :gatewayid , :monitorid , :hpid , :curUtc );";
			try {
				SQLQuery query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
				query.setParameter("userid", userid);
				query.setParameter("gatewayid", gatewayid);
				query.setParameter("monitorid", monitortype_id);
				query.setParameter("hpid", hp_id);
				query.setParameter("curUtc", curUTC);
				int update = query.executeUpdate();
				return true;
			} catch (Exception e) {
				qry = "UPDATE temp_sub_purchase SET hp_id=:hpid ,updated_date=:curUtc WHERE userid=:userid "
						+ "AND gatewayid=:gatewayid ";
				SQLQuery query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
				query.setParameter("hpid", hp_id);
				query.setParameter("curUtc", curUTC);
				query.setParameter("userid", userid);
				query.setParameter("gatewayid", gatewayid);
				int update = query.executeUpdate();
			}
			return true;
		}catch (Exception e) {
			log.error("Error in saveTemp_sub_purchase : " + e.getMessage());
			return false;
		}
	}

	@Override
	public ProductSubscription saveOrUpdateProductSubscription(ProductSubscription productSubscription) {
		log.info("Entered into saveOrUpdateProductSubscription :: order_id : "+productSubscription.getOrder_id());
		try {
			return (ProductSubscription) sessionFactory.getCurrentSession().merge(productSubscription);
		} catch (Exception e) {
			log.error("Error in saveOrUpdateProductSubscription :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}
	
	@Override
	public boolean saveReturnCancelSubscription(String cbsubid, String reasonType, String reasonDesc, long userid,
			String cancel_type, String cancelled_at, int refund_amount, double exchange_rate, String curr_code) {
		log.info(" Entered into saveRechargeCBSubStatus ");
		
		String curUTC = new Helper().getCurrentTimeinUTC();
		String query = "INSERT INTO `cb_cancelsub_req` (`cb_subid`, `reason_type`, `reason_desc`, `user_id`, `created_on`, cancel_type, cancelled_at, refund_amount, exchange_rate, currency_code) VALUES "
				+ "('"+cbsubid+"', '"+reasonType+"', '"+reasonDesc+"', "+userid+", '"+curUTC+"', '"+cancel_type+"', '"+cancelled_at+"', '"+refund_amount+"', '"+exchange_rate+"', '"+curr_code+"');";
		try {
			int update = this.sessionFactory.getCurrentSession().createSQLQuery(query).executeUpdate();
			if (update >= 1) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			//e.printStackTrace();
			log.error("Error in saveCancelSubscription : " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public AllSubscription getSubscriptionByGatewayId(String subscrp_id) {
		log.info("Entered into getProductSubscriptionByGatewayId :: subscription_id : " + subscrp_id);
		try {
			Session session = sessionFactory.getCurrentSession();
			
			Criteria criteria = session.createCriteria(AllSubscription.class);

			Criterion chargebeeId = Restrictions.eq("subscriptionId", subscrp_id);
			Criterion statusActive = Restrictions.ilike("subscriptionStatus", "active");
			Criterion statusNonRenewing = Restrictions.ilike("subscriptionStatus", "non_renewing");
			Criterion statusInTrial = Restrictions.ilike("subscriptionStatus", "in_trial");

			LogicalExpression status = Restrictions.or(statusNonRenewing, statusInTrial);
			status = Restrictions.or(statusActive, status);

			LogicalExpression id = Restrictions.and(chargebeeId, status);

			criteria.addOrder(Order.desc("updatedDate"));
			criteria.add(id);

			List<AllSubscription> allSubscriptionList = (List<AllSubscription>) criteria.list();
			if (allSubscriptionList.isEmpty()) {
				log.info("No ProductSubscription found for user_id : "+ subscrp_id);
				return null;
			}

			return allSubscriptionList.get(0);
		} catch (Exception e) {
			log.error("Error in getProductSubscriptionByGatewayId :: error : " + e.getLocalizedMessage());
			return null;
		}
	}
	
	@Override
	public AllProductSubscription getProductSubscriptionBySubId(String subscrp_id) {
		log.info("Entered into getProductSubscriptionBySubId :: Subscription id : " + subscrp_id);
		try {
			Session session = sessionFactory.getCurrentSession();
			
			Criteria criteria = session.createCriteria(AllProductSubscription.class);

			Criterion chargebeeId = Restrictions.eq("subscriptionId", subscrp_id);
			Criterion isDeleted = Restrictions.eq("isDeleted", 0);
			Criterion statusActive = Restrictions.ilike("subscriptionStatus", "active");
			Criterion statusNonRenewing = Restrictions.ilike("subscriptionStatus", "non_renewing");
			Criterion statusInTrial = Restrictions.ilike("subscriptionStatus", "in_trial");

			LogicalExpression status = Restrictions.or(statusNonRenewing, statusInTrial);
			status = Restrictions.or(statusActive, status);

			LogicalExpression id = Restrictions.and(chargebeeId, status);
			
			LogicalExpression dbid = Restrictions.and(id, isDeleted);

			criteria.addOrder(Order.desc("updatedDate"));
			criteria.add(dbid);

			List<AllProductSubscription> allSubscriptionList = (List<AllProductSubscription>) criteria.list();
			if (allSubscriptionList.isEmpty()) {
				log.info("No getProductSubscriptionBySubId found for sub_id : "+ subscrp_id);
				return null;
			}

			return allSubscriptionList.get(0);
		
		} catch (Exception e) {
			log.error("Error in getProductSubscriptionBySubId :: error : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public boolean saveCancelSubHistory(CancelsubHistory canSub) {
			log.info("Entered into saveCancelSubHistory :: email_id : "+canSub.getEmail());
			try {
				Session session = sessionFactory.getCurrentSession();
				canSub = (CancelsubHistory) session.merge(canSub);
				return true;
			} catch (Exception e) {
				log.error("Error in saveCancelSubHistory :: ");
			}
			return false;
	}
	
	@Override
	public String getSubscriptionByChargebeeIdNotinProductsub(String chargebeeid) {
	    log.info("Entered in getSubscriptionByChargebeeIdNotinProductsub ::");
	    String subscpId = "NA";

	    try {
	    	String qry = "SELECT AC.subscription_id FROM all_chargebee_subscription AC LEFT JOIN all_product_subscription AP "
	    			+ " ON AC.subscription_id = AP.subscription_id AND AP.chargebee_id =:chargebeeid "
	    			+ " WHERE AC.chargebee_id =:chargebeeid  AND ( AC.subscription_status NOT LIKE 'active' OR AC.subscription_status NOT LIKE 'non_renewing' OR AC.subscription_status NOT LIKE 'in_trial' ) AND AC.is_deleted = 0 AND AP.subscription_id IS NULL order by AC.updated_indb desc;";

	        Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
	        query.setParameter("chargebeeid", chargebeeid);

	        List<String> list = query.list();
	        return list.isEmpty() ? subscpId : list.get(0);
	    } catch (Exception e) {
	        log.error("Error in getSubscriptionByChargebeeIdNotinProductsub: " + e.getLocalizedMessage(), e);
	    }
	    return subscpId;
	}
	
	@Override
	public String getProductSubscriptionStatus(long gateway_id, String chargebee_id) {
	    log.info("Entered in getSubscriptionByChargebeeIdNotinProductsub ::");
	    String subscpId = "NA";

	    try {
	    	String qry = "SELECT AC.subscription_status FROM all_product_subscription AC "
	    			+ " WHERE AC.gateway_id =:gatewayId AND AC.chargebee_id =:chargebeeid AND AC.is_deleted = 0 AND AC.duplicate_subs = 0 order by AC.updated_indb desc;";

	        Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
	        query.setParameter("chargebeeid", chargebee_id);
	        query.setParameter("gatewayId", gateway_id);

	        List<String> list = query.list();
	        return list.isEmpty() ? subscpId : list.get(0);
	    } catch (Exception e) {
	        log.error("Error in getSubscriptionByChargebeeIdNotinProductsub: " + e.getLocalizedMessage(), e);
	    }
	    return subscpId;
	}
	
	@Override
	public String getPlanVersionbyplanname(String planid) {
	    log.info("Entered in getPlanVersionbyplanname :: planName: " + planid);
	    String plan_ver = "NA";
	    try {
	        Session session = sessionFactory.getCurrentSession();
	        String qry = "SELECT PL.plan_ver FROM plan PL JOIN plan_to_period PP ON PL.id = PP.plan_id "
	                   + "WHERE PP.chargebee_planid = :planName ;";
	        
	        Query query = session.createSQLQuery(qry);
	        query.setParameter("planName", planid);

	        List<String> list = query.list();
	        if (list.isEmpty()) {
	            return plan_ver;
	        }

	        return list.get(0);
	    } catch (Exception e) {
	        log.error("Error in getPlanVersionbyplanname", e);
	        return plan_ver;
	    }
	}
	
	@Override
	public String getProductSubscriptionByChargebee(String chargebee_id) {
		log.info("Entered into getProductSubscriptionByChargebee :: chargebee id : " + chargebee_id);
		try {

	        Session session = sessionFactory.getCurrentSession();
	        String qry = "SELECT temp.chargebee_id FROM ( SELECT chargebee_id FROM `all_product_subscription` WHERE ( DATE(subscription_created_at) BETWEEN DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND CURDATE()) "
	        		+ "		AND `subscription_status` != 'cancelled' AND `subscription_status` != 'paused' AND `is_deleted` = 0 AND `chargebee_id` =:chargebee_id "
	        		+ "		UNION SELECT chargebee_id FROM `all_chargebee_subscription` WHERE ( DATE(subscription_created_at) BETWEEN DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND CURDATE()) "
	        		+ "		AND `subscription_status` != 'cancelled' AND `subscription_status` != 'paused' AND `is_deleted` = 0 AND `chargebee_id` =:chargebee_id ) AS temp";
	        
	        Query query = session.createSQLQuery(qry);
	        query.setParameter("chargebee_id", chargebee_id);

	        List<String> list = query.list();
	        if (!list.isEmpty()) {
	        	return list.get(0);
	        }
	        return null;
		} catch (Exception e) {
			log.error("Error in getProductSubscriptionByChargebee :: error : " + e.getLocalizedMessage());
			return null;
		}
	}
	
	@Override
	public AllProductSubscription getProductSubscriptionByGateway(long gateway_id, String chargebee_id) {
		log.info("Entered into getProductSubscriptionByGateway :: gateway id : " + gateway_id);
		try {
			Session session = sessionFactory.getCurrentSession();
			
			Criteria criteria = session.createCriteria(AllProductSubscription.class);

			Criterion chargebeeId = Restrictions.eq("gateway_id", gateway_id);
			Criterion isDeleted = Restrictions.eq("isDeleted", 0);
			Criterion statusActive = Restrictions.ilike("subscriptionStatus", "active");
			Criterion statusNonRenewing = Restrictions.ilike("subscriptionStatus", "non_renewing");
			Criterion statusInTrial = Restrictions.ilike("subscriptionStatus", "in_trial");

			LogicalExpression status = Restrictions.or(statusNonRenewing, statusInTrial);
			status = Restrictions.or(statusActive, status);

			LogicalExpression id = Restrictions.and(chargebeeId, status);
			
			LogicalExpression dbid = Restrictions.and(id, isDeleted);

			criteria.addOrder(Order.desc("updatedDate"));
			criteria.add(dbid);

			List<AllProductSubscription> allSubscriptionList = (List<AllProductSubscription>) criteria.list();
			if (allSubscriptionList.isEmpty()) {
				log.info("No getProductSubscriptionByGateway found for gateway_id : "+ gateway_id);
				return null;
			}

			return allSubscriptionList.get(0);
		
		} catch (Exception e) {
			log.error("Error in getProductSubscriptionByGateway :: error : " + e.getLocalizedMessage());
			return null;
		}
	}
	
	@Override
	public boolean updategatewaybysubcriptionId(long gatewayId,String subId) {
		log.info("Entered into updategatewaybysubcriptionId");
		Helper _helper = new Helper();
		try {
			String qry = "UPDATE `all_product_subscription` SET gateway_id = " + gatewayId +
					", updated_indb = '" + _helper.getCurrentTimeinUTC() + "'" +
					" WHERE subscription_id = '" + subId + "'" +
					" AND monitor_type != 11";
			int saveOrUpdate = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			if (saveOrUpdate > 0)
				log.info("updategatewaybysubcriptionId gatewayId : "+gatewayId);
			else
				log.info("updategatewaybysubcriptionId : "+gatewayId);
		} catch (Exception e) {
			log.error("Error in updategatewaybysubcriptionId : " + e.getMessage());
		}
		return true;
	}

	@Override
	public boolean isVetPlanAvailable(String chargebeeid,long monitortype) {
	    log.info("Entered in isVetPlanAvailable");
	    boolean isPlanAvail = false;

	    try {
	    	String qry = "SELECT id FROM `all_product_subscription` WHERE is_deleted=0 AND chargebee_id=:chargebeeid AND monitor_type=:monitortype"
	    			+ " AND subscription_status IN('non_renewing','active')";

	        Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
	        query.setParameter("monitortype", monitortype);
	        query.setParameter("chargebeeid", chargebeeid);

	        List<String> list = query.list();
	        return list.isEmpty() ? false : true;
	    } catch (Exception e) {
	        log.error("Error in isVetPlanAvailable: " + e.getLocalizedMessage());
	    }
	    return isPlanAvail;
	}
	
	
	@Override
	public String getVetPlanSub(String chargebeeid,long monitortype) {
	    log.info("Entered in getVetPlanSub");
	    String subscription_id = "NA";

	    try {
	    	String qry = "SELECT subscription_id FROM `all_product_subscription` WHERE is_deleted=0 AND chargebee_id=:chargebeeid AND monitor_type=:monitortype";

	        Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
	        query.setParameter("monitortype", monitortype);
	        query.setParameter("chargebeeid", chargebeeid);

	        List<String> list = query.list();
	        
	        if(!list.isEmpty() ) {
	        	subscription_id = list.get(0);
	        }
	        
	    } catch (Exception e) {
	        log.error("Error in getVetPlanSub: " + e.getLocalizedMessage());
	    }
	    return subscription_id;
	}
	
	@Override
	public long getCurrentVetPlanid(String chargebeeid,long monitortype) {
	    log.info("Entered in getCurrentVetPlanid");
	    long planid = 0;

	    try {
	    	String qry = "SELECT PP.plan_id FROM `all_product_subscription` PS JOIN `plan_to_period` PP ON PS.plan_id=PP.`chargebee_planid` "
	    			+ " WHERE is_deleted=0 AND chargebee_id=:chargebeeid AND monitor_type=:monitortype";

	        Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
	        query.setParameter("monitortype", monitortype);
	        query.setParameter("chargebeeid", chargebeeid);

	        List list = query.list();
	        
	        if(!list.isEmpty() ) {
	        	planid = ((BigInteger) list.get(0)).longValue();
	        }
	        
	    } catch (Exception e) {
	        log.error("Error in getCurrentVetPlanid: " + e.getLocalizedMessage());
	    }
	    return planid;
	}

	@Override
	public List<AllProductSubscription> getproductSubscriptions(String chargebeeid, long gateway_id) {
		log.info(" Entered into getproductSubscriptions :: chargebeeId : " + chargebeeid +
				" : gateway_id "+gateway_id);
		try {
			Session session = sessionFactory.getCurrentSession();
			Criteria criteria = session.createCriteria(AllProductSubscription.class);

			Criterion chargebeeId = Restrictions.eq("customerId", chargebeeid);
			Criterion statusActive = Restrictions.ilike("subscriptionStatus", "active");
			Criterion statusNonRenewing = Restrictions.ilike("subscriptionStatus", "non_renewing");
			Criterion statusInTrial = Restrictions.ilike("subscriptionStatus", "in_trial");
			Criterion statusPaused = Restrictions.ilike("subscriptionStatus", "paused");
			Criterion statusDeleted = Restrictions.eq("isDeleted", 0);
			Criterion gatewayId = Restrictions.eq("gateway_id", gateway_id);

			LogicalExpression status = Restrictions.or(statusNonRenewing, statusInTrial);
			status = Restrictions.or(statusActive, status);

			LogicalExpression newStatus = Restrictions.or(status, statusPaused);

			LogicalExpression id = Restrictions.and(chargebeeId, newStatus);

			LogicalExpression dbid = Restrictions.and(id, statusDeleted);

			LogicalExpression gId = Restrictions.and(dbid, gatewayId);

			criteria.addOrder(Order.desc("updatedDate"));
			criteria.add(dbid);

			List<AllProductSubscription> allSubscription = (List<AllProductSubscription>) criteria.list();

			if (allSubscription.isEmpty()) {
				return null;
			}

			return allSubscription;

		} catch (Exception e) {
			log.error("Error in getproductSubscriptions : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public ComboPlanInfo checkActiveComboPlan(String chargebeeId) {
		log.info("Entered into checkActiveComboPlan :: chargebeeId : "+chargebeeId);
		try {
			ComboPlanInfo comboPlanInfo = new ComboPlanInfo();
			String qry = "SELECT APS.subscription_id,APS.plan_id FROM all_product_subscription APS " +
					"JOIN plan_to_period PP ON PP.chargebee_planid = APS.plan_id " +
					"JOIN plan P ON P.id = PP.plan_id " +
					"WHERE " +
					"APS.chargebee_id = '"+ chargebeeId +"' " +
					"AND APS.subscription_status='active' " +
					"AND APS.is_deleted = 0 " +
					"AND P.plan_type='Combo-Plan';";

			List planlist = sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			if(!planlist.isEmpty()) {
				Object[] obj = ((Object[]) planlist.get(0));
				comboPlanInfo.setSubscription_id((String) obj[0]);
				comboPlanInfo.setPlan_name((String) obj[1]);
				comboPlanInfo.setChargebee_id(chargebeeId);
				return comboPlanInfo;
			}

		} catch (Exception e) {
			log.error("Error in checkActiveComboPlan :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public String getUnmappedPlaninprodSubscription(String chargebeeid, long monitorType) {
		log.info("Entered into getUnmappedPlaninprodSubscription :: chargebeeId : "+chargebeeid);
		try {
			String qry = "SELECT APS.subscription_id FROM all_product_subscription APS " +
					"WHERE APS.chargebee_id = '"+ chargebeeid +"' " +
					" AND APS.monitor_type = "+ monitorType +" AND APS.subscription_status='active' " +
					" AND APS.gateway_id = 0 AND APS.is_deleted = 0 ;";

			List<String> planlist = sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			if(!planlist.isEmpty()) {
				return ((String) planlist.get(0));
			}

		} catch (Exception e) {
			log.error("Error in getUnmappedPlaninprodSubscription :: Error : "+e.getLocalizedMessage());
			return null;
		}
		return null;
	}

	public long getGatewayFeatureBySubId(String subId) {
		log.info("Entered into getGatewayFeatureBySubId :: subId : "+subId);
		long gateway = 0;
		try {
			String qry = "SELECT gateway_id FROM gateway_feature WHERE sub_id = '"+ subId +"' AND gateway_id != 0;";

			List<BigInteger> planlist = sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			if(!planlist.isEmpty()) {
				gateway = (planlist.get(0).longValue());
			}
			return gateway;
		} catch (Exception e) {
			log.error("Error in getUnmappedPlaninprodSubscription :: Error : "+e.getLocalizedMessage());
			return gateway;
		}
	}
	@Override
	public int getCancelSub(long gatewayId, String chargebeeid){
		log.info("Entered into getCancelSub :: subId : "+gatewayId);
		try {
			String qry = "    SELECT ms.id FROM all_product_subscription a " +
					"  INNER JOIN minicam_shipping_addr ms ON a.gateway_id=ms.gateway_id" +
					"  WHERE ms.gateway_id= :gatewayId  AND ms.created_date Between a.subscription_activated_at AND a.subscription_next_billing_at ;";
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("gatewayId", gatewayId);

			List list = query.list();

			if(!list.isEmpty() ) {
				return list.size();
			}
		} catch (Exception e) {
			log.error("Error in getCancelSub :: Error : "+e.getLocalizedMessage());
			return 0;
		}
		return 0;
	}

	public List<ManageList> getManageList() {
		log.info(" Entered into getManageList ");
		try {
			Session session = sessionFactory.getCurrentSession();
			Criteria criteria = session.createCriteria(ManageList.class);

			Criterion enable = Restrictions.eq("enable", true);
			criteria.add(enable);

			List<ManageList> managePlan = (List<ManageList>) criteria.list();

			if (managePlan.isEmpty()) {
				return null;
			}

			return managePlan;

		} catch (Exception e) {
			log.error("Error in getManageList : " + e.getLocalizedMessage());
			return null;
		}
	}

	public boolean getVetchatSetUpActivate(String email) {

		try {

			String qry = "SELECT 1 FROM all_product_subscription WHERE billing_email=:email AND monitor_type=11 AND subscription_status IN ('active', 'non_renewing')";

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("email", email);
			List list = query.list();

			return !list.isEmpty();
		} catch (Exception e) {
			log.error("Error in getVetchatSetUpActivate :: Error : {}",e.getLocalizedMessage());
		}

		return false;
	}

    @Override
    public String getPlanName(String cbSubid) {
        String planName = "";
        try {
            String qry = "SELECT plan_id FROM all_product_subscription WHERE subscription_id=:cbSubid LIMIT 1";
            Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
            query.setParameter("cbSubid", cbSubid);
            List list = query.list();
            if(!list.isEmpty()) {
                planName=(String)list.get(0);
            }
        } catch (Exception e) {
            log.error("Error in getPlanName :: Error : {}",e.getLocalizedMessage());
        }
        finally {
            return planName;
        }

    }

	@Override
	public SubscriptionWithoutDevice getSubscriptionWithoutDevice(long userId, String email) {
		log.info("Entered into getSubscriptionWithoutDevice :: {}", userId);
		try {
			String checkForGateway = "SELECT * FROM usergateway WHERE userId = :userId";
			Query checkForGatewayQuery = this.sessionFactory.getCurrentSession().createSQLQuery(checkForGateway);
			checkForGatewayQuery.setParameter("userId", userId);
			List list = checkForGatewayQuery.list();
			if(!list.isEmpty()) {
				log.info("User has gateway");
				return null;
			} else {
				log.info("User has no gateway");
				String getSubscriptionDetails = "SELECT A.plan_id, A.plan_period, A.subscription_status, A.subscription_next_billing_at, A.plan_amount " +
						"FROM `all_product_subscription` A WHERE monitor_type != 11 AND gateway_id = 0 AND billing_email = :email ORDER BY updated_indb";

				Query query = this.sessionFactory.getCurrentSession().createSQLQuery(getSubscriptionDetails);
				query.setParameter("email", email);
				List list1 = query.list();
				if(!list1.isEmpty()) {
					SubscriptionWithoutDevice subscriptionWithoutDevice = new SubscriptionWithoutDevice();

					Object[] obj = ((Object[]) list1.get(0));
					subscriptionWithoutDevice.setPlanName((String) obj[0]);
					subscriptionWithoutDevice.setPeriod((String) obj[1]);
					subscriptionWithoutDevice.setStatus((String) obj[2]);
					subscriptionWithoutDevice.setNext_billing_date(obj[3].toString());
					subscriptionWithoutDevice.setPlan_price(obj[4].toString());
					boolean autoRenewalStatus = subscriptionWithoutDevice.getStatus().equalsIgnoreCase("ACTIVE");
					subscriptionWithoutDevice.setAutoRenewalStatus(autoRenewalStatus);
					return subscriptionWithoutDevice;
				}

				return null;
			}
		} catch (Exception e) {
			log.error("Error in getSubscriptionWithoutDevice :: Error : {}",e.getLocalizedMessage());
		}

		return null;
	}
}
