package com.nimble.irisservices.dao.impl;

import java.math.BigInteger;
import java.util.List;

import com.nimble.irisservices.entity.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Criteria;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.nimble.irisservices.dto.JPetprofileFlutter;

@Repository
public class VetChatDaoImpl {

	private static final Logger log = LogManager.getLogger(VetChatDaoImpl.class);

	@Autowired
	private SessionFactory sessionFactory;

	public boolean existsByEmail(String email) {
		log.info("Entered existsByEmail :" + email);
		try {
			Session session = sessionFactory.getCurrentSession();
			Criteria criteria = session.createCriteria(VetChatEligibleUsers.class).add(Restrictions.eq("email", email));

			return criteria.list().get(0) != null;
		} catch (Exception e) {
			log.error("Error in existsByEmail : " + e.getLocalizedMessage());
		}
		return false;
	}

	public List<VetChatPlan> getVetChatPlans() {
		log.info("Entered getVetPlans ::");
		try {
			Session session = sessionFactory.getCurrentSession();
			Criteria criteria = session.createCriteria(VetChatPlan.class);

			return criteria.list();
		} catch (Exception e) {
			log.error("Error in getVetPlans : " + e.getLocalizedMessage());
		}
		return null;
	}

	public boolean saveVetChatPlan(VetChatPlanUser vetChatPlan) {
		log.info("Entered saveVetChatPlan ::");
		try {
			sessionFactory.getCurrentSession().merge(vetChatPlan);
			return true;
		} catch (Exception e) {
			log.error("Error in getVetPlans : " + e.getLocalizedMessage());
		}
		return false;
	}

	public VetChatPlanUser getVetChatUserPlan(long userid) {
		log.info("Entered getVetChatUserPlan :");
		try {
			Session session = sessionFactory.getCurrentSession();
			Criteria criteria = session.createCriteria(VetChatPlanUser.class)
					.add(Restrictions.eq("vetChatPlanUser.user_id", userid));

			return (VetChatPlanUser) criteria.list().get(0);
		} catch (Exception e) {
			log.error("Error in getVetChatUserPlan : " + e.getLocalizedMessage());
		}
		return null;
	}

	public boolean updateVetChatPlanProfileId(JPetprofileFlutter jpetprofile) {
		log.info("Entered updateVetChatPlanProfileId :");
		try {
			Session session = sessionFactory.getCurrentSession();
			SQLQuery sqlQuery = session.createSQLQuery("update vetchat_plan_user set pet_profile_id="
					+ jpetprofile.getId() + " where user_id=" + jpetprofile.getUser_id());
			int status = sqlQuery.executeUpdate();
			return status > 0;
		} catch (Exception e) {
			log.error("Error in getVetChatUserPlan : " + e.getLocalizedMessage());
		}
		return false;
	}

	public boolean updateVetChatPlan(VetChatPlanUser vetChatPlan) {
		log.info("Entered updateVetChatPlan :");
		try {
			VetChatPlanUserId vetChatPlanUserId = vetChatPlan.getVetChatPlanUser();
			SQLQuery sqlQuery = sessionFactory.getCurrentSession()
					.createSQLQuery("update vetchat_plan_user set plan_id=" + vetChatPlanUserId.getPlan_id()
							+ ",expiry_time='" + vetChatPlanUserId.getExpiry_time() + "',pet_profile_id="
							+ vetChatPlanUserId.getPet_profile_id() + ", chat_initiated_at="
							+ "'"+vetChatPlanUserId.getChat_initiated_at()+"' where user_id="
							+ vetChatPlanUserId.getUser_id());
			return sqlQuery.executeUpdate() > 0;
		} catch (Exception e) {
			log.error("Error in updateVetChatPlan : " + e.getLocalizedMessage());
		}
		return false;
	}

	public boolean removeEligibleUser(String email) {
		log.info("Entered removeEligibleUser ::");
		try {
			SQLQuery sqlQuery = sessionFactory.getCurrentSession()
					.createSQLQuery("DELETE FROM vetchat_eligible_users WHERE email='"+email+"';");
			return sqlQuery.executeUpdate() > 0;
		} catch (Exception e) {
			log.error("Error in removeEligibleUser : " + e.getLocalizedMessage());
		}
		return false;
	}

	public VetChatPlan getPlanById(int plan_id) {
		log.info("Entered getPlanById :: "+plan_id);
		try {
			VetChatPlan vetChatPlan = (VetChatPlan) sessionFactory.getCurrentSession()
					.createCriteria(VetChatPlan.class).add(Restrictions.eq("id", plan_id)).list().get(0);
			return vetChatPlan;
		} catch (Exception e) {
			log.error("Error in getPlanById : " + e.getLocalizedMessage());
		}
		return null;
	}

	public boolean removeEligibleUserPlan(long id) {
		log.info("Entered removeEligibleUserPlan :: "+id);
		try {
			int update = sessionFactory.getCurrentSession().createSQLQuery("delete from vetchat_plan_user where user_id="+id).executeUpdate();
			return update > 0;
		} catch (Exception e) {
			log.error("Error in removeEligibleUserPlan : " + e.getLocalizedMessage());
		}
		return false;
	}

	public boolean saveVetChatMail(long userid) {
		log.info("Entered saveVetChatMail :");
		try {
			SQLQuery sqlQuery = sessionFactory.getCurrentSession()
					.createSQLQuery("INSERT INTO `vet_email_user` ( `user_id`, `email_status`) VALUES ("+userid+", 1)");
			return sqlQuery.executeUpdate() > 0;
		} catch (Exception e) {
			log.error("Error in updateVetChatPlan : " + e.getLocalizedMessage());
		}
		return false;
	}

	public boolean getVetChatUserEmail(long userid) {
		log.info("Entered into getVetChatUserEmail :: user_id : "+ userid);
		try {

			String qry = "SELECT user_id FROM vet_email_user WHERE user_id= :user_id and email_status = 1; ";
			SQLQuery query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("user_id", userid);

			List<Object> obj = query.list();
			if(obj.size() > 0) {
				int cnt = ((BigInteger) obj.get(0)).intValue();

				if (cnt > 0)
					return true;
				else
					return false;
			}else{
				return false;
			}

		} catch (Exception e) {
			log.error("Error in getUserDeviceSpotByUserId :: Error : "+ e.getLocalizedMessage());
			return false;
		}
	}

}
