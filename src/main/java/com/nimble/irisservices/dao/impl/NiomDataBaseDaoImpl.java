package com.nimble.irisservices.dao.impl;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.nimble.irisservices.entity.OrderSkuDetails;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;
import org.hibernate.exception.ConstraintViolationException;
import org.hibernate.transform.AliasToBeanResultTransformer;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Repository;

import com.nimble.irisservices.dao.INiomDataBaseDao;
import com.nimble.irisservices.dto.JInventory;
import com.nimble.irisservices.dto.JQrcDetails;
import com.nimble.irisservices.dto.Jorder;
import com.nimble.irisservices.dto.OrderTrackingData;
import com.nimble.irisservices.dto.ShipmentDetailData;
import com.nimble.irisservices.dto.ShipmentDetailV2;
import com.nimble.irisservices.niom.entity.Device_history;
import com.nimble.irisservices.niom.entity.Devicelocation;
import com.nimble.irisservices.niom.entity.Devicestate;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.niom.entity.Order_account;
import com.nimble.irisservices.niom.entity.Ordermap;
import com.nimble.irisservices.niom.entity.Orders;
import com.nimble.irisservices.service.INiomDataBaseService;

@Repository
public class NiomDataBaseDaoImpl implements INiomDataBaseDao {

	@Autowired
	private SessionFactory niomSessionFactory;

	@Autowired
	private SessionFactory sessionFactory;

	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;

	@Value("${amazonBundleEndDate}")
	private String amazonBundleEndDate;

	private static final Logger log = LogManager.getLogger(NiomDataBaseDaoImpl.class);

	ArrayList<Long> locationids = new ArrayList<Long>();


	public boolean isMeidMappedInOrdermap(String meid) {
		log.info(" Entered NiomDataBaseDaoImpl :: isMeidMappedInOrdermap ");
		try {

			List<Ordermap> Ordermap = new ArrayList<Ordermap>();
			Session session = niomSessionFactory.getCurrentSession();

			String qry = "select * from ordermap where meid='" + meid + "'";

			SQLQuery query2 = session.createSQLQuery(qry);
			query2.addEntity(Ordermap.class);

			Ordermap = (List<Ordermap>) query2.list();

			if (!Ordermap.isEmpty()) {
				log.info("order_id : " + Ordermap.get(0).getOrder_id());
				log.info("Billing email : " + Ordermap.get(0).getBilling_email());
				return true;
			}
			return false;

		} catch (IndexOutOfBoundsException e) {
			log.error("isMeidMappedInOrdermap " + e.getMessage());
			return false;
		} catch (Exception e) {
			log.error("isMeidMappedInOrdermap " + e.getMessage());
			return false;
		}
	}

	@Override
	public List<Orders> getOrderById(String orderChannel, String orderID) {
		log.info(" Entered NiomDataBaseDaoImpl :: getOrderById ");

		String qry = "select ORD from Orders ORD, IN (ORD.order_acc_type) type  where ORD.order_id = '" + orderID
				+ "' and type.acc_type like '%" + orderChannel + "%'";

		if (orderChannel.equalsIgnoreCase("rv")) {
			qry = "select ORD from Orders ORD, IN (ORD.order_acc_type) type  where ORD.order_id = '" + orderID
					+ "' and type.acc_type like '%" + orderChannel + "%'";
		} else if (orderChannel.equalsIgnoreCase("amazon")) {
			if (orderID.length() == 7) {
				qry = "select ORD from Orders ORD, IN (ORD.order_acc_type) type " +
						"where (SUBSTRING(ORD.external_order_id, 5, 7) = '" + orderID + "' " +
						"or SUBSTRING(ORD.external_order_id, 13, 7) = '" + orderID + "') " +
						"and type.acc_type like '%" + orderChannel + "%'";
			} else {
				orderID = orderID.replace("-", "");
				qry = "select ORD from Orders ORD, IN (ORD.order_acc_type) type  where REPLACE(ORD.external_order_id,'-','')='"
						+ orderID + "' and type.acc_type like '%" + orderChannel + "%'";
			}
		} else {
			orderID = orderID.replace("-", "");
			qry = "select ORD from Orders ORD, IN (ORD.order_acc_type) type  where REPLACE(ORD.external_order_id,'-','')='"
					+ orderID + "' and type.acc_type like '%" + orderChannel + "%'";
		}

		try {
			List<Orders> orders = this.niomSessionFactory.getCurrentSession().createQuery(qry).list();
			return orders;
		} catch (Exception e) {
			log.error("getOrderById " + e.getMessage());
			return null;
		}
	}

	@Override
	public List<ShipmentDetailData> getShipmentDetails(String whereKey, String value) {
		log.info(" Entered NiomDataBaseDaoImpl :: getShipmentDetails ");

		if( whereKey.contains("order_id") ) {
			whereKey = "REPLACE(O.order_id,'-','')";
		}

		String qry = "SELECT OS.id,O.order_id,order_date,delivery_category,OS.delivery_status,tracking_number,OS.shipped_date,"
				+ " OS.product_name,OS.order_email,order_price,order_quantity,tracking_link,OA.acc_type_name AS sales_channel  FROM order_status OS "
				+ "JOIN orders O ON (O.order_id = OS.order_id OR O.external_order_id = OS.order_id) " +
				"JOIN order_account OA ON O.order_acc_typeid = OA.id WHERE "+ whereKey +" = '" + value+"'";

 		try {
    		Query query = this.niomSessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setResultTransformer(new AliasToBeanResultTransformer(ShipmentDetailData.class));

			((SQLQuery) query).addScalar("order_id", new StringType())
			.addScalar("order_date", new StringType())
			.addScalar("delivery_category", new StringType())
			.addScalar("delivery_status", new StringType())
			.addScalar("shipped_date", new StringType())
			.addScalar("tracking_number", new StringType())
			.addScalar("product_name", new StringType())
			.addScalar("order_email", new StringType())
			.addScalar("order_price", new StringType())
			.addScalar("order_quantity", new StringType())
			.addScalar("tracking_link", new StringType())
			.addScalar("id", new LongType())
			.addScalar("sales_channel", new StringType());


			List<ShipmentDetailData> Details = query.list();
			if( !Details.isEmpty() )
				return Details;
		} catch (Exception e) {
			log.error("checkshipmentDetailCount " + e.getMessage());
		}
		return null;
	}

	@Override
	public List<OrderTrackingData> getTrackingDetails(String trackingID) {
		log.info(" Entered NiomDataBaseDaoImpl :: orderStatusTracking ");

				String qry = "select OT.tracking_status, OST.tracking_date from niom.order_status_tracking OST join niom.order_tracking"
						+ " OT on OT.id = OST.order_tracking_id "
						+ "where OST.order_status_id =" + trackingID;

				try {
					Query query = this.niomSessionFactory.getCurrentSession().createSQLQuery(qry);
					query.setResultTransformer(new AliasToBeanResultTransformer(OrderTrackingData.class));
					((SQLQuery) query).addScalar("tracking_status", new StringType())
					.addScalar("tracking_date", new StringType());

					List<OrderTrackingData> TrackingDetails = query.list();
					if (!TrackingDetails.isEmpty()) {
						return TrackingDetails;
					}
				}catch (Exception e) {
					log.error("checkshipmentDetailCount " + e.getMessage());
				}
				return null;
	}





	@Override
	public List<Ordermap> checkOrderMappedCount(String orderId) {
		log.info(" Entered NiomDataBaseDaoImpl :: checkOrderMappedCount ");
		String qry = "from Ordermap O where O.order_id = '" + orderId + "'and O.replaced_device=0 ";
		try {
			List<Ordermap> orderMaps = this.niomSessionFactory.getCurrentSession().createQuery(qry).list();
			return orderMaps;
		} catch (Exception e) {
			log.error("checkOrderMappedCount " + e.getMessage());
			return null;
		}
	}

	@Override
	public boolean saveORupdateOrder(Orders order) {
		log.info(" Entered NiomDataBaseDaoImpl :: saveORupdateOrder ");
		boolean isSuccess = false;
		try {
			List<Orders> prevOrder = getOrderid(order.getOrder_id());
			Order_account oa = this.getOrderAcctype(order.giveOrder_acc_typeid());
			order.setOrder_acc_type(oa);
			if (prevOrder.size() > 0)
				order.setId(prevOrder.get(0).getId());

			niomSessionFactory.getCurrentSession().merge(order);
			isSuccess = true;
		} catch (Exception e) {
			log.error("saveORupdateOrder " + e.getMessage());
		}
		return isSuccess;
	}

	private Order_account getOrderAcctype(long id) {
		log.info(" Entered NiomDataBaseDaoImpl :: getOrderAcctype ");
		try {
			Session session = niomSessionFactory.getCurrentSession();
			Criteria criteria = session.createCriteria(Order_account.class).add(Restrictions.eq("id", id));
			Order_account oa = (Order_account) criteria.list().get(0);
			return oa;
		} catch (IndexOutOfBoundsException e) {
			log.error("getOrderAcctype : " + e.getLocalizedMessage());
			throw new IndexOutOfBoundsException();
		}
	}

	private List<Orders> getOrderid(long order_id) {
		log.info(" Entered NiomDataBaseDaoImpl :: getOrderid ");
		String qry = "from Orders where order_id = " + order_id;
		try {
			List<Orders> orderid = this.niomSessionFactory.getCurrentSession().createQuery(qry).list();
			return orderid;
		} catch (Exception e) {
			log.error("getOrderid " + e.getMessage());
			return null;
		}
	}

	@Override
	public List<Inventory> getInventory(String qrcCode) {
		log.info(" Entered NiomDataBaseDaoImpl :: getInventory :: qrc : "+qrcCode);
		String qry = "from Inventory where qrc = '" + qrcCode + "' or mac_id='"+qrcCode+"'";
		try {
			List<Inventory> inventory = this.niomSessionFactory.getCurrentSession().createQuery(qry).list();
			return inventory;
		} catch (Exception e) {
			log.error("Error in getInventory :: Error : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public String checkmeidsvalid(String meids, String devicemodel) {
		log.info(" Entered NiomDataBaseDaoImpl :: checkmeidsvalid ");
		String result = null;
		meids = meids.replaceAll("\'", "");
		String meid_list[] = meids.split(",");
		String invalid_meids = "";
		String valid_meids = "";
//		String[] devicemodellist = devicemodel.split("\\.(?!\\d)");
		List<String> devicemodellist = Arrays.asList(devicemodel.split("\\."));

		String deviceString = "";
		String meidString = "";

		meidString += " '" + meid_list[0] + "' ";
		for (int i = 1; i < meid_list.length; i++)
			meidString += ",'" + meid_list[i] + "'";

		for (String devicemode : devicemodellist) {

			if (devicemode.toLowerCase().contains("w5"))
				devicemode = devicemode.substring(0, 6);
			else if (devicemode.length() > 8)
				devicemode = devicemode.substring(0, 8);

			if (deviceString.isEmpty()) {
				deviceString = "(I.devicemodelnumber like'" + devicemode + "%' ";
			} else {
				deviceString += " OR I.devicemodelnumber like '" + devicemode + "%' ";
			}
		}

		deviceString += " ) ";

//		String qry = "SELECT I.*  FROM ordermap OM RIGHT JOIN inventory I  ON OM.meid =  I.meid where " + deviceString
//				+ " " + "and I.devicestate_id in ('2','8','9') and OM.meid IS NULL " + "and I.meid  in (" + meidString
//				+ ")";

		String qry = "select I.* From v_valid_meids_ordermap I WHERE " + deviceString + " and I.meid in (" + meidString
				+ ")";

		// meid not in (select meid from ordermap) "
		try {
			SQLQuery q = this.niomSessionFactory.getCurrentSession().createSQLQuery(qry);
			q.addEntity(Inventory.class);
			List<Inventory> inventories = q.list();

			boolean present = false;
			for (String meid : meid_list) {
				present = false;
				for (Inventory inventory : inventories) {
					if (meid.equals(inventory.getMeid())) {
						present = true;
						locationids.add(inventory.getDevice_location().getId());
						break;
					}
				}
				if (!present) {

					if (invalid_meids.isEmpty())
						invalid_meids = invalid_meids + meid;
					else
						invalid_meids = invalid_meids + "," + meid;
				} else {
					if (valid_meids.isEmpty())
						valid_meids = valid_meids + meid;
					else
						valid_meids = valid_meids + "," + meid;
				}
			}
			if (invalid_meids.length() != 0) {
				result = "Invalid meid(s) present:" + invalid_meids + "; Valid meid(s): " + valid_meids;
			} else
				result = "valid";

		} catch (Exception e) {
			log.error("checkmeidsvalid:: Exception: " + e.getLocalizedMessage());
		}
		return result;
	}

	@Override
	public boolean updateInventoryNewMeid(String deviceState_id, String meid, String order_id) {
		// boolean isSuccess = false;
		log.info(" Entered NiomDataBaseDaoImpl :: updateInventoryNewMeid ");
		try {
			if (meid != "") {
				String qry = "update Inventory set devicestate_id = '" + deviceState_id + "'";

				if (order_id == null) {
					qry = qry + ", order_id =" + order_id + " where meid in ('" + meid + "')";
				} else {

					qry = qry + ", order_id = '" + order_id + "' where meid in ('" + meid + "')";
				}

				int s = niomSessionFactory.getCurrentSession().createQuery(qry).executeUpdate();
				if (s >= 1) {
					return true;
				} else {
					return false;
				}
			}
		} catch (Exception e) {
			log.error("updateInventoryNewMeid:" + e.getLocalizedMessage());
			return false;
		}
		return false;
	}

	@Override
	public boolean saveDeviceHistory(Device_history dev_history) {
		log.info(" Entered NiomDataBaseDaoImpl :: saveDeviceHistory ");
		try {
			boolean isSuccess = false;
			Devicestate devicestate = this.getDeviceState(dev_history.givedevicestateid());
			dev_history.setDevicestate(devicestate);

			Devicelocation dev_loc = this.getLocation(dev_history.givelocationid());
			dev_history.setDevice_location(dev_loc);

			niomSessionFactory.getCurrentSession().save(dev_history);
			isSuccess = true;
			log.info(" device history saved : " + isSuccess);
			return isSuccess;
		} catch (Exception e) {
			log.error("Error in NiomDataBaseDaoImpl :: saveDeviceHistory : " + e.getMessage());
			return false;
		}

	}

	public Devicestate getDeviceState(long id) {
		log.info(" Entered NiomDataBaseDaoImpl :: getDeviceState ");
		try {
			Session session = niomSessionFactory.getCurrentSession();
			Criteria criteria = session.createCriteria(Devicestate.class).add(Restrictions.eq("id", id));
			Devicestate devicestate = (Devicestate) criteria.list().get(0);
			return devicestate;
		} catch (IndexOutOfBoundsException e) {
			log.error("getDeviceState : " + e.getLocalizedMessage());
			throw new IndexOutOfBoundsException();
		}
	}

	public Devicelocation getLocation(long id) {
		log.info(" Entered NiomDataBaseDaoImpl :: getLocation ");
		try {
			Session session = niomSessionFactory.getCurrentSession();
			Criteria criteria = session.createCriteria(Devicelocation.class).add(Restrictions.eq("id", id));
			Devicelocation deviceloc = (Devicelocation) criteria.list().get(0);
			return deviceloc;
		} catch (IndexOutOfBoundsException e) {
			log.error("getLocation : " + e.getLocalizedMessage());
			throw new IndexOutOfBoundsException();
		}
	}

	@Override
	public boolean updateOrderMappedDate(String mapped_date, String order_id) {
		log.info(" Entered NiomDataBaseDaoImpl :: updateOrderMappedDate ");
		String qry = "update Orders set mapped_date='" + mapped_date + "' where order_id=" + order_id;
		try {
			int res = this.niomSessionFactory.getCurrentSession().createQuery(qry).executeUpdate();
			if (res != 0)
				return true;
			else
				return false;
		} catch (Exception e) {
			log.error("Error NiomDataBaseDaoImpl :: updateOrderMappedDate : " + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public List<Jorder> getJorder(String order_id) {
		List<Jorder> jorders = null;
		try {
			jorders = convertOrdersToJorders(getOrder(order_id));
			return jorders;
		} catch (Exception e) {
			log.error("getJorder : " + e.getLocalizedMessage());
			return jorders;
		}
	}

	public List<Orders> getOrder(String orderid) {
		log.info(" Entered NiomDataBaseDaoImpl :: getOrder ");
		String qry = "from Orders where order_id = " + orderid;
		try {
			List<Orders> order_id = this.niomSessionFactory.getCurrentSession().createQuery(qry).list();
			return order_id;
		} catch (Exception e) {
			log.error("getOrder: " + e.getLocalizedMessage());
			return null;
		}
	}

	private List<Jorder> convertOrdersToJorders(List<Orders> orders) {
		log.info(" Entered NiomDataBaseDaoImpl :: convertOrdersToJorders ");
		List<Jorder> jorders = new ArrayList<Jorder>();

		for (Orders order : orders) {

			List<JInventory> jinventory = convertInventoryToJInventory(getInventory(order.getOrder_id()));

			Jorder jorder = new Jorder(order.getId(), order.getOrder_id(), order.getWoocom_id(), order.getDatetime(),
					order.getStatus(), order.getShipping_total(), order.getShipping_tax_total(), order.getFee_total(),
					order.getFee_tax_total(), order.getTax_total(), order.getDiscount_total(), order.getOrder_total(),
					order.getRefunded_total(), order.getOrder_currency(), order.getPayment_method(),
					order.getShipping_method(), order.getCustomer_id(), order.getBilling_first_name(),
					order.getBilling_last_name(), order.getBilling_company(), order.getBilling_email(),
					order.getBilling_phone(), order.getBilling_address_1(), order.getBilling_address_2(),
					order.getBilling_postcode(), order.getBilling_city(), order.getBilling_state(),
					order.getBilling_country(), order.getShipping_first_name(), order.getShipping_last_name(),
					order.getShipping_address_1(), order.getShipping_address_2(), order.getShipping_postcode(),
					order.getShipping_city(), order.getShipping_state(), order.getShipping_country(),
					order.getShipping_company(), order.getCustomer_note(), order.getLine_items(),
					order.getShipping_items(), order.getFee_items(), order.getTax_items(), order.getCoupons(),
					order.getOrder_notes(), order.getDownload_permissions_granted(), order.getAlert_mobile(),
					order.getAlert_email(), order.getLow_temp_alert(), order.getHigh_temp_alert(),
					order.getMobile_network(), order.getOrder_comments(), order.getComments(), order.getOrder_sku(),
					order.getDevicemodel(), order.getQuantity(), (order.getOrder_acc_type()).getId(),
					order.getMapped_date(), order.getLabel(), order.getTracking_num(), order.getPet_name(),
					order.getEmail_status(), order.getProvision_status(), order.getPostal_service(),
					order.getDelivery_status(), order.getDelivered_date(), order.getTracking_summary(),
					order.getExternal_order_id(), order.getWelcome_status());
			jorder.setJinventry(jinventory);
			jorders.add(jorder);
		}
		return jorders;
	}

	private List<JInventory> convertInventoryToJInventory(List<Inventory> inventorys) {
		log.info(" Entered NiomDataBaseDaoImpl :: convertInventoryToJInventory ");

		if (inventorys.size() > 0) {
			List<JInventory> jinventorys = new ArrayList<JInventory>();
			for (Inventory inventory : inventorys) {
				JInventory jnventory = new JInventory(inventory.getMeid(), inventory.getSim_no(), inventory.getMdn());
				jinventorys.add(jnventory);
			}
			return jinventorys;
		}
		return null;
	}

	public List<Inventory> getInventory(long orderid) {
		log.info(" Entered NiomDataBaseDaoImpl :: getInventory ");

		try {
			String qry = "from Inventory where order.order_id = '" + orderid + "'";

			List<Inventory> inventory = this.niomSessionFactory.getCurrentSession().createQuery(qry).list();
			return inventory;
		} catch (Exception e) {
			log.error("getInventory : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public boolean saveORupdateMappedOrder(Ordermap ordermap) {
		log.info(" Entered NiomDataBaseDaoImpl :: saveORupdateMappedOrder ");
		boolean isSuccess = false;
		try {
			List<Ordermap> prevOrder = getMappedOrder(ordermap.getMeid(), null);

			if (prevOrder.size() > 0)
				ordermap.setId(prevOrder.get(0).getId());

			niomSessionFactory.getCurrentSession().merge(ordermap);

			isSuccess = true;
			return isSuccess;
		} catch (DataIntegrityViolationException e) {
			log.error("saveORupdateMappedOrder:DataIntegrityExp : " + e.getLocalizedMessage());
			return isSuccess;
		} catch (ConstraintViolationException e) {
			log.error("saveORupdateMappedOrder: ConstraintExp : " + e.getLocalizedMessage());
			return isSuccess;
		} catch (Exception e) {
			log.error("saveORupdateMappedOrder : " + e.getLocalizedMessage());
			return isSuccess;
		}
	}

	private List<Ordermap> getMappedOrder(String meid, String order_id) {
		log.info(" Entered NiomDataBaseDaoImpl :: getMappedOrder ");
		String qry = null;
		if (meid != null && !meid.isEmpty())
			qry = "from Ordermap where meid = '" + meid + "'";
		else if (order_id != null && !order_id.isEmpty()) {
			int orderid = Integer.parseInt(order_id);
			qry = "from Ordermap where order_id ='" + orderid + "'";
		}
		try {
			List<Ordermap> ordermaps = this.niomSessionFactory.getCurrentSession().createQuery(qry).list();
			return ordermaps;
		} catch (Exception e) {
			log.error(" : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public boolean updateOrdermapUserDetails(long order_id, String username, String userid, String password) {
		log.info(" Entered NiomDataBaseDaoImpl :: updateirisuseraccount ");
		String qry = "update Ordermap set user_name='" + username + "',password='" + password + "'";
		if (userid != null && !userid.isEmpty() && !userid.equalsIgnoreCase(" "))
			qry = qry + ",user_id=" + userid;
		qry = qry + " where order_id=" + order_id;
		int res = 0;
		boolean status = false;
		try {
			res = this.niomSessionFactory.getCurrentSession().createQuery(qry).executeUpdate();
			if (res > 0)
				status = true;

			log.info("updateirisuseraccount:: execute query result = " + res);

		} catch (Exception e) {
			log.error("updateirisuseraccount:: exception =" + e.getMessage());
		}
		return status;
	}

	public boolean deleteMappedOrder(String meid) {
		log.info(" Entered NiomDataBaseDaoImpl :: deleteMappedOrder ");
		int resultVal = 0;
		String qry = "delete from ordermap where meid = '" + meid + "';";
		resultVal = this.niomSessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
		return (resultVal == 1) ? true : false;
	}

	@Override
	public boolean updatedevicestateqrccode(String qrccode, String meid, int deviceState) {
		log.info("meid : " + meid + " qrccode : " + qrccode + "DeviceState : " + deviceState);
		try {
			if (qrccode != "") {
				String qry = "update Inventory set devicestate_id = '" + deviceState + "' where meid = '" + meid + "'"
						+ "and qrc= '" + qrccode + "'";
				int s = niomSessionFactory.getCurrentSession().createQuery(qry).executeUpdate();
				if (s >= 1) {
					return true;
				} else {
					return false;
				}
			}
			return false;
		} catch (Exception e) {
			log.error("updatedevicestateqrccode: " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public Inventory getInventoryByMeid(String meid) {
		log.info("Entered into NiomDataBaseDaoImpl :: getInventoryByMeid : meid : " + meid);
		String qry = "from Inventory where meid = '" + meid + "'";
		try {
			Inventory inventory = (Inventory) this.niomSessionFactory.getCurrentSession().createQuery(qry).list()
					.get(0);
			return inventory;
		} catch (Exception e) {
			log.error("Error in NiomDataBaseDaoImpl :: getInventoryByMeid : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public boolean updateExternalOrdersInNiom(Orders order) {
		log.info("Entered into updateExternalOrdersInNiom : orderid" + order.getId());
		boolean isSuccess = false;
		try {
//			List<Orders> prevOrder = getOrderid(order.getOrder_id());
			Order_account oa = this.getOrderAcctype(order.giveOrder_acc_typeid());
			order.setOrder_acc_type(oa);
////			if (prevOrder.size() > 0)
//				order.setId(prevOrder.get(0).getId());
			order.setProduct_category("Waggle");
			niomSessionFactory.getCurrentSession().merge(order);
			isSuccess = true;
			return isSuccess;
		} catch (Exception e) {
			log.error("Error in updateExternalOrdersInNiom : " + e.getLocalizedMessage());
			return isSuccess;
		}
	}

	@Override
	public boolean updateMeidInOrderMap(String userID, String meid) {
		log.info("Entered UserDaoImpl :: updateMeidInOrderMap , MEID : " + meid);
		try {
			String updateMeid = "z" + (meid.substring(1, meid.toString().length()));

			String updateMeidQry = "UPDATE ordermap SET meid='" + updateMeid + "' WHERE user_id='" + userID
					+ "' AND meid='" + meid + "'; ";
			int updateResult = niomSessionFactory.getCurrentSession().createSQLQuery(updateMeidQry).executeUpdate();
			if (updateResult == 1) {
				log.info("Meid updated in niom ordermap table - " + updateMeid);
				return true;
			} else
				return false;

		} catch (Exception e) {
			log.error("Exception occurrs while updating meid in niom ordermap table :" + e.getLocalizedMessage());
			return false;
		}

	}

	@Override
	public boolean updateInventoryFotaVersion(String meid, String fota_version) {
		log.info("Entered updateInventoryFotaVersion DAO Impl!!!");
		try {
			String qry = "update inventory set curr_fota_version = '" + fota_version + "' where meid='" + meid + "'";
			int result = niomSessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			if (result >= 1) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			log.error("Error occurred in update inventory fota version:" + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public boolean changeMeidAndReplaceInOrderMap(String oldMeid, String newMeid) {
		log.info(" Entered changeMeidAndReplaceInOrderMap :: OldMeid : " + oldMeid + " NewMeid : " + newMeid);
		try {

			String qry = "update ordermap set replaced_device = 0, meid = '" + newMeid + "' where meid = '" + oldMeid
					+ "'";
			int isUpdated = niomSessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			if (isUpdated >= 1) {
				return true;
			} else {
				return false;
			}

		} catch (Exception e) {
			log.error("Error in changeMeidAndReplaceInOrderMap : " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public boolean enableReplaceInOrderMapByMeid(String meid, long userid) {
		log.info(" Entered enableReplaceInOrderMapByMeid :: Meid : " + meid);
		try {

			String qry = "update ordermap set replaced_device = 1 where meid = '" + meid + "' and user_id= '" + userid
					+ "' ";
			int isUpdated = niomSessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			if (isUpdated >= 1) {
				return true;
			} else {
				return false;
			}

		} catch (Exception e) {
			log.error("Error in enableReplaceInOrderMapByMeid : " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public boolean checkMeidIsAvailableInOrderMap(String oldMeid) {
		log.info("Entered into checkMeidIsAvailableInOrderMap :: meid : " + oldMeid);
		try {
			String qry = "select * from ordermap where meid = '" + oldMeid + "'";

			List<Ordermap> orderMapList = this.niomSessionFactory.getCurrentSession().createSQLQuery(qry).list();

			if (orderMapList.isEmpty()) {
				return false;
			}

			return true;
		} catch (Exception e) {
			return false;
		}
	}

	@Override
	public boolean disableReplaceInOrderMapByMeid(String meid, long userid) {
		log.info(" Entered disableReplaceInOrderMapByMeid :: Meid : " + meid);
		try {

			String qry = "update ordermap set replaced_device = 0 where meid = '" + meid + "' and user_id = '" + userid
					+ "'";
			int isUpdated = niomSessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			if (isUpdated >= 1) {
				return true;
			} else {
				return false;
			}

		} catch (Exception e) {
			log.error("Error in disableReplaceInOrderMapByMeid : " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public Inventory getInventoryByIccid(String iccid) {
		log.info("Entered into NiomDataBaseDaoImpl :: getInventoryByMeid : iccid : " + iccid);
		String qry = "from Inventory where sim_no = '" + iccid + "'";
		try {
			Inventory inventory = (Inventory) this.niomSessionFactory.getCurrentSession().createQuery(qry).list()
					.get(0);
			return inventory;
		} catch (Exception e) {
			log.error("Error in NiomDataBaseDaoImpl :: getInventoryByMeid : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public List<Ordermap> getMappedOrderByMeid(String meid) {
		log.info("Entered into NiomDataBaseDaoImpl :: getMappedOrderByMeid : meid : " + meid);
		String qry = "from Ordermap where meid = '" + meid + "'";

		try {
			List<Ordermap> ordermaps = this.niomSessionFactory.getCurrentSession().createQuery(qry).list();
			return ordermaps;
		} catch (Exception e) {
			log.error(" : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public String getOrderChannelById(int order_id) {
		log.info("Entered into NiomDataBaseDaoImpl :: getOrderChannelById : order_id : " + order_id);
		String qry = "select fulfillmentchannel from orders where order_id = '" + order_id + "'";

		try {
			List<String> orderList = (List<String>) this.niomSessionFactory.getCurrentSession().createQuery(qry).list();
			if (orderList != null && !orderList.isEmpty())
				return orderList.get(0);
			return "NA";
		} catch (Exception e) {
			log.error(" : " + e.getLocalizedMessage());
			return "NA";
		}
	}

	@Override
	public boolean updateInventoryMdn(String msisdn, String iccid) {
		log.info(" Entered updateInventoryMdn :: msisdn : " + msisdn);
		try {
			String qry = "update inventory set mdn='" + msisdn + "' where sim_no ='" + iccid + "';";
			int isUpdated = niomSessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			if (isUpdated >= 1) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			log.error("Error in updateInventoryMdn : " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public String getMeidBySim(String iccid) {
		log.info("Entered into getMeidBySim :: iccid : " + iccid);
		String qry = "select meid from inventory where sim_no='" + iccid + "'";
		try {
			List<String> invList = (List<String>) this.niomSessionFactory.getCurrentSession().createSQLQuery(qry)
					.list();
			if (invList != null && !invList.isEmpty())
				return invList.get(0);
			return null;
		} catch (Exception e) {
			log.error("Error in getMeidBySim : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public boolean updateOrdermapMdn(String msisdn, String meid) {
		log.info(" Entered updateOrdermapMdn :: msisdn : " + msisdn);
		try {
			String qry = "update ordermap set mdn='" + msisdn + "' where meid ='" + meid + "';";
			int isUpdated = niomSessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			if (isUpdated >= 1) {
				log.info("Updated MDN in ordermap table");
				return true;
			} else {
				log.info("MDN not updated in ordermap table");
				return false;
			}
		} catch (Exception e) {
			log.error("Error in updateOrdermapMdn : " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public Orders getOrderDetails(long orderId) {

		log.info("Entered getOrderDetails::");
		Orders order = new Orders();
		try {

			String quantity = "NA";
			int return_units = 0;
			String select_qry = "SELECT quantity,return_units FROM orders where order_id = :order_id";

			log.info("Select query:" + select_qry);
			Object[] orderDetail = (Object[]) this.niomSessionFactory.getCurrentSession().createSQLQuery(select_qry)
					.setParameter("order_id", orderId).list().get(0);
			if (orderDetail != null) {
				quantity = (String) orderDetail[0];
				return_units = (int) orderDetail[1];
			}
			order.setQuantity(quantity);
			order.setReturn_units(return_units);
		} catch (Exception e) {
			log.error("Error in getOrderDetails : " + e.getLocalizedMessage());
		}

		return order;

	}

	@Override
	public boolean updateOrdersTable(long order_id, String quantity, int return_units, String account_status) {
		boolean isUpdatedOrders = false;
		try {
			log.info("Entered updateOrdersTable:");

			char qty = quantity.charAt(0);
			int qtyValue = Character.getNumericValue(qty);

			if (return_units > qtyValue) {
				log.info("Return unit are higher than quantity");
				isUpdatedOrders = false;
			} else {

				String qry = "Update orders set account_status='" + account_status + "', return_units=:return_units, "
						+ "dnd_status=1 where order_id=:orderid";
				int updated_rows = this.niomSessionFactory.getCurrentSession().createSQLQuery(qry)
						.setParameter("return_units", return_units).setParameter("orderid", order_id).executeUpdate();

				if (updated_rows > 0) {
					isUpdatedOrders = true;
				} else {
					isUpdatedOrders = false;
				}
			}
		} catch (Exception e) {
			log.error("Error in updateOrdersTable" + e.getLocalizedMessage());
		}
		return isUpdatedOrders;

	}

	@Override
	public boolean updateOrderMap(long order_id, String meid, String updateKey) {
		boolean updateOrderMap = false;
		try {
			log.info("Entered updateOrderMap: ");

			String update_orderMap = "update ordermap set " + updateKey
					+ " = 1 where meid=:meid and order_id=:order_id";

			int updatedRows = this.niomSessionFactory.getCurrentSession().createSQLQuery(update_orderMap)
					.setParameter("meid", meid).setParameter("order_id", order_id).executeUpdate();
			if (updatedRows > 0) {
				updateOrderMap = true;
			} else {
				updateOrderMap = false;
			}
		} catch (Exception e) {
			log.error("Error in updateOrderMap" + e.getLocalizedMessage());

		}
		return updateOrderMap;
	}

	@Override
	public int getGatewayId(String meid) {
		int id = 0;
		log.info("Entered getGatewayId:");
		try {
			String gatewayId_selectqry = "Select id from gateway where meid=:meid";
			Object gateway_id = sessionFactory.getCurrentSession().createSQLQuery(gatewayId_selectqry)
					.setParameter("meid", meid).list().get(0);
			if (gateway_id != null) {
				id = ((BigInteger) gateway_id).intValue();
			}
		} catch (Exception e) {
			log.error("Error in getGatewayId:" + e.getLocalizedMessage());
		}
		return id;
	}


	public String getDateTimeByOrderId(String order_id) {
	    log.info("Entered into getDateTimeByOrderId " + order_id);

	    try {
	        String queryString = "SELECT COALESCE(DATE(datetime), '1753-01-01 00:00:00') FROM orders WHERE order_id = :orderId";
	        Query query = niomSessionFactory.getCurrentSession().createSQLQuery(queryString).setParameter("orderId", order_id);
	        return String.valueOf(query.uniqueResult());
	    } catch (Exception e) {
	        log.error("Error in getDateTimeByOrderId: " + e.getLocalizedMessage());
	    }
	   	    return "1753-01-01 00:00:00";
	}

	@Override
	public JQrcDetails getFirmwareAndFotaVersionDeviceModelNumber(String qrc) {
	    log.info("Entered getModelIdAndFirmwareV:");

        JQrcDetails qrcDetails = new JQrcDetails();
	    try {
	        String devicemodelnumber = "NA";
	        String firmware_version = "NA";
	        String fota_version="NA";
	        String product_category="NA";

	        String niomQuery = "SELECT devicemodelnumber, firmware_version, curr_fota_version, product_category FROM inventory  WHERE qrc = '" + qrc + "'";
	        Query niomQueryObject = niomSessionFactory.getCurrentSession().createSQLQuery(niomQuery);
	        Object[] niomResult = (Object[]) niomQueryObject.uniqueResult();

	        if (niomResult != null) {
	        	devicemodelnumber = (String.valueOf(niomResult[0]));
	            firmware_version = (String) niomResult[1];
	            fota_version=(String) niomResult[2];
	            product_category=(String) niomResult[3];
	        }

	        qrcDetails.setFirmware_version(firmware_version);
            qrcDetails.setDevicemodelnumber(devicemodelnumber);
            qrcDetails.setFota_version(fota_version);
            qrcDetails.setProduct_name(product_category);

	    } catch (Exception e) {
	        log.error("Error in getModelIdAndFirmwareV: " + e.getLocalizedMessage());
	    }

	    return qrcDetails;
	}

	@Override
	public List<Ordermap> getMappedOrderByOrderId(String order_id) {
		log.info("Entered into getMappedOrderByOrderId : order_id : " + order_id);
		String qry = "from Ordermap where meid = '" + order_id + "'";

		try {
			List<Ordermap> ordermaps = this.niomSessionFactory.getCurrentSession().createQuery(qry).list();
			return ordermaps;
		} catch (Exception e) {
			log.error(" Error in getMappedOrderByOrderId :: Error : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public JQrcDetails getOrderAndUserInfo(String order_id) {
		log.info("Entered into getOrderAndUserInfo :: order_id : "+ order_id);
		try {
			String qry = "SELECT "
					+ "O.order_id, "			//0
					+ "O.billing_first_name, "	//1
					+ "O.billing_last_name, "	//2
					+ "O.billing_email, "		//3
					+ "IF (OM.registered_date IS NULL,'NA', OM.registered_date) AS registered_date, "		//4
					+ "O.order_sku "            //5
					+ "FROM orders O "
					+ "LEFT JOIN ordermap OM ON OM.order_id = O.order_id "
					+ "WHERE O.order_id = '"+ order_id +"';";

			 Query qurey = niomSessionFactory.getCurrentSession().createSQLQuery(qry);
		     Object[] orderInfo = (Object[]) qurey.uniqueResult();
	        if (orderInfo != null) {

	        	JQrcDetails qrcDetails = new JQrcDetails();
	        	qrcDetails.setFirst_name( (String) orderInfo[1] );
	        	qrcDetails.setLast_name( (String) orderInfo[2] );
	        	qrcDetails.setEmail( (String) orderInfo[3] );
	        	qrcDetails.setRegistered_date( (String) orderInfo[4] );
				qrcDetails.setOrder_sku( (String) orderInfo[5] );

	        	return qrcDetails;
	        }

		} catch (Exception e) {
			log.error("Error in getOrderAndUserInfo :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public Orders getOrderByEmailOrPhone(long userId, String email, String mobile_no, String qrc) {

		log.info("Entered into getOrderByEmailOrPhone :: email : "+ email+" :: mobile_no : "+ mobile_no+" :: qrc : "+ qrc);
		try {
			Session session = sessionFactory.getCurrentSession();

			long signupTypeId = ((BigInteger) session.createSQLQuery("SELECT signuptype_id FROM `user` WHERE id = :userId")
					.setParameter("userId", userId)
					.uniqueResult()).longValue();

			if( signupTypeId <= 0 ) {
				log.info("No user found for user id : {}", userId);
				return null;
			}

            Session sessionNiom = niomSessionFactory.getCurrentSession();
			Criteria criteria = sessionNiom.createCriteria(Orders.class);

			if (signupTypeId > 1 && ("1234567890".equals(mobile_no) || "0123456789".equals(mobile_no))) {
				criteria.add(Restrictions.eq("billing_email", email));
			} else {
				criteria.add(Restrictions.disjunction()
						.add(Restrictions.eq("billing_email", email))
						.add(Restrictions.eq("billing_phone", mobile_no))
				);
			}

			List<Orders> orders = criteria.list();

			boolean isSameDeviceModel = false;

			if( orders.isEmpty() ) {
				log.info("No orders match with billing_email and billing_phone");
				return null;
			} else if ( orders.size() > 1 ) {
				log.info("this order id has more then one record");
				return null;
			} else {

				String quantityList[] = ((Orders) orders.get(0)).getQuantity().split(":");
				int totalOrderedQuantity = Integer.parseInt(quantityList[0]);
				List<Ordermap> ordermap = niomDbservice.checkOrderMappedCount(Long.toString(((Orders) orders.get(0)).getOrder_id()));

				if( ordermap != null && totalOrderedQuantity <= ordermap.size() ) {
					log.info("order quantity exceeded");
					return null;
				}

				List<Inventory> inventory = niomDbservice.getInventory(qrc);
				if( inventory != null && !inventory.isEmpty() ) {

					String[] deviceModels = ((Orders) orders.get(0)).getDevicemodel().split("\\.");
					for( String model : deviceModels ) {
						model = model.length() >= 9 ? model.substring(0, model.length() - 1) : model ;
						if( inventory.get(0).getDevicemodelnumber().contains( model ) ) {
							isSameDeviceModel = true;
							break;
						}
					}

					log.info("inventory device_state : "+inventory.get(0).getDevicestate().getId() +" :: inventory device_model : "+ inventory.get(0).getDevicemodelnumber() +" :: order device_model : "+((Orders) orders.get(0)).getDevicemodel());
					log.info("same_device model : "+ isSameDeviceModel);

					if( isSameDeviceModel && (inventory.get(0).getDevicestate().getId() == 2 || inventory.get(0).getDevicestate().getId() == 8 || inventory.get(0).getDevicestate().getId() == 9) )
						isSameDeviceModel = true;
					else
						isSameDeviceModel = false;
				}

			}

			if (isSameDeviceModel)
				return (Orders) orders.get(0);

		} catch (Exception e) {
			log.error("Error  getOrderByEmailOrPhone :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

//	@Override
//	public List<OrderTrackingData> getTrackingDetails(String trackingID) {
//		// TODO Auto-generated method stub
//		return null;
//	}

	@Override
	public boolean updateOrdermapEmailDetails(String from_email, String to_email) {

		log.info(" Entered updateOrdermapEmailDetails :: Email : " + from_email);
		try {

			String qry = "update ordermap set user_name =:to_email where user_name =:from_email ;";
			int isUpdated = niomSessionFactory.getCurrentSession().createSQLQuery(qry)
					.setParameter("to_email", to_email)
					.setParameter("from_email", from_email).executeUpdate();
			if (isUpdated >= 1) {
				return true;
			} else {
				return false;
			}

		} catch (Exception e) {
			log.error("Error in updateOrdermapEmailDetails : " + e.getLocalizedMessage());
			return false;
		}

	}

	@Override
    public int getIsBundle(String order_id, String orderChannel, long mtype_id) {
        log.info("Entered getBundle!");
        int is_bundle = 0;

        String order_bundle_qry = "";

        try {
            String get_bundle_qry = "SELECT `externalsku`, `startdate`,`enddate` FROM `externalsubscriptionconfig`;";
            Query query = niomSessionFactory.getCurrentSession().createSQLQuery(get_bundle_qry);
            List<Object[]> res = query.list();

            if (!res.isEmpty()) {
                int rowCount = 0;
                for (Object[] obj : res) {
                    if (rowCount == 0)
                        order_bundle_qry = " CASE ";

                    rowCount++;

                    String externalsku = obj[0] != null ? obj[0].toString() : "NA";
                    String startdate = obj[1] != null ? obj[1].toString() : "NA";
                    String enddate = obj[2] != null ? obj[2].toString() : "NA";

                    order_bundle_qry += " WHEN od.order_sku = '" + externalsku + "' AND od.order_date BETWEEN '"
                            + startdate + "' AND '" + enddate + "' THEN 1 ";
                }
                order_bundle_qry += " ELSE osd.is_bundle END ";
            }

        } catch (Exception e) {
            log.error("Error in getBundle date" + e.getLocalizedMessage());
        }
        try {
            if (order_bundle_qry.trim().isEmpty())
                order_bundle_qry = " osd.is_bundle ";

            if (orderChannel.equalsIgnoreCase("amazon"))
                order_id = order_id.replace("-", "");
            String selectQuery = "SELECT " + order_bundle_qry + " END AS is_bundle FROM order_sku_details osd JOIN order_details od ON osd.sku=od.order_sku WHERE REPLACE(od.order_id, '-','')='"
                    + order_id + "' AND osd.mtype_id='" + mtype_id + "';";

            if (orderChannel.equalsIgnoreCase("rv") || orderChannel.equalsIgnoreCase("walmart")) {
                selectQuery = "SELECT IF(osd.is_bundle = '1' , '1', '0') as is_bundle FROM order_sku_details osd JOIN order_details od ON osd.sku=od.order_sku WHERE od.order_id='"
                        + order_id + "' AND od.product_type='plan';";
            }

            log.info("getIsBundle Query - " + selectQuery);

            Query query = niomSessionFactory.getCurrentSession().createSQLQuery(selectQuery);
            List res = query.list();

            if (!res.isEmpty()) {
                is_bundle = ((String) res.get(0)).equalsIgnoreCase("1") ? 1 : 0;
            }

        } catch (Exception e) {
            log.error("Exception in getBundle - " + e.getLocalizedMessage());
        }
        return is_bundle;
    }

		@Override
		public Ordermap getMappedOrderByMeidSql (String meid){
			log.info("Entered into getMappedOrderByMeidSql : meid : " + meid);
			Ordermap orderMap = new Ordermap();
			String qry = "SELECT "
					+ "o.order_id,"
					+ "o.external_order_id,"
					+ "o.order_sku,"
					+ "o.quantity,"
					+ "o.order_total,"
					+ "om.order_date,"
					+ "om.registered_date, "
					+ "oa.acc_type "
					+ "FROM ordermap om "
					+ "LEFT JOIN orders o ON o.order_id=om.order_id "
					+ "LEFT JOIN `order_account` oa ON oa.id=o.`order_acc_typeid` "
					+ "WHERE om.meid='" + meid + "' order by order_date DESC";
			try {
				List<Object[]> ordermaps = this.niomSessionFactory.getCurrentSession().createSQLQuery(qry).list();
				if (ordermaps != null && !ordermaps.isEmpty()) {
					Object[] obj = ordermaps.get(0);
					if (obj[0] != null) {
						orderMap.setOrder_id(((BigInteger) obj[0]).intValue());
					}
					if (obj[1] != null) {
						orderMap.setExternal_order_id((String) obj[1]);
					}
					if (obj[2] != null) {
						orderMap.setOrderSku((String) obj[2]);
					}
					try {
						if (obj[3] != null) {
							orderMap.setQuantity(((String) obj[3]).split("\\:")[0]);
						}
					} catch (Exception e) {
						log.error("Error in quantity parsing : " + obj[3] + e.getLocalizedMessage());
					}
					if (obj[4] != null) {
						orderMap.setOrder_total((float) obj[4]);
					}
					if (obj[5] != null) {
						orderMap.setOrder_date(((Timestamp) obj[5]).toString());
					}
					if (obj[6] != null) {
						orderMap.setRegistered_date(((Timestamp) obj[6]).toString());
					}
					if (obj[7] != null) {
						orderMap.setOrder_account_type((String) obj[7]);
					}
				}
				return orderMap;
			} catch (Exception e) {
				log.error("Error in getMappedOrderByMeidSql : " + e.getLocalizedMessage());
				return null;
			}
		}

		@Override
		public String getInventoryBySerialNo (String serialno){
			log.info("Entered into getMeidBySim :: iccid : " + serialno);
			String qry = "select qrc from inventory where serialnumber='" + serialno + "'";
			try {
				List<String> invList = (List<String>) this.niomSessionFactory.getCurrentSession().createSQLQuery(qry)
						.list();
				if (invList != null && !invList.isEmpty())
					return invList.get(0);
				return null;
			} catch (Exception e) {
				log.error("Error in getMeidBySim : " + e.getLocalizedMessage());
				return null;
			}
		}

		@Override
		public Orders getOrderByOrderId (String orderId, String channel){
			log.info("Entered into getOrderByOrderId :: " + orderId);
			try {
				Criteria criteria = (Criteria) this.niomSessionFactory.getCurrentSession()
						.createCriteria(Orders.class);
				Criterion criterion = null;
				if (channel.equalsIgnoreCase("amazon")) {
					criterion = Restrictions.eq("external_order_id", orderId);
				} else {
					criterion = Restrictions.eq("order_id", Long.parseLong(orderId));
				}
				List<Orders> ordList = criteria.add(criterion).list();
				if (ordList != null && !ordList.isEmpty()) {
					Orders order = (Orders) ordList.get(0);
					return order;
				}
			} catch (Exception e) {
				log.error("Error in getOrderByOrderId : " + e.getLocalizedMessage());
				return null;
			}
			return null;
		}

		@Override
		public List<OrderSkuDetails> getSkuDetails () {
			log.info("Entered getSkuDetails ::");
			List<OrderSkuDetails> orderSkuDetailsList = new ArrayList<>();
			OrderSkuDetails orderSkuDetails = null;
			try {
				List<Object[]> orderSkuList = (List<Object[]>) niomSessionFactory.getCurrentSession()
						.createSQLQuery("SELECT sku,sales_channel," +
								"product_type,is_bundle,is_combo FROM order_sku_details").list();
				if (!orderSkuList.isEmpty()) {
					for (Object[] obj : orderSkuList) {
						orderSkuDetails = new OrderSkuDetails();
						if (obj[0] != null)
							orderSkuDetails.setSku((String) obj[0]);
						if (obj[1] != null)
							orderSkuDetails.setSales_channel((String) obj[1]);
						if (obj[2] != null)
							orderSkuDetails.setProduct_type((String) obj[2]);
						if (obj[3] != null)
							orderSkuDetails.setIs_bundle((boolean) obj[3]);
						if (obj[4] != null)
							orderSkuDetails.setIs_combo((boolean) obj[4]);
						orderSkuDetailsList.add(orderSkuDetails);
					}
				}
				return orderSkuDetailsList;
			} catch (Exception e) {
				log.error("Error in getSkuDetails : " + e.getLocalizedMessage());
			}
			return null;
		}

		@Override
		public ShipmentDetailV2 getShipmentDetailsV2 (String whereKey, String value){
			log.info(" Entered NiomDataBaseDaoImpl :: getShipmentDetails ");

			if (whereKey.contains("order_id")) {
				whereKey = "REPLACE(O.order_id,'-','')";
			}

			String qry = "SELECT OS.order_id, OS.order_date, DATE_ADD(OS.order_date, INTERVAL 7 DAY) AS delivery_dt," +
					"OS.delivery_status, OA.acc_type_name AS sales_channel " +
					"FROM order_status OS " +
					"JOIN orders O ON (O.order_id = OS.order_id OR O.external_order_id = OS.order_id) " +
					"JOIN order_account OA ON O.order_acc_typeid = OA.id " +
					"WHERE " + whereKey + " = '" + value + "'  " +
					"ORDER BY OS.id DESC LIMIT 1;";

			try {
				List<Object[]> orderSkuList = (List<Object[]>) niomSessionFactory.getCurrentSession().createSQLQuery(qry).list();
				if (!orderSkuList.isEmpty()) {
					SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");

					ShipmentDetailV2 shipDetail = new ShipmentDetailV2();
					String delivery_desc = "Hurry! Save 10% Before Your Waggle Device Arrives!";
					String delivery_msg = "On the Way";
					for (Object[] obj : orderSkuList) {
						if (obj[0] != null)
							shipDetail.setOrder_id((String) obj[0]);
						if (obj[2] != null) {

							Timestamp deli_date = (Timestamp) obj[2];
							String delivery = sdf.format(deli_date.getTime());
							Date todayDate = new Date();

							long difference = deli_date.getTime() - todayDate.getTime();
							int daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
							String delivery_days = daysBetween + " days left";
							shipDetail.setDelivery_days(delivery_days);
							shipDetail.setDelivered_date(delivery);
						}
						if (obj[3] != null)
							shipDetail.setDelivery_status((String) obj[3]);
						if (shipDetail.getDelivery_status().equals("delivered")) {
							shipDetail.setDelivery_msg("delivered");
						} else {
							shipDetail.setDelivery_msg(delivery_msg);
						}
						shipDetail.setDelivery_desc(delivery_desc);
					}
					return shipDetail;
				} else {
					return null;
				}
			} catch (Exception e) {
				log.error("checkshipmentDetailCount " + e.getMessage());
			}
			return null;
		}


	}
