package com.nimble.irisservices.dao.impl;

import java.io.File;
import java.io.FileInputStream;
import java.lang.reflect.Type;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TimeZone;
import java.util.stream.Collectors;

import com.nimble.irisservices.entity.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;
import org.hibernate.transform.AliasToBeanResultTransformer;
import org.hibernate.type.BooleanType;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.util.ResourceUtils;

import com.chargebee.Environment;
import com.chargebee.ListResult;
import com.chargebee.models.Plan;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.dao.ICreditSystemDao;
import com.nimble.irisservices.dto.JATCount;
import com.nimble.irisservices.dto.JAddonPlan;
import com.nimble.irisservices.dto.JAlertContent;
import com.nimble.irisservices.dto.JAlertRemaining;
import com.nimble.irisservices.dto.JAlertTemplate;
import com.nimble.irisservices.dto.JCouponInfo;
import com.nimble.irisservices.dto.JFeatureCredit;
import com.nimble.irisservices.dto.JFeatureList;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JGatewayFeature;
import com.nimble.irisservices.dto.JGatewaySubSetup;
import com.nimble.irisservices.dto.JPauseHistory;
import com.nimble.irisservices.dto.JPlanDetail;
import com.nimble.irisservices.dto.JPlanInfo;
import com.nimble.irisservices.dto.JPlanOffer;
import com.nimble.irisservices.dto.JPlanToUpgrade;
import com.nimble.irisservices.dto.JProductSubResV2;
import com.nimble.irisservices.dto.JProductSubscription;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSubPeriodDetail;
import com.nimble.irisservices.dto.JSubPlanDetails;
import com.nimble.irisservices.dto.JSubscriptionPeriod;
import com.nimble.irisservices.dto.JSubscriptionPlan;
import com.nimble.irisservices.dto.JSubscriptionPlanReport;
import com.nimble.irisservices.dto.JTerms;
import com.nimble.irisservices.dto.JVPMPlan;
import com.nimble.irisservices.dto.JVetPlanDetails;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;


@Repository
public class CreditSystemDaoImpl implements ICreditSystemDao {
	@Autowired
	private SessionFactory sessionFactory; 

	@Autowired
	private SessionFactory slave4SessionFactory; 

	@Autowired
	private SessionFactory slave3SessionFactory;

	@Autowired
	private SessionFactory slave5SessionFactory;

	@Autowired
	private SessionFactory slave1SessionFactory;

	@Autowired
	private SessionFactory slave2SessionFactory;
	
	@Autowired
	IUserService userService;
	
	@Autowired
	IUserServiceV4 userServiceV4;
	
	@Autowired
	ICompanyService companyService; 

	@Autowired
	IGatewayService gatewayService;

	@Autowired
	IGatewayServiceV4 gatewayServiceV4;
	
	@Value("${show_nextrenewal_popup}")
	private boolean show_nextrenewal_popup;
	
	@Value("${days_tohandle_nextrenewal}")
	private int days_tohandle_nextrenewal;
	
	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	Helper _helper = new Helper();

	@Autowired
	ICreditSystemService crService;

	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportcontactnumber;

	@Value("#{${supportemail}}")
	private Map<String,String> supportemail;
	
	@Value("${vpm_freecall}")
	private boolean vpm_freecall;
	
	@Value("${vpmbuynow_url}")
	private String vpmbuynow_url;
	
	@Value("${redirtPetUrl}")
	private String redirtPetUrl;

	@Value("${embedupdate}")
	private boolean embedupdate;
	
	@Value("${device_count_config}")
	private int device_count_config;
	
	@Value("${ul_check_limit}")
	private int ul_check_limit=0;
	@Value("${orderdate_trial}")
	private String orderdate_trial;

	@Value("${offer_days}")
	private int offer_days;
	
	@Value("${coupon_img_new}")
	private String coupon_img_new;
	@Value("${coupon_desc_new}")
	private String coupon_desc_new;
	@Value("${coupon_code_new}")
	private String coupon_code_new;
	
	@Value("${coupon_img_update}")
	private String coupon_img_update;
	@Value("${coupon_desc_update}")
	private String coupon_desc_update;
	@Value("${coupon_code_update}")
	private String coupon_code_update;


	@Value("${coupon_img_upgrade}")
	private String coupon_img_upgrade;
	@Value("${coupon_desc_upgrade}")
	private String coupon_desc_upgrade;
	@Value("${coupon_code_upgrade}")
	private String coupon_code_upgrade;

	@Value("${coupon_note}")
	private String coupon_note;
	
	private static final Logger log = LogManager.getLogger(CreditSystemDaoImpl.class);
	
	public boolean saveOrUpdateSubscriptionPlan(SubscriptionPlan plan) throws Exception {
		log.info("Entered saveOrUpdateSubscriptionPlan :: DAO");
		boolean isSuccess = false;
		try {
			sessionFactory.getCurrentSession().merge(plan);
			isSuccess = true;
		}catch (Exception e) {
			log.error("saveOrUpdate SubscriptionPlan : ",e.getLocalizedMessage());
			isSuccess = false;
		}
		return isSuccess;
	}

	public SubscriptionPlan getSubsPlanById(long id) {
		try {
			log.info("Entered getSubsPlanById :: DAO");
			//Session session = sessionFactory.openSession();
			Criteria criteria = this.slave3SessionFactory.getCurrentSession().createCriteria(SubscriptionPlan.class).add(Restrictions.eq("id", id));
			SubscriptionPlan plan = (SubscriptionPlan) criteria.list().get(0);
			return plan;
		} catch (Exception e) {
			log.error("get SubsPlan ById : "+e.getLocalizedMessage());
			return null;
		}
	}

	public List<SubscriptionPlan> listSubscriptionPlan(){
		try {
			log.info("Entered listSubscriptionPlan :: DAO");
			Criteria criteria =  this.sessionFactory.getCurrentSession().createCriteria(SubscriptionPlan.class);
			List<SubscriptionPlan> planList = (List<SubscriptionPlan>) criteria.list();
			return planList;
		}catch (Exception e) {
			log.error("list SubscriptionPlan: ",e.getLocalizedMessage());
			return null;
		}
	}
	
	
	public boolean deleteSubscriptionPlan(long id) throws Exception {
		log.info("Entered deleteSubscriptionPlan :: DAO");
		String qry = "delete from plan where id = " + id + ";";
		int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
		return (resultVal == 1) ? true : false;
	}

	public boolean saveOrUpdatePlanToMonitorType(PlanToMonitorType plan) throws Exception {
		log.info("Entered saveOrUpdatePlanToMonitorType :: DAO");
		boolean isSuccess = false;
		try {			
			sessionFactory.getCurrentSession().merge(plan);
			isSuccess = true;
		} catch (Exception e) {
			log.error("saveOrUpdate PlanToMonitorType : ",e.getLocalizedMessage());
			isSuccess = false;
		}

		return isSuccess;
	}

	public boolean deletePlanToMonitorType(long id) throws Exception {
		log.info("Entered deletePlanToMonitorType :: DAO");
		String qry = "delete from plan_to_monitortype where id = " + id + ";";
		int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
		return (resultVal == 1) ? true : false;
	}

	public PlanToMonitorType getPlanToMonitorTypeById(long id) {
		try {
			log.info("Entered getPlanToMonitorTypeById :: DAO");
			//Session session = sessionFactory.openSession();
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(PlanToMonitorType.class).add(Restrictions.eq("id", id));
			PlanToMonitorType plan = (PlanToMonitorType) criteria.list().get(0);
			return plan;
		} catch (Exception e) {
			log.error("get PlanToMonitorType ById : ",e.getLocalizedMessage());
			return null;
		}
	}
	
	public List<PlanToMonitorType> listPlanToMonitorType(){
		try {
			log.info("Entered listPlanToMonitorType :: DAO");
			List<PlanToMonitorType> planMTypeList = this.sessionFactory.getCurrentSession().createCriteria(PlanToMonitorType.class).list();
			return planMTypeList;
		}catch (Exception e) {
			log.error("list PlanToMonitorType  : ",e.getLocalizedMessage());
			return null;
		}
	}
	
	public boolean saveOrUpdatePlanToPeriod(PlanToPeriod plan) throws Exception {
		log.info("Entered saveOrUpdatePlanToPeriod :: DAO");
		boolean isSuccess = false;
		try {			
			sessionFactory.getCurrentSession().saveOrUpdate(plan);
			isSuccess = true;	
			
		} catch (Exception e) {
			log.error("saveOrUpdate PlanToPeriod : ",e.getLocalizedMessage());
			isSuccess = false;
		}
		return isSuccess;
	}

	public boolean deletePlanToPeriod(long id) throws Exception {
		log.info("Entered deletePlanToPeriod :: DAO");
		String qry = "delete from plan_to_period where id = " + id + ";";
		int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
		return (resultVal == 1) ? true : false;
	}

	public PlanToPeriod getPlanToPeriodById(long id) {
		try {
			//Session session = sessionFactory.openSession();
			log.info("Entered getPlanToPeriodById :: DAO");
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(PlanToPeriod.class).add(Restrictions.eq("id", id));
			PlanToPeriod plan = (PlanToPeriod) criteria.list().get(0);
			return plan;
		} catch (Exception e) {
			log.error("get PlanToPeriod ById : ",e.getLocalizedMessage());
			return null;
		}
	}
	
	public List<PlanToPeriod> listPlanToPeriod(){
		try {
			log.info("Entered listPlanToPeriod :: DAO");
			List<PlanToPeriod> planMTypeList = this.sessionFactory.getCurrentSession().createCriteria(PlanToPeriod.class).list();
			return planMTypeList;
		}catch (Exception e) {
			log.error("list plan to Period : ",e.getLocalizedMessage());
			return null;
		}
	}
	
	public int getPlanToPeriodByName(String chargebee_planid) {
		int id = 1; // chum plan - ptpid
		try {
			String qry = "SELECT id FROM plan_to_period WHERE chargebee_planid ='" + chargebee_planid+ "';";
	
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();
			if (!res.isEmpty()) {
//				Object[] tuple = (Object[]) res.get(0);
//				id=((BigInteger)tuple[0]).intValue();
				id = ((BigInteger) res.get(0)).intValue();
			} 
		}catch (Exception e) {
			log.error("get getPlanToPeriodByName : ",e.getLocalizedMessage());
		}
		return id;
	}
	
	public int getPlanFreeTrialPeriodByName(String chargebee_planid) {
		int id = 0; // chum plan - ptpid
		try {
			String qry = "SELECT free_trial_days FROM plan_to_period WHERE chargebee_planid ='" + chargebee_planid+ "';";
	
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();
			if (!res.isEmpty()) {
//				Object[] tuple = (Object[]) res.get(0);
//				id=((BigInteger)tuple[0]).intValue();
				id = (int) res.get(0);
			} 
		}catch (Exception e) {
//			System.out.println(e.getLocalizedMessage());
			log.error("get getPlanToPeriodByName : ",e.getLocalizedMessage());
		}
		return id;
	}
	
	public SubscriptionPeriod getSubsPeriodById(long id) {
		try {
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(SubscriptionPeriod.class).add(Restrictions.eq("id", id));
			SubscriptionPeriod period = (SubscriptionPeriod) criteria.list().get(0);
			return period;
		} catch (Exception e) {
			log.error("get SubscriptionPeriod ById : ",e.getLocalizedMessage());
			return null;
		}
	}
	
	public List<SubscriptionPeriod> listSubPeriod() throws Exception {
		try {
			List<SubscriptionPeriod> periodList = this.sessionFactory.getCurrentSession().createCriteria(SubscriptionPeriod.class).list();
			return periodList;
		}catch (Exception e) {
			log.error("list SubPeriod : ",e.getLocalizedMessage());
			return null;
		}
	}
	
	public boolean saveOrUpdatePlanToUpgrade(PlanToUpgrade plantoupgrade) throws Exception {
		boolean isSuccess = false;
		try {			
			sessionFactory.getCurrentSession().merge(plantoupgrade);
			isSuccess = true;	
			
		} catch (Exception e) {
			log.error("saveOrUpdate PlanToUpgrade : ",e.getLocalizedMessage());
			isSuccess = false;
		}
		return isSuccess;
	}

	public boolean deletePlanToUpgrade(long id) throws Exception {
		String qry = "delete from plan_to_upgrade where id = " + id + ";";
		int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
		return (resultVal == 1) ? true : false;
	}

	public PlanToUpgrade getPlanToUpgradeById(long id) {
		try {
			//Session session = sessionFactory.openSession();
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(PlanToUpgrade.class).add(Restrictions.eq("id", id));
			PlanToUpgrade plan = (PlanToUpgrade) criteria.list().get(0);
			return plan;
		} catch (Exception e) {
			log.error("get PlanToUpgrade ById : ",e.getLocalizedMessage());
			return null;
		}
	}
	
	public List<PlanToUpgrade> listPlanToUpgrade(){
		try {
			List<PlanToUpgrade> planToUpgradeList = this.sessionFactory.getCurrentSession().createCriteria(PlanToUpgrade.class).list();
			return planToUpgradeList;
		}catch (Exception e) {
			log.error("list plan to upgrade : ",e.getLocalizedMessage());
			return null;
		}
	}
	
	
	public Map<String, Object> getAvailUpgradePlans(long planid, long periodid,long userid,String country) {
		Map<String, Object> resp = new HashMap<String, Object>();

		try {
			resp = getAvailUpgradePlanNew(planid, periodid,userid,0,country);

		} catch (Exception e) {
			log.error("getCompanyCreditMonitorByCmpy : ",e.getLocalizedMessage());
		}

		return resp;
	}

	public ArrayList<Integer> getPlanAndPeriod(String chargebee_planid) {
		log.info("Entered getPlanAndPeriod :: DAO");
		ArrayList<Integer> ids = new ArrayList<Integer>();
		String qry = "SELECT plan_id,sub_period_id FROM plan_to_period WHERE chargebee_planid ='" + chargebee_planid+ "';";

		Query query = this.slave2SessionFactory.getCurrentSession().createSQLQuery(qry);
		List res = query.list();
		if (!res.isEmpty()) {
			Object[] tuple = (Object[]) res.get(0);
			ids.add(((BigInteger) tuple[0]).intValue());
			ids.add(((BigInteger) tuple[1]).intValue());
		} 
		else {
			ids.add(0,1);
			ids.add(1,1);
		}
		return ids;
	}

	public ArrayList<String> getDeviceConfig(long planid) {
		log.info("Entered getDeviceConfig :: DAO");
		ArrayList<String> config = new ArrayList<String>();
		String qry = "SELECT monitortype_id,device_config,no_cnt FROM plan_to_monitortype WHERE plan_id=" + planid+ ";";

		Query query = this.slave5SessionFactory.getCurrentSession().createSQLQuery(qry);
		List res = query.list();
		if (!res.isEmpty()) {
			Object[] tuple = (Object[]) res.get(0);
			config.add((String) tuple[0]);
			config.add((String) tuple[1]);
			config.add(Integer.toString((Integer) tuple[2]));
		}
		return config;
	}
	
	public int getDeviceConfigV4(long userid,long planid) {
		log.info("Entered getDeviceConfigV4 :: DAO");
		int cnt = 0;

		try {
			String qry = "SELECT txn_limit AS no_cnt FROM user_feature UF JOIN feature F ON UF.feature_id = F.id"
					+ " WHERE UF.`enable` = 1 And F.feature_code='N_DEVICE' AND UF.user_id=" + userid+ ";";
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);

			List res = query.list();
			
			if(res.isEmpty()) {
				qry = "SELECT device_cnt FROM plan WHERE id=" + planid+ ";";
				query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
				res = query.list();
			}
			
			if (!res.isEmpty())
				cnt = (int)res.get(0);

			return cnt;
		}catch (Exception e) {
			log.error(e.getLocalizedMessage());
		}
		return cnt;
	}

	public int getMaxDeviceCount(long planid) {
		log.info("Entered getMaxDeviceCount :: DAO");
		int cnt = 0;

		try {
			String qry = "SELECT device_cnt FROM plan WHERE id=" + planid + ";";
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();

			if (!res.isEmpty())
				cnt = (int) res.get(0);

			return cnt;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
		}
		return cnt;
	}
	public String[] getChargebeePlanById(long planid, long periodid, String country_code) {
		log.info("Entering getChargebeePlanById DAO Impl!");
		
		String[] cbPlanAndTrialPeriod = new String[7] ;
		String cb_plan = "NA";
		String free_trial_periods="0";
		String cb_coupon_id = "NA";
		String cb_addon_id="NA";
		String plan_to_period_id = "0";
		String coupon_code = "";
		int percent=0;
		try {
			String qry = "SELECT chargebee_planid,free_trial_days,cb_coupon_id,cb_addon_id,PP.id,coupon_code,SP.percent FROM plan_to_period PP "
					+ " JOIN sub_period SP ON PP.sub_period_id = SP.id WHERE plan_id =:planid "
					+ "and sub_period_id=:subperiodid and country_code=:countrycode ;";

			Query query = this.slave1SessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("planid", planid);
			query.setParameter("subperiodid", periodid);
			query.setParameter("countrycode", country_code);
			List res = query.list();
			if (!res.isEmpty()) {
				Object[] tuple = (Object[]) res.get(0);
				cb_plan = (String) tuple[0];
				free_trial_periods = tuple[1].toString();
				cb_coupon_id = tuple[2].toString();
				cb_addon_id = tuple[3].toString();
				plan_to_period_id = ((BigInteger) tuple[4]).toString();
				coupon_code = (String) tuple[5];
				percent = (int)tuple[6];
			}

			cbPlanAndTrialPeriod[0] = cb_plan;
			cbPlanAndTrialPeriod[1] = free_trial_periods;
			cbPlanAndTrialPeriod[2] = cb_coupon_id;
			cbPlanAndTrialPeriod[3] = cb_addon_id;
			cbPlanAndTrialPeriod[4] = plan_to_period_id;
			cbPlanAndTrialPeriod[5] = coupon_code;
			cbPlanAndTrialPeriod[6] = String.valueOf(percent);
			
			log.debug("getChargebeePlanById : ",
					cb_plan + "Free Trial Period : " + free_trial_periods + ": " + cb_coupon_id);
		}catch (Exception e) {
			log.info("Exception in getChargebeePlanById : "+e.getLocalizedMessage());
		}
		return cbPlanAndTrialPeriod;
	}

	public Map<String, Object> getplanlist(String qry,HashMap<String, Long> userDevConfig, long curplan, long curperiod, String country)
	{
		log.info("Entering getplanlist DAO Impl!");
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);

		Map<String, Object> resp = new HashMap<String, Object>();
		float price = 0f;
		float divide_price = 0f;
		int divide_period = 0;
		int percent = 0;
		int amt = 0;
		String display_price = "";
		String display_info = "";
		try {
			List<HashMap<String, Object>> planlist = new ArrayList<HashMap<String, Object>>();

			HashMap<String, Object> plan = new HashMap<String, Object>();
			HashMap<String, Object> period = new HashMap<String, Object>();
			ArrayList<HashMap<String, Object>> periodlist = new ArrayList<HashMap<String, Object>>();
			ArrayList<HashMap<String, Object>> revPeriodlist = new ArrayList<HashMap<String, Object>>();
			ArrayList<String> produt_ids = new ArrayList<String>();
			
			Query query = this.slave5SessionFactory.getCurrentSession().createSQLQuery(qry);

			List<Object> res = query.list();
			List<Object> planResp = new ArrayList<Object>();
			int size = 0;
			String cb_planid = "";
			String plan_id = "";
			String per_id = "";
			String savepercent = "";
			String saveamt = "";
			ArrayList<String> config = null;
			String mType = null;
			String[] dConfig = null;
			String[] devCount = null;
			long userMaxDevCnt = 0;
			long maxDevCnt = 0;
			DecimalFormat df = new DecimalFormat("0.0");
			String saveperc_msg = "";

			if (!res.isEmpty()) {
				size = res.size();
				if (userDevConfig != null) {
					if (userDevConfig.containsKey("1"))
						userMaxDevCnt = userMaxDevCnt + userDevConfig.get("1");
					if (userDevConfig.containsKey("2"))
						userMaxDevCnt = userMaxDevCnt + userDevConfig.get("2");

					for (int i = 0; i < size; i++) {

						// checking device configure to add device
						if (userDevConfig != null) {

							Object[] tuple = (Object[]) res.get(i);
							plan_id = ((BigInteger) tuple[0]).toString();
							per_id = ((BigInteger) tuple[3]).toString();

							if (Long.parseLong(plan_id) == curplan && Long.parseLong(per_id) == curperiod) {
								continue;
							}

//							config = crService.getDeviceConfig(Long.parseLong(plan_id));
//							mType = config.get(0);
//							dConfig = config.get(1).split(",");
//							maxDevCnt = Integer.parseInt(config.get(2));
							
							// Reading device count from plan table
							mType = "1";
							dConfig = new String[1];
							dConfig[0] = "0";
							maxDevCnt = crService.getMaxDeviceCount(Long.parseLong(plan_id));

							long existCnt1 = 0;
							long existCnt2 = 0;

							if (userDevConfig.containsKey("1"))
								existCnt1 = userDevConfig.get("1");

							if (userDevConfig.containsKey("2"))
								existCnt2 = userDevConfig.get("2");

							if (dConfig.length == 1 && (dConfig[0].equalsIgnoreCase("0"))) {

								if (userMaxDevCnt <= maxDevCnt) {
									planResp.add(tuple);
									continue;
								}
							} else {
								// for (int i = 0; i < dConfig.length; i++) {
								devCount = dConfig[0].split("/");

								long curCnt1 = Long.parseLong(devCount[0].trim());
								long curCnt2 = Long.parseLong(devCount[1].trim());

								if (existCnt1 > 0 && existCnt1 <= curCnt1) {
									planResp.add(tuple);
									continue;
								} else if (existCnt2 > 0 && existCnt2 <= curCnt2) {
									planResp.add(tuple);
									continue;
								}
							}
						}
					}
				} else {
					planResp.addAll(res);
				}
			}
			if (!planResp.isEmpty()) {
				size = planResp.size();
				Object[] tuple = (Object[]) planResp.get(0);
				plan_id = ((BigInteger) tuple[0]).toString();

				plan.put("planid", plan_id);
				plan.put("planname", (String) tuple[1]);
				plan.put("desc", (String) tuple[2]);
				plan.put("display_msg", (String) tuple[7]);
				String strikeprice = "";
				per_id = ((BigInteger) tuple[3]).toString();
				divide_period = ((Integer) tuple[9]);
				int offer_id = ((Integer) tuple[10]);
				String offer_label =(String) tuple[11];
				String offer_desc = (String) tuple[12];
				String offer_content =  (String) tuple[13];
				for (int i = 0; i < size - 1; i++) {
					
					Object[] tupleprev;
					
					if(i==0)
						tupleprev = (Object[]) planResp.get(0);
					else
						tupleprev = (Object[]) planResp.get(i - 1);
					
					tuple = (Object[]) planResp.get(i);
					Object[] tuplenext = (Object[]) planResp.get(i + 1);

					plan_id = ((BigInteger) tuple[0]).toString();
					String prev_planid = ((BigInteger) tupleprev[0]).toString();
					per_id = ((BigInteger) tuple[3]).toString();
					String periodname = (String) tuple[4];
					cb_planid = (String) tuple[5];
					String plan_id1 = ((BigInteger) tuplenext[0]).toString();
					String plan_name1 = (String) tuplenext[1];
					String desc1 = (String) tuplenext[2];
					String display_msg = (String) tuplenext[7];
					divide_period = ((Integer) tuple[9]);
					offer_id = ((Integer) tuple[10]);
					offer_label =(String) tuple[11];
					offer_desc = (String) tuple[12];
					offer_content =  (String) tuple[13];
					strikeprice = ((Integer) tuple[8]) > 0 ? "<del>$"+((Integer) tuple[8]).toString()+"</del>" : " ";
					
					price = 0f;
					int plan_duration =0;
					ListResult planRes = Plan.list().id().is(cb_planid).request();
					
					for (ListResult.Entry planR : planRes) {
						price = (float) planR.plan().price() / 100;
						if(planR.plan().periodUnit().toString().equalsIgnoreCase("year"))
							plan_duration =( planR.plan().period())*12;
						else
							plan_duration =planR.plan().period();						
					}
					
					
					if(i==0 || (!prev_planid.equalsIgnoreCase(String.valueOf(plan_id)))) {
						if(!per_id.equalsIgnoreCase(String.valueOf(divide_period))) {
							
							String[] cbPlanAndTrialPeriod = crService.getChargebeePlanById(Long.parseLong(plan_id), (long) divide_period, null);
							
							cb_planid=cbPlanAndTrialPeriod[0];
//							int freeTrialPeriod = Integer.parseInt(cbPlanAndTrialPeriod[1]);
						
//							cb_planid = crService.getChargebeePlanById(Long.parseLong(plan_id), (long) divide_period);

							planRes = Plan.list().id().is(cb_planid).request();
							for (ListResult.Entry planR : planRes) {
								divide_price = (float) planR.plan().price() / 100;
							}
						}else {
							divide_price = price;
						}
						if(divide_period==1) {
							//divide_price = price;
							saveperc_msg = " vs. Monthly Plan";	
						}
						else if(divide_period==2) {
							divide_price = divide_price/3;
							saveperc_msg = " vs. Quarterly Plan";
						}
						else if(divide_period==3) {
							divide_price = divide_price/6;
							saveperc_msg = " vs. Half-yearly Plan";
						}
					}				
					
					if (price > 0 && (Long.parseLong(per_id)>divide_period)) {
						percent = calculateSavePercent(divide_price, price, per_id);
						if(percent >0)
							savepercent = "Save "+String.valueOf(percent) + "% " + saveperc_msg;
						else
							savepercent = "";
					} else
						savepercent = "";
					
					if (per_id.equalsIgnoreCase("1")) {
						saveamt = "Start/Stop Subscription anytime**";
						display_price = " $"+String.valueOf(price) + "/mo";
					} else {
						if(Long.parseLong(per_id)>divide_period) {
							amt = calculateSaveAmount(divide_price, price, per_id);
							saveamt =  "Save $"+ String.valueOf(amt)  + saveperc_msg;
						}else {
							saveamt ="";
						}
						
						if(per_id.equalsIgnoreCase("2"))
							display_price = " $"+String.valueOf(df.format(price/3)) + "/mo*";
						else if(per_id.equalsIgnoreCase("3"))
							display_price = " $"+String.valueOf(df.format(price/6)) + "/mo*";
						else if(per_id.equalsIgnoreCase("4"))
							display_price = " $"+String.valueOf(df.format(price/12)) + "/mo*";
						else 
							display_price = " $"+String.valueOf(df.format(price/plan_duration)) + "/mo*";
					}
					
					if(Long.parseLong(per_id)==1) {
						display_info = "";
					}
					else if(Long.parseLong(per_id)==2) {
						display_info = "* Billed quarterly";
					}
					else if(Long.parseLong(per_id)==3) {
						display_info = "* Billed half-yearly";
					}
					else if(Long.parseLong(per_id)==4) {
						display_info = "* Billed yearly";
					}
					else 
						display_info = "* Billed for "+plan_duration+" months";
					
					
					if (!plan_id.equalsIgnoreCase(plan_id1)) {
						period = new HashMap<String, Object>();

						period.put("periodid", per_id);
						period.put("periodname", periodname);
						period.put("display_price",display_price);
						period.put("price", "$" + String.valueOf(price));
						period.put("save", savepercent);
						period.put("display_save", saveamt);
						period.put("display_info", display_info);
						period.put("strikeprice",strikeprice);
						period.put("product_id",cb_planid);
						period.put("offer_id", offer_id);
						period.put("offer_label",offer_label);
						period.put("offer_desc", offer_desc);
						period.put("offer_content", offer_content);
						periodlist.add(period);
						// Append the elements in reverse order to display yearly first
						revPeriodlist = new ArrayList<HashMap<String, Object>>();
						for (int ii = periodlist.size() - 1; ii >= 0; ii--) {							  
							revPeriodlist.add(periodlist.get(ii)); 
					    }
						plan.put("periodlist", revPeriodlist);

						planlist.add(plan);

						plan = new HashMap<String, Object>();
						plan.put("planid", plan_id1);
						plan.put("planname", plan_name1);
						plan.put("desc", desc1);
						plan.put("display_msg",display_msg);
						periodlist = new ArrayList<HashMap<String, Object>>();
					} else {
						period = new HashMap<String, Object>();
						period.put("periodid", per_id);
						period.put("periodname", periodname);
						period.put("display_price",display_price);
						period.put("price", "$" + String.valueOf(price));
						period.put("save", savepercent);
						period.put("display_save", saveamt);
						period.put("display_info", display_info);
						period.put("strikeprice",strikeprice);
						period.put("product_id",cb_planid);
						period.put("offer_id", offer_id);
						period.put("offer_label",offer_label);
						period.put("offer_desc", offer_desc);
						period.put("offer_content", offer_content);
						periodlist.add(period);
					}

				}

				// The following code block to process last record period
				tuple = (Object[]) planResp.get(size - 1);

				price = 0f;
				per_id = ((BigInteger) tuple[3]).toString();
				cb_planid = (String) tuple[5];
				strikeprice = ((Integer) tuple[8]) > 0 ? "<del>$"+((Integer) tuple[8]).toString()+"</del>" : " ";
				//divide_period = ((Integer) tuple[9]);

				offer_id = ((Integer) tuple[10]);
				offer_label =(String) tuple[11];
				offer_desc = (String) tuple[12];
				offer_content =  (String) tuple[13];
				
				ListResult planRes = Plan.list().id().is((String) tuple[5]).request();
				int plan_duration = 0;
				for (ListResult.Entry planR : planRes) {
					price = (float) planR.plan().price() / 100;

					if(planR.plan().periodUnit().toString().equalsIgnoreCase("year"))
						plan_duration =( planR.plan().period())*12;
					else
						plan_duration =planR.plan().period();
				}

				if (price > 0 && (Long.parseLong(per_id)>divide_period)) {
					percent = calculateSavePercent(divide_price, price, per_id);
					savepercent = "Save "+String.valueOf(percent) + "% " + saveperc_msg;
				} else
					savepercent = "";
				
				if (per_id.equalsIgnoreCase("1")) {
					saveamt = "Start/Stop Subscription anytime**";
					display_info = "";
					display_price = " $"+String.valueOf(price) + "/mo";
				} else {
					if(Long.parseLong(per_id)>divide_period) {
						amt = calculateSaveAmount(divide_price, price, per_id);
						saveamt =  "Save $"+ String.valueOf(amt)  + saveperc_msg;
					}else {
						saveamt ="";
					}
					
					if(per_id.equalsIgnoreCase("2"))
						display_price = " $"+String.valueOf(df.format(price/3)) + "/mo*";
					else if(per_id.equalsIgnoreCase("3"))
						display_price = " $"+String.valueOf(df.format(price/6)) + "/mo*";
					else if(per_id.equalsIgnoreCase("4"))
						display_price = " $"+String.valueOf(df.format(price/12)) + "/mo*";
					else 
						display_price = " $"+String.valueOf(df.format(price/plan_duration)) + "/mo*";

				}
			
				if(Long.parseLong(per_id)==1) {
					display_info = "";
				}
				else if(Long.parseLong(per_id)==2) {
					display_info = "* Billed quarterly";
				}
				else if(Long.parseLong(per_id)==3) {
					display_info = "* Billed half-yearly";
				}
				else if(Long.parseLong(per_id)==4) {
					display_info = "* Billed yearly";
				}
				else
					display_info = "* Billed for "+plan_duration+" months";

				period = new HashMap<String, Object>();
				period.put("periodid", ((BigInteger) tuple[3]).toString());
				period.put("periodname", (String) tuple[4]);
				period.put("display_price",display_price);
				period.put("price", "$" + String.valueOf(price));
				period.put("save", savepercent);
				period.put("display_save", saveamt);
				period.put("display_info", display_info);
				period.put("strikeprice",strikeprice);
				period.put("product_id",cb_planid);
				period.put("offer_id", offer_id);
				period.put("offer_label",offer_label);
				period.put("offer_desc", offer_desc);
				period.put("offer_content", offer_content);
				periodlist.add(period);
				
				// Append the elements in reverse order to display yearly first
				revPeriodlist = new ArrayList<HashMap<String, Object>>();
				for (int ii = periodlist.size() - 1; ii >= 0; ii--) {							  
					revPeriodlist.add(periodlist.get(ii)); 
			    }
		        
				plan.put("periodlist", revPeriodlist);

				planlist.add(plan);

			}
			// Adding customized plan
			plan = new HashMap<String, Object>();
			plan.put("planname", "Contact Us");
			plan.put("contactnumber", supportcontactnumber.get(country));
			plan.put("email", supportemail.get(country));

			resp.put("Status", 1);
			resp.put("Msg", "Success");
			resp.put("plan", planlist);
			resp.put("defaultplan", plan);
			resp.put("produt_ids", produt_ids);
		} catch (Exception e) {
			resp.put("Status", 0);
			resp.put("Msg", "Exception occured");
			log.error("getplanlist : ", e.getLocalizedMessage());
		}
		return resp;
	}
	
	public Map<String, Object> getIosplanlist(String qry,HashMap<String, Long> userDevConfig, long curplan, long curperiod,String country)
	{
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);

		Map<String, Object> resp = new HashMap<String, Object>();
		float price = 0f;
		float divide_price = 0f;
		int divide_period = 0;
		int percent = 0;
		int amt = 0;
		String display_price = "";
		String display_info = "";
		try {
			List<HashMap<String, Object>> planlist = new ArrayList<HashMap<String, Object>>();

			HashMap<String, Object> plan = new HashMap<String, Object>();
			HashMap<String, Object> period = new HashMap<String, Object>();
			ArrayList<HashMap<String, Object>> periodlist = new ArrayList<HashMap<String, Object>>();
			ArrayList<HashMap<String, Object>> revPeriodlist = new ArrayList<HashMap<String, Object>>();
			ArrayList<String> produt_ids = new ArrayList<String>();
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);

			List<Object> res = query.list();
			List<Object> planResp = new ArrayList<Object>();
			int size = 0;
			String product_id = "";
			String plan_id = "";
			String per_id = "";
			String savepercent = "";
			String saveamt = "";
			ArrayList<String> config = null;
			String mType = null;
			String[] dConfig = null;
			String[] devCount = null;
			long userMaxDevCnt = 0;
			long maxDevCnt = 0;
			DecimalFormat df = new DecimalFormat("0.0");
			String saveperc_msg = "";

			if (!res.isEmpty()) {
				size = res.size();
				if (userDevConfig != null) {
					if (userDevConfig.containsKey("1"))
						userMaxDevCnt = userMaxDevCnt + userDevConfig.get("1");
					if (userDevConfig.containsKey("2"))
						userMaxDevCnt = userMaxDevCnt + userDevConfig.get("2");

					for (int i = 0; i < size; i++) {

						// checking device configure to add device
						if (userDevConfig != null) {

							Object[] tuple = (Object[]) res.get(i);
							plan_id = ((BigInteger) tuple[0]).toString();
							per_id = ((BigInteger) tuple[3]).toString();

							if (Long.parseLong(plan_id) == curplan && Long.parseLong(per_id) == curperiod) {
								continue;
							}

							config = crService.getDeviceConfig(Long.parseLong(plan_id));
							mType = config.get(0);
							dConfig = config.get(1).split(",");
							maxDevCnt = Integer.parseInt(config.get(2));

							long existCnt1 = 0;
							long existCnt2 = 0;

							if (userDevConfig.containsKey("1"))
								existCnt1 = userDevConfig.get("1");

							if (userDevConfig.containsKey("2"))
								existCnt2 = userDevConfig.get("2");

							if (dConfig.length == 1 && (dConfig[0].equalsIgnoreCase("0"))) {

								if (userMaxDevCnt <= maxDevCnt) {
									planResp.add(tuple);
									continue;
								}
							} else {
								// for (int i = 0; i < dConfig.length; i++) {
								devCount = dConfig[0].split("/");

								long curCnt1 = Long.parseLong(devCount[0].trim());
								long curCnt2 = Long.parseLong(devCount[1].trim());

								if (existCnt1 > 0 && existCnt1 <= curCnt1) {
									planResp.add(tuple);
									continue;
								} else if (existCnt2 > 0 && existCnt2 <= curCnt2) {
									planResp.add(tuple);
									continue;
								}
							}
						}
					}
				} else {
					planResp.addAll(res);
				}
			}
			if (!planResp.isEmpty()) {
				String strikeprice = "";
				size = planResp.size();
				Object[] tuple = (Object[]) planResp.get(0);
				plan_id = ((BigInteger) tuple[0]).toString();

				plan.put("planid", plan_id);
				plan.put("planname", (String) tuple[1]);
				plan.put("desc", (String) tuple[2]);
				plan.put("display_msg", (String) tuple[7]);
				float ios_price = 0f;
				per_id = ((BigInteger) tuple[3]).toString();
				divide_period = ((Integer) tuple[9]);
				int offer_id = ((Integer) tuple[11]);
				String offer_label =(String) tuple[12];
				String offer_desc = (String) tuple[13];
				String offer_content =  (String) tuple[14];

				for (int i = 0; i < size - 1; i++) {
					
					Object[] tupleprev;
					
					if(i==0)
						tupleprev = (Object[]) planResp.get(0);
					else
						tupleprev = (Object[]) planResp.get(i - 1);
					
					tuple = (Object[]) planResp.get(i);
					Object[] tuplenext = (Object[]) planResp.get(i + 1);

					plan_id = ((BigInteger) tuple[0]).toString();
					String prev_planid = ((BigInteger) tupleprev[0]).toString();
					per_id = ((BigInteger) tuple[3]).toString();
					String periodname = (String) tuple[4];
					product_id = (String) tuple[5];
					String plan_id1 = ((BigInteger) tuplenext[0]).toString();
					String plan_name1 = (String) tuplenext[1];
					String desc1 = (String) tuplenext[2];
					String display_msg = (String) tuplenext[7];
					divide_period = ((Integer) tuple[9]);
					strikeprice = ((Integer) tuple[10]) > 0 ? "<del>$"+((Integer) tuple[10]).toString()+"</del>" : " ";;
					
					offer_id = ((Integer) tuple[11]);
					offer_label =(String) tuple[12];
					offer_desc = (String) tuple[13];
					offer_content = (String) tuple[14];
					ios_price = (Float) tuple[8];					
					price = ios_price;					
					
					if(i==0 || (!prev_planid.equalsIgnoreCase(plan_id))) {
						if(!per_id.equalsIgnoreCase(String.valueOf(divide_period))) {
							divide_price = crService.getIosPlanPrice(Long.parseLong(plan_id), (long)divide_period);
						}else {
							divide_price = price;
						}
						
						if(divide_period==1) {
							saveperc_msg = " vs. Monthly Plan";	
						}
						else if(divide_period==2) {
							divide_price = divide_price/3;
							saveperc_msg = " vs. Quarterly Plan";
						}
						else if(divide_period==3) {
							divide_price = divide_price/6;
							saveperc_msg = " vs. Half-yearly Plan";
						}
					}				
					
					if (price > 0 && (Long.parseLong(per_id)>divide_period)) {
						percent = calculateSavePercent(divide_price, price, per_id);
						savepercent = "Save "+String.valueOf(percent) + "% " + saveperc_msg;
					} else
						savepercent = "";
					
					if (per_id.equalsIgnoreCase("1")) {
						saveamt = "Start/Stop Subscription anytime**";
						display_price = " $"+String.valueOf(price) + "/mo";
					} else {
						if(Long.parseLong(per_id)>divide_period) {
							amt = calculateSaveAmount(divide_price, price, per_id);
							saveamt =  "Save $"+ String.valueOf(amt)  + saveperc_msg;
						}else {
							saveamt ="";
						}
						
						if(per_id.equalsIgnoreCase("2"))
							display_price = " $"+String.valueOf(df.format(price/3)) + "/mo*";
						else if(per_id.equalsIgnoreCase("3"))
							display_price = " $"+String.valueOf(df.format(price/6)) + "/mo*";
						else if(per_id.equalsIgnoreCase("4"))
							display_price = " $"+String.valueOf(df.format(price/12)) + "/mo*";
					}
					
					if(Long.parseLong(per_id)==1) {
						display_info = "";
					}
					else if(Long.parseLong(per_id)==2) {
						display_info = "* Billed quarterly";
					}
					else if(Long.parseLong(per_id)==3) {
						display_info = "* Billed half-yearly";
					}
					else if(Long.parseLong(per_id)==4) {
						display_info = "* Billed yearly";
					}
					
					
					if (!plan_id.equalsIgnoreCase(plan_id1)) {
						period = new HashMap<String, Object>();

						period.put("periodid", per_id);
						period.put("periodname", periodname);
						period.put("display_price",display_price);
						period.put("price", "$" + String.valueOf(price));
						period.put("save", savepercent);
						period.put("display_save", saveamt);
						period.put("display_info", display_info);
						period.put("strikeprice",strikeprice);
						period.put("product_id", product_id);
						period.put("offer_id", offer_id);
						period.put("offer_label",offer_label);
						period.put("offer_desc", offer_desc);
						period.put("offer_content", offer_content);
						periodlist.add(period);
						// Append the elements in reverse order to display yearly first
						revPeriodlist = new ArrayList<HashMap<String, Object>>();
						for (int ii = periodlist.size() - 1; ii >= 0; ii--) {							  
							revPeriodlist.add(periodlist.get(ii)); 
					    }
						plan.put("periodlist", revPeriodlist);

						planlist.add(plan);

						plan = new HashMap<String, Object>();
						plan.put("planid", plan_id1);
						plan.put("planname", plan_name1);
						plan.put("desc", desc1);
						plan.put("display_msg",display_msg);
						periodlist = new ArrayList<HashMap<String, Object>>();
					} else {
						period = new HashMap<String, Object>();
						period.put("periodid", per_id);
						period.put("periodname", periodname);
						period.put("display_price",display_price);
						period.put("price", "$" + String.valueOf(price));
						period.put("save", savepercent);
						period.put("display_save", saveamt);
						period.put("display_info", display_info);
						period.put("strikeprice",strikeprice);
						period.put("product_id", product_id);
						period.put("offer_id", offer_id);
						period.put("offer_label",offer_label);
						period.put("offer_desc", offer_desc);
						period.put("offer_content", offer_content);

						periodlist.add(period);
					}
					produt_ids.add(product_id);

				}

				// The following code block to process last record period
				tuple = (Object[]) planResp.get(size - 1);

				price = 0f;
				per_id = ((BigInteger) tuple[3]).toString();
				product_id = (String) tuple[5];
				ios_price = ((Float) tuple[8]) ;
				price = ios_price;
				strikeprice = ((Integer) tuple[10]) > 0 ? "<del>$"+((Integer) tuple[10]).toString()+"</del>" : " ";;

				divide_period = ((Integer) tuple[9]);
				offer_id = ((Integer) tuple[11]);
				offer_label =(String) tuple[12];
				offer_desc = (String) tuple[13];
				offer_content = (String) tuple[14];

				if(!per_id.equalsIgnoreCase(String.valueOf(divide_period))) {
					divide_price = crService.getIosPlanPrice(Long.parseLong(plan_id), (long)divide_period);
				}else {
					divide_price = price;
				}
				
				if (price > 0 && (Long.parseLong(per_id)>divide_period)) {
					percent = calculateSavePercent(divide_price, price, per_id);
					savepercent = "Save "+String.valueOf(percent) + "% " + saveperc_msg;
				} else
					savepercent = "";
				
				if (per_id.equalsIgnoreCase("1")) {
					saveamt = "Start/Stop Subscription anytime**";
					display_info = "";
					display_price = " $"+String.valueOf(price) + "/mo";
				} else {
					if(Long.parseLong(per_id)>divide_period) {
						amt = calculateSaveAmount(divide_price, price, per_id);
						saveamt =  "Save $"+ String.valueOf(amt)  + saveperc_msg;
					}else {
						saveamt ="";
					}
					
					if(per_id.equalsIgnoreCase("2"))
						display_price = " $"+String.valueOf(df.format(price/3)) + "/mo*";
					else if(per_id.equalsIgnoreCase("3"))
						display_price = " $"+String.valueOf(df.format(price/6)) + "/mo*";
					else if(per_id.equalsIgnoreCase("4"))
						display_price = " $"+String.valueOf(df.format(price/12)) + "/mo*";

				}
			
				if(Long.parseLong(per_id)==1) {
					display_info = "";
				}
				else if(Long.parseLong(per_id)==2) {
					display_info = "* Billed quarterly";
				}
				else if(Long.parseLong(per_id)==3) {
					display_info = "* Billed half-yearly";
				}
				else if(Long.parseLong(per_id)==4) {
					display_info = "* Billed yearly";
				}

				period = new HashMap<String, Object>();
				period.put("periodid", ((BigInteger) tuple[3]).toString());
				period.put("periodname", (String) tuple[4]);
				period.put("display_price",display_price);
				period.put("price", "$" + String.valueOf(price));
				period.put("save", savepercent);
				period.put("display_save", saveamt);
				period.put("display_info", display_info);
				period.put("strikeprice",strikeprice);
				period.put("product_id", product_id);
				period.put("offer_id", offer_id);
				period.put("offer_label",offer_label);
				period.put("offer_desc", offer_desc);
				period.put("offer_content", offer_content);

				periodlist.add(period);
				
				// Append the elements in reverse order to display yearly first
				revPeriodlist = new ArrayList<HashMap<String, Object>>();
				for (int ii = periodlist.size() - 1; ii >= 0; ii--) {							  
					revPeriodlist.add(periodlist.get(ii)); 
			    }
		        
				plan.put("periodlist", revPeriodlist);

				planlist.add(plan);
				produt_ids.add(product_id);
			}
			// Adding customized plan
			plan = new HashMap<String, Object>();
			plan.put("planname", "Contact Us");
			plan.put("contactnumber", supportcontactnumber.get(country));
			plan.put("email", supportemail.get(country));

			resp.put("Status", 1);
			resp.put("Msg", "Success");
			resp.put("plan", planlist);
			resp.put("defaultplan", plan);
			resp.put("produt_ids", produt_ids);
		} catch (Exception e) {
			resp.put("Status", 0);
			resp.put("Msg", "Exception occured");
			log.error("getiosplanlist : ", e.getLocalizedMessage());
		}
		return resp;
	}

	public int calculateSavePercent(float month_price, float price, String per_id) {
		int save_percent = 0;
		
		if (per_id.equalsIgnoreCase("1")) {
			save_percent = 0;
		} else if (per_id.equalsIgnoreCase("2")) {
			save_percent = Math.round(((month_price * 3 - price) / (month_price * 3)) * 100);
		} else if (per_id.equalsIgnoreCase("3")) {
			save_percent = Math.round(((month_price * 6 - price) / (month_price * 6)) * 100);
		} else if (per_id.equalsIgnoreCase("4")) {
			save_percent = Math.round(((month_price * 12 - price) / (month_price * 12)) * 100);
		} else if (per_id.equalsIgnoreCase("5")) {
			save_percent = Math.round(((month_price * 24 - price) / (month_price * 24)) * 100);
		} else if (per_id.equalsIgnoreCase("6")) {
			save_percent = Math.round(((month_price * 60 - price) / (month_price * 60)) * 100);
		}
		
		return save_percent;
	}
	public int calculateSaveAmount(float month_price, float price, String per_id) {
		int save_amt = 0;
		
		if (per_id.equalsIgnoreCase("1")) {
			save_amt = 0;
		} else if (per_id.equalsIgnoreCase("2")) {
			save_amt = Math.round((month_price * 3) - price);
		} else if (per_id.equalsIgnoreCase("3")) {
			save_amt = Math.round((month_price * 6 )- price);
		} else if (per_id.equalsIgnoreCase("4")) {
			save_amt = Math.round((month_price * 12) - price);
		} else if (per_id.equalsIgnoreCase("5")) {
			save_amt = Math.round((month_price * 24) - price);
		}else if (per_id.equalsIgnoreCase("6")) {
			save_amt = Math.round((month_price * 60) - price);
		}
		
		return save_amt;
	}

	public long getDeviceCountByUser(long userid, long monitor_id) {
		log.info("Entering getDeviceCountByUser for user id : "+userid);
		String qry = "SELECT COUNT(UG.gatewayid) FROM usergateway UG JOIN gateway G ON G.id=UG.gatewayid "
				+ " JOIN assetmodel AM ON AM.id=G.model_id  WHERE  AM.monitor_type_id="+monitor_id
				+" AND UG.userId =" + userid + ";";
		
		Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);

		List res = query.list();
		long cnt = 0;

		if (!res.isEmpty())
			cnt = ((BigInteger) res.get(0)).longValue();

		return cnt;
	}

	public List<Object> getDeviceCountByMonitorType(long userid, long monitortype) {
		log.info("Entering getDeviceCountByMonitorType for user id : "+userid);
		String subqry1 = " ";

		if(monitortype>0)
			subqry1 = "AND  m.id="+monitortype;

		String qry = "SELECT  m.id, COUNT(m.id) FROM usergateway ug JOIN gateway g ON g.id = ug.gatewayId "
				+ "JOIN assetmodel a ON a.id = g.model_id JOIN monitortype m ON m.id= a.monitor_type_id "
				+ " WHERE  m.enable =1 AND ug.userId =" + userid + " " + subqry1 + " GROUP BY m.id;";

		Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);

		List res = query.list();

		return res;
	}
	
	@SuppressWarnings("rawtypes")
	public Map<String, Object> getAvailUpgradePlanNew(long planid, long periodid,long userid,long monitortype,String country) {
		Map<String, Object> resp = new HashMap<String, Object>();
		HashMap<String, Long> userDevConfig = new HashMap<String, Long>();
		
		try {
			long cnt = 0;
			long count = 0;
			String qry = "";
			cnt = crService.getDeviceCountByUser(userid, monitortype);
			
			if(cnt == 0 && monitortype != 0)  // This is for new user those who have no device configure yet
				cnt = 1;
			
			if (cnt > 0) {
				int size = 0;
				List<Long> monitortypeList = new ArrayList<Long>();

				List res = crService.getDeviceCountByMonitorType(userid, 0);  // if it is 0 , will list all monitor types
				
				StringBuffer monitortypeid = new StringBuffer();
				long mtype = 0;
				
				if (!res.isEmpty() || cnt>0) {
					size = res.size();						
					
					for (int i = 0; i < size; i++) {
						Object[] tuple = (Object[]) res.get(i);
						mtype = ((BigInteger) tuple[0]).longValue();	
						long tempcount = ((BigInteger) tuple[1]).longValue();
						
						//applicable for monitor type based API
						if(mtype == monitortype)
							tempcount = tempcount+1;
						
						count = count + tempcount;
					
						userDevConfig.put(String.valueOf(mtype), tempcount);
						
						if (!monitortypeList.contains(mtype)) {
							monitortypeList.add(mtype);
						}
					}
					
					if (monitortype != 0 && !monitortypeList.contains(monitortype)) {
						monitortypeList.add(monitortype);
						userDevConfig.put(String.valueOf(monitortype), 1L);
						count = count +1;
					}
					
					Collections.sort(monitortypeList);
					
					for (Long in : monitortypeList) {
						monitortypeid.append(in.toString());
						monitortypeid.append(",");
					}
					
					monitortypeid.deleteCharAt(monitortypeid.length() - 1);

					qry = "select RPT.*  from (SELECT pp.plan_id ,p.plan_name,p.description,sp.id,sp.period_name,pp.chargebee_planid,p.orderno,"
							+ "p.display_msg,pp.strike_price,p.pricecompare_period,pp.offer_id,pp.offer_label,IFNULL(pp.offer_desc,'NA') as offer_desc,"
							+ "IFNULL(pp.offer_content,'NA') as offer_content FROM plan_to_period pp JOIN plan p ON p.id = pp.plan_id "
							+ "JOIN sub_period sp ON sp.id=pp.sub_period_id WHERE  p.plan_ver='V1' and p.enable=1 AND pp.plan_id "
							+ "IN(SELECT p.plan_id FROM plan_to_monitortype p "
							+ "WHERE custom=0 AND monitortype_id like '%" + monitortypeid + "%' AND no_cnt>=" + count
							+ " ) AND pp.chargebee_planid  IN(SELECT pp.chargebee_planid   FROM plan_to_upgrade pu " + 
							"JOIN plan_to_period pp ON pu.upgradeplan_id = pp.id JOIN plan pl ON pl.id = pp.plan_id " + 
							"JOIN sub_period sp ON sp.id=pp.sub_period_id WHERE  pl.plan_ver='V1' and pl.enable=1 AND pu.plan_to_period_id " + 
							"IN( SELECT id FROM plan_to_period WHERE plan_id="+planid+" AND sub_period_id= "+ periodid +
							")) GROUP BY p.id,sp.id) as RPT order by orderno,plan_id,id";
					resp = getplanlist(qry,userDevConfig,planid,periodid,country);
				}
			}
			else {
				qry = "SELECT pp.plan_id,pl.plan_name,pl.description,sp.id,sp.period_name,pp.chargebee_planid,pl.orderno,pl.display_msg,pp.strike_price "
					+ " ,pl.pricecompare_period,pp.offer_id,pp.offer_label,IFNULL(pp.offer_desc,'NA') as offer_desc,"
					+ "IFNULL(pp.offer_content,'NA') as offer_content FROM plan_to_upgrade pu "
					+ " JOIN plan_to_period pp ON pu.upgradeplan_id = pp.id	JOIN plan pl ON pl.id = pp.plan_id "
					+ "	JOIN sub_period sp ON sp.id=pp.sub_period_id WHERE pl.plan_ver='V1' and pl.enable=1 AND pu.plan_to_period_id "
					+ "	IN( SELECT id FROM plan_to_period WHERE  plan_id=" + planid + " AND sub_period_id= " + periodid
					+ ") ORDER BY pl.orderno ASC,pp.plan_id,sp.id;";
				resp = getplanlist(qry,null,0,0,country);
			}
		} catch (Exception e) {
			log.error("getCompanyCreditMonitorByCmpy : ",e.getLocalizedMessage());
		}

		return resp;
	}
	
	@SuppressWarnings("rawtypes")
	public Map<String, Object> getIosAvailUpgradePlan(long planid, long periodid,long userid,long monitortype,String country) {
		Map<String, Object> resp = new HashMap<String, Object>();
		HashMap<String, Long> userDevConfig = new HashMap<String, Long>();
		
		try {
			long cnt = 0;
			long count = 0;
			String qry = "";
			cnt = crService.getDeviceCountByUser(userid, monitortype);
			
			if(cnt == 0 && monitortype != 0)  // This is for new user those who have no device configure yet
				cnt = 1;
			
			if (cnt > 0) {
				int size = 0;
				List<Long> monitortypeList = new ArrayList<Long>();

				List res = crService.getDeviceCountByMonitorType(userid, 0);  // if it is 0 , will list all monitor types
				
				StringBuffer monitortypeid = new StringBuffer();
				long mtype = 0;
				
				if (!res.isEmpty() || cnt>0) {
					size = res.size();						
					
					for (int i = 0; i < size; i++) {
						Object[] tuple = (Object[]) res.get(i);
						mtype = ((BigInteger) tuple[0]).longValue();	
						long tempcount = ((BigInteger) tuple[1]).longValue();
						
						//applicable for monitor type based API
						if(mtype == monitortype)
							tempcount = tempcount+1;
						
						count = count + tempcount;
					
						userDevConfig.put(String.valueOf(mtype), tempcount);
						
						if (!monitortypeList.contains(mtype)) {
							monitortypeList.add(mtype);
						}
					}
					
					if (monitortype != 0 && !monitortypeList.contains(monitortype)) {
						monitortypeList.add(monitortype);
						userDevConfig.put(String.valueOf(monitortype), 1L);
						count = count +1;
					}
					
					Collections.sort(monitortypeList);
					
					for (Long in : monitortypeList) {
						monitortypeid.append(in.toString());
						monitortypeid.append(",");
					}
					
					monitortypeid.deleteCharAt(monitortypeid.length() - 1);

					qry = "select RPT.*  from (select pp.plan_id ,p.plan_name,p.description,sp.id,sp.period_name,pp.ios_planid,p.orderno,"
							+ "p.display_msg,pp.ios_price,p.pricecompare_period,pp.strike_price,pp.offer_id,pp.offer_label,"
							+ "IFNULL(pp.offer_desc,'NA') as offer_desc,IFNULL(pp.offer_content,'NA') as offer_content FROM plan_to_period pp JOIN plan p ON p.id = pp.plan_id "
							+ "JOIN sub_period sp ON sp.id=pp.sub_period_id WHERE pp.ios_showplan=1 AND p.plan_ver='V1' AND p.enable=1 AND pp.plan_id "
							+ "IN(SELECT p.plan_id FROM plan_to_monitortype p "
							+ "WHERE custom=0 AND monitortype_id like '%" + monitortypeid + "%' AND no_cnt>=" + count
							+ " ) AND pp.ios_planid  IN(SELECT pp.ios_planid   FROM plan_to_upgrade pu " + 
							"JOIN plan_to_period pp ON pu.upgradeplan_id = pp.id JOIN plan pl ON pl.id = pp.plan_id " + 
							"JOIN sub_period sp ON sp.id=pp.sub_period_id WHERE pl.plan_ver='V1' AND pl.enable=1 AND pu.plan_to_period_id " + 
							"IN( SELECT id FROM plan_to_period WHERE plan_id="+planid+" AND sub_period_id= "+ periodid +
							")) GROUP BY p.id,sp.id) as RPT order by orderno,plan_id,id";
					resp = getIosplanlist(qry,userDevConfig,planid,periodid,country);
				}
			}
			else {
				qry = "SELECT pp.plan_id ,p.plan_name,p.description,sp.id,sp.period_name,pp.ios_planid,p.orderno,p.display_msg,pp.ios_price,"
					+ " p.pricecompare_period,pp.strike_price,pp.offer_id,pp.offer_label,IFNULL(pp.offer_desc,'NA') as offer_desc,"
					+ " IFNULL(pp.offer_content,'NA') as offer_content"
					+ " FROM plan_to_upgrade pu JOIN plan_to_period pp ON pu.upgradeplan_id = pp.id JOIN plan p ON p.id = pp.plan_id "
					+ "	JOIN sub_period sp ON sp.id=pp.sub_period_id WHERE pp.ios_showplan=1 AND p.enable=1 AND p.plan_ver='V1' AND pu.plan_to_period_id "
					+ "	IN( SELECT id FROM plan_to_period WHERE plan_id=" + planid + " AND sub_period_id= " + periodid
					+ ") ORDER BY p.orderno ASC,pp.plan_id,sp.id;";
				//System.out.println(qry);
				resp = getIosplanlist(qry,null,0,0,country);
			}
		} catch (Exception e) {
			log.error("getIosAvailUpgradePlan : ",e.getLocalizedMessage());
		}

		return resp;
	}

	@SuppressWarnings("rawtypes")
	public Map<String, Object> getAvailUpgradePlanV4(long planid, long periodid,long userid,long monitortype,String country) {
		Map<String, Object> resp = new HashMap<String, Object>();
		HashMap<String, Long> userDevConfig = new HashMap<String, Long>();
		
		try {
			long cnt = 0;
			long count = 0;
			String qry = "";
			cnt = crService.getDeviceCountByUser(userid, monitortype);
			
			if(cnt == 0 && monitortype != 0)  // This is for new user those who have no device configure yet
				cnt = 1;
			
			if (cnt > 0) {
				int size = 0;
				List<Long> monitortypeList = new ArrayList<Long>();

				List res = crService.getDeviceCountByMonitorType(userid, 0);  // if it is 0 , will list all monitor types
				
				StringBuffer monitortypeid = new StringBuffer();
				long mtype = 0;
				
				if (!res.isEmpty() || cnt>0) {
					size = res.size();						
					
					for (int i = 0; i < size; i++) {
						Object[] tuple = (Object[]) res.get(i);
						mtype = ((BigInteger) tuple[0]).longValue();	
						long tempcount = ((BigInteger) tuple[1]).longValue();
						
						//applicable for monitor type based API
						if(mtype == monitortype)
							tempcount = tempcount+1;
						
						count = count + tempcount;
					
						userDevConfig.put(String.valueOf(mtype), tempcount);
						
						if (!monitortypeList.contains(mtype)) {
							monitortypeList.add(mtype);
						}
					}
					
					if (monitortype != 0 && !monitortypeList.contains(monitortype)) {
						monitortypeList.add(monitortype);
						userDevConfig.put(String.valueOf(monitortype), 1L);
						count = count +1;
					}
					
					Collections.sort(monitortypeList);
					
					for (Long in : monitortypeList) {
						monitortypeid.append(in.toString());
						monitortypeid.append(",");
					}
					
					monitortypeid.deleteCharAt(monitortypeid.length() - 1);

					qry = "select RPT.*  from (SELECT pp.plan_id ,p.plan_name,p.description,sp.id,sp.period_name,pp.chargebee_planid,p.orderno,"
							+ "p.display_msg,pp.strike_price  FROM plan_to_period pp JOIN plan p ON p.id = pp.plan_id "
							+ "JOIN sub_period sp ON sp.id=pp.sub_period_id WHERE p.enable=1 AND pp.plan_id "
							+ "IN(SELECT PF.plan_id  FROM plan_feature PF JOIN feature F ON PF.feature_id = F.id JOIN plan P ON P.id = PF.plan_id" + 
							" WHERE P.custom=0 AND PF.monitortype_id LIKE '%"+monitortypeid+"%' AND PF.txn_limit>=" + count +" F.type_id=2"
							+ " ) AND pp.chargebee_planid  IN(SELECT pp.chargebee_planid   FROM plan_to_upgrade pu " + 
							"JOIN plan_to_period pp ON pu.upgradeplan_id = pp.id JOIN plan pl ON pl.id = pp.plan_id " + 
							"JOIN sub_period sp ON sp.id=pp.sub_period_id WHERE pl.enable=1 AND  pu.plan_to_period_id " + 
							"IN( SELECT id FROM plan_to_period WHERE enable=1 AND plan_id="+planid+" AND sub_period_id= "+ periodid +
							")) GROUP BY p.id,sp.id) as RPT order by orderno,plan_id,id";
					
					resp = getplanlist(qry,userDevConfig,planid,periodid,country);
				}
			}
			else {
				qry = "SELECT pp.plan_id,pl.plan_name,pl.description,sp.id,sp.period_name,pp.chargebee_planid,pl.orderno,pl.display_msg,pp.strike_price "
					+ " FROM plan_to_upgrade pu JOIN plan_to_period pp ON pu.upgradeplan_id = pp.id	JOIN plan pl ON pl.id = pp.plan_id "
					+ "	JOIN sub_period sp ON sp.id=pp.sub_period_id WHERE pl.enable=1 AND pl.plan_type='Data-Plan' AND pu.plan_to_period_id "
					+ "	IN( SELECT id FROM plan_to_period WHERE enable=1 AND plan_id=" + planid + " AND sub_period_id= " + periodid
					+ ") ORDER BY pl.orderno ASC,pp.plan_id,sp.id;";
				
				resp = getplanlist(qry,null,0,0,country);
			}
		} catch (Exception e) {
			log.error("getCompanyCreditMonitorByCmpy : ",e.getLocalizedMessage());
		}

		return resp;
	}
	
	@Override
	public LinkedHashMap<String, Gateway> getGatewaysByReportTime(long userid, int monitortype) {
		LinkedHashMap<String, Gateway> gatewaylist=new LinkedHashMap<String, Gateway>();
		String  qry="";
		
		if(monitortype==1)
			qry=" SELECT gateway_id FROM lastgatewayreport FR JOIN gateway G ON G.id = FR.gateway_id JOIN usergateway UG " +
				" ON G.id= UG.gatewayid  JOIN assetmodel A ON A.id = G.model_id JOIN monitortype M ON M.id= A.monitor_type_id " +
				" WHERE UG.userid="+userid+" AND G.isenable=1 AND M.id=1 ORDER BY  FR.datetime Desc ;";
		else if(monitortype==2)
			qry = "Select A.gateway_id FROM (SELECT FR.gateway_id,MAX(FR.createdon) dt FROM `furbitreport` FR JOIN gateway G  " +
					" ON G.id = FR.gateway_id JOIN usergateway UG  ON G.id= UG.gatewayid JOIN assetmodel A ON A.id = G.model_id " +
					" JOIN monitortype M ON M.id= A.monitor_type_id WHERE UG.userid="+userid+" AND" +
					" G.isenable=1 AND M.id=2 GROUP BY  FR.gateway_id) as A ORDER BY dt DESC;";
		
		Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
		List res = query.list();
		
		if (!res.isEmpty()) {
			for(Object gid:res)
			{
				long gatewayid = ((BigInteger) gid).longValue();
				gatewaylist.put(gatewayid+"",null);
			}
		}
		
		return gatewaylist;
	}

	@Override
	public ResetType viewResetType(long id) throws Exception {
		try {
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(ResetType.class).add(Restrictions.eq("id", id));
			ResetType resetType = (ResetType) criteria.list().get(0);
			return resetType;
		} catch (Exception e) {
			throw e;
		}
	}
	
	@Override
	public List<ResetType> listResetType() throws Exception {
		try {
			List<ResetType> resettypeList = this.sessionFactory.getCurrentSession().createCriteria(ResetType.class).list();
			return resettypeList;
		}catch (Exception e) {
			throw e;
		}
	}
	
	@Override
	public FeatureType viewFeatureType(long id) throws Exception {
		try {
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(FeatureType.class).add(Restrictions.eq("id", id));
			FeatureType featuretype = (FeatureType) criteria.list().get(0);
			return featuretype;
		} catch (Exception e) {
			throw e;
		}
	}

	@Override
	public List<FeatureType> listFeatureType() throws Exception {
		try {
			List<FeatureType> featureTList = this.sessionFactory.getCurrentSession().createCriteria(FeatureType.class).list();
			return featureTList;
		}catch (Exception e) {
			throw e;
		}
	}
	
	public boolean saveOrUpdateFeature(Feature feature) throws Exception {
		try {
			sessionFactory.getCurrentSession().merge(feature);
			return true;
		}catch (Exception e) {
			throw e;
		}
	}

	public boolean deleteFeature(long id) throws Exception {
		try {
			String qry = "delete from feature where id = " + id + ";";
			int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			return (resultVal == 1) ? true : false;
		}catch (Exception e) {
			throw e;
		}
	}

	public long getFeatureId(String fName) {

		long id = 0;
		try {
			String qry = " SELECT F.id from feature F WHERE F.feature_name='"+fName+"'";
	
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();
			if (!res.isEmpty()) {
				id =((BigInteger) res.get(0)).longValue();
			}
		}
		catch (Exception e) {
			log.error("getUserFeatureAvailabilty : " + e.getLocalizedMessage());
		}
		return id;
	}
	
	public Feature getFeatureById(long id) {
		try {
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(Feature.class).add(Restrictions.eq("id", id));
			Feature feature = (Feature) criteria.list().get(0);
			return feature;
		} catch (Exception e) {
			throw e;
		}
	}
	
	public Feature getFeatureByName(String name) {
		try {
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(Feature.class).add(Restrictions.eq("feature_name", name));
			Feature feature = (Feature) criteria.list().get(0);
			return feature;
		} catch (Exception e) {
			return null;
		}
	}
	
	public List<Feature> listFeature(){
		try {
			List<Feature> featureList = this.sessionFactory.getCurrentSession().createCriteria(Feature.class).list();
			return featureList;
		}catch (Exception e) {
			throw e;
		}
	}
	
	public boolean saveOrUpdateUsertoFeature(UsertoFeature plan) throws Exception {
		try {			
			sessionFactory.getCurrentSession().merge(plan);
			return true;	
			
		} catch (Exception e) {
			throw e;
		}
	}

	public boolean deleteUsertoFeature(long id) throws Exception {
		try {
			String qry = "delete from user_feature where id = " + id + ";";
			int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			return (resultVal == 1) ? true : false;
		}catch (Exception e) {
			throw e;
		}
	}
	
	public List<UsertoFeature> listUsertoFeature(long user_id){
		try {
			List<UsertoFeature> usertoFeatureList = (List<UsertoFeature>)this.sessionFactory.getCurrentSession().createCriteria(UsertoFeature.class)
					.add(Restrictions.eq("user_id", user_id)).list();
			return usertoFeatureList;
		}catch (Exception e) {
			throw e;
		}
	}
	
	public boolean saveOrUpdatePlantoFeature(PlanToFeature plan) throws Exception {
		try {			
			sessionFactory.getCurrentSession().merge(plan);
			return true;	
			
		} catch (Exception e) {
			throw e;
		}
	}

	public boolean deletePlantoFeature(long id) throws Exception {
		try {
			String qry = "delete from plan_feature where id = " + id + ";";
			int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			return (resultVal == 1) ? true : false;
		}catch (Exception e) {
			throw e;
		}
	}
	
	public List<PlanToFeature> listPlantoFeature(long planid){
		try {
			List<PlanToFeature> ptfList = (List<PlanToFeature>)this.sessionFactory.getCurrentSession().createCriteria(PlanToFeature.class)
					.add(Restrictions.eq("plan_id.id", planid)).list();
			return ptfList;
		}catch (Exception e) {
			throw e;
		}
	}
	
	public PlanToFeature getPFByPlanFeature(long planid, long featureid) {
		try {
			PlanToFeature ptf = (PlanToFeature)this.sessionFactory.getCurrentSession().createCriteria(PlanToFeature.class)
					.add(Restrictions.eq("plan_id.id", planid))
					.add(Restrictions.eq("feature_id.id", featureid)).list().get(0);
			return ptf;
		}catch (Exception e) {
			return null;
		}
	}
	
	public UsertoFeature getUFByUserFeature(long userid, long featureid) {
		try {
			UsertoFeature utf = (UsertoFeature)this.sessionFactory.getCurrentSession().createCriteria(UsertoFeature.class)
					.add(Restrictions.eq("user_id", userid))
					.add(Restrictions.eq("feature_id.id", featureid)).list().get(0);
			return utf;
		}catch (Exception e) {
			return null;
		}
	}
	
	public boolean upateCredits(CompanyCreditMonitor ccm) throws Exception {
		boolean isSuccess = false;
		sessionFactory.getCurrentSession().merge(ccm);
		isSuccess = true;

		return isSuccess;
	}

	public CompanyCreditMonitor getCompanyCreditMonitorByCmpy(long cmp_id) {
		try {
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(CompanyCreditMonitor.class)
					.add(Restrictions.eq("cmp_id", cmp_id));
			CompanyCreditMonitor ccm = (CompanyCreditMonitor) criteria.list().get(0);
			return ccm;
		} catch (Exception e) {
			log.error("getCompanyCreditMonitorByCmpy : ",e.getLocalizedMessage());
			return null;
		}
	}

	public boolean upadteTransactionSummary(CompanyTransactionSummary cts) throws Exception {

		boolean isSuccess = false;
		sessionFactory.getCurrentSession().merge(cts);
		isSuccess = true;

		return isSuccess;
	}

	public CompanyTransactionSummary getCompanyTxnSmryByGatewayAndFeature(long gateway_id, long feature_id) {
		try {
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(CompanyTransactionSummary.class)
					.add(Restrictions.eq("gateway_id", gateway_id)).add(Restrictions.eq("feature_id", feature_id));
			CompanyTransactionSummary cts = (CompanyTransactionSummary) criteria.list().get(0);
			return cts;
		} catch (Exception e) {
			log.error("getCompanyCreditMonitorByCmpy : ",e.getLocalizedMessage());
			return null;
		}
	}
	
	public ArrayList<String> getFeatureList(long userid, long plan_id) {

		ArrayList<String> feaList = new ArrayList<String>();

		String qry = " SELECT UF.feature_id,F.feature_name ,IF(IFNULL(UT.no_txn,'')='',(UF.txn_limit+UF.extra_txn_limit),((UF.txn_limit+UF.extra_txn_limit) - UT.no_txn)) AS available FROM "
				+ " user_feature UF  LEFT JOIN user_txn UT ON UF.feature_id = UT.feature_id AND UF.user_id=UT.user_id JOIN feature F ON UF.feature_id = F.id WHERE  UF.user_id="+userid+" AND "
				+ "UF.ENABLE=1 UNION SELECT PF.feature_id,F.feature_name ,IF(IFNULL(UT.no_txn,'')='',(PF.txn_limit+PF.extra_txn_limit),((PF.txn_limit+PF.extra_txn_limit) - UT.no_txn)) AS available"
				+ " FROM  plan_feature PF LEFT JOIN user_txn UT ON PF.feature_id = UT.feature_id JOIN feature F ON PF.feature_id = F.id WHERE  PF.plan_id="+plan_id+" AND PF.ENABLE=1 "
				+ " AND PF.feature_id NOT IN ( SELECT feature_id FROM user_feature WHERE user_id="+userid+" AND ENABLE=1  )";

		Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
		List res = query.list();
		if (!res.isEmpty()) {
			Object[] tuple = (Object[]) res.get(0);
			feaList.add(((BigInteger) tuple[0]).toString());
			feaList.add((String) tuple[1]);
			feaList.add(((BigInteger) tuple[2]).toString());
		}
		return feaList;
	}
	
	public int getUserFeatureAvailabilty(long userid, String fName,long plan_id) {

		int avialCnt = 0;
		try {
			String qry = " SELECT IF(IFNULL(UT.no_txn,'')='',(UF.txn_limit+UF.extra_txn_limit),"
				+ "((UF.txn_limit+UF.extra_txn_limit) - UT.no_txn)) AS available "
				+ " FROM  user_feature UF JOIN user_txn UT ON UF.feature_id = UT.feature_id AND UF.user_id=UT.user_id JOIN feature F ON "
				+ " UF.feature_id = F.id WHERE F.feature_name='"+fName+"' AND UF.user_id="+userid+" AND UF.ENABLE=1";
	
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();
			if(res.isEmpty())
			{
				 qry = "SELECT IF(IFNULL(UT.no_txn,'')='',(PF.txn_limit+PF.extra_txn_limit),"
				 	+ "((PF.txn_limit+PF.extra_txn_limit) - UT.no_txn)) AS available FROM  plan_feature PF LEFT JOIN user_txn UT " 
				 	+ "ON PF.feature_id = UT.feature_id JOIN feature F ON PF.feature_id = F.id WHERE  PF.plan_id="+plan_id+" AND"
				 	+ " F.feature_name='"+fName+ "' AND PF.ENABLE=1";
	
				 query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
				 res = query.list();
			}
			log.info(qry);
			if (!res.isEmpty()) {
				avialCnt =((BigInteger) res.get(0)).intValue();
//				Object[] tuple = (Object[]) res.get(0);
//				avialCnt=((BigInteger)tuple[0]).intValue();
			}
		}
		catch (Exception e) {
			log.error("getUserFeatureAvailabilty : " + e.getLocalizedMessage());
		}
		return avialCnt;
	}
	
	public ArrayList<Integer> getVPMAvailabilty(long userid, String fCode,long plan_id,String cb_planid) {

		int avialCnt = 0;
		ArrayList<Integer> vpmCnt = new ArrayList<Integer>();

		try {
			String qry = "SELECT id FROM user_feature WHERE enable=1 and user_id="+userid+" AND feature_code = '"+fCode+"';";
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();
//			long featureid = this.getFeatureId(fCode);
//			String curUTC = IrisservicesUtil.getCurrentTimeUTC();

			if(res.isEmpty())
			{
				if(!cb_planid.trim().equalsIgnoreCase("NA"))
					avialCnt = crService.getVPMPlanTxnCount(cb_planid);
				else {
					if(vpm_freecall == true) {
						avialCnt = 1;
					}else {
						avialCnt = 0;
					}
				}
				vpmCnt.add(avialCnt);
				vpmCnt.add(0);

//				qry = " INSERT INTO user_feature (`user_id`, `feature_id` ,`feature_code`, `enable` ,  `txn_limit` ,  `extra_txn_limit` ,  `resettype_id`,remaining_limit)" + 
//						" VALUES("+userid+","+featureid+",'VPM',1,"+avialCnt+",0,1,"+avialCnt+");";
//				int resultVal1 = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
//				
//				qry = " INSERT INTO user_txn (`user_id`, `feature_id` , `no_txn` , `last_reset` )" + 
//						" VALUES("+userid+","+featureid+",0,'"+curUTC+"');";
//				int resultVal2 = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
//				
//				qry = " INSERT INTO user_txn_history (`user_id`, `feature_id` , `no_txn` ,`txn_date` )" + 
//						" VALUES("+userid+","+featureid+",0,'"+curUTC+"');";
//				int resultVal3 = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
				
			}else {
			
//				qry = " SELECT IF(IFNULL(UT.no_txn,'')='',(UF.txn_limit+UF.extra_txn_limit),"
//					+ "((UF.txn_limit+UF.extra_txn_limit) - UT.no_txn)) AS available,UF.txn_limit+UF.extra_txn_limit as tot "
//					+ " FROM  user_feature UF LEFT JOIN user_txn UT ON UF.feature_id = UT.feature_id AND UF.user_id=UT.user_id JOIN feature F ON "
//					+ " UF.feature_id = F.id WHERE UF.feature_code='"+fCode+"' AND UF.user_id="+userid+" AND UF.ENABLE=1";
				
				qry = "SELECT UF.remaining_limit + UF.extra_txn_limit + UF.addon_limit AS available , UF.txn_limit AS tot FROM user_feature UF " + 
						"  WHERE enable=1 and user_id="+userid+" AND UF.feature_code='"+fCode+"';";
				
				query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
				res = query.list();
//				if(res.isEmpty())
//				{
//					 qry = "SELECT IF(IFNULL(UT.no_txn,'')='',(PF.txn_limit+PF.extra_txn_limit),"
//					 	+ "((PF.txn_limit+PF.extra_txn_limit) - UT.no_txn)) AS available, PF.txn_limit+PF.extra_txn_limit as tot FROM  plan_feature PF LEFT JOIN user_txn UT " 
//					 	+ "ON PF.feature_id = UT.feature_id JOIN feature F ON PF.feature_id = F.id WHERE  PF.plan_id="+plan_id+" AND"
//					 	+ " F.feature_name='"+fName+ "' AND PF.ENABLE=1";
//		
//					 query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
//					 res = query.list();
//				}
				//log.info(qry);
				if (!res.isEmpty()) {
					Object[] tuple = (Object[]) res.get(0);
					int avail = ((BigInteger) tuple[0]).intValue();
					if(avail < 0)
						avail = 0;
					
					vpmCnt.add(avail); // available
					vpmCnt.add(((Integer) tuple[1]).intValue()); // total
					
				}
			}
		}
		catch (Exception e) {
			log.error("getVPMAvailabilty : " + e.getLocalizedMessage());
		}
		return vpmCnt;
	}
	
	public boolean updateUserTransaction(UserTransaction uTxn) throws Exception{
		try {			
			sessionFactory.getCurrentSession().merge(uTxn);
			return true;	
			
		} catch (Exception e) {
			throw e;
		}
	}

	public UserTransaction getUserTransactionByUserFeature(long user_id, String feature) {
		try {
			String qry = " SELECT * FROM user_txn WHERE feature_id = (SELECT id FROM feature WHERE feature_name='"+feature+"')"
					+ " AND user_id="+user_id+";";
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry).addEntity(UserTransaction.class);
			UserTransaction txn =  (UserTransaction) query.list().get(0); 
			return txn;
		} catch (Exception e) {
			log.error("getUserTransaction : ",e.getLocalizedMessage());
			return null;
		}
	}
	
	public boolean updateUserTransactionHistory(UserTransactionHistory uTxnHistory) throws Exception{
		try {			
			sessionFactory.getCurrentSession().merge(uTxnHistory);
			return true;	
			
		} catch (Exception e) {
			throw e;
		}
	}
	
	public UserTransactionHistory getUserTransactionHistoryByUserFeature(long user_id, String feature,String date) {
		try {
			String qry = "SELECT * FROM user_txn_history WHERE feature_id = (SELECT id FROM feature WHERE feature_name='"+feature+"')"
					+ " AND user_id="+user_id+" AND txn_date='"+date+"';";
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry).addEntity(UserTransactionHistory.class);
			UserTransactionHistory history = (UserTransactionHistory)query.list().get(0); 
			return history;
		} catch (Exception e) {
			log.error("getUserTransactionHistory : ",e.getLocalizedMessage());
			return null;
		}
	}	
	
	public boolean saveOrUpdateUserTxn(long user_id,long feature_id) {

		try {
			Session ses = sessionFactory.getCurrentSession();
			String qry = "SELECT * FROM user_txn WHERE feature_id = "+feature_id+" AND user_id="+user_id+";";

			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry).addEntity(UserTransaction.class);
			UserTransaction userTxn = !(query.list().isEmpty()) ? (UserTransaction)(query.list().get(0)) : null; 

			if(userTxn != null) {
				String updateQry = "UPDATE user_txn SET no_txn=no_txn+1 WHERE feature_id = "+feature_id+""
						+ " AND user_id="+user_id+";";

				int insertCount  = ses.createSQLQuery(updateQry).executeUpdate();
				log.info("updateQry : "+insertCount);
			}
			else {
				String insertQry = "INSERT INTO user_txn( user_id,feature_id,no_txn) VALUES("
						+ ""+user_id+","+feature_id+",1);";

				int insertCount  = ses.createSQLQuery(insertQry).executeUpdate();
				log.info("insertCount : "+insertCount);
			}
		}catch(Exception e) {
			log.error("Exception in saveOrUpdateUserTxn: "+e.getLocalizedMessage());
			return false;
		}		
		return true;
	}

	public boolean saveOrUpdateUserTxnHistory(long user_id,long feature_id) {

		String cur_date = IrisservicesUtil.getUtcDateTime();
		try {
			Session ses = sessionFactory.getCurrentSession();
			String qry = "SELECT * FROM user_txn_history WHERE feature_id = "+feature_id+" AND user_id="+user_id+" AND DATE(txn_date)=DATE('"+cur_date+"');";

			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry).addEntity(UserTransactionHistory.class);
			UserTransactionHistory history = !(query.list().isEmpty()) ? (UserTransactionHistory)(query.list().get(0)) : null; 


			if(history != null) {
				String updateQry = "UPDATE user_txn_history SET no_txn=no_txn+1 WHERE feature_id = "+feature_id+""
						+ " AND user_id="+user_id+" AND DATE(txn_date)=DATE('"+cur_date+"')";

				int insertCount  = ses.createSQLQuery(updateQry).executeUpdate();
				log.info("updateQry : "+insertCount);
			}
			else {
				String insertQry = "INSERT INTO user_txn_history( user_id,feature_id,no_txn,txn_date) VALUES("
						+ ""+user_id+","+feature_id+",1,'"+cur_date+"');";

				int insertCount  = ses.createSQLQuery(insertQry).executeUpdate();
				log.info("insertCount : "+insertCount);
			}
		}catch(Exception e) {
			log.error("Exception in saveOrUpdateUserTxnHistory: "+e.getLocalizedMessage());
			log.error("saveOrUpdateUserTxnHistory details not found for : "+ user_id);
			return false;
		}		
		return true;
	}

	@Override
	public float getIosPlanPrice(long planid, long periodid) {
		try {
			String qry = "SELECT ios_price FROM plan_to_period WHERE plan_id=" + planid + " AND sub_period_id="
					+ periodid + ";";

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);

			List res = query.list();
			float price = 0;

			if (!res.isEmpty())
				price = (float) res.get(0);

			return price;
		} catch (Exception e) {
			log.error("Exception in getIosPlanPrice: " + e.getLocalizedMessage());
			return 0;
		}
	}

	@Override
	public VersionMapping getVersionMapping(String app_version, String os_type) {
		log.info("Entering getVersionMapping for app_version " +app_version+ " and os_type " +os_type);
		try {
			String qry = "SELECT *  FROM version_mapping WHERE app_version=:appver AND os_type=:ostype";
			Query query = slave3SessionFactory.getCurrentSession().createSQLQuery(qry).addEntity(VersionMapping.class);
			query.setParameter("appver", app_version);
			query.setParameter("ostype", os_type);
			VersionMapping verObj = (VersionMapping) query.list().get(0);
			return verObj;
		}catch (Exception e) {
			log.error("Get VersionMapping : ",e.getLocalizedMessage());
		}
		return null;
	}
		
	public JSubscriptionPlanReport getInappSubscriptionByUser(long userid) {
		try {
			String curUTC = IrisservicesUtil.getCurrentTimeUTC();
			String qry = " SELECT P.id AS planid,P.plan_name,P.description,P.alert_setting,SP.id AS periodid, SP.period_name,"
				+ "PP.ios_planid,PP.ios_price,original_transaction_id,I.created_date,I.start_date,I.expires_date,I.renew_status,"
				+ "I.auto_renew FROM plan_to_period PP JOIN plan P ON P.id = PP.plan_id JOIN `inapp_subscription` I "
				+ "ON PP.ios_planid=I.product_id JOIN sub_period SP ON SP.id=PP.sub_period_id  WHERE "
				+ " DATE(expires_date)>=DATE('"+curUTC+"') AND I.user_id="+userid+" limit 1;";
	
			
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();
			if (!res.isEmpty()) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
				
				Object[] subObj = (Object[]) res.get(0);
				
				String planid=subObj[0].toString();
				String planname=subObj[1].toString();
				String desc=subObj[2].toString();
				boolean alert_setting=subObj[3].toString().equalsIgnoreCase("1")? true : false;
				
				long periodid = ((BigInteger) subObj[4]).longValue();
				String period=subObj[5].toString();
				String product_id=subObj[6].toString();
				String price="$"+(subObj[7]).toString();
				String cbSubId=subObj[8].toString();
				String createDate=sdf.format(((Timestamp)subObj[9]).getTime());
				String startedAt=sdf.format(((Timestamp)subObj[10]).getTime());
				String nextrenew_date=sdf.format(((Timestamp)subObj[11]).getTime());
				String status=subObj[12].toString();
				boolean setupAutoRenewal=(boolean)subObj[13];
				boolean cancel = false;
				String cbSubStatus="";	
				//:TODO cancel based on subs status - applicable for in trial 
								
				Date nextPaymentDate = sdf.parse(nextrenew_date);
				Date todayDate = sdf.parse(IrisservicesUtil.getCurrentTimeUTC());
				
				int days_remaining=0;				
				
				long difference = nextPaymentDate.getTime() - todayDate.getTime();
				days_remaining = (int) (difference / (1000 * 60 * 60 * 24));
			
				List<JGatewaySubSetup> listJGatewaySubSetup=null;
				String autoRenewalStatus="";				
				String updateDate="";				
				String availCredit="0";
				String chargebeeid="NA";
				JSubscriptionPlanReport jpr = new JSubscriptionPlanReport();

				jpr = new JSubscriptionPlanReport(planid, planname, price, period, nextrenew_date, days_remaining, status, setupAutoRenewal,
						listJGatewaySubSetup, desc, autoRenewalStatus, createDate, updateDate, cbSubId, cbSubStatus, availCredit, alert_setting,
						chargebeeid, startedAt, product_id,String.valueOf(periodid),cancel,1,false);
				
				return jpr;	
			}else
				return null;
		}catch (Exception e) {
			return null;
		}
	}
	
	@Override
	public List<JVPMPlan> getVPMPlanList(String plantype) {
		List<JVPMPlan>  vpmList = new ArrayList<JVPMPlan>();
		try {
		String qry = "SELECT plan_id, sub_period_id,chargebee_planid,offer_content FROM plan_to_period WHERE"
				+ " enable =1 and plan_id in (select id from plan where plan_type ='" + plantype + "' );";

		Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
		List<Object[]> res = query.list();

		if (!res.isEmpty()) {
			String cb_planid="NA";
			long plan_id=0;
			long period_id =0;
			String display_content="NA";
			for(Object[] planObj:res)
			{
				//Object[] tuple = (Object[]) res.get(0);
				plan_id = ((BigInteger)planObj[0]).longValue();
				period_id=((BigInteger)planObj[1]).longValue();	
				cb_planid=(String)planObj[2];
				display_content =(String) planObj[3];
								
				JVPMPlan plan = new JVPMPlan(cb_planid, plan_id, period_id, display_content);
				vpmList.add(plan);
			}
			
		}
		}catch (Exception e) {
			return vpmList; 
		}
		return vpmList;
	}
	
	@Override
	public int getVPMPlanTxnCount(String cb_planid) {
		try {
			String qry = "SELECT txn_limit FROM plan_feature  WHERE plan_period_id = ( SELECT id FROM plan_to_period "
					+ "WHERE chargebee_planid= '"+cb_planid+"');";

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);

			List res = query.list();
			int cnt = 0;

			if (!res.isEmpty())
				cnt = (int)res.get(0);

			return cnt;
		} catch (Exception e) {
			log.error("Exception in getVPMPlanTxnCount: " + e.getLocalizedMessage());
			return 0;
		}
	}
	
	@Override
	public List<PlanToPeriod> listPlanToPeriodByPlanId(long plan_id) {
		try {
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(SubscriptionPlan.class).add(Restrictions.eq("id", plan_id));
			SubscriptionPlan plan = (SubscriptionPlan) criteria.list().get(0);
			List<PlanToPeriod> planToPeriodList = this.sessionFactory.getCurrentSession().createCriteria(PlanToPeriod.class).add(Restrictions.eq("plan_id", plan)).list();
			return planToPeriodList;
		}catch (Exception e) {
			log.error("list plan to Period by plan id: ",e.getLocalizedMessage());
			return null;
		}
	}
	
	@Override
	public String[] getCouponId(String cbPlanid) {
		String[] planDetails = new String[2];
		String cb_coupon_id = "NA";
		String cb_addonid = "NA";
		try {
			String qry = "SELECT cb_coupon_id,cb_addon_id FROM plan_to_period WHERE chargebee_planid ='" + cbPlanid + "';";

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			
			List res = query.list();
			if (!res.isEmpty()) {
				Object[] tuple = (Object[]) res.get(0);
				cb_coupon_id = tuple[0].toString();
				cb_addonid = tuple[1].toString();
			}

			planDetails[0] = cb_coupon_id;
			planDetails[1] = cb_addonid;

			log.debug("chargebeePlanById : " + cbPlanid + " : " + cb_coupon_id);
		} catch (Exception e) {
			log.error("list plan to Period by plan id: ",e.getLocalizedMessage());
		}
		return planDetails;
	}
	
	@Override
	public JResponse upgradePlanList(long userid, long curplan_id, long curperiod_id,boolean freetrial,String country) {
		JResponse response = new JResponse();
		try {
			long device_count= crService.getDeviceCountByUser(userid, 1);
			
			String qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pp.plan_price,pl.orderno,pp.free_trial_days,"
					+ "pp.content_1,pp.content_2,pp.content_3,pp.content_4,pp.strike_price FROM plan_to_upgrade pu "
					+ " JOIN plan_to_period pp ON pu.upgradeplan_id = pp.id JOIN plan pl ON pl.id = pp.plan_id JOIN sub_period sp ON "
					+ " sp.id=pp.sub_period_id WHERE pl.plan_ver='V2' AND pl.plan_type='Data-Plan' AND pl.device_cnt >="+device_count+" AND pl.enable=1 AND pl.custom=0 AND"
					+ " pp.enable=1 AND pp.custom=0 AND pu.plan_to_period_id IN(SELECT id FROM plan_to_period WHERE  plan_id="
					+ curplan_id+" AND sub_period_id=" +curperiod_id+" ) AND pp.country_code= '"+country+"' ORDER BY pl.orderno ASC,pp.plan_id,sp.id;";
			
			if(device_count>device_count_config) {
				
				UserV4 user = null; 
				try {
					user = userServiceV4.verifyAuthV4("id", userid+"");
				} catch (InvalidAuthoException e) {
					log.error("Error in upgradePlanList :: user id : "+userid+" :: Session Name : sessionFactory :: error : "+e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg","Contact Support");
					return response; 
				}
				
				CompanyConfig companyConfig = companyService.getCompanyConfig(user.getCmpId());
				
				if( companyConfig != null && companyConfig.isCustom_plan_enable() )
					qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pp.plan_price,pl.orderno,pp.free_trial_days,"
						+ "pp.content_1,pp.content_2,pp.content_3,pp.content_4,pp.strike_price FROM plan_to_upgrade pu "
						+ " JOIN plan_to_period pp ON pu.upgradeplan_id = pp.id JOIN plan pl ON pl.id = pp.plan_id JOIN sub_period sp ON "
						+ " sp.id=pp.sub_period_id WHERE pl.plan_ver='V2' AND pl.plan_type='Data-Plan' AND pl.device_cnt ="+device_count+" AND pl.enable=1 AND"
						+ " pp.enable=1 AND pu.plan_to_period_id IN(SELECT id FROM plan_to_period WHERE  plan_id="
						+ curplan_id+" AND sub_period_id=" +curperiod_id+" ) AND pp.country_code= '"+country+"' ORDER BY pl.orderno ASC,pp.plan_id,sp.id;";
			}

			
		log.info(qry);
		Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
		List<Object[]> planResp = query.list();
		long plan_id = 0;
		long per_id = 0;
		String plan_name="";
		boolean freetrial_avail = false;
		String buynow_desc = "Get Peace of Mind";
		String trial_desc ="";
		int trial_days =0;
		String content1 = "";
		String content2 = "";
		String content3 = "";
		String content4 = "";
		String strike_price_v2 = "NA";
		boolean is_best_seller = false; 

		ArrayList<JSubscriptionPlan> plan_list = new ArrayList<JSubscriptionPlan>();
		ArrayList<JSubscriptionPeriod> period_list = new ArrayList<JSubscriptionPeriod>();
		
		if (!planResp.isEmpty()) {
			int size = planResp.size();
			log.info("upgradePlanList : size:" + size);
			JSubscriptionPlan subsPlan = new JSubscriptionPlan();
			Object[] tuple = (Object[]) planResp.get(0);
			
			if(size==1) {
				plan_id = ((BigInteger) tuple[0]).longValue();
				per_id = ((BigInteger) tuple[2]).longValue();
				String feature_list = (String) tuple[3];
				String plan_price =  (String) tuple[4];
				plan_name = (String) tuple[1];
				trial_days = (int)tuple[6];
				content1 = (String) tuple[7];
				content2 = (String) tuple[8];
				content3 = (String) tuple[9];
				content4 = "NA";//(String) tuple[10];
				strike_price_v2 = (((Integer)tuple[11]) == 0) ? "NA" : "$"+((Integer) tuple[11]);
				
//				if( strike_price_v2 != null && !strike_price_v2.isEmpty() && !strike_price_v2.equalsIgnoreCase("NA") ) {
//					is_best_seller = true;
//				}

				if(freetrial && trial_days > 0) {
					freetrial_avail = true;
					if(trial_days == 1) {
						trial_desc = "<hr style='width:19%;color: #717171;text-align:center;margin-top: 15px;margin-bottom: -10px;'>"+
								 "<p style='font-size: 17px;margin-bottom: 0px;color:#000;font-family: 'Montserrat', sans-serif;font-weight: 600;'>"
								 + trial_days +" day Trial"+"</p></center>";

						buynow_desc = "Start your "+ trial_days+" day free Trial";
						content4 ="1 day Trial";
					}else {
						trial_desc = "<hr style='width:19%;color: #717171;text-align:center;margin-top: 15px;margin-bottom: -10px;'>"+
								 "<p style='font-size: 17px;margin-bottom: 0px;color:#000;font-family: 'Montserrat', sans-serif;font-weight: 600;'>"
								 + trial_days +" days Trial"+"</p></center>";

						buynow_desc = "Start your "+ trial_days+" days free Trial";
						content4 = trial_days +" days Trial";
					}
					plan_price = plan_price.replace("</center>", trial_desc);

				}else {
					freetrial_avail = false;
					trial_desc ="";
					buynow_desc = "Get Peace of Mind";
				}
				
				subsPlan.setPlanid(plan_id);
				subsPlan.setPlanname(plan_name);
				subsPlan.setFeature_list(feature_list);
				subsPlan.setFreetrial_avail(freetrial_avail);
				subsPlan.setIs_best_seller(is_best_seller);
				
				JSubscriptionPeriod periodObj = new JSubscriptionPeriod();
				periodObj.setPeriod_id(per_id);
				periodObj.setPlan_id(plan_id);
				
				periodObj.setPrice_detail(plan_price);
				periodObj.setBuynow_desc(buynow_desc);
				periodObj.setContent_1(content1);
				periodObj.setContent_2(content2);
				periodObj.setContent_3(content3);
				periodObj.setContent_4(content4);
				periodObj.setStrike_price_v2(strike_price_v2);
				
				period_list.add(periodObj);
				subsPlan.setPeriod_list(period_list);
				plan_list.add(subsPlan);
			}
			
			for (int i = 0; i < size-1; i++) {		
				is_best_seller = false;
				tuple = (Object[]) planResp.get(i);
				Object[] tupleNext = (Object[]) planResp.get(i+1);
				plan_id = ((BigInteger) tuple[0]).longValue();
				per_id = ((BigInteger) tuple[2]).longValue();
				String feature_list = (String) tuple[3];
				String plan_price =  (String) tuple[4];
				plan_name = (String) tuple[1];
				trial_days = (int)tuple[6];
				content1 = (String) tuple[7];
				content2 = (String) tuple[8];
				content3 = (String) tuple[9];
				content4 = "NA";//(String) tuple[10];
				strike_price_v2 = (((Integer)tuple[11]) == 0) ? "NA" : "$"+((Integer) tuple[11]);
				
//				if( strike_price_v2 != null && !strike_price_v2.isEmpty() && !strike_price_v2.equalsIgnoreCase("NA") ) {
//					is_best_seller = true;
//				}
				
				if(freetrial && trial_days > 0) {
					freetrial_avail = true;

					if(trial_days == 1) {
						trial_desc = "<hr style='width:19%;color: #717171;text-align:center;margin-top: 15px;margin-bottom: -10px;'>"+
								 "<p style='font-size: 17px;margin-bottom: 0px;color:#000;font-family: 'Montserrat', sans-serif;font-weight: 600;'>"
								 + trial_days +" day Trial"+"</p></center>";
						content4 = trial_days +" day Trial";
						buynow_desc = "Start your "+ trial_days+" day free Trial";
					}else {
						trial_desc = "<hr style='width:19%;color: #717171;text-align:center;margin-top: 15px;margin-bottom: -10px;'>"+
								 "<p style='font-size: 17px;margin-bottom: 0px;color:#000;font-family: 'Montserrat', sans-serif;font-weight: 600;'>"
								 + trial_days +" days Trial"+"</p></center>";

						buynow_desc = "Start your "+ trial_days+" days free Trial";
						content4 = trial_days +" days Trial";
					}
					plan_price = plan_price.replace("</center>", trial_desc);
				}else {
					//freetrial_avail = false;
					trial_desc ="";
					buynow_desc = "Get Peace of Mind";
				}
				subsPlan.setPlanid(plan_id);
				subsPlan.setPlanname(plan_name);
				subsPlan.setFeature_list(feature_list);
				subsPlan.setFreetrial_avail(freetrial_avail);
				subsPlan.setIs_best_seller(is_best_seller);
				
				long plan_id_next = ((BigInteger) tupleNext[0]).longValue();
				String plan_name_next = (String) tupleNext[1];
				long per_id_next = ((BigInteger) tupleNext[2]).longValue();
				String feature_list_next = (String) tupleNext[3];
				String plan_price_next =  (String) tupleNext[4];
				int trial_days_next = (int)tupleNext[6];
				boolean freetrial_avail_next = false;
				String buynow_desc_next = "Get Peace of Mind";
				String trial_desc_next = "";
				String content1_next = (String) tupleNext[7];
				String content2_next = (String) tupleNext[8];
				String content3_next = (String) tupleNext[9];
				String content4_next ="NA";// (String) tupleNext[10];
				String strike_price_v2_next = (((Integer)tupleNext[11]) == 0) ? "NA" : "$"+((Integer) tupleNext[11]);
				boolean is_best_seller_next = false;
				
//				if( strike_price_v2_next != null && !strike_price_v2_next.isEmpty() && !strike_price_v2_next.equalsIgnoreCase("NA") ) {
//					is_best_seller_next = true;
//				}
				
				if(freetrial && trial_days_next > 0) {
					freetrial_avail_next = true;
					if(trial_days_next == 1) {
						trial_desc_next = "<hr style='width:19%;color: #717171;text-align:center;margin-top: 15px;margin-bottom: -10px;'>"+
								 "<p style='font-size: 17px;margin-bottom: 0px;color:#000;font-family: 'Montserrat', sans-serif;font-weight: 600;'>"
								 + trial_days_next +" day Trial"+"</p></center>";

						buynow_desc_next = "Start your "+ trial_days_next+" day free Trial";
						content4_next = trial_days_next +" day Trial";
					}else {
						trial_desc_next = "<hr style='width:19%;color: #717171;text-align:center;margin-top: 15px;margin-bottom: -10px;'>"+
								 "<p style='font-size: 17px;margin-bottom: 0px;color:#000;font-family: 'Montserrat', sans-serif;font-weight: 600;'>"
								 + trial_days_next +" days Trial"+"</p></center>";

						buynow_desc_next = "Start your "+ trial_days_next+" days free Trial";
						content4_next = trial_days_next +" days Trial";
					}
					plan_price_next = plan_price_next.replace("</center>", trial_desc_next);

				}else {
					//freetrial_avail_next = false;
					trial_desc_next ="";
					buynow_desc_next = "Get Peace of Mind";
				}
				
				JSubscriptionPeriod periodObj = new JSubscriptionPeriod();
				
				periodObj.setPeriod_id(per_id);
				periodObj.setPlan_id(plan_id);
				periodObj.setPrice_detail(plan_price);
				periodObj.setBuynow_desc(buynow_desc);
				periodObj.setContent_1(content1);
				periodObj.setContent_2(content2);
				periodObj.setContent_3(content3);
				periodObj.setContent_4(content4);
				periodObj.setStrike_price_v2(strike_price_v2);
				
				period_list.add(periodObj);
				
				if (plan_id!=plan_id_next) {
					subsPlan.setPeriod_list(period_list);
					plan_list.add(subsPlan);	
					subsPlan = new JSubscriptionPlan();	
					period_list = new ArrayList<JSubscriptionPeriod>();
					freetrial_avail = false;
				}
				
				if ( i == (size-2)) {
					JSubscriptionPeriod periodObj_next = new JSubscriptionPeriod();

//					if(freetrial && trial_days_next > 0) {
//						freetrial_avail_next = true;
//						if(trial_days_next == 1) {
//							trial_desc = "<hr style='width:19%;color: #717171;text-align:center;margin-top: 15px;margin-bottom: -10px;'>"+
//									 "<p style='font-size: 17px;margin-bottom: 0px;color:#000;font-family: 'Montserrat', sans-serif;font-weight: 600;'>"
//									 + trial_days_next +" day Trial"+"</p></center>";
//
//							buynow_desc_next = "Start your "+ trial_days_next+" day free Trial";
//						}else {
//							trial_desc = "<hr style='width:19%;color: #717171;text-align:center;margin-top: 15px;margin-bottom: -10px;'>"+
//									 "<p style='font-size: 17px;margin-bottom: 0px;color:#000;font-family: 'Montserrat', sans-serif;font-weight: 600;'>"
//									 + trial_days_next +" days Trial"+"</p></center>";
//
//							buynow_desc = "Start your "+ trial_days_next+" days free Trial";
//						}
//						plan_price_next = plan_price_next.replace("</center>", trial_desc);
//
//					}else {
//						freetrial_avail_next = false;
//						trial_desc ="";
//						buynow_desc = "Buy Now";
//					}
					
					periodObj_next.setPeriod_id(per_id_next);
					periodObj_next.setPlan_id(plan_id_next);
					periodObj_next.setPrice_detail(plan_price_next);
					periodObj_next.setBuynow_desc(buynow_desc_next);
					periodObj_next.setContent_1(content1_next);
					periodObj_next.setContent_2(content2_next);
					periodObj_next.setContent_3(content3_next);
					periodObj_next.setContent_4(content4_next);
					periodObj_next.setStrike_price_v2(strike_price_v2_next);
					
					if(plan_id == plan_id_next) {
						period_list.add(periodObj_next);
					}
					else {
						subsPlan = new JSubscriptionPlan();	
						period_list = new ArrayList<JSubscriptionPeriod>();
						
						subsPlan.setPlanid(plan_id_next);
						subsPlan.setPlanname(plan_name_next);
						subsPlan.setFeature_list(feature_list_next);
						subsPlan.setFreetrial_avail(freetrial_avail_next);
						subsPlan.setIs_best_seller(is_best_seller_next);
						
						period_list.add(periodObj_next);
						
					}
					subsPlan.setPeriod_list(period_list);
					plan_list.add(subsPlan);
				}
			}
			
		}
		response.put("Status", 1);
		response.put("Msg","Success");

		response.put("planlist", plan_list);
		// for custom plan
		response.put("planname", "To add monitor or to customize the existing plan, please contact Support");
		response.put("contactnumber", supportcontactnumber.get(country));
		response.put("email", supportemail.get(country));

		}catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg","Contact Support");

			return response; 
		}
		return response;
	} 

	@Override
	public String getPlanVersion(String cb_planid) {
		String plan_ver = "";
		
		try {
			String qry = "	SELECT plan_ver FROM plan P JOIN plan_to_period PP ON"
					+ " P.id = PP.plan_id WHERE PP.chargebee_planid = '"+cb_planid+"';";

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);

			List res = query.list();

			if (!res.isEmpty())
				plan_ver = (String)res.get(0);

		} catch (Exception e) {
			log.error("Exception in getPlanVersion: " + e.getLocalizedMessage());
		}
		
		return plan_ver;

	}
	
	@Override
	public ArrayList<JFeatureCredit> getSettingFeatures(long user_id) {
		log.info("Entering getSettingFeatures DAO Impl for user id : "+user_id);
		ArrayList<JFeatureCredit> setting_features = new ArrayList<JFeatureCredit>();
		String feature_code = "";
		long alerttype_id = 0;
		int f_cnt = 0;
		try {
			String qry = "SELECT F.feature_code ,UF.txn_limit, alerttype_id FROM feature F JOIN user_feature UF ON UF.feature_id=F.id WHERE UF.enable=1 AND UF.user_id="
					+user_id+" AND alerttype_id>0 GROUP BY alerttype_id 	UNION ALL 	SELECT F.feature_code ,UF.txn_limit, alerttype_id FROM feature F"
					+ " JOIN user_feature UF ON UF.feature_id=F.id WHERE UF.enable=1 AND UF.user_id="+user_id+" AND F.feature_code IN('N_MOBILE','N_EMAIL','N_DEVICE','PUSH_NOTIFY');";

			Query query = this.slave5SessionFactory.getCurrentSession().createSQLQuery(qry);
			
			List res = query.list();
			
			if (!res.isEmpty()) {
				for (int i=0; i<res.size(); i++ ) {
					Object[] tuple = (Object[]) res.get(i);
					feature_code = tuple[0].toString();
					f_cnt = ((Integer) tuple[1]);
					alerttype_id = ((BigInteger) tuple[2]).longValue();
					
					JFeatureCredit featureObj = new JFeatureCredit(alerttype_id,feature_code,f_cnt);
					setting_features.add(featureObj);
				}
			}

			log.info("getSettingFeatures : " + user_id);
		} catch (Exception e) {
			log.error("getSettingFeatures: Exep: ",e.getLocalizedMessage());
		}
		return setting_features;
	}

	@Override
	public List<JAddonPlan> getAddonPlanList(long plan_id, long period_id) {
		log.info("Entered getaddonplans list DAO IMPL");

		List<JAddonPlan>  addonList = new ArrayList<JAddonPlan>();
		try {
			String qry = "SELECT A.cb_addonid,plan_name,price FROM plan_addon PA JOIN addon A ON A.id= PA.addon_id WHERE plan_id="+plan_id+" ;";

			Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);
			List<Object[]> res = query.list();

			if (!res.isEmpty()) {
				double price = 0.0;
				String plan_price = "NA";
				String cb_addonid = "NA";
				String plan_name = "";
				DecimalFormat df = new DecimalFormat("##.##");
				for (Object[] planObj : res) {
					
					cb_addonid = (String) planObj[0];
					plan_name = (String) planObj[1];
					price = Double.parseDouble( planObj[2]+"");
					plan_price = "$"+df.format(price);

					JAddonPlan plan = new JAddonPlan(plan_name, cb_addonid, plan_price,price);
					addonList.add(plan);
				}

			}
		}catch (Exception e) {
			log.error("getAddonPlanList : "+e.getLocalizedMessage());
			return addonList; 
		}
		return addonList;
	
	}
	
	@Override
	public ArrayList<JAlertRemaining> getalertslimit(long user_id, String alertlimit_basedon) {
		log.info("Entered getalertslimit DAO IMPL for user id : "+user_id);

		ArrayList<JAlertRemaining> alertRemaingList = new ArrayList<JAlertRemaining>();
		ArrayList<JATCount> atCountList = new ArrayList<JATCount>();

		try {
			String qry ="";
			
			if(alertlimit_basedon.equalsIgnoreCase("alert-based"))
				qry = "SELECT AT.name, (remaining_limit+extra_txn_limit+addon_limit) AS remaining,UF.txn_limit + UF.extra_txn_limit  AS total,"
					+ " AT.id,UF.unlimited_cr,F.feature_code FROM user_feature UF JOIN feature F ON UF.feature_id = F.id JOIN alerttype AT ON AT.id = F.alerttype_id"
					+ " WHERE UF.enable =1 AND UF.user_id="+user_id+" AND F.alerttype_id>0  AND UF.feature_code LIKE '%_COUNT'  ORDER BY alerttype_id ;";
			else
				qry = "SELECT AT.name, (remaining_limit+extra_txn_limit+addon_limit) AS remaining,UF.txn_limit + UF.extra_txn_limit  AS total,"
						+ " AT.id,UF.unlimited_cr,F.feature_code FROM user_feature UF JOIN feature F ON UF.feature_id = F.id JOIN alerttype AT ON AT.id = F.alerttype_id"
						+ " WHERE UF.enable =1 AND UF.user_id="+user_id+" AND F.alerttype_id>0  AND UF.feature_code NOT LIKE '%_COUNT'  ORDER BY alerttype_id ;";
				
				Query query = this.slave1SessionFactory.getCurrentSession().createSQLQuery(qry);
				List<Object[]> res = query.list();
				String color_code = "";
				
				if (!res.isEmpty()) {
					int size = res.size();
					String at_name ;

					if(size == 1) {
						Object[] alertObj = res.get(0);
						String alertname = (String) alertObj[0];
						int remaining_cnt = ((BigInteger) alertObj[1]).intValue();
						int total_cnt = ((BigInteger) alertObj[2]).intValue();
						long alert_type = ((BigInteger) alertObj[3]).longValue();
						boolean unlimited = (boolean) alertObj[4];
						String feature_code = (String) alertObj[5];
					
						JAlertRemaining alertRemainObj = new JAlertRemaining(alert_type, alertname);

						if(feature_code.contains("MAIL")) {
							at_name = "Email Alerts";
							color_code = "#9DCD6F";
						}else {
							at_name = "Text Alerts";
							color_code = "#64B5E3";
						}
						
						JATCount jATCount = new JATCount(at_name, unlimited, total_cnt, remaining_cnt, color_code,0);

						if(feature_code.contains("COUNT") && !unlimited) {
							at_name = "Email Alerts";
							color_code = "#9DCD6F";
							JATCount jATCount1 = new JATCount(at_name, unlimited, total_cnt, remaining_cnt, color_code,0);
							
							atCountList.add(jATCount1);
						}
						
						atCountList.add(jATCount);
						alertRemainObj.setAtCountList(atCountList);
						
						alertRemaingList.add(alertRemainObj);
				} else {

					for (int i = 0; i < size - 1; i++) {
						Object[] alertObj = res.get(i);
						Object[] alertObjNext = res.get(i + 1);
						String alertname = (String) alertObj[0];
						int remaining_cnt = ((BigInteger) alertObj[1]).intValue();
						int total_cnt = ((BigInteger) alertObj[2]).intValue();
						long alert_type = ((BigInteger) alertObj[3]).longValue();
						boolean unlimited = (boolean) alertObj[4];
						String feature_code = (String) alertObj[5];

						String alertnameNext = (String) alertObjNext[0];
						int remaining_cntNext = ((BigInteger) alertObjNext[1]).intValue();
						int total_cntNext = ((BigInteger) alertObjNext[2]).intValue();
						long alert_typeNext = ((BigInteger) alertObjNext[3]).longValue();
						boolean unlimitedNext = (boolean) alertObjNext[4];
						String feature_codeNext = (String) alertObjNext[5];

						JAlertRemaining alertRemainObj = new JAlertRemaining(alert_type, alertname);

						if (feature_code.contains("MAIL")) {
							at_name = "Email Alerts";
							color_code = "#9DCD6F";
						} else {
							at_name = "Text Alerts";
							color_code = "#64B5E3";
						}

						JATCount jATCount = new JATCount(at_name, unlimited, total_cnt, remaining_cnt, color_code,0);

						if(feature_code.contains("COUNT") && !unlimited) {
							at_name = "Email Alerts";
							color_code = "#9DCD6F";
							JATCount jATCount1 = new JATCount(at_name, unlimited, total_cnt, remaining_cnt, color_code,0);
							
							atCountList.add(jATCount1);
						}
						
						if (alert_type == alert_typeNext) {
							atCountList.add(jATCount);
						} else {
							atCountList.add(jATCount);

							alertRemainObj.setAtCountList(atCountList);

							alertRemaingList.add(alertRemainObj);

							atCountList = new ArrayList<JATCount>();
						}

						if (i == size - 2) {
							if (alert_type != alert_typeNext) {
								alertRemainObj = new JAlertRemaining(alert_typeNext, alertnameNext);
							}
							if (feature_codeNext.contains("MAIL")) {
								at_name = "Email Alerts";
								color_code = "#9DCD6F";
							} else {
								at_name = "Text Alerts";
								color_code = "#64B5E3";
							}
							JATCount jATCountLast = new JATCount(at_name, unlimitedNext, total_cntNext,
									remaining_cntNext, color_code,0);
							
							if(feature_codeNext.contains("COUNT") && !unlimited) {
								at_name = "Email Alerts";
								color_code = "#9DCD6F";
								JATCount jATCount1 = new JATCount(at_name, unlimitedNext, total_cntNext, remaining_cntNext, color_code,0);
								
								atCountList.add(jATCount1);
							}
							
							atCountList.add(jATCountLast);
							alertRemainObj.setAtCountList(atCountList);
							alertRemaingList.add(alertRemainObj);
						}
					}
				}
				}
		}catch (Exception e) {
			log.info("getalertslimit: "+ e.getLocalizedMessage());
		}
		return alertRemaingList;	
	}
	
	@Override
	public JResponse getalertslimitV2(long user_id, String alertlimit_basedon, boolean enable_appnotify,String reqVer) {
		JResponse response = new JResponse();

		try {
			ArrayList<JAlertRemaining> alertRemaingList = new ArrayList<JAlertRemaining>();
			ArrayList<JATCount> atCountList = new ArrayList<JATCount>();

			String qry = "";
			boolean upgrade_flag = false;
			String upgrade_label = "";
			boolean unlimited_plan = false;
			boolean alert_based = true;
			String subQry = "";
			if (!enable_appnotify) {
				subQry = " AND UF.feature_code NOT LIKE '%_NOTIFY' ";
			}
			if (alertlimit_basedon.equalsIgnoreCase("alert-based")) {
				alert_based = true;
				qry = "SELECT AT.name, (remaining_limit+extra_txn_limit+addon_limit) AS remaining,UF.txn_limit + UF.extra_txn_limit  AS total,"
						+ " AT.id,UF.unlimited_cr,F.feature_code FROM user_feature UF JOIN feature F ON UF.feature_id = F.id JOIN alerttype AT ON AT.id = F.alerttype_id"
						+ " WHERE UF.enable =1 AND UF.user_id=" + user_id
						+ " AND F.alerttype_id>0  AND UF.feature_code LIKE '%_COUNT' ORDER BY CASE WHEN alerttype_id = 1 THEN 1 WHEN alerttype_id = 14 THEN 2 WHEN alerttype_id = 2 THEN 3 WHEN alerttype_id = 3 THEN 4 WHEN alerttype_id = 17 THEN 5 WHEN alerttype_id = 11 THEN 6 WHEN alerttype_id = 4 THEN 7 ELSE 8 END ;";
			}
			else {
				qry = "SELECT AT.name, (remaining_limit+extra_txn_limit+addon_limit) AS remaining,UF.txn_limit + UF.extra_txn_limit  AS total,"
						+ " AT.id,UF.unlimited_cr,F.feature_code FROM user_feature UF JOIN feature F ON UF.feature_id = F.id JOIN alerttype AT ON AT.id = F.alerttype_id"
						+ " WHERE UF.enable =1 AND UF.user_id=" + user_id
						+ " AND F.alerttype_id>0  AND UF.feature_code NOT LIKE '%_COUNT' " + subQry
						+ " ORDER BY CASE WHEN alerttype_id = 1 THEN 1 WHEN alerttype_id = 14 THEN 2 WHEN alerttype_id = 2 THEN 3 WHEN alerttype_id = 3 THEN 4 WHEN alerttype_id = 17 THEN 5 WHEN alerttype_id = 11 THEN 6 WHEN alerttype_id = 4 THEN 7 ELSE 8 END,feature_id ;";
				alert_based = false;
			}

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List<Object[]> res = query.list();
			String color_code = "";
			int alert_total = 0;
			int alert_remaining = 0;

			int type_total = 0;
			int type_remaining = 0;

			if (!res.isEmpty()) {
				int size = res.size();
				String at_name = "";
				String caution_desc = "80% of alerts used";
				if (size == 1) {
					Object[] alertObj = res.get(0);
					String alertname = (String) alertObj[0];
					if (alertname.equalsIgnoreCase("Device Not Reporting"))
						alertname = "Network";
					else
						alertname = alertname.replace(" Alert", "");
					int remaining_cnt = ((BigInteger) alertObj[1]).intValue();
					int total_cnt = ((BigInteger) alertObj[2]).intValue();
					long alert_type = ((BigInteger) alertObj[3]).longValue();
					boolean unlimited = (boolean) alertObj[4];
					String feature_code = (String) alertObj[5];
					alert_total = total_cnt;
					alert_remaining = remaining_cnt;
					double remain_percent = Math.round(((double)remaining_cnt / (double)total_cnt) * 100);

					type_total = total_cnt;
					type_remaining = remaining_cnt;
					JAlertRemaining alertRemainObj = new JAlertRemaining(alert_type, alertname);

					if (feature_code.contains("MAIL")) {
						at_name = "Email Alerts";
						color_code = "#545EE3";
					} else if (feature_code.contains("SMS")) {
						at_name = "Text Alerts";
						color_code = "#A17373";
					} else if (feature_code.contains("NOTIFY")) {
						at_name = "In-App Alerts";
						color_code = "#9DCD6F";
					} else {
						at_name = "Alerts Remaining";
						color_code = "#FF5D5D";
					}

					JATCount jATCount = new JATCount(at_name, unlimited, total_cnt, remaining_cnt, color_code,
							(int)remain_percent);

					atCountList.add(jATCount);
					alertRemainObj.setAlerttype_total(type_total);
					alertRemainObj.setAlerttype_remaining(type_remaining);
					alertRemainObj.setAtCountList(atCountList);

					alertRemaingList.add(alertRemainObj);
					// work arround for buddy customer. if txn limit > 1000 ; then instead show remaining, unlimited text will be displayed
					if((total_cnt >= ul_check_limit) && (alert_type == 1 || alert_type == 3))
						unlimited = true;
					
					if (unlimited) {
						
						upgrade_flag = false;
						upgrade_label = "All Alerts unlimited";
						unlimited_plan = true;
					}else if ((total_cnt * 0.25) > remaining_cnt) {
						upgrade_flag = true;
						upgrade_label = "Unlock Pet Protection";
					}
					if(remaining_cnt < 1) {
						caution_desc = "100% of Alerts Exhausted";
					}
				} else {

					for (int i = 0; i < size - 1; i++) {
						Object[] alertObj = res.get(i);
						Object[] alertObjNext = res.get(i + 1);
						String alertname = (String) alertObj[0];
						if (alertname.equalsIgnoreCase("Device Not Reporting"))
							alertname = "Network";
						else
							alertname = alertname.replace(" Alert", "");
						int remaining_cnt = ((BigInteger) alertObj[1]).intValue();
						int total_cnt = ((BigInteger) alertObj[2]).intValue();
						long alert_type = ((BigInteger) alertObj[3]).longValue();
						boolean unlimited = (boolean) alertObj[4];

						// work arround for buddy customer. if txn limit > 1000 ; then instead show remaining, unlimited text will be displayed
						if((total_cnt >= ul_check_limit) && (alert_type == 1 || alert_type == 3))
							unlimited = true;
						
						if (unlimited) {
								upgrade_label = "All Alerts unlimited";
								unlimited_plan = true;
								upgrade_flag = false;
							if( reqVer.equalsIgnoreCase("V1") ) {
								break;
							}
								
						} else 
							if ((total_cnt * 0.25) > remaining_cnt) {
							upgrade_flag = true;
							upgrade_label = "Unlock Pet Protection";
						}
						if(remaining_cnt < 1) {
							caution_desc = "100% of Alerts Exhausted";
						}
						String feature_code = (String) alertObj[5];
						double remain_percent = Math.round(((double)remaining_cnt / (double)total_cnt) * 100);

						String alertnameNext = (String) alertObjNext[0];
						if (alertnameNext.equalsIgnoreCase("Device Not Reporting"))
							alertnameNext = "Network Alerts";
						else
							alertnameNext = alertnameNext + "s";

						int remaining_cntNext = ((BigInteger) alertObjNext[1]).intValue();
						int total_cntNext = ((BigInteger) alertObjNext[2]).intValue();
						long alert_typeNext = ((BigInteger) alertObjNext[3]).longValue();
						boolean unlimitedNext = (boolean) alertObjNext[4];
						String feature_codeNext = (String) alertObjNext[5];
						double remain_percentNext = Math.round(((double)remaining_cntNext / (double)total_cntNext) * 100);

						alert_total = alert_total + total_cnt;
						alert_remaining = alert_remaining + remaining_cnt;

						JAlertRemaining alertRemainObj = new JAlertRemaining(alert_type, alertname);

						if (feature_code.contains("MAIL")) {
							at_name = "Email Alerts";
							color_code = "#545EE3";
						} else if (feature_code.contains("SMS")) {
							at_name = "Text Alerts";
							color_code = "#A17373";
						} else if (feature_code.contains("NOTIFY")) {
							at_name = "In-App Alerts";
							color_code = "#9DCD6F";
						} else {
							at_name = "Alerts Remaining";
							color_code = "#FF5D5D";
						}

						JATCount jATCount = new JATCount(at_name, unlimited, total_cnt, remaining_cnt, color_code,
								(int)remain_percent);
						type_total = type_total + total_cnt;
						type_remaining = type_remaining + remaining_cnt;

						if (alert_type == alert_typeNext) {
							atCountList.add(jATCount);
						} else {
							atCountList.add(jATCount);

							alertRemainObj.setAtCountList(atCountList);
							alertRemainObj.setAlerttype_total(type_total);
							alertRemainObj.setAlerttype_remaining(type_remaining);
							alertRemaingList.add(alertRemainObj);

							atCountList = new ArrayList<JATCount>();
							type_total = 0;
							type_remaining = 0;
						}
						
						if (i == size - 2) {
							alert_total = alert_total + total_cntNext;
							alert_remaining = alert_remaining + remaining_cntNext;

							if (alert_type != alert_typeNext) {
								type_total = total_cntNext;
								type_remaining = remaining_cntNext;
								alertRemainObj = new JAlertRemaining(alert_typeNext, alertnameNext);
								alertRemainObj.setAlerttype_total(type_total);
								alertRemainObj.setAlerttype_remaining(type_remaining);
							} else {
								type_total = type_total + total_cntNext;
								type_remaining = type_remaining + remaining_cntNext;
							}

							// work arround for buddy customer. if txn limit > 1000 ; then instead show remaining, unlimited text will be displayed
							if((total_cntNext >= ul_check_limit) && (alert_typeNext == 1 || alert_typeNext == 3))
								unlimitedNext = true;
							
							if (unlimitedNext) {
								upgrade_label = "All Alerts unlimited";
								unlimited_plan = true;
								upgrade_flag = false;
								if( reqVer.equalsIgnoreCase("V1") ) {
								break;
								}
							}else if ((total_cntNext * 0.25) > remaining_cntNext) {
								upgrade_flag = true;
								upgrade_label = "Unlock Pet Protection";
							}
							if(remaining_cnt < 1) {
								caution_desc = "100% of Alerts Exhausted";
							}
							if (feature_codeNext.contains("MAIL")) {
								at_name = "Email Alerts";
								color_code = "#545EE3";
							} else if (feature_codeNext.contains("SMS")) {
								at_name = "Text Alerts";
								color_code = "#A17373";
							} else if (feature_codeNext.contains("NOTIFY")) {
								at_name = "In-App Alerts";
								color_code = "#9DCD6F";
							} else {
								feature_codeNext = "Alerts Remaining";
								color_code = "#FF5D5D";
							}

							JATCount jATCountLast = new JATCount(at_name, unlimitedNext, total_cntNext,
									remaining_cntNext, color_code, (int)remain_percentNext);

							atCountList.add(jATCountLast);
							alertRemainObj.setAlerttype_total(type_total);
							alertRemainObj.setAlerttype_remaining(type_remaining);
							alertRemainObj.setAtCountList(atCountList);

							alertRemaingList.add(alertRemainObj);
							
						}

					}
				}
			
				String nextRenewalDate = crService.getNextRenewalDate(user_id);
				if( !nextRenewalDate.equalsIgnoreCase("NA") ) 
					response.put("next_alert_renewal_content", "Next Renewal");
				if( reqVer.equalsIgnoreCase("V2") ) {
					upgrade_label = "Alerts Remaining";
				}
				response.put("next_renewal_date", nextRenewalDate);
				response.put("alertsCntList", alertRemaingList);
				response.put("upgrade_flag", upgrade_flag);
				response.put("upgrade_label", upgrade_label);
				response.put("unlimited_plan", unlimited_plan);
				response.put("alert_total", alert_total);
				response.put("alert_remaining", alert_remaining);
				response.put("alert_based", alert_based);
				response.put("caution_desc", caution_desc);
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Please try after sometime");
			}
		} catch (Exception e) {
			log.info("getalertslimitv2: " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());

		}
		return response;

	}
	
//	@Override
//	public JResponse getalertslimitV5(long user_id, String alertlimit_basedon, boolean enable_appnotify,String reqVer,boolean smsEnable, boolean emailEnable, boolean appNotifyEnable) {
//		JResponse response = new JResponse();
//
//		try {
//			ArrayList<JAlertTemplate> alertList = new ArrayList<JAlertTemplate>();
//			ArrayList<JAlertTemplate> alertListDark = new ArrayList<JAlertTemplate>();
//
//			String qry = "";
//			boolean alert_based = true;
//			String subQry = " ";
//			JAlertTemplate alertTemplate = null;
//			JAlertTemplate alertTemplateDark = null;
//			boolean upgrade_flag = false;
//			String upgrade_label = "";
//			
//			String content = alertsPageHtmlHeader();
//			if (!enable_appnotify) {
//				subQry = " AND UF.feature_code NOT LIKE '%_NOTIFY' ";
//			}
//			if (alertlimit_basedon.equalsIgnoreCase("alert-based")) {
//				alert_based = true;
//				qry = "SELECT AT.name, (remaining_limit+extra_txn_limit+addon_limit) AS remaining,UF.txn_limit + UF.extra_txn_limit  AS total,"
//						+ " AT.id,UF.unlimited_cr,F.feature_code FROM user_feature UF JOIN feature F ON UF.feature_id = F.id JOIN alerttype AT ON AT.id = F.alerttype_id"
//						+ " WHERE UF.enable =1 AND UF.user_id=" + user_id
//						+ " AND F.alerttype_id>0  AND UF.feature_code LIKE '%_COUNT' ORDER BY CASE WHEN alerttype_id = 1 THEN 1 WHEN alerttype_id = 14 THEN 2 WHEN alerttype_id = 2 THEN 3 WHEN alerttype_id = 3 THEN 4 WHEN alerttype_id = 17 THEN 5 WHEN alerttype_id = 11 THEN 6 WHEN alerttype_id = 4 THEN 7 ELSE 8 END ;";
//				alertTemplate = new JAlertTemplate();
//				alertTemplateDark = new JAlertTemplate();
//				
//				alertTemplate.setAlert_type("All Alerts");
//				alertTemplate.setAlert_content(content);
//				alertTemplateDark.setAlert_type("All Alerts");
//				alertTemplateDark.setAlert_content(alertsPageHtmlDarkHeader());
//			}
//			else {
//				qry = "SELECT AT.name, (remaining_limit+extra_txn_limit+addon_limit) AS remaining,UF.txn_limit + UF.extra_txn_limit  AS total,"
//						+ " AT.id,UF.unlimited_cr,F.feature_code FROM user_feature UF JOIN feature F ON UF.feature_id = F.id JOIN alerttype AT ON AT.id = F.alerttype_id"
//						+ " WHERE UF.enable =1 AND UF.user_id=" + user_id
//						+ " AND F.alerttype_id>0  AND UF.feature_code NOT LIKE '%_COUNT' " + subQry
//						+ " ORDER BY CASE WHEN alerttype_id = 1 THEN 1 WHEN alerttype_id = 14 THEN 2 WHEN alerttype_id = 2 THEN 3 WHEN alerttype_id = 3 THEN 4 WHEN alerttype_id = 17 THEN 5 WHEN alerttype_id = 11 THEN 6 WHEN alerttype_id = 4 THEN 7 ELSE 8 END,feature_id ;";
//				alert_based = false;
//			}
//
//			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
//			List<Object[]> res = query.list();
//
//			boolean emailUnlimitedCredits = false;
//			boolean textUnlimitedCredits = false;
//			boolean appNotifyUnlimitedCredits = false;
//
//			JAlertTemplate emailTemp = new JAlertTemplate();
//			JAlertTemplate textTemp = new JAlertTemplate();
//			JAlertTemplate appNotifyTemp = new JAlertTemplate();
//
//			JAlertTemplate emailTempDark = new JAlertTemplate();
//			JAlertTemplate textTempDark = new JAlertTemplate();
//			JAlertTemplate appNotifyTempDark = new JAlertTemplate();
//			
//			long totalCount = 0;
//			long remainingCount = 0;
//			double remainingAlertPercent = 0.0;
//			boolean total_unlimited = true;
//			boolean unlimited = false;
//			String caution_desc = "";
//
//			if (!res.isEmpty()) {
//				int size = res.size();
//
//				if(emailEnable) {
//					emailTemp.setAlert_type("Email Alerts");
//					emailTemp.setAlert_content(content);
//					
//					emailTempDark.setAlert_type("Email Alerts");
//					emailTempDark.setAlert_content(alertsPageHtmlDarkHeader());
//
//				}
//				
//				if(smsEnable) {
//					textTemp.setAlert_type("Text Alerts");
//					textTemp.setAlert_content(content);
//					
//					textTempDark.setAlert_type("Text Alerts");
//					textTempDark.setAlert_content(alertsPageHtmlDarkHeader());
//				}
//				
//				if(enable_appnotify && appNotifyEnable) {
//					appNotifyTemp.setAlert_type("In-App Alerts");
//					appNotifyTemp.setAlert_content(content);
//					
//					appNotifyTempDark.setAlert_type("In-App Alerts");
//					appNotifyTempDark.setAlert_content(alertsPageHtmlDarkHeader());				
//
//				}
//
////				if(!emailEnable) {
////					emailTemp.setAlert_content("<center><p>Upgrade Your Plan to Unlock this Feature</p></center>");
////					emailTempDark.setAlert_content("<center style=\"color: #919191;\"><p>Upgrade Your Plan to Unlock this Feature</p></center>");
////
////					emailTemp.setTotal_cnt(0);
////					emailTemp.setRemaining_cnt(0);
////
////					emailTempDark.setTotal_cnt(0);
////					emailTempDark.setRemaining_cnt(0);
////
////				}
////
////				if(!smsEnable) {
////					textTemp.setAlert_content("<center><p>Upgrade Your Plan to Unlock this Feature</p></center>");
////					textTempDark.setAlert_content("<center style=\"color: #919191;\"><p>Upgrade Your Plan to Unlock this Feature</p></center>");
////
////					textTemp.setTotal_cnt(0);
////					textTemp.setRemaining_cnt(0);
////
////					textTempDark.setTotal_cnt(0);
////					textTempDark.setRemaining_cnt(0);
////				}
//
////				if(!appNotifyEnable) {
////					appNotifyTemp.setAlert_content("<center><p>Upgrade Your Plan to Unlock this Feature</p></center>");
////					appNotifyTempDark.setAlert_content("<center style=\"color: #919191;\"><p>Upgrade Your Plan to Unlock this Feature</p></center>");
////
////					appNotifyTemp.setTotal_cnt(0);
////					appNotifyTemp.setRemaining_cnt(0);
////
////					appNotifyTempDark.setTotal_cnt(0);
////					appNotifyTempDark.setRemaining_cnt(0);
////				}
//
//				for (int i = 0; i < size; i++) {
//					Object[] alertObj = res.get(i);
//					String alertname = (String) alertObj[0];
//					if (alertname.equalsIgnoreCase("Device Not Reporting"))
//						alertname = "Network";
//					else
//						alertname = alertname.replace(" Alert", "");
//					int remaining_cnt = ((BigInteger) alertObj[1]).intValue();
//					int total_cnt = ((BigInteger) alertObj[2]).intValue();
//					long alert_type = ((BigInteger) alertObj[3]).longValue();
//					unlimited = (boolean) alertObj[4];
//					String feature_code = (String) alertObj[5];
//
//					// work arround for buddy customer. if txn limit > 1000 ; then instead show remaining, unlimited text will be displayed
//					if((total_cnt >= ul_check_limit) && (alert_type == 1 || alert_type == 3))
//						unlimited = true;
//					
//					if(alert_type == 1 || alert_type == 2 || alert_type == 3 || alert_type == 11 || alert_type == 14 || alert_type == 4 || alert_type == 17) {
//
//						if (feature_code.contains("MAIL") && !emailUnlimitedCredits && emailEnable) {
//							emailTemp.setUnlimited(unlimited);
//							emailTempDark.setUnlimited(unlimited);
//							
//							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
//							emailTemp.setAlert_content(emailTemp.getAlert_content() + result);
//							emailTempDark.setAlert_content( emailTempDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));
//
//							if(!unlimited) {
//								
//								totalCount += total_cnt;
//								remainingCount += remaining_cnt;
//
//								if(!result.isEmpty()) {
//									emailTemp.setTotal_cnt(emailTemp.getTotal_cnt() + total_cnt);
//									emailTemp.setRemaining_cnt(emailTemp.getRemaining_cnt() + remaining_cnt);
//
//									emailTempDark.setTotal_cnt(emailTempDark.getTotal_cnt() + total_cnt);
//									emailTempDark.setRemaining_cnt(emailTempDark.getRemaining_cnt() + remaining_cnt);
//								}
//								total_unlimited = false;
//							} else {
//								upgrade_flag = false;
//								emailUnlimitedCredits = true;
//							}
//						} else if (feature_code.contains("SMS") && !textUnlimitedCredits && smsEnable) {
//							textTemp.setUnlimited(unlimited);
//							textTempDark.setUnlimited(unlimited);
//							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
//							textTemp.setAlert_content(textTemp.getAlert_content() + result);
//							textTempDark.setAlert_content(textTempDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));
//							
//							if(!unlimited) {
//								totalCount += total_cnt;
//								remainingCount += remaining_cnt;
//
//								if(!result.isEmpty()) {
//									textTemp.setTotal_cnt(textTemp.getTotal_cnt() + total_cnt);
//									textTemp.setRemaining_cnt(textTemp.getRemaining_cnt() + remaining_cnt);
//
//									textTempDark.setTotal_cnt(textTempDark.getTotal_cnt() + total_cnt);
//									textTempDark.setRemaining_cnt(textTempDark.getRemaining_cnt() + remaining_cnt);
//								}
//								total_unlimited = false;
//							} else {
//								upgrade_flag = false;
//								textUnlimitedCredits = true;
//							}
//						} else if (enable_appnotify && feature_code.contains("NOTIFY") && !appNotifyUnlimitedCredits && appNotifyEnable) {
//							appNotifyTemp.setUnlimited(unlimited);
//							appNotifyTempDark.setUnlimited(unlimited);
//							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
//							appNotifyTemp.setAlert_content(appNotifyTemp.getAlert_content() + result);
//							appNotifyTempDark.setAlert_content(appNotifyTempDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));
//							
//							if(!unlimited) {
//								
//								totalCount += total_cnt;
//								remainingCount += remaining_cnt;
//
//								if(!result.isEmpty()) {
//									appNotifyTemp.setTotal_cnt(appNotifyTemp.getTotal_cnt() + total_cnt);
//									appNotifyTemp.setRemaining_cnt(appNotifyTemp.getRemaining_cnt() + remaining_cnt);
//
//									appNotifyTempDark.setTotal_cnt(appNotifyTempDark.getTotal_cnt() + total_cnt);
//									appNotifyTempDark.setRemaining_cnt(appNotifyTempDark.getRemaining_cnt() + remaining_cnt);
//								}
//								total_unlimited = false;
//							} else {
//								upgrade_flag = false;
//								appNotifyUnlimitedCredits = true;
//							}
//						} else if(feature_code.contains("COUNT") && alert_based && !alertTemplate.isUnlimited()) {
//
//							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
//
//							alertTemplate.setAlert_content(alertTemplate.getAlert_content() + result);
//							alertTemplateDark.setAlert_content( alertTemplateDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));
//
//							if(unlimited) {
//								upgrade_flag = false;
//								alertTemplate.setUnlimited(unlimited);
//								alertTemplateDark.setUnlimited(unlimited);
//							} else {
//																
//								totalCount += total_cnt;
//								remainingCount += remaining_cnt;
//
//								if(!result.isEmpty()) {
//									alertTemplate.setTotal_cnt(alertTemplate.getTotal_cnt() + total_cnt);
//									alertTemplate.setRemaining_cnt(alertTemplate.getRemaining_cnt() + remaining_cnt);
//									alertTemplateDark.setTotal_cnt(alertTemplateDark.getTotal_cnt() + total_cnt);
//									alertTemplateDark.setRemaining_cnt(alertTemplateDark.getRemaining_cnt() + remaining_cnt);
//								}
//								total_unlimited = false;
//							}
//						}
//					}
//				}
//				
//				if(alert_based) {
//					double alertPercent = Math.round(((double)alertTemplate.getRemaining_cnt() / (double)alertTemplate.getTotal_cnt()) * 100);
//					
//					/** prepare alerts object **/
//					alertTemplate.setColor_code(alertPercent>30 ? "#9DCD6F" : "#FF5D5D");
//					alertTemplate.setRemaining_percent((int) alertPercent + "%");
//					alertTemplateDark.setColor_code(alertPercent>30 ? "#9DCD6F" : "#FF5D5D");
//					alertTemplateDark.setRemaining_percent((int) alertPercent + "%");
//					if(emailEnable) {
//						alertTemplate.setAlert_content(alertTemplate.getAlert_content() + alertsPageHtmlFooter());
//						alertTemplateDark.setAlert_content(alertTemplateDark.getAlert_content() + alertsPageHtmlFooter());
//					}
//					alertList.add(alertTemplate);
//					alertListDark.add(alertTemplateDark);
//				} else {
//					if (emailEnable) {
//						double emailAlertPercent = Math.round(((double) emailTemp.getRemaining_cnt() /
//								(double) emailTemp.getTotal_cnt()) * 100);
//
//						/** prepare email object **/
//						emailTemp.setColor_code(emailAlertPercent > 30 ? "#9DCD6F" : "#FF5D5D");
//						emailTemp.setRemaining_percent((int) emailAlertPercent + "%");
//						emailTempDark.setColor_code(emailAlertPercent > 30 ? "#9DCD6F" : "#FF5D5D");
//						emailTempDark.setRemaining_percent((int) emailAlertPercent + "%");
//						// if(emailEnable) {
//						emailTemp.setAlert_content(emailTemp.getAlert_content() + alertsPageHtmlFooter());
//						emailTempDark.setAlert_content(
//								emailTempDark.getAlert_content() + alertsPageHtmlFooter());
//						// }
//						alertList.add(emailTemp);
//						alertListDark.add(emailTempDark);
//					}
//					/** prepare text object **/
//					if (smsEnable) {
//						double textAlertPercent = Math.round(((double) textTemp.getRemaining_cnt() /
//								(double) textTemp.getTotal_cnt()) * 100);
//
//						textTemp.setColor_code(textAlertPercent > 30 ? "#9DCD6F" : "#FF5D5D");
//						textTemp.setRemaining_percent((int) textAlertPercent + "%");
//						textTempDark.setColor_code(textAlertPercent > 30 ? "#9DCD6F" : "#FF5D5D");
//						textTempDark.setRemaining_percent((int) textAlertPercent + "%");
//						// if(smsEnable) {
//						textTemp.setAlert_content(textTemp.getAlert_content() + alertsPageHtmlFooter());
//						textTempDark
//								.setAlert_content(textTempDark.getAlert_content() + alertsPageHtmlFooter());
//						// }
//						alertList.add(textTemp);
//						alertListDark.add(textTempDark);
//					}
//					/** prepare appnotify object **/
//					if (appNotifyEnable && enable_appnotify) {
//						double appNotifyAlertPercent = Math.round(((double) appNotifyTemp.getRemaining_cnt() /
//														(double) appNotifyTemp.getTotal_cnt()) * 100);
//
//						appNotifyTemp.setColor_code(appNotifyAlertPercent > 30 ? "#9DCD6F" : "#FF5D5D");
//						appNotifyTemp.setRemaining_percent((int) appNotifyAlertPercent + "%");
//						appNotifyTempDark.setColor_code(appNotifyAlertPercent > 30 ? "#9DCD6F" : "#FF5D5D");
//						appNotifyTempDark.setRemaining_percent((int) appNotifyAlertPercent + "%");
//						// if(appNotifyEnable && enable_appnotify) {
//						appNotifyTemp.setAlert_content(appNotifyTemp.getAlert_content() + alertsPageHtmlFooter());
//						appNotifyTempDark.setAlert_content(
//								appNotifyTempDark.getAlert_content() + alertsPageHtmlFooter());
//						// }
//						alertList.add(appNotifyTemp);
//						alertListDark.add(appNotifyTempDark);
//					}
//				}
//				
//				if ((!unlimited) && (totalCount * 0.20) >= remainingCount) {
//					upgrade_flag = true;
//					upgrade_label = "Upgrade";
//				}
//				
//				if(totalCount > 0) {
//					remainingAlertPercent = ((double) remainingCount / totalCount) * 100;
//					if(remainingAlertPercent > 0.0 && remainingAlertPercent < 1.0)
//						remainingAlertPercent = 1.0;
//					else
//						remainingAlertPercent = Math.round(remainingAlertPercent);
//				}
//				
//				if(remainingAlertPercent == 0.0) {
//					caution_desc = "100% of Alerts Exhausted";
//				} else if(remainingAlertPercent <= 10.0) {
//					caution_desc = "90% of Alerts Exhausted";
//				} else if(remainingAlertPercent <= 20.0) {
//					caution_desc = "80% of Alerts Exhausted";
//				}
//
//				String nextRenewalDate = crService.getNextRenewalDate(user_id);
//				if( !nextRenewalDate.equalsIgnoreCase("NA") ) 
//					response.put("next_alert_renewal_content", "Next Alert Cycle");
//				
//				Properties prop = new Properties();
//				File file = ResourceUtils.getFile("classpath:iris3.properties");
//				prop.load(new FileInputStream(file));
//				
//				String unlimitedProp = prop.getProperty("total_unlimited");
//				if(unlimitedProp.equalsIgnoreCase("true")) {
//					total_unlimited = true;
//				}
//
//				response.put("next_renewal_date", nextRenewalDate);
//				response.put("alert_based", alert_based);
//				response.put("alert_list", alertList);
//				response.put("alert_list_dark", alertListDark);
//				response.put("upgrade_flag", upgrade_flag);
//				response.put("upgrade_label", upgrade_label);
//				response.put("remaining_alert_percent", (int) remainingAlertPercent + "%");
//				response.put("total_alert_count", totalCount);
//				response.put("caution_desc", caution_desc);
//				response.put("remaining_alert_count", remainingCount);
//				response.put("total_unlimited", total_unlimited);
//				response.put("Status", 1);
//				response.put("Msg", "Success");
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "Please try after some time");
//			}
//			
//			
//		} catch (Exception e) {
//			log.info("getalertslimitV5 : " + e.getLocalizedMessage());
//			response.put("Status", 0);
//			response.put("Msg", e.getMessage());
//		}
//		return response;
//
//	}
	
	@Override
	public JResponse getalertslimitV5(long user_id, String alertlimit_basedon, boolean enable_appnotify,String reqVer,boolean smsEnable, boolean emailEnable, boolean appNotifyEnable,long gateway_id) {
		JResponse response = new JResponse();

		try {
			ArrayList<JAlertTemplate> alertList = new ArrayList<JAlertTemplate>();
			ArrayList<JAlertTemplate> alertListDark = new ArrayList<JAlertTemplate>();
			
			ArrayList<JAlertContent> emailAlertList = new ArrayList<JAlertContent>();
			ArrayList<JAlertContent> textAlertList = new ArrayList<JAlertContent>();
			ArrayList<JAlertContent> inAppAlertList = new ArrayList<JAlertContent>();
			
			ArrayList<JAlertContent> commonAlertList = new ArrayList<JAlertContent>();

			String qry = "";
			boolean alert_based = true;
			String subQry = " ";
			JAlertTemplate alertTemplate = null;
			JAlertTemplate alertTemplateDark = null;
			JAlertContent alertContent = null;
			boolean upgrade_flag = false;
			String upgrade_label = "";
			
			String content = alertsPageHtmlHeader();
			if (!enable_appnotify) {
				subQry = " AND UF.feature_code NOT LIKE '%_NOTIFY' ";
			}
			
			if (alertlimit_basedon.equalsIgnoreCase("alert-based")) {
				alert_based = true;
				qry = "SELECT AT.name, (remaining_limit+extra_txn_limit+addon_limit) AS remaining,UF.txn_limit + UF.extra_txn_limit  AS total,"
						+ " AT.id,UF.unlimited_cr,F.feature_code FROM user_feature UF JOIN feature F ON UF.feature_id = F.id JOIN alerttype AT ON AT.id = F.alerttype_id"
						+ " WHERE UF.enable =1 AND UF.user_id=" + user_id
						+ " AND F.alerttype_id>0  AND UF.feature_code LIKE '%_COUNT' ORDER BY "
						+ "CASE WHEN alerttype_id = 1 THEN 1 WHEN alerttype_id = 14 THEN 2 WHEN alerttype_id = 2 "
						+ "THEN 3 WHEN alerttype_id = 3 THEN 4 WHEN alerttype_id = 17 THEN 5 WHEN alerttype_id = 11 "
						+ "THEN 6 WHEN alerttype_id = 4 THEN 7 WHEN alerttype_id = 18 THEN 8 WHEN alerttype_id = 19 THEN 9 ELSE 10 END ASC;";
				alertTemplate = new JAlertTemplate();
				alertTemplate.setAlert_type("All Alerts");
			}
			else {
				qry = "SELECT AT.name, (remaining_limit+extra_txn_limit+addon_limit) AS remaining,UF.txn_limit + UF.extra_txn_limit  AS total,"
						+ " AT.id,UF.unlimited_cr,F.feature_code FROM user_feature UF JOIN feature F ON UF.feature_id = F.id JOIN alerttype AT ON AT.id = F.alerttype_id"
						+ " WHERE UF.enable =1 AND UF.user_id=" + user_id
						+ " AND F.alerttype_id>0  AND UF.feature_code NOT LIKE '%_COUNT' " + subQry
						+ " ORDER BY CASE WHEN alerttype_id = 1 THEN 1 WHEN alerttype_id = 14 THEN 2 "
						+ "WHEN alerttype_id = 2 THEN 3 WHEN alerttype_id = 3 THEN 4 WHEN alerttype_id = 17 "
						+ "THEN 5 WHEN alerttype_id = 11 THEN 6 WHEN alerttype_id = 4 THEN 7 WHEN alerttype_id = 18 THEN 8 WHEN alerttype_id = 19 THEN 9 ELSE 10 END ASC;";
				alert_based = false;
			}

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List<Object[]> res = query.list();

			boolean emailUnlimitedCredits = false;
			boolean textUnlimitedCredits = false;
			boolean appNotifyUnlimitedCredits = false;

			JAlertTemplate emailTemp = new JAlertTemplate();
			JAlertTemplate textTemp = new JAlertTemplate();
			JAlertTemplate appNotifyTemp = new JAlertTemplate();

			JAlertTemplate emailTempDark = new JAlertTemplate();
			JAlertTemplate textTempDark = new JAlertTemplate();
			JAlertTemplate appNotifyTempDark = new JAlertTemplate();
			
			long totalCount = 0;
			long remainingCount = 0;
			double remainingAlertPercent = 0.0;
			boolean total_unlimited = true;
			boolean unlimited = false;
			boolean smsUnlimited = false;
			String caution_desc = "";

			if (!res.isEmpty()) {
				int size = res.size();

				if(emailEnable) {
					emailTemp.setAlert_type("Email Alerts");
					emailTemp.setAlert_content(content);
					
					emailTempDark.setAlert_type("Email Alerts");
					emailTempDark.setAlert_content(alertsPageHtmlDarkHeader());

				}
				
				if(smsEnable) {
					textTemp.setAlert_type("Text Alerts");
					textTemp.setAlert_content(content);
					
					textTempDark.setAlert_type("Text Alerts");
					textTempDark.setAlert_content(alertsPageHtmlDarkHeader());
				}
				
				if(enable_appnotify && appNotifyEnable) {
					appNotifyTemp.setAlert_type("In-App Alerts");
					appNotifyTemp.setAlert_content(content);
					
					appNotifyTempDark.setAlert_type("In-App Alerts");
					appNotifyTempDark.setAlert_content(alertsPageHtmlDarkHeader());				

				}

//				if(!emailEnable) {
//					emailTemp.setAlert_content("<center><p>Upgrade Your Plan to Unlock this Feature</p></center>");
//					emailTempDark.setAlert_content("<center style=\"color: #919191;\"><p>Upgrade Your Plan to Unlock this Feature</p></center>");
//
//					emailTemp.setTotal_cnt(0);
//					emailTemp.setRemaining_cnt(0);
//
//					emailTempDark.setTotal_cnt(0);
//					emailTempDark.setRemaining_cnt(0);
//
//				}
//
//				if(!smsEnable) {
//					textTemp.setAlert_content("<center><p>Upgrade Your Plan to Unlock this Feature</p></center>");
//					textTempDark.setAlert_content("<center style=\"color: #919191;\"><p>Upgrade Your Plan to Unlock this Feature</p></center>");
//
//					textTemp.setTotal_cnt(0);
//					textTemp.setRemaining_cnt(0);
//
//					textTempDark.setTotal_cnt(0);
//					textTempDark.setRemaining_cnt(0);
//				}

//				if(!appNotifyEnable) {
//					appNotifyTemp.setAlert_content("<center><p>Upgrade Your Plan to Unlock this Feature</p></center>");
//					appNotifyTempDark.setAlert_content("<center style=\"color: #919191;\"><p>Upgrade Your Plan to Unlock this Feature</p></center>");
//
//					appNotifyTemp.setTotal_cnt(0);
//					appNotifyTemp.setRemaining_cnt(0);
//
//					appNotifyTempDark.setTotal_cnt(0);
//					appNotifyTempDark.setRemaining_cnt(0);
//				}

				for (int i = 0; i < size; i++) {
					Object[] alertObj = res.get(i);
					String alertname = (String) alertObj[0];
					if (alertname.equalsIgnoreCase("Device Not Reporting"))
						alertname = "Network";
					else
						alertname = alertname.replace(" Alert", "");
					int remaining_cnt = ((BigInteger) alertObj[1]).intValue();
					int total_cnt = ((BigInteger) alertObj[2]).intValue();
					long alert_type = ((BigInteger) alertObj[3]).longValue();
					unlimited = (boolean) alertObj[4];
					String feature_code = (String) alertObj[5];

					// work arround for buddy customer. if txn limit > 1000 ; then instead show remaining, unlimited text will be displayed
					if((feature_code.contains("SMS") )&&(total_cnt >= ul_check_limit)) {
						unlimited = true;
						smsUnlimited = true;
					}
					
					if((alert_type == 1 || alert_type == 2 || alert_type == 3 || alert_type == 4
							|| alert_type == 11 || alert_type == 14 || alert_type == 17 || alert_type == 18 || alert_type == 19) && gateway_id > 0) {
						
						alertContent = new JAlertContent();
						alertContent.setAlertname(alertname);
						alertContent.setTotal(total_cnt);
						alertContent.setLeft(remaining_cnt);
						alertContent.setIcon_url(getAlertIcon(alert_type));

						if (feature_code.contains("MAIL") && !emailUnlimitedCredits && emailEnable) {
							emailTemp.setUnlimited(unlimited);
							emailTempDark.setUnlimited(unlimited);
							
							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
							emailTemp.setAlert_content(emailTemp.getAlert_content() + result);
							emailTempDark.setAlert_content( emailTempDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));

							if(!unlimited) {
								
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									emailTemp.setTotal_cnt(emailTemp.getTotal_cnt() + total_cnt);
									emailTemp.setRemaining_cnt(emailTemp.getRemaining_cnt() + remaining_cnt);

									emailTempDark.setTotal_cnt(emailTempDark.getTotal_cnt() + total_cnt);
									emailTempDark.setRemaining_cnt(emailTempDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							} else {
								upgrade_flag = false;
								emailUnlimitedCredits = true;
							}
							emailAlertList.add(alertContent);
						} else if (feature_code.contains("SMS") && !textUnlimitedCredits && smsEnable) {
							textTemp.setUnlimited(unlimited);
							textTempDark.setUnlimited(unlimited);
							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
							textTemp.setAlert_content(textTemp.getAlert_content() + result);
							textTempDark.setAlert_content(textTempDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));
							
							if(!unlimited) {
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									textTemp.setTotal_cnt(textTemp.getTotal_cnt() + total_cnt);
									textTemp.setRemaining_cnt(textTemp.getRemaining_cnt() + remaining_cnt);

									textTempDark.setTotal_cnt(textTempDark.getTotal_cnt() + total_cnt);
									textTempDark.setRemaining_cnt(textTempDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							} else {
								smsUnlimited = true;
								upgrade_flag = false;
								textUnlimitedCredits = true;
							}
							textAlertList.add(alertContent);
						} else if (enable_appnotify && feature_code.contains("NOTIFY") && !appNotifyUnlimitedCredits && appNotifyEnable) {
							appNotifyTemp.setUnlimited(unlimited);
							appNotifyTempDark.setUnlimited(unlimited);
							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
							appNotifyTemp.setAlert_content(appNotifyTemp.getAlert_content() + result);
							appNotifyTempDark.setAlert_content(appNotifyTempDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));
							
							if(!unlimited) {
								
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									appNotifyTemp.setTotal_cnt(appNotifyTemp.getTotal_cnt() + total_cnt);
									appNotifyTemp.setRemaining_cnt(appNotifyTemp.getRemaining_cnt() + remaining_cnt);

									appNotifyTempDark.setTotal_cnt(appNotifyTempDark.getTotal_cnt() + total_cnt);
									appNotifyTempDark.setRemaining_cnt(appNotifyTempDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							} else {
								upgrade_flag = false;
								appNotifyUnlimitedCredits = true;
							}
							inAppAlertList.add(alertContent);
						} else if(feature_code.contains("COUNT") && alert_based && !alertTemplate.isUnlimited()) {

							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);

							alertTemplate.setAlert_content(alertTemplate.getAlert_content() + result);
							alertTemplateDark.setAlert_content( alertTemplateDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));

							if(unlimited) {
								upgrade_flag = false;
								alertTemplate.setUnlimited(unlimited);
								alertTemplateDark.setUnlimited(unlimited);
							} else {
																
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									alertTemplate.setTotal_cnt(alertTemplate.getTotal_cnt() + total_cnt);
									alertTemplate.setRemaining_cnt(alertTemplate.getRemaining_cnt() + remaining_cnt);
									alertTemplateDark.setTotal_cnt(alertTemplateDark.getTotal_cnt() + total_cnt);
									alertTemplateDark.setRemaining_cnt(alertTemplateDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							}
							
							commonAlertList.add(alertContent);
						}
					}else if(alert_type == 1 || alert_type == 2 || alert_type == 3 || alert_type == 4
							|| alert_type == 11 || alert_type == 14 || alert_type == 17) {

						
						alertContent = new JAlertContent();
						alertContent.setAlertname(alertname);
						alertContent.setTotal(total_cnt);
						alertContent.setLeft(remaining_cnt);
						alertContent.setIcon_url(getAlertIcon(alert_type));

						if (feature_code.contains("MAIL") && !emailUnlimitedCredits && emailEnable) {
							emailTemp.setUnlimited(unlimited);
							emailTempDark.setUnlimited(unlimited);
							
							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
							emailTemp.setAlert_content(emailTemp.getAlert_content() + result);
							emailTempDark.setAlert_content( emailTempDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));

							if(!unlimited) {
								
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									emailTemp.setTotal_cnt(emailTemp.getTotal_cnt() + total_cnt);
									emailTemp.setRemaining_cnt(emailTemp.getRemaining_cnt() + remaining_cnt);

									emailTempDark.setTotal_cnt(emailTempDark.getTotal_cnt() + total_cnt);
									emailTempDark.setRemaining_cnt(emailTempDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							} else {
								upgrade_flag = false;
								emailUnlimitedCredits = true;
							}
							emailAlertList.add(alertContent);
						} else if (feature_code.contains("SMS") && !textUnlimitedCredits && smsEnable) {
							textTemp.setUnlimited(unlimited);
							textTempDark.setUnlimited(unlimited);
							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
							textTemp.setAlert_content(textTemp.getAlert_content() + result);
							textTempDark.setAlert_content(textTempDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));
							
							if(!unlimited) {
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									textTemp.setTotal_cnt(textTemp.getTotal_cnt() + total_cnt);
									textTemp.setRemaining_cnt(textTemp.getRemaining_cnt() + remaining_cnt);

									textTempDark.setTotal_cnt(textTempDark.getTotal_cnt() + total_cnt);
									textTempDark.setRemaining_cnt(textTempDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							} else {
								upgrade_flag = false;
								textUnlimitedCredits = true;
							}
							textAlertList.add(alertContent);
						} else if (enable_appnotify && feature_code.contains("NOTIFY") && !appNotifyUnlimitedCredits && appNotifyEnable) {
							appNotifyTemp.setUnlimited(unlimited);
							appNotifyTempDark.setUnlimited(unlimited);
							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
							appNotifyTemp.setAlert_content(appNotifyTemp.getAlert_content() + result);
							appNotifyTempDark.setAlert_content(appNotifyTempDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));
							
							if(!unlimited) {
								
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									appNotifyTemp.setTotal_cnt(appNotifyTemp.getTotal_cnt() + total_cnt);
									appNotifyTemp.setRemaining_cnt(appNotifyTemp.getRemaining_cnt() + remaining_cnt);

									appNotifyTempDark.setTotal_cnt(appNotifyTempDark.getTotal_cnt() + total_cnt);
									appNotifyTempDark.setRemaining_cnt(appNotifyTempDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							} else {
								upgrade_flag = false;
								appNotifyUnlimitedCredits = true;
							}
							inAppAlertList.add(alertContent);
						} else if(feature_code.contains("COUNT") && alert_based && !alertTemplate.isUnlimited()) {

							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);

							alertTemplate.setAlert_content(alertTemplate.getAlert_content() + result);
							alertTemplateDark.setAlert_content( alertTemplateDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));

							if(unlimited) {
								upgrade_flag = false;
								alertTemplate.setUnlimited(unlimited);
								alertTemplateDark.setUnlimited(unlimited);
							} else {
																
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									alertTemplate.setTotal_cnt(alertTemplate.getTotal_cnt() + total_cnt);
									alertTemplate.setRemaining_cnt(alertTemplate.getRemaining_cnt() + remaining_cnt);
									alertTemplateDark.setTotal_cnt(alertTemplateDark.getTotal_cnt() + total_cnt);
									alertTemplateDark.setRemaining_cnt(alertTemplateDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							}
							
							commonAlertList.add(alertContent);
						}
					
					}
				}
				
				if(alert_based) {
					double alertPercent = Math.round(((double)alertTemplate.getRemaining_cnt() / (double)alertTemplate.getTotal_cnt()) * 100);
					
					/** prepare alerts object **/
					alertTemplate.setColor_code(alertPercent>30 ? "#FF0085FF" : "#FFB47E57");
					alertTemplate.setRemaining_percent((int) alertPercent + "%");
					alertTemplateDark.setColor_code(alertPercent>30 ? "#FF0085FF" : "#FFB47E57");
					alertTemplateDark.setRemaining_percent((int) alertPercent + "%");
					if(emailEnable) {
						alertTemplate.setAlert_content(alertTemplate.getAlert_content() + alertsPageHtmlFooter());
						alertTemplateDark.setAlert_content(alertTemplateDark.getAlert_content() + alertsPageHtmlFooter());
					}
					alertTemplate.setAlert_content_new(commonAlertList);
					alertTemplateDark.setAlert_content_new(commonAlertList);
					alertList.add(alertTemplate);
					alertListDark.add(alertTemplateDark);
				} else {
					if (emailEnable) {
						double emailAlertPercent = Math.round(((double) emailTemp.getRemaining_cnt() /
								(double) emailTemp.getTotal_cnt()) * 100);

						/** prepare email object **/
						emailTemp.setColor_code(emailAlertPercent > 30 ? "#FF0085FF" : "#FFB47E57");
						emailTemp.setRemaining_percent((int) emailAlertPercent + "%");
						emailTempDark.setColor_code(emailAlertPercent > 30 ? "#FF0085FF" : "#FFB47E57");
						emailTempDark.setRemaining_percent((int) emailAlertPercent + "%");
						// if(emailEnable) {
						emailTemp.setAlert_content(emailTemp.getAlert_content() + alertsPageHtmlFooter());
						emailTempDark.setAlert_content(
								emailTempDark.getAlert_content() + alertsPageHtmlFooter());
						// }
						emailTemp.setAlert_content_new(emailAlertList);
						emailTempDark.setAlert_content_new(emailAlertList);
						emailTemp.setAlert_icon("email_icon");
						alertList.add(emailTemp);
						alertListDark.add(emailTempDark);
					}
					/** prepare text object **/
					if (smsEnable) {
						double textAlertPercent = Math.round(((double) textTemp.getRemaining_cnt() /
								(double) textTemp.getTotal_cnt()) * 100);

						textTemp.setColor_code(textAlertPercent > 30 ? "#FF0085FF" : "#FFB47E57");
						textTemp.setRemaining_percent((int) textAlertPercent + "%");
						textTempDark.setColor_code(textAlertPercent > 30 ? "#FF0085FF" : "#FFB47E57");
						textTempDark.setRemaining_percent((int) textAlertPercent + "%");
						// if(smsEnable) {
						textTemp.setAlert_content(textTemp.getAlert_content() + alertsPageHtmlFooter());
						textTempDark
								.setAlert_content(textTempDark.getAlert_content() + alertsPageHtmlFooter());
						// }
						textTemp.setAlert_content_new(textAlertList);
						textTempDark.setAlert_content_new(textAlertList);
						textTemp.setAlert_icon("text_icon");
						alertList.add(textTemp);
						alertListDark.add(textTempDark);
					}
					/** prepare appnotify object **/
					if (appNotifyEnable && enable_appnotify) {
						double appNotifyAlertPercent = Math.round(((double) appNotifyTemp.getRemaining_cnt() /
														(double) appNotifyTemp.getTotal_cnt()) * 100);

						appNotifyTemp.setColor_code(appNotifyAlertPercent > 30 ? "#FF0085FF" : "#FFB47E57");
						appNotifyTemp.setRemaining_percent((int) appNotifyAlertPercent + "%");
						appNotifyTempDark.setColor_code(appNotifyAlertPercent > 30 ? "#FF0085FF" : "#FFB47E57");
						appNotifyTempDark.setRemaining_percent((int) appNotifyAlertPercent + "%");
						// if(appNotifyEnable && enable_appnotify) {
						appNotifyTemp.setAlert_content(appNotifyTemp.getAlert_content() + alertsPageHtmlFooter());
						appNotifyTempDark.setAlert_content(
								appNotifyTempDark.getAlert_content() + alertsPageHtmlFooter());
						// }
						appNotifyTemp.setAlert_content_new(inAppAlertList);
						appNotifyTempDark.setAlert_content_new(inAppAlertList);
						appNotifyTemp.setAlert_icon("inapp_icon");
						alertList.add(appNotifyTemp);
						alertListDark.add(appNotifyTempDark);
					}
				}
				
				if ((!smsUnlimited) && (totalCount * 0.20) >= remainingCount) {
					upgrade_flag = true;
					upgrade_label = "Upgrade";
				}
				
				if(totalCount > 0) {
					remainingAlertPercent = ((double) remainingCount / totalCount) * 100;
					if(remainingAlertPercent > 0.0 && remainingAlertPercent < 1.0)
						remainingAlertPercent = 1.0;
					else
						remainingAlertPercent = Math.round(remainingAlertPercent);
				}
				
				if(textUnlimitedCredits) {
					caution_desc = "";
					upgrade_flag = false;
				}else {
					if(remainingAlertPercent == 0.0) {
						caution_desc = "100% of alerts used";
					} else if(remainingAlertPercent <= 10.0) {
						caution_desc = "90% of alerts used";
					} else if(remainingAlertPercent <= 20.0) {
						caution_desc = "80% of alerts used";
					}
				}

				String nextRenewalDate = crService.getNextRenewalDate(user_id);
				if( !nextRenewalDate.equalsIgnoreCase("NA") ) 
					response.put("next_alert_renewal_content", "Next Renewal");
				
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				Properties prop = new Properties();
				prop.load(new FileInputStream(file));
				String unlimitedProp = prop.getProperty("total_unlimited");
				if(unlimitedProp.equalsIgnoreCase("true")) {
					total_unlimited = true;
				}

				response.put("next_renewal_date", nextRenewalDate);
				response.put("alert_based", alert_based);
				response.put("alert_list", alertList);
				response.put("alert_list_dark", alertListDark);
				response.put("total_label", "Total");
				response.put("left_label", "Left");
				response.put("upgrade_flag", upgrade_flag);
				response.put("upgrade_label", upgrade_label);
				response.put("remaining_alert_percent", (int) remainingAlertPercent + "%");
				response.put("total_alert_count", totalCount);
				response.put("caution_desc", caution_desc);
				response.put("remaining_alert_count", remainingCount);
				response.put("total_unlimited", total_unlimited);
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Please try after some time");
			}
			
			
		} catch (Exception e) {
			log.info("getalertslimitV5 : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}
		return response;

	}

	private String getAlertIcon(long alert_type) {
		switch ((int) alert_type) {
		case 1:
			return "ic_temp_alert";
		case 2:
			return "ic_bat_alert";
		case 3:
			return "ic_power_loss_alert";
		case 4:
			return "ic_geo_alert";
		case 11:
			return "ic_net_alert";
		case 14:
			return "ic_hum_alert";
		case 17:
			return "ic_power_back_alert";
		case 18:
			return "ic_aqi_alert";
		case 19:
			return "ic_co2_alert";
		default:
			return "ic_temp_alert";
		}
	}

private String alertsPageHtmlHeader() {
	return "<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css'><script src='https://code.jquery.com/jquery-3.3.1.slim.min.js'   integrity='sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo' crossorigin='anonymous'></script><script src='https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js' integrity='sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q' crossorigin='anonymous'></script><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table td, .table th {border-top: none;}.container {overflow-x: hidden;}.table td{ border-left: 4px solid #F0EDED;} .table td:first-child { border-left: none; } .table th {background-color:#F0EDED;border-bottom : none !important;} .text-center { text-align:center;}</style><body><div class='container' style='background-color:#F7F7F7; padding-left: 0px;padding-right: 0px;'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12'><table class='table'><thead><tr><th></th><th class='text-center' style='font-size: 14px;'>Total</th><th class='text-center' style='font-size: 14px;'>Left</th></tr></thead><tbody>";
}

private String alertsPageHtmlFooter() {
	return "</tbody></table></div></div></div></body></html>";
}

private String appendAlerts(boolean unlimited, int type_total, int type_remaining, String alertname) {
	String content = "";
	if(unlimited)
		content = "<tr>" + 
				"<td style='color:#000;font-size: 12px;font-weight: 500;'>"+alertname+"</td>" + 
				"<td style='color:#000;font-size: 12px;font-weight: 500;' class='text-center'> Unlimited</td>" + 
				"<td style='color:#000;font-size: 12px;font-weight: 500;' class='text-center'> Unlimited</td>" + 
				"</tr>";
	else
		content = "<tr>" + 
				"<td style='color:#000;font-size: 12px;font-weight: 500;'>"+alertname+"</td>" + 
				"<td class='text-center'>"+type_total+"</td>" + 
				"<td class='text-center'>"+type_remaining+"</td>" + 
				"</tr>";
	return content;
}

private String alertsPageHtmlDarkHeader() {
	return "<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><script src='https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js' integrity='sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q' crossorigin='anonymous'></script><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table{background-color: #1B1826;} .table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;}</style><body style='background-color:#1B1826;'><div class='container' style='padding-left: 0px;padding-right: 0px;'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12'><table class='table'><thead><tr class='highlight_column'><th style='font-size: 14px;float:left;'></th><th class='table_header_bg text-center' style='font-size: 14px;color:#ffffff;'>Total</th><th class='table_header_bg text-center' style='font-size: 14px;color:#ffffff;'>Left</th></tr></thead><tbody>";
}

private String appendAlertsDark(boolean unlimited, int type_total, int type_remaining, String alertname) {
	String content = "";
	if(unlimited)
		content = "<tr>" + 
				"<td style='color:#ffffff;font-size: 12px;font-weight: 500;'>"+alertname+"</td>" + 
				"<td style='color:#ffffff;font-size: 12px;font-weight: 500;' class='text-center' style='color:#ffffff;'>Unlimited</td>" + 
				"<td style='color:#ffffff;font-size: 12px;font-weight: 500;' class='text-center' style='color:#ffffff;'>Unlimited</td>" + 
				"</tr>";
	else
		content = "<tr>" + 
				"<td style='color:#ffffff;font-size: 12px;font-weight: 500;'>"+alertname+"</td>" + 
				"<td class='text-center' style='color:#ffffff;'>"+type_total+"</td>" + 
				"<td class='text-center' style='color:#ffffff;'>"+type_remaining+"</td>" + 
				"</tr>";
	return content;
}

	public boolean saveOrUpdateUserFeatureCount(long user_id,long feature_id)
	{
		try {

			Session ses = this.sessionFactory.getCurrentSession();
				
			int txn_limit = 0;
			int extra_txn_limit = 0;
			int addon_limit = 0;
			String curUtc = IrisservicesUtil.getCurrentTimeUTC();
			
			String qry = "SELECT remaining_limit,extra_txn_limit,addon_limit,feature_id FROM user_feature WHERE  user_id="+
					user_id+" AND feature_id='"+feature_id+"';";

			SQLQuery query = ses.createSQLQuery(qry);
			
			List res = query.list();
			if (!res.isEmpty()) {
				Object[] tuple = (Object[]) res.get(0);
				txn_limit = (Integer) tuple[0];
				extra_txn_limit = (Integer) tuple[1];
				addon_limit = (Integer) tuple[2];
				feature_id = ((BigInteger)tuple[3]).longValue();
								
				String updateQry = "";
				int updatecount = 0;
				// UPDATE user_feature
				if(txn_limit > 0) {					
					updateQry = "UPDATE user_feature SET remaining_limit=remaining_limit-1 WHERE user_id="
					+ user_id+" AND feature_id='"+feature_id+"';";
					
					updatecount  = ses.createSQLQuery(updateQry).executeUpdate();
					log.info(updateQry);
				}				
				else if(extra_txn_limit>0) {				
					
					updateQry = "UPDATE user_feature SET extra_txn_limit=extra_txn_limit-1 WHERE user_id="
							+ user_id+" AND feature_id='"+feature_id+"';";
					updatecount  = ses.createSQLQuery(updateQry).executeUpdate();
					log.info(updateQry);

				}				
				else if(addon_limit>0 ) {					
					updateQry = "UPDATE user_feature SET addon_limit=addon_limit-1 WHERE user_id="
							+ user_id+" AND feature_id='"+feature_id+"';";
					updatecount  = ses.createSQLQuery(updateQry).executeUpdate();
					log.info(updateQry);
				}			

			}else {
				log.info("user feature not found : ");
			}
	
			return true;
		}catch (Exception e) {
			log.error("saveOrUpdateUserFeatureCount : "+e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public List<JGatewaySubSetup> checkDeviceConfigStatusV2(long planid, long userid, int days_remaining) {
		log.info("Entering checkDeviceConfigStatusV2:");
		List<JGatewaySubSetup> setupList = new ArrayList<JGatewaySubSetup>();

		long add_device_cnt = 0;
		int remaindays = -1;
		int maxDevCnt = 0;
		boolean setupActivate = false;
		String showNextRenewal_withContent = "";
		
		try {

			if (planid > 0) {
				maxDevCnt = crService.getDeviceConfigV4(userid,planid);
			}
			
			LinkedList<JGateway> gatewayList = gatewayServiceV4.getGatewaysByInstalledDate(userid);

			for (JGateway gateway : gatewayList) {
				if (planid > 0) {
					if (maxDevCnt == 0) {
						setupActivate = true;
					} else {
						if (maxDevCnt > add_device_cnt) {
							setupActivate = false;
							add_device_cnt = add_device_cnt + 1;
							remaindays = days_remaining;
						} else {
							setupActivate = true;
							remaindays = -1;
						}
					}
				} else {
					setupActivate = true;
					remaindays = -1;
				}

				if((remaindays < days_tohandle_nextrenewal) && (remaindays > 0) && show_nextrenewal_popup) {
					showNextRenewal_withContent = "Subscription renews in " + remaindays + " days";
				}
				
				JGatewaySubSetup setup = new JGatewaySubSetup(gateway.getId(), setupActivate, remaindays, gateway.getMeid(), showNextRenewal_withContent);

				setupList.add(setup);
			}
		} catch (Exception e) {
			log.info("checkDeviceConfigStatusV2 : " + e.getLocalizedMessage());
		}
		return setupList;
	}
	
	@Override
	public JProductSubscription getCBPlan(String order_id) {
		log.info("getCBPlan DAO Impl!");
		String plan_id = "NA";
		Object[] productSubscriptionObj = null;
		JProductSubscription jProductSubscription = null; 
		try {
			String qry = "SELECT plan_id,order_date,subscription_period_days FROM `product_subscription` WHERE is_subscription_activated = 0 AND order_id='"+order_id+"';";

			Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);

			List res = query.list();

			if (!res.isEmpty()) {
				productSubscriptionObj = (Object[])res.get(0);
				jProductSubscription = new JProductSubscription();
				jProductSubscription.setPlan_id( (String) productSubscriptionObj[0] );
				jProductSubscription.setOrder_date( timeStampToString ( ( Timestamp ) productSubscriptionObj[1]) );
				jProductSubscription.setSubscription_period_days( ((BigInteger) productSubscriptionObj[2]).intValue() );
			}
				

		} catch (Exception e) {
			log.error("Exception in getCBPlan :  " + e.getLocalizedMessage());
		}		
		return jProductSubscription;
	}
	
	String timeStampToString(Timestamp orderDateTS  ){
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String string  = dateFormat.format(orderDateTS);
		return string;
	}
	
	@Override
	public boolean updateSubStatus(String order_id,String user_id) {
		log.info("Entered updateSubStatus DAO Impl!");
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			
			String qry = "update  `product_subscription`  set status ='sub_activated',is_subscription_activated=1,updatedon='"+sdf.format( new Date())+"' WHERE  order_id='"+order_id+"';";

			int stat = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();

			if (stat > 0) {
				log.info("sub_created status updated : order_id : "+order_id);
				return true;
			}else
				return false;

		} catch (Exception e) {
			log.error("Exception in updateSubStatus: " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public boolean updateReSubStatus(String order_id,String cb_sub_status) {
		log.info("Entered updateReSubStatus DAO Impl!");
		try {
			String qry = "update  `recharge_latest_sub_history` set is_sub_activated=1,cb_sub_status='"
					+cb_sub_status+"', mapped_count=mapped_count+1  WHERE  order_id='"+order_id+"';";

			int stat = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();

			if (stat > 0) {
				log.info("ReSubStatus updated : order_id : "+order_id);
				return true;
			}else
				return false;

		} catch (Exception e) {
			log.error("Exception in updateReSubStatus: " + e.getLocalizedMessage());
			return false;
		}
	}
	@Override
	public JResponse getplanoffers(String plan_ver,long planid,long periodid, String country) {
		JResponse response = new JResponse();
		ArrayList<JPlanOffer> planofferList = new ArrayList<JPlanOffer>();

		try {
			String curDt = IrisservicesUtil.getCurrentTimeUTC();
			String qry ="SELECT "
					+ " p.plan_name,"
					+ " sp.period_name,"
					+ " pp.chargebee_planid,"
					+ " po.coupon_id,"
					+ " po.offer_desc1,"
					+ " po.offer_desc1_dark, "
					+ " po.period_content, "
					+ " po.offer_content "
				+ " FROM plan p JOIN  plan_to_period pp ON p.id=pp.plan_id JOIN sub_period sp ON sp.id = pp.sub_period_id"
				+ " JOIN plan_offer po ON pp.id = po.plan_period_id JOIN plan_to_upgrade pu ON pu.upgradeplan_id = pp.id"
				+ " WHERE  pu.plan_to_period_id = (SELECT id FROM plan_to_period  WHERE plan_id="+planid+" AND sub_period_id = "+periodid+") AND pp.country_code='"+country+"' AND"
				+ " po.enable=1 AND po.expiry_date >='"+curDt+"'";
			
				Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
				List<Object[]> res = query.list();
				
				if (!res.isEmpty()) {
					int size = res.size();
					String plan_desc = (String)res.get(0)[0] ;
					

					for (int i = 0; i < size; i++) {
						Object[] alertObj = res.get(i);
						String plan_name = (String) alertObj[0];
						
						if(!plan_desc.contains(plan_name))
							plan_desc = plan_desc+","+plan_name;
						
						String period_name = (String) alertObj[1];
						String chargebee_planid = (String) alertObj[2];
						String coupon_id = (String) alertObj[3];
						String offer_desc = (String) alertObj[4];
						String offer_desc_dark = (String) alertObj[5];
						String period_content = (String) alertObj[6];
						String offer_content = (String) alertObj[7];
						
						JPlanOffer offerObj = new JPlanOffer(chargebee_planid, coupon_id, offer_desc, offer_desc_dark,
								period_content, offer_content);
						
						planofferList.add(offerObj);		

					}
					String plan_desc_dark = "<center><p style='color:#ffffff;font-size: 17px;padding-left: 5px;'>Available Offers</p></center>";
					plan_desc = "<center><p style='color:#000;font-size: 17px;padding-left: 5px;'>Available Offers</p></center>";
					
					
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("plan_desc", plan_desc);
					response.put("plan_desc_dark", plan_desc_dark);
					response.put("planofferList", planofferList);
				}else {
					response.put("Status", 0);
					response.put("Msg", "No offers found");
				}
		}catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Please try after sometime!");
			response.put("Error", e.getLocalizedMessage());
			log.info("get planoffers: "+ e.getLocalizedMessage());
		}
		return response;
	
	}

	@Override
	public String[] getOfferStatus(String chargebee_planid) {
		String[] offer_status = new String[3];
		
		try {
			String qry = " SELECT offer_desc2, offer_desc2_dark, offer_success_content FROM plan_offer WHERE plan_period_id = "
					+ "(SELECT id FROM plan_to_period WHERE chargebee_planid = '"+chargebee_planid+"');";

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);

			List res = query.list();

			if (!res.isEmpty()) {
				Object[] obj = (Object[]) res.get(0);
				offer_status[0] = (String) obj[0];
				offer_status[1] = (String) obj[1];
				offer_status[2] = (String) obj[2];
				return offer_status;
			}
			else {
				return null;
			}
				

		} catch (Exception e) {
			offer_status[0] = "NA";
			offer_status[1] = "NA";
			log.error("Exception in getOfferStatus: " + e.getLocalizedMessage());
		}
		
		return null;

	}

	@Override
	public boolean updateUserIdInProsuctSubs(String order_id, long user_id) {
		log.info("Entered into updateUserIdInProsuctSubs :: user_id : "+user_id+" :: order_id : "+order_id);
		try {
			
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String qry = "update  `product_subscription`  set user_id='"+user_id+"',updatedon='"+sdf.format( new Date())+"',`order_mapping_status`=1,`active_subscription`=0 WHERE  order_id='"+order_id+"';";

			int stat = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();

			if (stat > 0) {
				log.info("user_id updated : order_id : "+order_id);
				return true;
			}else
				return false;
			
		} catch (Exception e) {
			log.error("Error in updateUserIdInProsuctSubs :: Error : "+e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public String getNextRenewalDate(long user_id) {
		log.info("Entered into getNextRenewalDate :: user_id : "+user_id);
		try {
			
			String qry = "SELECT NEXT_FIRE_TIME FROM `CREDIT_TRIGGERS` WHERE `JOB_NAME` = '"+user_id+"'";

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);

			List res = query.list();

			if (!res.isEmpty()) {
				long nextRenewalDate = ((BigInteger) res.get(0)).longValue();
				SimpleDateFormat sdf = new SimpleDateFormat("dd MMM yyyy");
				Date date = new Date(nextRenewalDate);
				String d = sdf.format(date);
				return sdf.format(date);
			} else {
				return "NA";
			}
		} catch (Exception e) {
			log.error("Error in getNextRenewalDate :: Error : "+e.getLocalizedMessage());
			return "NA";
		}
	}
	
	@Override
	public JResponse upgradePlanList_v5(UserV4 user, long curplan_id, long curperiod_id,
			boolean freetrial, String country, String type) {
		log.info("Entered into upgradePlanList_v5 :: user_id : " + user.getId());
		JResponse response = new JResponse();
		try {

			long device_count = crService.getDeviceCountByUser(user.getId(), 1);

			String joinCondition = "pu.upgradeplan_id";
			String whereCondition = "pu.plan_to_period_id";
			String qry = "";

			JPlanInfo plan = crService.getPlan(curplan_id);
			if (plan == null) {
				plan = new JPlanInfo();
			}

			if (type.equalsIgnoreCase("downgrade")) {
				joinCondition = "pu.plan_to_period_id";
				whereCondition = "pu.upgradeplan_id";
			}

			if (type.equalsIgnoreCase("upgrade")) {

				qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pp.plan_price,pl.orderno,pp.free_trial_days,"
						+ "pp.content_1,pp.content_2,pp.content_3,pp.content_4,pp.strike_price,pl.feature_list_flutter,"
						+ "pp.is_best_deal,pp.img_url,pl.feature_list_dark_flutter,pl.feature_list_ui_new,"
						+ "pl.cur_feature_ui_new,pl.feature_list_ui_new_1,pp.content_5 FROM plan_to_upgrade pu "
						+ " JOIN plan_to_period pp ON pu.upgradeplan_id "
						+ " = pp.id JOIN plan pl ON pl.id = pp.plan_id JOIN sub_period sp ON "
						+ " sp.id=pp.sub_period_id WHERE  pl.monitor_type=1 AND  pl.plan_ver='V2' AND pl.plan_type='Data-Plan' AND pl.device_cnt >="
						+ device_count + " AND pl.enable=1 AND pl.custom=0 AND" + " pp.enable=1 AND pp.custom=0 AND "
						+ " pu.plan_to_period_id IN(SELECT id FROM plan_to_period WHERE  plan_id=" + curplan_id
						+ " AND sub_period_id=" + curperiod_id + " ) AND pp.country_code= '" + country
						+ "' ORDER BY pl.orderno ASC,pp.plan_id,sp.id DESC;";

				if (device_count > device_count_config) {

					CompanyConfig companyConfig = companyService.getCompanyConfig(user.getCmpId());

					if (companyConfig != null && companyConfig.isCustom_plan_enable())
						qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pp.plan_price,pl.orderno,pp.free_trial_days,"
								+ "pp.content_1,pp.content_2,pp.content_3,pp.content_4,pp.strike_price,"
								+ "pl.feature_list_flutter,pp.is_best_deal,pp.img_url,pl.feature_list_dark_flutter, "
								+ "pl.feature_list_ui_new,pl.cur_feature_ui_new,pl.feature_list_ui_new_1,pp.content_5 FROM plan_to_upgrade pu"
								+ " JOIN plan_to_period pp ON pu.upgradeplan_id "
								+ " = pp.id JOIN plan pl ON pl.id = pp.plan_id JOIN sub_period sp ON "
								+ " sp.id=pp.sub_period_id WHERE pl.plan_ver='V2' AND pl.plan_type='Data-Plan' AND pl.device_cnt = '"
								+ device_count + "'  AND pl.monitor_type=1 AND pl.enable=1 AND" + " pp.enable=1 AND pu.plan_to_period_id"
								+ " IN(SELECT id FROM plan_to_period WHERE  plan_id=" + curplan_id
								+ " AND sub_period_id=" + curperiod_id + " ) AND pp.country_code= '" + country
								+ "' ORDER BY pl.orderno ASC,pp.plan_id,sp.id DESC;";
				}

			} else {

				if (type.equalsIgnoreCase("downgrade")) {

					qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pp.plan_price,pl.orderno,pp.free_trial_days,"
							+ "pp.content_1,pp.content_2,pp.content_3,pp.content_4,pp.strike_price,pl.feature_list_flutter,"
							+ "pp.is_best_deal,pp.img_url,pl.feature_list_dark_flutter,pl.feature_list_ui_new,"
							+ "pl.cur_feature_ui_new,pl.feature_list_ui_new_1,pp.content_5 FROM plan_to_upgrade pu "
							+ " JOIN plan_to_period pp ON pu.plan_to_period_id  "
							+ " = pp.id JOIN plan pl ON pl.id = pp.plan_id JOIN sub_period sp ON "
							+ " sp.id=pp.sub_period_id WHERE  pl.monitor_type=1 AND pl.plan_ver='V2' AND pl.plan_type='Data-Plan' AND pl.device_cnt >="
							+ device_count + " AND pl.enable=1 AND pl.custom=0 AND"
							+ " pp.enable=1 AND pp.custom=0 AND "
							+ " pu.upgradeplan_id IN(SELECT id FROM plan_to_period WHERE  plan_id=" + curplan_id
							+ " AND sub_period_id=" + curperiod_id + " ) AND pp.country_code= '" + country
							+ "' ORDER BY pl.orderno ASC,pp.plan_id,sp.id DESC;";

					if ((device_count > device_count_config) || !plan.isEnable()) {

						CompanyConfig companyConfig = companyService.getCompanyConfig(user.getCmpId());

						if ((companyConfig != null && companyConfig.isCustom_plan_enable()) || !plan.isEnable())
							qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pp.plan_price,pl.orderno,pp.free_trial_days,"
									+ "pp.content_1,pp.content_2,pp.content_3,pp.content_4,pp.strike_price,"
									+ "pl.feature_list_flutter,pp.is_best_deal,pp.img_url,pl.feature_list_dark_flutter,"
									+ "pl.feature_list_ui_new,pl.cur_feature_ui_new,pl.feature_list_ui_new_1,pp.content_5 FROM plan_to_upgrade pu "
									+ " JOIN plan_to_period pp ON pu.plan_to_period_id "
									+ " = pp.id JOIN plan pl ON pl.id = pp.plan_id JOIN sub_period sp ON "
									+ " sp.id=pp.sub_period_id WHERE  pl.monitor_type=1 AND pl.plan_ver='V2' AND pl.plan_type='Data-Plan' AND pl.device_cnt = '"
									+ device_count + "'  AND pl.enable=1 AND" + " pp.enable=1 AND pu.upgradeplan_id"
									+ " IN(SELECT id FROM plan_to_period WHERE  plan_id=" + curplan_id
									+ " AND sub_period_id=" + curperiod_id + " ) AND pp.country_code= '" + country
									+ "' ORDER BY pl.orderno ASC,pp.plan_id,sp.id DESC;";
					}

				} else {
					qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pp.plan_price,pl.orderno,pp.free_trial_days,pp.content_1,pp.content_2,pp.content_3,pp.content_4,pp.strike_price,pl.feature_list_flutter,pp.is_best_deal,pp.img_url,pl.feature_list_dark_flutter,"
							+ "pl.feature_list_ui_new,pl.cur_feature_ui_new,pl.feature_list_ui_new_1,pp.content_5 "
							+ "FROM plan_to_period pp " + "JOIN plan pl ON pl.id = pp.plan_id "
							+ "JOIN sub_period sp ON sp.id = pp.sub_period_id WHERE pl.monitor_type=1 AND pp.country_code= '" + country
							+ "' AND pl.plan_name != 'Chum' AND pp.enable=1 AND ";

					if (plan.isEnable())
						qry += "pl.id = " + curplan_id + " AND pp.sub_period_id !=" + curperiod_id
								+ " ORDER BY pl.orderno ASC,period_id DESC";
					else
						qry += "pl.device_cnt=" + device_count+ " AND pl.enable = 1 ORDER BY pl.orderno ASC,period_id DESC";
				}
			}
			log.info(qry);
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List<Object[]> planResp = query.list();
			long plan_id = 0;
			long per_id = 0;
			String plan_name = "";
			boolean freetrial_avail = false;
			String buynow_desc = "PROTECT MY PET";
			String trial_desc = "";
			int trial_days = 0;
			String content1 = "";
			String content2 = "";
			String content3 = "";
			String content4 = "";
			String content5 = "";
			String strike_price_v2 = "NA";
			boolean is_best_seller = false;
			String img_url = "NA";
			String feature_list_dark_flutter = "NA";
			String feature_list_ui_new = "NA";
			String cur_feature_ui_new = "NA";
			String feature_list_ui_new_1 = "NA";
			boolean is_best_deal = false;

			ArrayList<JSubscriptionPlan> plan_list = new ArrayList<JSubscriptionPlan>();
			ArrayList<JSubscriptionPeriod> period_list = new ArrayList<JSubscriptionPeriod>();

			if (!planResp.isEmpty()) {
				int size = planResp.size();
				log.info("upgradePlanList : size:" + size);
				JSubscriptionPlan subsPlan = new JSubscriptionPlan();
				Object[] tuple = (Object[]) planResp.get(0);
				boolean user_show_benefits = crService.checkAdditionalBenifits(user.getEmail());
				boolean show_benefits = false;

				if (size == 1) {
					plan_id = ((BigInteger) tuple[0]).longValue();
					per_id = ((BigInteger) tuple[2]).longValue();
					String feature_list = (String) tuple[3];
					String plan_price = (String) tuple[4];
					plan_name = (String) tuple[1];
					trial_days = (int) tuple[6];
					content1 = (String) tuple[7];
					content2 = (String) tuple[8];
					content3 = (String) tuple[9];
					content4 = (String) tuple[10];
					strike_price_v2 = (((Integer) tuple[11]) == 0) ? "NA" : "$" + ((Integer) tuple[11]);
					String feature_list_flutter = (String) tuple[12];
					is_best_deal = (boolean) tuple[13];
					img_url = (String) tuple[14];
					feature_list_dark_flutter = (String) tuple[15];
					feature_list_ui_new = (String) tuple[16];
					cur_feature_ui_new = (String) tuple[17];
					feature_list_ui_new_1 = (String) tuple[18];
					content5 = (String) tuple[19];
					ArrayList<JFeatureList> featureList = new ArrayList<>();
					try {
						Type listType = new TypeToken<List<JFeatureList>>() {}.getType();
						featureList = new Gson().fromJson(feature_list_ui_new_1, listType);
					} catch (JSONException err) {
						log.error("featureList:"+err.getLocalizedMessage());
					}

					if (freetrial && trial_days > 0) {
						/* currently not used
						freetrial_avail = true;
						if (trial_days == 1) {
							trial_desc = "<hr style='width:19%;color: #717171;text-align:center;margin-top: 15px;margin-bottom: -10px;'>"
									+ "<p style='font-size: 17px;margin-bottom: 0px;color:#000;font-family: 'Montserrat', sans-serif;font-weight: 600;'>"
									+ trial_days + " day Trial" + "</p></center>";

							buynow_desc = "Start your " + trial_days + " day free Trial";
							content4 = "1 day Trial";
						} else {
							trial_desc = "<hr style='width:19%;color: #717171;text-align:center;margin-top: 15px;margin-bottom: -10px;'>"
									+ "<p style='font-size: 17px;margin-bottom: 0px;color:#000;font-family: 'Montserrat', sans-serif;font-weight: 600;'>"
									+ trial_days + " days Trial" + "</p></center>";

							buynow_desc = "Start your " + trial_days + " days free Trial";
							content4 = trial_days + " days Trial";
						}
						plan_price = plan_price.replace("</center>", trial_desc); */

					} else {
						freetrial_avail = false;
						trial_desc = "";
						buynow_desc = "PROTECT MY PET";
					}

					subsPlan.setPlanid(plan_id);
					subsPlan.setPlanname(plan_name);
					subsPlan.setFeature_list(feature_list);
					subsPlan.setFreetrial_avail(freetrial_avail);
					subsPlan.setIs_best_seller(is_best_seller);
					subsPlan.setFeature_list_flutter(feature_list_flutter);
					subsPlan.setFeature_list_dark_flutter(feature_list_dark_flutter);
					subsPlan.setFeature_list_ui_new(feature_list_ui_new);
					subsPlan.setCur_feature_ui_new(cur_feature_ui_new);
					subsPlan.setFeature_list_ui_new_1(featureList);

					JSubscriptionPeriod periodObj = new JSubscriptionPeriod();
					periodObj.setPeriod_id(per_id);
					periodObj.setPlan_id(plan_id);

					periodObj.setPrice_detail(plan_price);
					periodObj.setBuynow_desc(buynow_desc);
					periodObj.setContent_1(content1);
					periodObj.setContent_2(content2);
					periodObj.setContent_3(content3);
					periodObj.setContent_4(content4);
					periodObj.setContent_5(content5);
					periodObj.setStrike_price_v2(strike_price_v2);
					periodObj.setIs_best_deal(is_best_deal);
					periodObj.setImg_url(img_url);
					LinkedList<String> benefits = new LinkedList<String>();

					if (user_show_benefits) {
						benefits = crService.listPlanBenefits(per_id);
						show_benefits = true;
					}

					if (benefits.isEmpty())
						show_benefits = false;

					periodObj.setShow_benefits(show_benefits);
					periodObj.setBenefits(benefits);

					period_list.add(periodObj);
					subsPlan.setPeriod_list(period_list);
					plan_list.add(subsPlan);
				}

				for (int i = 0; i < size - 1; i++) {
					is_best_seller = false;
					tuple = (Object[]) planResp.get(i);
					Object[] tupleNext = (Object[]) planResp.get(i + 1);
					plan_id = ((BigInteger) tuple[0]).longValue();
					per_id = ((BigInteger) tuple[2]).longValue();
					String feature_list = (String) tuple[3];
					String plan_price = (String) tuple[4];
					plan_name = (String) tuple[1];
					trial_days = (int) tuple[6];
					content1 = (String) tuple[7];
					content2 = (String) tuple[8];
					content3 = (String) tuple[9];
					content4 = (String) tuple[10];
					strike_price_v2 = (((Integer) tuple[11]) == 0) ? "NA" : "$" + ((Integer) tuple[11]);
					String feature_list_flutter = (String) tuple[12];
					is_best_deal = (boolean) tuple[13];
					img_url = (String) tuple[14];
					feature_list_dark_flutter = (String) tuple[15];
					feature_list_ui_new = (String) tuple[16];
					cur_feature_ui_new = (String) tuple[17];
					feature_list_ui_new_1 = (String) tuple[18];
					content5 = (String) tuple[19];
					ArrayList<JFeatureList> featureList = new ArrayList<>();
					try {
						Type listType = new TypeToken<List<JFeatureList>>() {}.getType();
						featureList = new Gson().fromJson(feature_list_ui_new_1, listType);
					} catch (JSONException err) {
						log.error("featureList:"+err.getLocalizedMessage());
					}

					if (freetrial && trial_days > 0) {
						/*freetrial_avail = true;

						if (trial_days == 1) {
							trial_desc = "<hr style='width:19%;color: #717171;text-align:center;margin-top: 15px;margin-bottom: -10px;'>"
									+ "<p style='font-size: 17px;margin-bottom: 0px;color:#000;font-family: 'Montserrat', sans-serif;font-weight: 600;'>"
									+ trial_days + " day Trial" + "</p></center>";
							content4 = trial_days + " day Trial";
							buynow_desc = "Start your " + trial_days + " day free Trial";
						} else {
							trial_desc = "<hr style='width:19%;color: #717171;text-align:center;margin-top: 15px;margin-bottom: -10px;'>"
									+ "<p style='font-size: 17px;margin-bottom: 0px;color:#000;font-family: 'Montserrat', sans-serif;font-weight: 600;'>"
									+ trial_days + " days Trial" + "</p></center>";

							buynow_desc = "Start your " + trial_days + " days free Trial";
							content4 = trial_days + " days Trial";
						}
						plan_price = plan_price.replace("</center>", trial_desc);*/
					} else {
						trial_desc = "";
						buynow_desc = "PROTECT MY PET";
					}
					subsPlan.setPlanid(plan_id);
					subsPlan.setPlanname(plan_name);
					subsPlan.setFeature_list(feature_list);
					subsPlan.setFreetrial_avail(freetrial_avail);
					subsPlan.setIs_best_seller(is_best_seller);
					subsPlan.setFeature_list_flutter(feature_list_flutter);
					subsPlan.setFeature_list_dark_flutter(feature_list_dark_flutter);
					subsPlan.setFeature_list_ui_new(feature_list_ui_new);
					subsPlan.setCur_feature_ui_new(cur_feature_ui_new);
					subsPlan.setFeature_list_ui_new_1(featureList);

					long plan_id_next = ((BigInteger) tupleNext[0]).longValue();
					String plan_name_next = (String) tupleNext[1];
					long per_id_next = ((BigInteger) tupleNext[2]).longValue();
					String feature_list_next = (String) tupleNext[3];
					String plan_price_next = (String) tupleNext[4];
					int trial_days_next = (int) tupleNext[6];
					boolean freetrial_avail_next = false;
					String buynow_desc_next = "PROTECT MY PET";
					String trial_desc_next = "";
					String content1_next = (String) tupleNext[7];
					String content2_next = (String) tupleNext[8];
					String content3_next = (String) tupleNext[9];
					String content4_next = (String) tupleNext[10];
					String strike_price_v2_next = (((Integer) tupleNext[11]) == 0) ? "NA"
							: "$" + ((Integer) tupleNext[11]);
					boolean is_best_seller_next = false;
					String img_url_next = (String) tupleNext[14];
					boolean is_best_deal_next = (boolean) tupleNext[13];
					;
					String feature_list_flutter_next = (String) tupleNext[12];
					String feature_list_dark_flutter_next = (String) tupleNext[15];
					// check
					String feature_list_ui_new_next = (String) tupleNext[16];
					String cur_feature_ui_new_next = (String) tupleNext[17];
					String feature_list_ui_new_1_next = (String) tupleNext[18];
					String content5_next = (String) tupleNext[19];
					ArrayList<JFeatureList> featureListNext = new ArrayList<>();
					try {
						Type listType = new TypeToken<List<JFeatureList>>() {}.getType();
						featureListNext = new Gson().fromJson(feature_list_ui_new_1_next, listType);
					} catch (JSONException err) {
						log.error("featureListNext : "+err.getLocalizedMessage());
					}

					if (freetrial && trial_days_next > 0) {
						/*freetrial_avail_next = true;
						if (trial_days_next == 1) {
							trial_desc_next = "<hr style='width:19%;color: #717171;text-align:center;margin-top: 15px;margin-bottom: -10px;'>"
									+ "<p style='font-size: 17px;margin-bottom: 0px;color:#000;font-family: 'Montserrat', sans-serif;font-weight: 600;'>"
									+ trial_days_next + " day Trial" + "</p></center>";

							buynow_desc_next = "Start your " + trial_days_next + " day free Trial";
							content4_next = trial_days_next + " day Trial";
						} else {
							trial_desc_next = "<hr style='width:19%;color: #717171;text-align:center;margin-top: 15px;margin-bottom: -10px;'>"
									+ "<p style='font-size: 17px;margin-bottom: 0px;color:#000;font-family: 'Montserrat', sans-serif;font-weight: 600;'>"
									+ trial_days_next + " days Trial" + "</p></center>";

							buynow_desc_next = "Start your " + trial_days_next + " days free Trial";
							content4_next = trial_days_next + " days Trial";
						}
						plan_price_next = plan_price_next.replace("</center>", trial_desc_next);*/

					} else {
						trial_desc_next = "";
						buynow_desc_next = "PROTECT MY PET";
					}

					JSubscriptionPeriod periodObj = new JSubscriptionPeriod();

					periodObj.setPeriod_id(per_id);
					periodObj.setPlan_id(plan_id);
					periodObj.setPrice_detail(plan_price);
					periodObj.setBuynow_desc(buynow_desc);
					periodObj.setContent_1(content1);
					periodObj.setContent_2(content2);
					periodObj.setContent_3(content3);
					periodObj.setContent_4(content4);
					periodObj.setContent_5(content5);
					periodObj.setStrike_price_v2(strike_price_v2);
					periodObj.setImg_url(img_url);
					periodObj.setIs_best_deal(is_best_deal);

					LinkedList<String> benefits = new LinkedList<String>();
					if (user_show_benefits) {
						benefits = crService.listPlanBenefits(per_id);
						show_benefits = true;
					}

					if (benefits.isEmpty())
						show_benefits = false;

					periodObj.setShow_benefits(show_benefits);
					periodObj.setBenefits(benefits);

					period_list.add(periodObj);

					if (plan_id != plan_id_next) {
						subsPlan.setPeriod_list(period_list);
						plan_list.add(subsPlan);
						subsPlan = new JSubscriptionPlan();
						period_list = new ArrayList<JSubscriptionPeriod>();
						freetrial_avail = false;
					}

					if (i == (size - 2)) {
						JSubscriptionPeriod periodObj_next = new JSubscriptionPeriod();

						periodObj_next.setPeriod_id(per_id_next);
						periodObj_next.setPlan_id(plan_id_next);
						periodObj_next.setPrice_detail(plan_price_next);
						periodObj_next.setBuynow_desc(buynow_desc_next);
						periodObj_next.setContent_1(content1_next);
						periodObj_next.setContent_2(content2_next);
						periodObj_next.setContent_3(content3_next);
						periodObj_next.setContent_4(content4_next);
						periodObj_next.setContent_5(content5_next);
						periodObj_next.setStrike_price_v2(strike_price_v2_next);
						periodObj_next.setIs_best_deal(is_best_deal_next);
						periodObj_next.setImg_url(img_url_next);

						benefits = new LinkedList<String>();
						if (user_show_benefits) {
							benefits = crService.listPlanBenefits(per_id_next);
							show_benefits = true;
						}

						if (benefits.isEmpty())
							show_benefits = false;

						periodObj_next.setShow_benefits(show_benefits);
						periodObj_next.setBenefits(benefits);

						if (plan_id == plan_id_next) {
							period_list.add(periodObj_next);
						} else {
							subsPlan = new JSubscriptionPlan();
							period_list = new ArrayList<JSubscriptionPeriod>();

							subsPlan.setPlanid(plan_id_next);
							subsPlan.setPlanname(plan_name_next);
							subsPlan.setFeature_list(feature_list_next);
							subsPlan.setFreetrial_avail(freetrial_avail_next);
							subsPlan.setIs_best_seller(is_best_seller_next);
							subsPlan.setFeature_list_flutter(feature_list_flutter_next);
							subsPlan.setFeature_list_dark_flutter(feature_list_dark_flutter_next);
							subsPlan.setFeature_list_ui_new(feature_list_ui_new_next);
							subsPlan.setCur_feature_ui_new(cur_feature_ui_new_next);
							subsPlan.setFeature_list_ui_new_1(featureListNext);

							period_list.add(periodObj_next);

						}
						subsPlan.setPeriod_list(period_list);
						plan_list.add(subsPlan);
					}
				}

			}
			boolean show_coupon = true;
			JCouponInfo coupon_info = new JCouponInfo();
			String btn_text = "Continue";
			String chargeBeeId=user.getChargebeeid().equals(null)?"NA":user.getChargebeeid();
			String currentSubStatus = getCurrentSubStatusForChargebeeUser(chargeBeeId);

			if(plan.isIs_freeplan() && chargeBeeId.equalsIgnoreCase("NA")) {
				coupon_info = new JCouponInfo(coupon_img_new, coupon_desc_new, coupon_code_new,btn_text);
			}
			else if (currentSubStatus.equalsIgnoreCase("active") || currentSubStatus.equalsIgnoreCase("non_renewing")){
				coupon_info = new JCouponInfo(coupon_img_upgrade, coupon_desc_upgrade, coupon_code_upgrade,btn_text);
			}
			else if(currentSubStatus.equalsIgnoreCase("cancelled")){
				coupon_info = new JCouponInfo(coupon_img_update, coupon_desc_update, coupon_code_update,btn_text);
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("coupon_info", coupon_info);
			response.put("show_coupon", show_coupon);
			response.put("planlist", plan_list);
			// for custom plan
			response.put("planname", "To add monitor or to customize the existing plan, please contact Support");
			response.put("contactnumber", supportcontactnumber.get(country));
			response.put("email", supportemail.get(country));

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Contact Support");

			return response;
		}
		return response;
	}
	
	@Override
	public LinkedList<String> listPlanBenefits(long periodid) {
		log.info("Entered listPlanBenefits :: DAO");
		LinkedList<String> benefits = new LinkedList<String>();
		try {
		String qry = "SELECT benefits FROM `plan_benefits` WHERE period_id ="+periodid+" AND `enable`=1 AND expiry_date>= CURRENT_TIMESTAMP ORDER BY order_no ASC;";

		Query query = this.slave5SessionFactory.getCurrentSession().createSQLQuery(qry);
		List res = query.list();
		if (!res.isEmpty()) {
			for (int i = 0; i < res.size(); i++) {
				//Object[]  tuple = (Object[]) res.get(i);
				benefits.add((String)res.get(i));
			}
		}
		return benefits;
		}catch (Exception e) {
			//e.printStackTrace();
			log.error(e.getLocalizedMessage());
			return benefits;
		}
	}

	
	@Override
	public boolean checkAdditionalBenifits(String user_name) {
		log.info("Entered into checkAdditionalBenifits :: user_name : "+user_name);
		boolean isAvail = false;
		try {
			
			String qry = "SELECT user_name FROM additional_benefits_user WHERE `user_name`=:user_name";

			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("user_name", user_name);
			List res = query.list();

			if (!res.isEmpty()) {
				isAvail = true; 
			}
			
		} catch (Exception e) {
			log.error("Error in checkAdditionalBenifits Exp : "+e.getLocalizedMessage());
		}
		return isAvail;
	}
	
	@Override
	public boolean checkAdditionalBenifitsCreated(String user_name,int periodId) {
		log.info("Entered into checkAdditionalBenifits :: user_name : "+user_name);
		boolean isAvail = false;
		try {
			
			String qry = "SELECT AU.user_name FROM additional_benefits_user  AU JOIN additional_benefits_credit AC ON "
					+ " AU.user_name=AC.emailid WHERE AU.user_name=:user_name AND AC.planId in (SELECT id FROM `benefits_plan_details` WHERE period_id=:periodId)";
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("user_name", user_name);
			query.setParameter("periodId", periodId);
			List res = query.list();

			if (!res.isEmpty()) {
				isAvail = true; 
			}
			
		} catch (Exception e) {
			log.error("Error in checkAdditionalBenifits Exp : "+e.getLocalizedMessage());
		}
		return isAvail;
	}
	
	@Override
	public boolean insertAddiUser(String username) {
		log.info("Entered insertAddiUser DAO Impl!");
		try {
			
			String qry = "insert into  `additional_benefits_user` (user_name) values ('"+username+"');";

			int stat = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
				return true;

		} catch (Exception e) {
			log.error("Exception in insertAddiUser: " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public JPlanToUpgrade getPlanToUpgrade(int upgrade_plan_to_period_id, int cur_plan_to_period_id) {
		log.info("Entered into getPlanToUpgrade :: upgrade_plan_to_period_id : "+upgrade_plan_to_period_id+" :: cur_plan_to_period_id : " + cur_plan_to_period_id);
		List<JPlanToUpgrade> planToUpgradeList = new ArrayList<>();
		try {
			
			String qry = "SELECT id,plan_to_period_id,upgradeplan_id FROM plan_to_upgrade WHERE upgradeplan_id =:upgradeplan_id AND plan_to_period_id = :plan_to_period";
			
			SQLQuery query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setResultTransformer(new AliasToBeanResultTransformer(JPlanToUpgrade.class));
			query.setParameter("plan_to_period", cur_plan_to_period_id);
			query.setParameter("upgradeplan_id", upgrade_plan_to_period_id);
			
			query.addScalar("id", new LongType()).
			addScalar("plan_to_period_id", new LongType()).
			addScalar("upgradeplan_id", new LongType());
			
			planToUpgradeList = query.list();
			
			if( planToUpgradeList.isEmpty() ) {
				log.info("No plan to upgrade found");
				return null;
			}
			
			return planToUpgradeList.get(0);
		} catch (Exception e) {
			log.error("Error in getPlanToUpgrade :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public JPlanInfo getPlan(long plan_id) {
		log.info("Entered into getPlan :: plan_id : "+plan_id);
		List<JPlanInfo> planList = new ArrayList<>();
		try {
			
			String qry = "SELECT id,plan_name,enable,device_cnt,monitor_type,is_freeplan FROM plan WHERE id = :id";
			
			SQLQuery query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setResultTransformer(new AliasToBeanResultTransformer(JPlanInfo.class));
			query.setParameter("id", plan_id);
			
			query.addScalar("id", new LongType())
			.addScalar("plan_name", new StringType())
			.addScalar("enable", new BooleanType())
			.addScalar("device_cnt", new LongType())
			.addScalar("monitor_type", new LongType())
			.addScalar("is_freeplan", new BooleanType());
			
			planList = query.list();
			
			if( planList.isEmpty() ) {
				log.info("No plan found");
				return null;
			}
			
			return planList.get(0);
		} catch (Exception e) {
			log.error("Error in getPlan :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public UserRetained getUserRetainedById(long id) {
		log.info("Entered into getUserRetainedById :: id : "+ id);
		try {
			List userRestainList = sessionFactory.getCurrentSession().createCriteria(UserRetained.class)
					.add( Restrictions.eq("id", id) )
					.list();

			if( userRestainList.isEmpty() ) {
				log.info("user retained not found for id : "+id);
				return null;
			}

			return (UserRetained) userRestainList.get(0);
		} catch (Exception e) {
			log.error("Error in getUserRetainedById :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public UserRetained getUserRetainedByUserId(long user_id) {
		log.info("Entered into getUserRetainedByUserId :: user_id : "+ user_id);
		try {
			List userRestainList = sessionFactory.getCurrentSession().createCriteria(UserRetained.class)
									.add( Restrictions.eq("user_id", user_id) )
									.list();
			
			if( userRestainList.isEmpty() ) {
				log.info("user retained not found for user_id : "+user_id);
				return null;
			}
			
			return (UserRetained) userRestainList.get(0);
		} catch (Exception e) {
			log.error("Error in getUserRetainedByUserId :: Error : "+e.getLocalizedMessage());
		}
		return null;		
	}

	@Override
	public UserRetained saveOrUpdateUserRetained(UserRetained userRetained) {
		log.info("Entered into saveOrUpdateUserRetained :: user_id : "+ userRetained.getUser_id());
		try {
			return (UserRetained) sessionFactory.getCurrentSession().merge(userRetained);
		} catch (Exception e) {
			log.error("Error in saveOrUpdateUserRetained :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public boolean checkRenewalDateInUserRetained(long user_id) {
		log.info("Entered into checkRenewalDateInUserRetained :: user_id : "+user_id);
		try {
			String qry = "SELECT * FROM `user_retained` WHERE user_id = "+ user_id +" AND activated=1 AND next_renewal_date >= '"+ _helper.getCurrentTimeinUTC() +"';";
			List  userRetainedList = sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			
			if( userRetainedList.isEmpty() ) {
				return false;
			}else 
				return true;
			
		} catch (Exception e) {
			log.error("Error in checkRenewalDateInUserRetained :: Error : "+ e.getLocalizedMessage());
		}
		return true;
	}

	@Override
	public String getRechargeSubscriptionId(String username) {
		log.info("Entered into getRechargeSubscriptionId :: username : "+ username);
		try {
			
			String qry = "SELECT sub_id FROM `recharge_latest_sub_history` RLSH "
					+ "JOIN `user` U ON U.recharge_custid = RLSH.customer_id "
					+ "WHERE U.username = '" + username + "';";
			
			List rechargeIdList = sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			
			if( rechargeIdList.isEmpty() ) {
				log.info("There is no recharge customer id for username : "+username);
				return "NA";
			}
			
			return (String) rechargeIdList.get(0);
		} catch (Exception e) {
			log.error("Error in getRechargeSubscriptionId :: Error : "+ e.getLocalizedMessage());
		}
		return "NA";
	}
	
	@Override
	public PlanMigration getCurrentValidPlan(long plan_id, long period_id) {
		log.info("Entered into getCurrentValidPlan :: plan_id : "+ plan_id+" :: period_id : "+ period_id);
		try {
			
			List planMigration = sessionFactory.getCurrentSession().createCriteria(PlanMigration.class)
			.add( Restrictions.eq("old_plan_id", plan_id) )
			.add( Restrictions.eq("old_period_id", period_id) )
			.list();
			
			if( planMigration.isEmpty() ) {
				log.info("plan_migration not found");
			} else {
				log.info("plan_migration found");
				return (PlanMigration) planMigration.get(0);
			}
			
		} catch (Exception e) {
			log.error("Error in getCurrentValidPlan :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}
	
	@Override
	public long findaqiqndcoenabledevice(long userid) {
		log.info("Entered into findaqiqndcoenabledevice :: userid : "+ userid);
		try {
			
			String qry = "SELECT G.id FROM gateway G join usergateway UG on UG.gatewayId = G.id "
					+ "JOIN assetmodel AM on AM.id = G.model_id AND AM.is_aqi = 1 "
					+ " WHERE UG.userId = '" + userid + "';";
			
			List gatewayIdList = sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			
			if( gatewayIdList.isEmpty() ) {
				log.info("There is no aqi gateway in user id : "+userid);
				return 0;
			}
			long gatewayId = ((BigInteger)  gatewayIdList.get(0)).longValue();
			return gatewayId;
		} catch (Exception e) {
			log.error("Error in findaqiqndcoenabledevice :: Error : "+ e.getLocalizedMessage());
		}
		return 0;
	}

	@Override
	public JGatewayFeature getGatewayFeatureById(Long gateway_id) {
		log.info("Entered into getGatewayFeatureById :: gatewayid :"+ gateway_id);
		JGatewayFeature gatewayFeature = new JGatewayFeature();
		try {
			String qry = "SELECT gateway_id,plan_id,sub_id,period_id FROM gateway_feature GF WHERE "
					+ "GF.gateway_id=:gatewayid and enable=1 limit 1;";
			
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("gatewayid", gateway_id);
			
			List<Object[]> gatewayIdList = (List<Object[]>) query.list();
			
			if( gatewayIdList.isEmpty() ) {
				log.info("There is no gateway feature");
				return gatewayFeature;
			}
			
			Object[] obj = gatewayIdList.get(0);
			gatewayFeature.setGateway_id(((BigInteger) obj[0]).longValue());
			gatewayFeature.setPlan_id(((BigInteger) obj[1]).intValue());
			if (obj[2] != null) {
				gatewayFeature.setSub_id((String) obj[2]);
			}
			gatewayFeature.setPeriod_id(((BigInteger) obj[3]).intValue());
			return gatewayFeature;
		} catch (Exception e) {
			log.error("Error in getGatewayFeatureById :: Error : "+ e.getLocalizedMessage());
		}
		return gatewayFeature;
	}
	
	//@Override
	public JResponse upgradeSubPlanList_v5(UserV4 user, long curplan_id, long curperiod_id,
			String country, long monitor_type) {
		log.info("Entered into upgradePlanList_v5 :: user_id : " + user.getId());
		JResponse response = new JResponse();
		try {

			long device_count = crService.getDeviceCountByUser(user.getId(), monitor_type);

			String qry = "";

			JPlanInfo plan = crService.getPlan(curplan_id);
			if (plan == null) {
				plan = new JPlanInfo();
			}

			qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pl.orderno,"
					+ "pp.content_1,pl.is_freeplan FROM plan_to_upgrade pu "
					+ "JOIN plan_to_period pp ON pu.upgradeplan_id=pp.id JOIN plan pl ON pl.id = pp.plan_id JOIN sub_period sp ON sp.id=pp.sub_period_id "
					+ "WHERE pl.plan_ver='V3' AND pl.plan_type='Data-Plan' AND pl.device_cnt >="+device_count
					+ "AND pl.enable=1 AND pl.custom=0 AND pp.enable=1 AND pp.custom=0 AND "
					+ "pu.plan_to_period_id IN(SELECT id FROM plan_to_period WHERE  plan_id="+curplan_id
					+ "AND sub_period_id="+curperiod_id+" ) AND pl.monitor_type="+monitor_type+" AND pp.country_code= '"+country+"' ORDER BY pl.orderno ASC,pp.plan_id,sp.id;";

			
			log.info(qry);
			
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List<Object[]> planResp = query.list();
			long plan_id = 0;
			long per_id = 0;
			String plan_name = "";
			boolean freetrial_avail = false;
			String buynow_desc = "PROTECT MY PET";
			String trial_desc = "";
			int trial_days = 0;
			String content1 = "";
			String content2 = "";
			String content3 = "";
			String content4 = "";
			String strike_price_v2 = "NA";
			boolean is_best_seller = false;
			String img_url = "NA";
			String feature_list_dark_flutter = "NA";
			boolean is_best_deal = false;

			ArrayList<JSubscriptionPlan> plan_list = new ArrayList<JSubscriptionPlan>();
			ArrayList<JSubscriptionPeriod> period_list = new ArrayList<JSubscriptionPeriod>();

			if (!planResp.isEmpty()) {
				int size = planResp.size();
				log.info("upgradePlanList : size:" + size);
				JSubscriptionPlan subsPlan = new JSubscriptionPlan();
				Object[] tuple = (Object[]) planResp.get(0);
				boolean user_show_benefits = crService.checkAdditionalBenifits(user.getEmail());
				boolean show_benefits = false;

				if (size == 1) {
					plan_id = ((BigInteger) tuple[0]).longValue();
					per_id = ((BigInteger) tuple[2]).longValue();
					String feature_list = (String) tuple[3];
					String plan_price = (String) tuple[4];
					plan_name = (String) tuple[1];
					trial_days = (int) tuple[6];
					content1 = (String) tuple[7];
					content2 = (String) tuple[8];
					content3 = (String) tuple[9];
					content4 = "NA";// (String) tuple[10];
					strike_price_v2 = (((Integer) tuple[11]) == 0) ? "NA" : "$" + ((Integer) tuple[11]);
					String feature_list_flutter = (String) tuple[12];
					is_best_deal = (boolean) tuple[13];
					img_url = (String) tuple[14];
					feature_list_dark_flutter = (String) tuple[15];

					subsPlan.setPlanid(plan_id);
					subsPlan.setPlanname(plan_name);
					subsPlan.setFeature_list(feature_list);
					subsPlan.setFreetrial_avail(freetrial_avail);
					subsPlan.setIs_best_seller(is_best_seller);
					subsPlan.setFeature_list_flutter(feature_list_flutter);
					subsPlan.setFeature_list_dark_flutter(feature_list_dark_flutter);

					JSubscriptionPeriod periodObj = new JSubscriptionPeriod();
					periodObj.setPeriod_id(per_id);
					periodObj.setPlan_id(plan_id);

					periodObj.setPrice_detail(plan_price);
					periodObj.setBuynow_desc(buynow_desc);
					periodObj.setContent_1(content1);
					periodObj.setContent_2(content2);
					periodObj.setContent_3(content3);
					periodObj.setContent_4(content4);
					periodObj.setStrike_price_v2(strike_price_v2);
					periodObj.setIs_best_deal(is_best_deal);
					periodObj.setImg_url(img_url);
					LinkedList<String> benefits = new LinkedList<String>();

					if (user_show_benefits) {
						benefits = crService.listPlanBenefits(per_id);
						show_benefits = true;
					}

					if (benefits.isEmpty())
						show_benefits = false;

					periodObj.setShow_benefits(show_benefits);
					periodObj.setBenefits(benefits);

					period_list.add(periodObj);
					subsPlan.setPeriod_list(period_list);
					plan_list.add(subsPlan);
				}

				for (int i = 0; i < size - 1; i++) {
					is_best_seller = false;
					tuple = (Object[]) planResp.get(i);
					Object[] tupleNext = (Object[]) planResp.get(i + 1);
					plan_id = ((BigInteger) tuple[0]).longValue();
					per_id = ((BigInteger) tuple[2]).longValue();
					String feature_list = (String) tuple[3];
					String plan_price = (String) tuple[4];
					plan_name = (String) tuple[1];
					trial_days = (int) tuple[6];
					content1 = (String) tuple[7];
					content2 = (String) tuple[8];
					content3 = (String) tuple[9];
					content4 = "NA";// (String) tuple[10];
					strike_price_v2 = (((Integer) tuple[11]) == 0) ? "NA" : "$" + ((Integer) tuple[11]);
					String feature_list_flutter = (String) tuple[12];
					is_best_deal = (boolean) tuple[13];
					img_url = (String) tuple[14];
					feature_list_dark_flutter = (String) tuple[15];

					subsPlan.setPlanid(plan_id);
					subsPlan.setPlanname(plan_name);
					subsPlan.setFeature_list(feature_list);
					subsPlan.setFreetrial_avail(freetrial_avail);
					subsPlan.setIs_best_seller(is_best_seller);
					subsPlan.setFeature_list_flutter(feature_list_flutter);
					subsPlan.setFeature_list_dark_flutter(feature_list_dark_flutter);

					long plan_id_next = ((BigInteger) tupleNext[0]).longValue();
					String plan_name_next = (String) tupleNext[1];
					long per_id_next = ((BigInteger) tupleNext[2]).longValue();
					String feature_list_next = (String) tupleNext[3];
					String plan_price_next = (String) tupleNext[4];
					int trial_days_next = (int) tupleNext[6];
					boolean freetrial_avail_next = false;
					String buynow_desc_next = "PROTECT MY PET";
					String trial_desc_next = "";
					String content1_next = (String) tupleNext[7];
					String content2_next = (String) tupleNext[8];
					String content3_next = (String) tupleNext[9];
					String content4_next = "NA";// (String) tupleNext[10];
					String strike_price_v2_next = (((Integer) tupleNext[11]) == 0) ? "NA"
							: "$" + ((Integer) tupleNext[11]);
					boolean is_best_seller_next = false;
					String img_url_next = (String) tupleNext[14];
					boolean is_best_deal_next = (boolean) tupleNext[13];
					;
					String feature_list_flutter_next = (String) tupleNext[12];
					String feature_list_dark_flutter_next = (String) tupleNext[15];
				

					JSubscriptionPeriod periodObj = new JSubscriptionPeriod();

					periodObj.setPeriod_id(per_id);
					periodObj.setPlan_id(plan_id);
					periodObj.setPrice_detail(plan_price);
					periodObj.setBuynow_desc(buynow_desc);
					periodObj.setContent_1(content1);
					periodObj.setContent_2(content2);
					periodObj.setContent_3(content3);
					periodObj.setContent_4(content4);
					periodObj.setStrike_price_v2(strike_price_v2);
					periodObj.setImg_url(img_url);
					periodObj.setIs_best_deal(is_best_deal);

					LinkedList<String> benefits = new LinkedList<String>();
					if (user_show_benefits) {
						benefits = crService.listPlanBenefits(per_id);
						show_benefits = true;
					}

					if (benefits.isEmpty())
						show_benefits = false;

					periodObj.setShow_benefits(show_benefits);
					periodObj.setBenefits(benefits);

					period_list.add(periodObj);

					if (plan_id != plan_id_next) {
						subsPlan.setPeriod_list(period_list);
						plan_list.add(subsPlan);
						subsPlan = new JSubscriptionPlan();
						period_list = new ArrayList<JSubscriptionPeriod>();
						freetrial_avail = false;
					}

					if (i == (size - 2)) {
						JSubscriptionPeriod periodObj_next = new JSubscriptionPeriod();

						periodObj_next.setPeriod_id(per_id_next);
						periodObj_next.setPlan_id(plan_id_next);
						periodObj_next.setPrice_detail(plan_price_next);
						periodObj_next.setBuynow_desc(buynow_desc_next);
						periodObj_next.setContent_1(content1_next);
						periodObj_next.setContent_2(content2_next);
						periodObj_next.setContent_3(content3_next);
						periodObj_next.setContent_4(content4_next);
						periodObj_next.setStrike_price_v2(strike_price_v2_next);
						periodObj_next.setIs_best_deal(is_best_deal_next);
						periodObj_next.setImg_url(img_url_next);

						benefits = new LinkedList<String>();
						if (user_show_benefits) {
							benefits = crService.listPlanBenefits(per_id_next);
							show_benefits = true;
						}

						if (benefits.isEmpty())
							show_benefits = false;

						periodObj_next.setShow_benefits(show_benefits);
						periodObj_next.setBenefits(benefits);

						if (plan_id == plan_id_next) {
							period_list.add(periodObj_next);
						} else {
							subsPlan = new JSubscriptionPlan();
							period_list = new ArrayList<JSubscriptionPeriod>();

							subsPlan.setPlanid(plan_id_next);
							subsPlan.setPlanname(plan_name_next);
							subsPlan.setFeature_list(feature_list_next);
							subsPlan.setFreetrial_avail(freetrial_avail_next);
							subsPlan.setIs_best_seller(is_best_seller_next);
							subsPlan.setFeature_list_flutter(feature_list_flutter_next);
							subsPlan.setFeature_list_dark_flutter(feature_list_dark_flutter_next);

							period_list.add(periodObj_next);

						}
						subsPlan.setPeriod_list(period_list);
						plan_list.add(subsPlan);
					}
				}

			}
			response.put("Status", 1);
			response.put("Msg", "Success");

			response.put("planlist", plan_list);
			// for custom plan
			response.put("planname", "To add monitor or to customize the existing plan, please contact Support");
			response.put("contactnumber", supportcontactnumber.get(country));
			response.put("email", supportemail.get(country));

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Contact Support");

			return response;
		}
		return response;
	}

	@Override
	public JResponse getupgradesubplansV5(UserV4 user, long curplan_id, long curperiod_id,long monitor_type,String country,
			boolean defaultFreePlan,long gatewayid, String type) {
		log.info("Entered into upgradePlanList_v5 :: user_id : " + user.getId());
		JResponse response = new JResponse();
		try {

			//long device_count = crService.getDeviceCountByUser(user.getId());
			String planName="";
			String qry = "";
			JPlanInfo plan = crService.getPlan(curplan_id);
			if (plan == null) {
				plan = new JPlanInfo();
			}

			if (type.equalsIgnoreCase("upgrade")) {

				qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pl.orderno, pp.content_1,pl.is_freeplan,"
						+ "sp.period_name,strike_price,plan_price ,content_2,pp.free_trial_days,content_4,content_5,content_3,pp.is_best_deal FROM plan_to_upgrade pu "
						+ " JOIN plan_to_period pp ON pu.upgradeplan_id=pp.id JOIN plan pl ON pl.id = pp.plan_id JOIN sub_period sp"
						+ " ON sp.id=pp.sub_period_id "
						+ " WHERE pl.plan_ver='V3' AND pl.plan_type='Data-Plan' "
						+ " AND pl.enable=1 AND pl.custom=0 AND pp.enable=1 AND pp.custom=0 AND "
						+ " pu.plan_to_period_id IN(SELECT id FROM plan_to_period WHERE  plan_id=:planid"
						+ " AND sub_period_id=:periodid ) AND pl.monitor_type=:monitorid AND pp.country_code=:countrycode "
						+ "ORDER BY pl.orderno ASC,sp.id DESC;";
			} else {
				if (type.equalsIgnoreCase("downgrade")) {
					qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pl.orderno, pp.content_1,pl.is_freeplan,"
							+ "sp.period_name,strike_price,plan_price ,content_2,pp.free_trial_days,content_4,content_5,content_3,pp.is_best_deal  FROM plan_to_upgrade pu "
							+ " JOIN plan_to_period pp ON pu.plan_to_period_id=pp.id JOIN plan pl ON pl.id = pp.plan_id JOIN sub_period sp"
							+ " ON sp.id=pp.sub_period_id "
							+ " WHERE pl.plan_ver='V3' AND pl.plan_type='Data-Plan' "
							+ " AND pl.enable=1 AND pl.custom=0 AND pp.enable=1 AND pp.custom=0 AND "
							+ " pu.upgradeplan_id IN(SELECT id FROM plan_to_period WHERE  plan_id=:planid"
							+ " AND sub_period_id=:periodid ) AND pl.monitor_type=:monitorid AND pp.country_code=:countrycode "
							+ "ORDER BY pl.orderno ASC,sp.id DESC;";

				} else if (type.equalsIgnoreCase("cancelall")) {
					qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pl.orderno, pp.content_1,pl.is_freeplan, "
							+ " sp.period_name,strike_price,plan_price ,content_2,pp.free_trial_days,content_4,content_5,content_3,pp.is_best_deal  FROM plan pl "
							+ " JOIN plan_to_period pp ON pl.id=pp.plan_id  JOIN sub_period sp "
							+ " ON sp.id=pp.sub_period_id  "
							+ " WHERE pl.plan_ver='V3' AND pl.plan_type='Data-Plan' and pp.sub_period_id !=:periodid  "
							+ " AND pl.enable=1 AND pl.custom=0 AND pp.enable=1 AND pl.monitor_type=:monitorid AND pp.country_code=:countrycode "
							+ " ORDER BY pl.orderno DESC,sp.id ASC; ";
				}else {

					qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pl.orderno, pp.content_1,pl.is_freeplan, "
							+ " sp.period_name,strike_price,plan_price ,content_2,pp.free_trial_days,content_4,content_5,content_3,pp.is_best_deal  FROM plan pl "
							+ " JOIN plan_to_period pp ON pl.id=pp.plan_id  JOIN sub_period sp "
							+ " ON sp.id=pp.sub_period_id  "
							+ " WHERE pl.plan_ver='V3' AND pl.plan_type='Data-Plan' "
							+ " AND pl.enable=1 AND pl.custom=0 AND pp.enable=1 AND pl.monitor_type=:monitorid AND pp.country_code=:countrycode "
							+ " ORDER BY pl.orderno DESC,sp.id ASC; ";
				}
			}

			log.info(qry);
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			if(type.equalsIgnoreCase("cancelall")) {
				query.setParameter("periodid", curperiod_id);
			}else if (!type.equalsIgnoreCase("all")) {
				query.setParameter("planid", curplan_id);
				query.setParameter("periodid", curperiod_id);
			}
			query.setParameter("monitorid", monitor_type);
			query.setParameter("countrycode", country);
			List<Object[]> planResp = query.list();
			long plan_id = 0;
			long per_id = 0;
			String content1 = "";
			String period_name = "";
			String free_buynow="";
			String strike_price="";
			String plan_price = "";
			String display_price="";
			boolean is_free = false;
			JSubPlanDetails planDetail = new JSubPlanDetails();
			String plan_name ="";
			int free_trial_days = 0;
			String display_msg="";
			String billed_price = "";
			String month_price = "";
			boolean is_best_deal = false;
			if (!planResp.isEmpty()) {
				int size = planResp.size();
				log.info("upgradePlanList : size:" + size);
				JSubPlanDetails subsPlan = new JSubPlanDetails();
				Object[] tuple = (Object[]) planResp.get(0);

				long free_plan = 0;
				long free_period = 0;
				boolean isfree_eligible = false;
				JPlanDetail free_features=null;
				JSubPeriodDetail periodObj = new JSubPeriodDetail();
				ArrayList<JSubPeriodDetail > periodlist = new ArrayList<JSubPeriodDetail>();

				int freeTrialPeriod = 0;
				boolean orderAvail = false;
				if(monitor_type == 6 && gatewayid>0 && defaultFreePlan) {
					String[] dtRange = orderdate_trial.split(":");

					orderAvail = gatewayServiceV4.checkOrderWithinRange(gatewayid, dtRange[0], dtRange[1]);
				}
				if (size == 1) {
					plan_id = ((BigInteger) tuple[0]).longValue();
					plan_name = (String) tuple[1];
					per_id = ((BigInteger) tuple[2]).longValue();
					String feature_list = (String) tuple[3];
					JPlanDetail jplandetail = new JPlanDetail();

					try {

						jplandetail = new Gson().fromJson(feature_list, JPlanDetail.class);

					} catch (JSONException err) {
						log.error("jplandetail:"+err.getLocalizedMessage());
					}

					content1 = (String) tuple[5];
					is_free = (boolean)tuple[6];
					period_name = (String) tuple[7];
					strike_price= ((int)tuple[8] >0) ? "$"+tuple[8]: "";
					plan_price = ((String) tuple[9])==null ? "" : (String) tuple[9] ;
					display_price=(String) tuple[10];
					display_msg = (String) tuple[12];
					billed_price = (String) tuple[13];
					month_price = (String) tuple[14];
					is_best_deal = (boolean) tuple[15];

					if(is_free) {
						isfree_eligible = true;
						free_plan = plan_id;
						free_period =per_id;
						free_features = jplandetail;
						//long freeplan_id, long period_id, String free_buynow, long paidplan_id,
						//String period_name,	String paid_buynow
						periodObj = new JSubPeriodDetail(free_plan, free_period, content1, 0,period_name, "",
								strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal);
						periodlist.add(periodObj);

						if(monitor_type == 6 && orderAvail && defaultFreePlan) {
							free_trial_days = offer_days;
						}else {
							free_trial_days = defaultFreePlan ? (int) tuple[11] : 0;
						}

					}else {
						planName = plan_name;
						periodObj = new JSubPeriodDetail( 0,per_id, "",plan_id,period_name,content1,
								strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal);
						periodlist.add(periodObj);
					}

					planDetail.setFeature_list(jplandetail.getPlans());
					planDetail.setPeriod_list(periodlist);

				}

				for (int i = 0; i < size - 1; i++) {
					tuple = (Object[]) planResp.get(i);
					Object[] tupleNext = (Object[]) planResp.get(i + 1);
					plan_id = ((BigInteger) tuple[0]).longValue();
					plan_name = (String) tuple[1];
					per_id = ((BigInteger) tuple[2]).longValue();
					String feature_list = (String) tuple[3];
					JPlanDetail jplandetail = new JPlanDetail();
					content1 = (String) tuple[5];
					is_free = (boolean)tuple[6];
					period_name = (String) tuple[7];
					strike_price= ((int)tuple[8] >0) ? "$"+tuple[8]: "";
					plan_price = ((String) tuple[9])==null ? "" : (String) tuple[9] ;

					display_price=(String) tuple[10];
					display_msg = (String) tuple[12];
					billed_price = (String) tuple[13];
					month_price = (String) tuple[14];
					is_best_deal = (boolean) tuple[15];
					try {
						jplandetail = new Gson().fromJson(feature_list, JPlanDetail.class);
					} catch (JSONException err) {
						log.error("jplandetail:"+err.getLocalizedMessage());
					}
					if(is_free) {
						isfree_eligible = true;
						free_plan = plan_id;
						free_period =per_id;
						free_features = jplandetail;
						free_buynow = content1;

						if(monitor_type == 6 && orderAvail && defaultFreePlan) {
							free_trial_days = offer_days;
						}else {
							free_trial_days = defaultFreePlan ? (int) tuple[11] : 0;
						}
					}else
						planName = plan_name;

					long plan_id_next = ((BigInteger) tupleNext[0]).longValue();
					String plan_name_next = (String) tupleNext[1];
					long per_id_next = ((BigInteger) tupleNext[2]).longValue();
					String feature_list_next = (String) tupleNext[3];
					JPlanDetail jplandetailNext = new JPlanDetail();
					boolean is_free_next = (boolean)tupleNext[6];
					String content1_next = (String) tupleNext[5];
					String period_name_next = (String) tupleNext[7];
					String strike_price_next = ((int)tupleNext[8] >0) ? "$"+tupleNext[8]: "";
					String plan_price_next = ((String) tupleNext[9])==null ? "" : (String) tupleNext[9] ;
					String display_price_next=(String) tupleNext[10];
					String display_msg_next = (String) tupleNext[12];
					String billed_price_next = (String) tupleNext[13];
					String month_price_next = (String) tupleNext[14];
					boolean is_best_deal_next = (boolean) tupleNext[15];
					try {
						jplandetailNext = new Gson().fromJson(feature_list_next, JPlanDetail.class);
					} catch (JSONException err) {
						log.error("jplandetail:"+err.getLocalizedMessage());
					}

					if(is_free_next) {
						free_plan = plan_id_next;
						free_period =per_id_next;
						free_features = jplandetailNext;
						if(monitor_type == 6 && orderAvail && defaultFreePlan) {
							free_trial_days = offer_days;
						}else {
							free_trial_days = defaultFreePlan ? (int) tupleNext[11] : 0;
						}
					}
					if ((plan_id != plan_id_next)) {
						if (isfree_eligible) {
							periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id_next, content1,
									plan_id_next,period_name_next , content1_next,
									strike_price_next, plan_price_next,  display_price_next,display_msg_next,billed_price_next,month_price_next,is_best_deal_next);
							periodlist.add(periodObj);
							planDetail.setFeature_list(jplandetail.getPlans());
							planDetail.setPeriod_list(periodlist);
						} else {
							periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
									plan_id,period_name , content1,
									strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal);
							periodlist.add(periodObj);
							planDetail.setFeature_list(jplandetail.getPlans());
							planDetail.setPeriod_list(periodlist);
						}
					}
					else if ((plan_id == plan_id_next) && !isfree_eligible){
						periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, "", plan_id, period_name, content1,
								strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal);
						periodlist.add(periodObj);
					}

					if (i == (size - 2)) {
						JSubPeriodDetail periodObj_next = new JSubPeriodDetail();

						if(isfree_eligible) {
							planDetail.setFeature_list(free_features.getPlans());
							periodObj_next = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id_next, free_buynow, plan_id_next, period_name_next, content1_next,
									strike_price_next, plan_price_next,  display_price_next,display_msg_next,billed_price_next,month_price_next,is_best_deal_next);
						}
						else {
							planDetail.setFeature_list(jplandetail.getPlans());
							periodObj_next = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id_next, "", plan_id_next, period_name_next, content1_next,
									strike_price_next, plan_price_next,  display_price_next,display_msg_next,billed_price_next,month_price_next,is_best_deal_next);
						}
						periodlist.add(periodObj_next);
						planDetail.setPeriod_list(periodlist);
					}
				}
			}

			String free_text = monitor_type == 4 || monitor_type==1 ? "" : " for free";
			String plan_lbl = plan_name+ (defaultFreePlan ? free_text :"") +" benefits";
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("planlist", planDetail);
			response.put("plan_lbl",plan_lbl);

			response.put("popup", "Worried about your furry friend's well-being? Enjoy $~"+free_trial_days+"-days trial of the "+plan_name+", for your pet's health.");
//			response.put("popup", "Are you worried about your pet's well-being? For the love of pets, we would like to offer a $~"+free_trial_days+"-days free trial$ of our "+plan_name+".");			// for custom plan
			// for custom plan
			response.put("planname", "Please contact Support");
			response.put("contactnumber", supportcontactnumber.get(country));
			response.put("email", supportemail.get(country));

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Contact Support");

			return response;
		}
		return response;
	}
	
	@Override
	public List<Object[]> getUpgradeSubPlanV6(UserV4 user, long curplan_id, long curperiod_id, long monitor_type, String country,
											  boolean defaultFreePlan, long gatewayid, String type, String plantype, boolean is_flexi) {
		log.info("Entered into upgradePlanList_v5 :: user_id : " + user.getId());
		try {
			String planName="";
			String qry = "";
			JPlanInfo plan = crService.getPlan(curplan_id);
			if (plan == null) {
				plan = new JPlanInfo();
			}
			
			if (type.equalsIgnoreCase("upgrade")) {

			qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pl.orderno, pp.content_1,pl.is_freeplan,"
					+ "sp.period_name,strike_price,plan_price ,content_2,pp.free_trial_days,content_4,content_5,content_3,pp.is_best_deal,pl.`feature_list_ui_new` FROM plan_to_upgrade pu "
					+ " JOIN plan_to_period pp ON pu.upgradeplan_id=pp.id JOIN plan pl ON pl.id = pp.plan_id JOIN sub_period sp"
					+ " ON sp.id=pp.sub_period_id "
					+ " WHERE pl.plan_ver='V3' AND pl.plan_type IN( :plantype ) "
					+ " AND pl.enable=1 AND pl.custom=0 AND pp.enable=1 AND pp.custom=0 AND "
					+ " pu.plan_to_period_id IN(SELECT id FROM plan_to_period WHERE  plan_id=:planid"
					+ " AND sub_period_id=:periodid ) AND pl.monitor_type=:monitorid AND pp.country_code=:countrycode "
					+ "ORDER BY pp.plan_id ASC ,pl.orderno ASC,sp.id DESC;";
			} else {
				if (type.equalsIgnoreCase("downgrade")) {
					String restrict_flexi_plan = " AND pl.plan_name NOT LIKE '%flexi%' ";
					if(is_flexi) {
						restrict_flexi_plan = "";
					}
					qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pl.orderno, pp.content_1,pl.is_freeplan,"
							+ "sp.period_name,strike_price,plan_price ,content_2,pp.free_trial_days,content_4,content_5,content_3,pp.is_best_deal,pl.`feature_list_ui_new`  FROM plan_to_upgrade pu "
							+ " JOIN plan_to_period pp ON pu.plan_to_period_id=pp.id JOIN plan pl ON pl.id = pp.plan_id JOIN sub_period sp"
							+ " ON sp.id=pp.sub_period_id "
							+ " WHERE pl.plan_ver='V3' AND pl.plan_type IN( :plantype ) "
							+ " AND pl.enable=1 AND pl.custom=0 AND pp.enable=1 AND pp.custom=0 AND "
							+ " pu.upgradeplan_id IN(SELECT id FROM plan_to_period WHERE  plan_id=:planid"
							+ " AND sub_period_id=:periodid ) AND pl.monitor_type=:monitorid AND pp.country_code=:countrycode "
							+ restrict_flexi_plan
							+ "ORDER BY pp.plan_id ASC ,pl.orderno ASC,sp.id DESC;";
					
				} else if (type.equalsIgnoreCase("cancelall")) {
					qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pl.orderno, pp.content_1,pl.is_freeplan, "
							+ " sp.period_name,strike_price,plan_price ,content_2,pp.free_trial_days,content_4,content_5,content_3,pp.is_best_deal,`feature_list_ui_new`  FROM plan pl "
							+ " JOIN plan_to_period pp ON pl.id=pp.plan_id  JOIN sub_period sp "
							+ " ON sp.id=pp.sub_period_id  "
							+ " WHERE pl.plan_ver='V3' AND pl.plan_type IN( :plantype ) and pp.sub_period_id !=:periodid  "
							+ " AND pl.enable=1 AND pl.custom=0 AND pp.enable=1 AND pl.monitor_type=:monitorid AND pp.country_code=:countrycode "
							+ " ORDER BY pp.plan_id ASC ,pl.orderno ASC,sp.id DESC; ";
				}else {
					
					qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pl.orderno, pp.content_1,pl.is_freeplan, "
							+ " sp.period_name,strike_price,plan_price ,content_2,pp.free_trial_days,content_4,content_5,content_3,pp.is_best_deal,`feature_list_ui_new`  FROM plan pl "
							+ " JOIN plan_to_period pp ON pl.id=pp.plan_id  JOIN sub_period sp "
							+ " ON sp.id=pp.sub_period_id  "
							+ " WHERE pl.plan_ver='V3' AND pl.plan_type IN( :plantype ) "
							+ " AND pl.enable=1 AND pl.custom=0 AND pp.enable=1 AND pl.monitor_type=:monitorid AND pp.country_code=:countrycode "
							+ " ORDER BY pp.plan_id ASC ,pl.orderno ASC,sp.id DESC; ";
				}
			}
			
			log.info(qry);
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			if(type.equalsIgnoreCase("cancelall")) {
				query.setParameter("periodid", curperiod_id);
			}else if (!type.equalsIgnoreCase("all")) {
				query.setParameter("planid", curplan_id);
				query.setParameter("periodid", curperiod_id);
			}
			List<String> plantypes = plantype.contains(",") ?
					Arrays.asList(plantype.split("\\s*,\\s*")) :
					Collections.singletonList(plantype);
			query.setParameterList("plantype", plantypes);
			query.setParameter("monitorid", monitor_type);
			query.setParameter("countrycode", country);
			List<Object[]> planResp = query.list();
			
			return planResp;

		} catch (Exception e) {
			log.error("Error in getUpgradeSubPlanV6 : "+e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public List<Object[]> getUpgradeSubPlanV7(UserV4 user, long curplan_id, long curperiod_id, long monitor_type, String country,
											  boolean defaultFreePlan, long gatewayid, String type, String plantype, boolean is_flexi) {
		log.info("Entered into getUpgradeSubPlanV7 :: user_id : " + user.getId());
		try {
			String planName = "";
			String qry = "";
			String subQry1 = "";
			String subQry2 = "";

			JPlanInfo plan = crService.getPlan(curplan_id);

			boolean isComboExists = crService.checkComboExists(user.getChargebeeid());

			boolean isComboExistsinallCha = crService.checkComboExistsinallchargebee(user.getChargebeeid());
			if (plan == null) {
				plan = new JPlanInfo();
			}

			if (type.equalsIgnoreCase("upgrade")) {

				if(plantype.contains("vet-plan")) {
					qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pl.orderno, pp.content_1,pl.is_freeplan,"
							+ "sp.period_name,strike_price,plan_price ,content_2,pp.free_trial_days,content_4,content_5,content_3,pp.is_best_deal,"
							+ " pl.`feature_list_ui_new`,pl.plan_type,pl.description,pl.is_best_deal AS is_best_deal_plan,pp.compare_image,pp.product_image," +
							"pl.monitor_type,pp.free_minicam_avil,pl.display_msg,pl.product_list, pm.monitortype_id FROM plan_to_upgrade pu "
							+ " JOIN plan_to_period pp ON pu.upgradeplan_id=pp.id JOIN plan_to_monitortype pm ON pm.plan_id = pp.plan_id JOIN plan pl ON pl.id = pp.plan_id JOIN sub_period sp"
							+ " ON sp.id=pp.sub_period_id "
							+ " WHERE pl.plan_ver='V3' AND pl.plan_type IN( :plantype ) "
							+ " AND pl.enable=1 AND pl.custom=0 AND pp.enable=1 AND pp.custom=0 AND "
							+ " pu.plan_to_period_id IN(SELECT id FROM plan_to_period WHERE  plan_id=:planid"
							+ " AND sub_period_id=:periodid ) AND pl.monitor_type=:monitorid AND pp.country_code=:countrycode "
							+ "ORDER BY pp.plan_id ASC ,pl.orderno ASC,sp.id DESC;";
				}else {
					if((!isComboExists && !isComboExistsinallCha) &&(monitor_type==8 || monitor_type==12))
						subQry2 = " pl.id IN (62,59) AND "; // hard coded to list only 4g combo plan
					else if((!isComboExists && !isComboExistsinallCha) && monitor_type!=1 )
						subQry2 = " pl.id NOT IN (62) AND "; // handled for monitortype other than 8 and 12 WagWatch should not be listed

					if(!isComboExists && !isComboExistsinallCha)
						subQry1 = "	 UNION ALL "
								+ "	SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pl.orderno, pp.content_1,pl.is_freeplan,sp.period_name,"
								+ "	strike_price,plan_price ,content_2,pp.free_trial_days,content_4,content_5,content_3,pp.is_best_deal,pl.`feature_list_ui_new`,"
								+ "plan_type,pl.description,pl.is_best_deal AS is_best_deal_plan,pp.compare_image,pp.product_image,"
								+ "	pl.monitor_type,pp.free_minicam_avil,pl.display_msg,pl.product_list, pm.monitortype_id"
								+ "	FROM plan_to_upgrade pu  JOIN plan_to_period pp ON pu.upgradeplan_id=pp.id JOIN plan_to_monitortype pm ON pm.plan_id = pp.plan_id JOIN plan pl ON pl.id = pp.plan_id JOIN sub_period sp ON "
								+ "	sp.id=pp.sub_period_id  WHERE "+subQry2+" pl.plan_ver='V3' AND pl.plan_type IN( 'combo-plan')  AND pl.enable=1 AND pl.custom=0"
								+ "	AND pp.enable=1  AND pp.custom=0 AND  pp.country_code=:countrycode ";
					
					qry = " SELECT A.plan_id,A.plan_name,period_id,A.feature_list,A.orderno, A.content_1,A.is_freeplan,A.period_name,"
						+ "  strike_price,plan_price ,content_2,A.free_trial_days,content_4,content_5,content_3,A.is_best_deal,"
						+ " A.`feature_list_ui_new` ,plan_type,description,A.is_best_deal_plan,compare_image,product_image,monitor_type,free_minicam_avil,display_msg ,product_list, monitortype_id"
						+ "  FROM (SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pl.orderno, pp.content_1,pl.is_freeplan,"
						+ "sp.period_name,strike_price,plan_price ,content_2,pp.free_trial_days,content_4,content_5,content_3,pp.is_best_deal,"
						+ " pl.`feature_list_ui_new` ,pl.plan_type,pl.description,pl.is_best_deal AS is_best_deal_plan,pp.compare_image,pp.product_image," +
						"pl.monitor_type,pp.free_minicam_avil,pl.display_msg,pl.product_list, pm.monitortype_id FROM plan_to_upgrade pu "
						+ " JOIN plan_to_period pp ON pu.upgradeplan_id=pp.id JOIN plan_to_monitortype pm ON pm.plan_id = pp.plan_id JOIN plan pl ON pl.id = pp.plan_id JOIN sub_period sp"
						+ " ON sp.id=pp.sub_period_id " + " WHERE pl.plan_ver='V3' AND pl.plan_type IN( :plantype ) "
						+ " AND pl.enable=1 AND pl.custom=0 AND pp.enable=1 AND pp.custom=0 AND "
						+ " pu.plan_to_period_id IN(SELECT id FROM plan_to_period WHERE  plan_id=:planid"
						+ " AND sub_period_id=:periodid ) AND pl.monitor_type=:monitorid AND pp.country_code=:countrycode "
						+ subQry1 +") AS A "
						+ "  ORDER BY orderno ASC,period_id DESC";
				}
			} else {
				if (type.equalsIgnoreCase("downgrade")) {
					String restrict_flexi_plan = " AND pl.plan_name NOT LIKE '%flexi%' ";
					if (!is_flexi) {
						restrict_flexi_plan = "";
					}
					qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pl.orderno, pp.content_1,pl.is_freeplan,"
							+ "sp.period_name,strike_price,plan_price ,content_2,pp.free_trial_days,content_4,content_5,content_3,"
							+ " pp.is_best_deal,pl.`feature_list_ui_new`,pl.plan_type,pl.description,pl.is_best_deal AS is_best_deal_plan," +
							"pp.compare_image,pp.product_image,pl.monitor_type,pp.free_minicam_avil,pl.display_msg,pl.product_list, pm.monitortype_id FROM plan_to_upgrade pu "
							+ " JOIN plan_to_period pp ON pu.plan_to_period_id=pp.id JOIN plan_to_monitortype pm ON pm.plan_id = pp.plan_id JOIN plan pl ON pl.id = pp.plan_id JOIN sub_period sp"
							+ " ON sp.id=pp.sub_period_id "
							+ " WHERE pl.plan_ver='V3' AND pl.plan_type IN( :plantype ) "
							+ " AND pl.enable=1 AND pl.custom=0 AND pp.enable=1 AND pp.custom=0 AND "
							+ " pu.upgradeplan_id IN(SELECT id FROM plan_to_period WHERE  plan_id=:planid"
							+ " AND sub_period_id=:periodid ) AND pl.monitor_type=:monitorid AND pp.country_code=:countrycode "
							+ restrict_flexi_plan + "ORDER BY pl.orderno ASC,sp.id DESC;";

				} else if (type.equalsIgnoreCase("cancelall")) {
					qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pl.orderno, pp.content_1,pl.is_freeplan, "
							+ " sp.period_name,strike_price,plan_price ,content_2,pp.free_trial_days,content_4,content_5,content_3,"
							+ " pp.is_best_deal,`feature_list_ui_new`,pl.plan_type,pl.description,pl.is_best_deal AS is_best_deal_plan," +
							"pp.compare_image,pp.product_image,pl.monitor_type,pp.free_minicam_avil,pl.display_msg,pl.product_list, pm.monitortype_id FROM plan pl "
							+ " JOIN plan_to_period pp ON pl.id=pp.plan_id JOIN plan_to_monitortype pm ON pm.plan_id = pp.plan_id JOIN sub_period sp "
							+ " ON sp.id=pp.sub_period_id  "
							+ " WHERE pl.plan_ver='V3' AND pl.plan_type IN( :plantype ) and pp.sub_period_id !=:periodid  "
							+ " AND pl.enable=1 AND pl.custom=0 AND pp.enable=1 AND pl.monitor_type=:monitorid AND pp.country_code=:countrycode "
							+ " ORDER BY pl.orderno ASC,sp.id DESC; ";
				} else {

					qry = "SELECT pp.plan_id,pl.plan_name,sp.id AS period_id,pl.feature_list,pl.orderno, pp.content_1,pl.is_freeplan, "
							+ " sp.period_name,strike_price,plan_price ,content_2,pp.free_trial_days,content_4,content_5,content_3,"
							+ " pp.is_best_deal,`feature_list_ui_new`,pl.plan_type,pl.description,pl.is_best_deal AS is_best_deal_plan," +
							"pp.compare_image,pp.product_image,pl.monitor_type,pp.free_minicam_avil,pl.display_msg,pl.product_list, pm.monitortype_id FROM plan pl "
							+ " JOIN plan_to_period pp ON pl.id=pp.plan_id JOIN plan_to_monitortype pm ON pm.plan_id = pp.plan_id JOIN sub_period sp "
							+ " ON sp.id=pp.sub_period_id  "
							+ " WHERE pl.plan_ver='V3' AND pl.plan_type IN( :plantype ) "
							+ " AND pl.enable=1 AND pl.custom=0 AND pp.enable=1 AND pl.monitor_type=:monitorid AND pp.country_code=:countrycode "
							+ " ORDER BY pp.plan_id ASC ,pl.orderno ASC,sp.id DESC; ";
				}
			}

			log.info(qry);
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			if (type.equalsIgnoreCase("cancelall")) {
				query.setParameter("periodid", curperiod_id);
			} else if (!type.equalsIgnoreCase("all")) {
				query.setParameter("planid", curplan_id);
				query.setParameter("periodid", curperiod_id);
			}
			List<String> plantypes = plantype.contains(",") ? Arrays.asList(plantype.split("\\s*,\\s*"))
					: Collections.singletonList(plantype);
			query.setParameterList("plantype", plantypes);
			query.setParameter("monitorid", monitor_type);
			query.setParameter("countrycode", country);
			List<Object[]> planResp = query.list();

			return planResp;

		} catch (Exception e) {
			log.error("Error in getUpgradeSubPlanV6 : "+e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public JProductSubResV2 getFlexiPlanHistory(Long gatewayId, String subscriptionId) {
		log.info("Entered into getFlexiPlanHistory :: "+ gatewayId);
		JProductSubResV2 productSub = new JProductSubResV2();
		try {
			String Qry = "SELECT * FROM flexi_plan_history WHERE " +
					"gateway_id = :gatewayid AND subscription_id = :subscriptionid ;";

			Query query = sessionFactory.getCurrentSession().createSQLQuery(Qry);
			query.setParameter("gatewayid", gatewayId);
			query.setParameter("subscriptionid", subscriptionId);

			List<Object[]> res = query.list();
			if (!res.isEmpty()) {
				Object[] obj = (Object[]) res.get(0);
				if (obj[4] != null) {
					productSub.setFlexi_plan_start_date(((Timestamp) obj[4]).toString());
				}
				if (obj[5] != null){
					productSub.setFlexi_next_billing_date(((Timestamp) obj[5]).toString());
				}
				if (obj[6] != null){
					productSub.setRemaining_month((int) obj[6]);
				}
				if (obj[7] != null){
					productSub.setIs_paused((boolean) obj[7]);
				}
				if (obj[9] != null){
					productSub.setPaused_count((int) obj[9]);
				}
				return productSub;
			}
		} catch (Exception e) {
			log.error("Error in getFlexiPlanHistory :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public long getPlanIdByCountry(long plan_id, String country, long monitorTyp) {
		log.info("Entered into getPlanIdByCountry :: planid : "+ plan_id+":"+country+":"+monitorTyp);
		try {
			String Qry = "SELECT id FROM plan WHERE is_freeplan =1 " 
					+ " AND monitor_type = :monitor_type"
			+ " AND country_code = :country_code limit 1;";
			
			Query query = sessionFactory.getCurrentSession().createSQLQuery(Qry);
			query.setParameter("country_code", country);
			query.setParameter("monitor_type", monitorTyp);
			
			long id = 0;
			List res = query.list();
			if (!res.isEmpty()) {
				id = ((BigInteger) res.get(0)).longValue();
			}
			
			return id;
		} catch (Exception e) {
			log.error("Error in getPlanIdByCountry :: Error : "+ e.getLocalizedMessage());
		}
		return 0;
	}

	@Override
	public List<JGatewayFeature> getGatewayFeaturesByPlanId(int plan_id, String sub_id) {
		log.info("Entered into getGatewayReportsByPlanId :: planid : "+ plan_id);
		try {
			List<JGatewayFeature> gatewayFeatureList = new ArrayList<JGatewayFeature>();
			String qry = "SELECT gateway_id,sub_id,enable FROM gateway_feature GF WHERE GF.plan_id = :planid " 
					+ " and enable=1 and sub_id=:subid GROUP BY GF.gateway_id;";
			
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("planid", plan_id);
			query.setParameter("subid", sub_id);
			List<Object[]> gatewayIdList = (List<Object[]>) query.list();
			
			if( gatewayIdList.isEmpty() ) {
				log.info("There is no gateway feature");
				return null;
			}
			
			for (Object[] obj : gatewayIdList) {
				JGatewayFeature gatewayFeature = new JGatewayFeature(); 
				gatewayFeature.setGateway_id(((BigInteger) obj[0]).longValue());
				gatewayFeature.setSub_id((String) obj[1]);
				gatewayFeature.setEnable((boolean) obj[2] ? 1 : 0);
				gatewayFeatureList.add(gatewayFeature);
			}
			
			return gatewayFeatureList;
		} catch (Exception e) {
			log.error("Error in getGatewayReportsByPlanId :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public String getMeariKey(long userID, long gatewayId, int period_id) {
		log.info("Entered into getMeariKey :: userid : "+ userID);
		try {
			String qry = "SELECT key_name FROM meari_subscription where gateway_id=:gatewayid AND is_activated = 1 AND "
					+ "UTC_TIMESTAMP() > activated_date AND UTC_TIMESTAMP() < expiry_date AND period_id=:periodid ";
			
			Query query = null;
			if (userID != 0) {
				query = sessionFactory.getCurrentSession().createSQLQuery(qry + "AND user_id=:userid");
				query.setParameter("gatewayid", gatewayId);
				query.setParameter("periodid", period_id);
				query.setParameter("userid", userID);
			} else {
				query = sessionFactory.getCurrentSession().createSQLQuery(qry);
				query.setParameter("gatewayid", gatewayId);
				query.setParameter("periodid", period_id);
			}

			List<String> meariKeyList = (List<String>) query.list();

			if( meariKeyList.isEmpty() ) {
				log.info("There is no key found");
				return "NA";
			}

			return meariKeyList.get(0);
		} catch (Exception e) {
			log.error("Error in getMeariKey :: Error : "+ e.getLocalizedMessage());
		}
		return "NA";
	}

	@Override
	public boolean isUpgradeAvailable(int plan_id, int period_id) {
		log.info("Entered isUpgradeAvailable : "+plan_id);
		try {
			String qry = "SELECT PU.id FROM plan_to_upgrade PU JOIN plan_to_period PP ON PU.plan_to_period_id = "
					+ "PP.id WHERE plan_id=:planid AND sub_period_id=:periodid";

			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("planid", plan_id);
			query.setParameter("periodid", period_id);
			List<BigInteger> planToUpgradeList = (List<BigInteger>) query.list();
			if( planToUpgradeList.isEmpty() ) {
				log.info("No upgrade plan found");
				return false;
			}

			return true;
		} catch (Exception e) {
			log.error("Error in isUpgradeAvailable : "+e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public long getMonitorTypeByCBPlan(String subs_planid) {
		log.info("Entered into getMonitorTypeByCBPlan ::plan_id : "+ subs_planid);
		try {
			String qry = "SELECT P.monitor_type "
					+ " FROM plan P "
					+ " JOIN plan_to_period PP ON PP.plan_id = P.id "
					+ " WHERE PP.chargebee_planid =:subs_planid ";
			
			List monitor_type_list = sessionFactory.getCurrentSession().createSQLQuery(qry)
			.setParameter("subs_planid", subs_planid)
			.list();
		
			if( monitor_type_list.isEmpty() ) {
				log.info("No monitor type found for subs_planid : "+subs_planid);
				return 0;
			}
			
			return ((BigInteger) monitor_type_list.get(0)).longValue();
		} catch (Exception e) {
			log.error("Error in getMonitorTypeByCBPlan :: Error : "+e.getLocalizedMessage());
		}
		return 0;
	}

	@Override
	public boolean updateSubsStatus(String status, String subId) {
		
		String updateQry = "UPDATE `all_product_subscription` SET `subscription_status` = '"+status+"' WHERE `subscription_id` = '"+subId+"';";

		int insertCount  = sessionFactory.getCurrentSession().createSQLQuery(updateQry).executeUpdate();
		if(insertCount>0)
			return true;
		else
			return false;
	}

	@Override
	public CancelCustomerRetain getCancelCustomerRetain(long user_id) {
		log.info("Entered into getCancelCustomerRetain :: user_id : "+user_id);
		try {
			List cancelCustomerRetain = sessionFactory.getCurrentSession().createCriteria( CancelCustomerRetain.class ).add( Restrictions.eq("user_id", user_id) ).list();
			if( cancelCustomerRetain.isEmpty() ) {
				log.info("cancel_customer_retain is not found");
				return null;
			}
			return (CancelCustomerRetain) cancelCustomerRetain.get(0);
		} catch (Exception e) {
			log.error("Error in getCancelCustomerRetain :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public CancelCustomerRetain saveOrUpdateCancelCustomerRetain(CancelCustomerRetain cancelCustomerRetain) {
		log.info("Entered into saveOrUpdateCancelCustomerRetain :: user_id : "+ cancelCustomerRetain.getUser_id());
		try {
			return (CancelCustomerRetain) sessionFactory.getCurrentSession().merge(cancelCustomerRetain);
		} catch (Exception e) {
			log.error("Error in saveOrUpdateCancelCustomerRetain :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}
	
	
	@Override
	public boolean saveOrUpdatePauseHistory(String cbsubid, long userId, String date, int status, String feedbackid, String review) {
		log.info("Entered :: saveOrUpdatePauseHistory :: ");
		Session ses = sessionFactory.getCurrentSession();
		try {
			SQLQuery qry = ses.createSQLQuery(
					"SELECT user_id FROM subscription_pause_history WHERE user_id=" + userId + " AND  cb_sub_id='" +cbsubid+"';");
			String CurrentTime = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATETIMEFORMAT,
					IrisservicesConstants.UTCFORMAT);

			List<BigInteger> ids = qry.list();
			if (!ids.isEmpty()) {

			if(status == 1) {
				String updateQry = " UPDATE subscription_pause_history SET `pause_status` = " + status + ",resume_date='" + date + "',updatedon='"+CurrentTime+"',feedback_id = "+Integer.valueOf(feedbackid)+",customer_review='"+review+"' where  user_id=" + userId + " AND cb_sub_id='" +cbsubid+"';";

				int updateCount = ses.createSQLQuery(updateQry).executeUpdate();
				log.info("User : " + userId + "updateQry : " + updateCount);
			}else {
				String updateQry = " UPDATE subscription_pause_history SET `pause_status` = " + status + ",resume_date='" + date + "',updatedon='"+CurrentTime+"' where  user_id=" + userId + " AND cb_sub_id='" +cbsubid+"';";

				int updateCount = ses.createSQLQuery(updateQry).executeUpdate();
				log.info("User : " + userId + "updateQry : " + updateCount);
			}

			} else {
				
				String insertQry = "INSERT INTO `subscription_pause_history` (`user_id`, `resume_date`, `pause_status`, `cb_sub_id`, `feedback_id`, `customer_review`)"
						+ "VALUES(" + userId + ",'" + date + "'," + status + ",'" + cbsubid + "',"+Integer.valueOf(feedbackid)+",'"+review+"' );";

				int insertCount = ses.createSQLQuery(insertQry).executeUpdate();
				log.info("User : " + userId + " : insertQry : " + insertQry);
			}
			return true;
		} catch (Exception e) {
			log.error("Error in saveOrUpdatePauseHistory :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public JPauseHistory getPauseHistoryRecord(String cbsubid) {
		log.info("Entered into getPauseHistoryRecord :: cbsubid :"+ cbsubid);
		JPauseHistory history = new JPauseHistory();
		try {
			String qry = "SELECT GF.resume_date,GF.pause_status FROM subscription_pause_history GF JOIN `all_product_subscription` AP ON AP.subscription_id = GF.cb_sub_id WHERE GF.cb_sub_id=:cbsubid AND AP.subscription_started_at < GF.`updatedon` LIMIT 1;";
			
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("cbsubid", cbsubid);
			SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy");
			List<Object[]> gatewayIdList = (List<Object[]>) query.list();
			
			if( gatewayIdList.isEmpty() ) {
				log.info("There is no subscription_pause_history");
				return null;
			}
			
			Object[] obj = gatewayIdList.get(0);
			history.setResumeDate(dateFormat.format((Date) obj[0]));
			history.setStatus(Long.valueOf((Byte) obj[1]));
			return history;
		} catch (Exception e) {
			log.error("Error in getGatewayFeatureById :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}
	
	@Override
	public ArrayList<JFeatureCredit> getSettingGatewayFeatures(long gateway_id) {
		log.info("Entering getSettingGatewayFeatures DAO Impl for gateway id : "+gateway_id);
		ArrayList<JFeatureCredit> setting_features = new ArrayList<JFeatureCredit>();
		String feature_code = "";
		long alerttype_id = 0;
		int f_cnt = 0;
		try {
			String qry = "SELECT F.feature_code ,UF.txn_limit, alerttype_id FROM feature F JOIN gateway_feature UF ON UF.feature_id=F.id WHERE UF.enable=1 AND UF.gateway_id="
					+gateway_id+" AND alerttype_id>0 GROUP BY alerttype_id 	UNION ALL 	SELECT F.feature_code ,UF.txn_limit, alerttype_id FROM feature F"
					+ " JOIN gateway_feature UF ON UF.feature_id=F.id WHERE UF.enable=1 AND UF.gateway_id="+gateway_id+" AND F.feature_code IN('N_MOBILE','N_EMAIL','N_DEVICE','PUSH_NOTIFY');";

			Query query = this.slave5SessionFactory.getCurrentSession().createSQLQuery(qry);
			
			List res = query.list();
			
			if (!res.isEmpty()) {
				for (int i=0; i<res.size(); i++ ) {
					Object[] tuple = (Object[]) res.get(i);
					feature_code = tuple[0].toString();
					f_cnt = ((Integer) tuple[1]);
					alerttype_id = ((BigInteger) tuple[2]).longValue();
					
					JFeatureCredit featureObj = new JFeatureCredit(alerttype_id,feature_code,f_cnt);
					setting_features.add(featureObj);
				}
			}

			log.info("getSettingGatewayFeatures : " + gateway_id);
		} catch (Exception e) {
			log.error("getSettingGatewayFeatures: Exep: ",e.getLocalizedMessage());
		}
		return setting_features;
	}
	
	@Override
	public long findaqiqndcoenabledevicebyGateway(long gatewayid) {
		log.info("Entered into findaqiqndcoenabledevicebyGateway :: gatewayid : "+ gatewayid);
		try {
			
			String qry = "SELECT G.id FROM gateway G "
					+ "JOIN assetmodel AM on AM.id = G.model_id AND AM.is_aqi = 1 "
					+ " WHERE G.id = '" + gatewayid + "';";
			
			List gatewayIdList = sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			
			if( gatewayIdList.isEmpty() ) {
				log.info("There is no aqi gateway : "+gatewayid);
				return 0;
			}
			long gatewayId = ((BigInteger)  gatewayIdList.get(0)).longValue();
			return gatewayId;
		} catch (Exception e) {
			log.error("Error in findaqiqndcoenabledevicebyGateway :: Error : "+ e.getLocalizedMessage());
		}
		return 0;
	}
	
	@Override
	public JResponse getalertslimitV5ByGateway(long gateway_id, String alertlimit_basedon, boolean enable_appnotify,String reqVer,boolean smsEnable, boolean emailEnable, boolean appNotifyEnable,long aqigatewayid) {
		JResponse response = new JResponse();

		try {
			ArrayList<JAlertTemplate> alertList = new ArrayList<JAlertTemplate>();
			ArrayList<JAlertTemplate> alertListDark = new ArrayList<JAlertTemplate>();
			
			ArrayList<JAlertContent> emailAlertList = new ArrayList<JAlertContent>();
			ArrayList<JAlertContent> textAlertList = new ArrayList<JAlertContent>();
			ArrayList<JAlertContent> inAppAlertList = new ArrayList<JAlertContent>();
			
			ArrayList<JAlertContent> commonAlertList = new ArrayList<JAlertContent>();

			String qry = "";
			boolean alert_based = true;
			String subQry = " ";
			JAlertTemplate alertTemplate = null;
			JAlertTemplate alertTemplateDark = null;
			JAlertContent alertContent = null;
			boolean upgrade_flag = false;
			String upgrade_label = "";
			
			String content = alertsPageHtmlHeader();
			if (!enable_appnotify) {
				subQry = " AND UF.feature_code NOT LIKE '%_NOTIFY' ";
			}
			
			if (alertlimit_basedon.equalsIgnoreCase("alert-based")) {
				alert_based = true;
				qry = "SELECT AT.name, (remaining_limit+extra_txn_limit+addon_limit) AS remaining,UF.txn_limit + UF.extra_txn_limit  AS total,"
						+ " AT.id,UF.unlimited_cr,F.feature_code FROM gateway_feature UF JOIN feature F ON UF.feature_id = F.id JOIN alerttype AT ON AT.id = F.alerttype_id"
						+ " WHERE UF.enable =1 AND UF.gateway_id=" + gateway_id
						+ " AND F.alerttype_id>0  AND UF.feature_code LIKE '%_COUNT' ORDER BY "
						+ "CASE WHEN alerttype_id = 1 THEN 1 WHEN alerttype_id = 14 THEN 2 WHEN alerttype_id = 2 "
						+ "THEN 3 WHEN alerttype_id = 3 THEN 4 WHEN alerttype_id = 17 THEN 5 WHEN alerttype_id = 11 "
						+ "THEN 6 WHEN alerttype_id = 4 THEN 7 WHEN alerttype_id = 18 THEN 8 WHEN alerttype_id = 19 THEN 9 ELSE 10 END ASC;";
				alertTemplate = new JAlertTemplate();
				alertTemplate.setAlert_type("All Alerts");
			}
			else {
				qry = "SELECT AT.name, (remaining_limit+extra_txn_limit+addon_limit) AS remaining,UF.txn_limit + UF.extra_txn_limit  AS total,"
						+ " AT.id,UF.unlimited_cr,F.feature_code FROM gateway_feature UF JOIN feature F ON UF.feature_id = F.id JOIN alerttype AT ON AT.id = F.alerttype_id"
						+ " WHERE UF.enable =1 AND UF.gateway_id=" + gateway_id
						+ " AND F.alerttype_id>0  AND UF.feature_code NOT LIKE '%_COUNT' " + subQry
						+ " ORDER BY CASE WHEN alerttype_id = 1 THEN 1 WHEN alerttype_id = 14 THEN 2 "
						+ "WHEN alerttype_id = 2 THEN 3 WHEN alerttype_id = 3 THEN 4 WHEN alerttype_id = 17 "
						+ "THEN 5 WHEN alerttype_id = 11 THEN 6 WHEN alerttype_id = 4 THEN 7 WHEN alerttype_id = 18 THEN 8 WHEN alerttype_id = 19 THEN 9 ELSE 10 END ASC;";
				alert_based = false;
			}

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List<Object[]> res = query.list();

			boolean emailUnlimitedCredits = false;
			boolean textUnlimitedCredits = false;
			boolean appNotifyUnlimitedCredits = false;

			JAlertTemplate emailTemp = new JAlertTemplate();
			JAlertTemplate textTemp = new JAlertTemplate();
			JAlertTemplate appNotifyTemp = new JAlertTemplate();

			JAlertTemplate emailTempDark = new JAlertTemplate();
			JAlertTemplate textTempDark = new JAlertTemplate();
			JAlertTemplate appNotifyTempDark = new JAlertTemplate();
			
			long totalCount = 0;
			long remainingCount = 0;
			double remainingAlertPercent = 0.0;
			boolean total_unlimited = true;
			boolean unlimited = false;
			String caution_desc = "";

			if (!res.isEmpty()) {
				int size = res.size();

				if(emailEnable) {
					emailTemp.setAlert_type("Email Alerts");
					emailTemp.setAlert_content(content);
					
					emailTempDark.setAlert_type("Email Alerts");
					emailTempDark.setAlert_content(alertsPageHtmlDarkHeader());

				}
				
				if(smsEnable) {
					textTemp.setAlert_type("Text Alerts");
					textTemp.setAlert_content(content);
					
					textTempDark.setAlert_type("Text Alerts");
					textTempDark.setAlert_content(alertsPageHtmlDarkHeader());
				}
				
				if(enable_appnotify && appNotifyEnable) {
					appNotifyTemp.setAlert_type("In-App Alerts");
					appNotifyTemp.setAlert_content(content);
					
					appNotifyTempDark.setAlert_type("In-App Alerts");
					appNotifyTempDark.setAlert_content(alertsPageHtmlDarkHeader());				

				}

//				if(!emailEnable) {
//					emailTemp.setAlert_content("<center><p>Upgrade Your Plan to Unlock this Feature</p></center>");
//					emailTempDark.setAlert_content("<center style=\"color: #919191;\"><p>Upgrade Your Plan to Unlock this Feature</p></center>");
//
//					emailTemp.setTotal_cnt(0);
//					emailTemp.setRemaining_cnt(0);
//
//					emailTempDark.setTotal_cnt(0);
//					emailTempDark.setRemaining_cnt(0);
//
//				}
//
//				if(!smsEnable) {
//					textTemp.setAlert_content("<center><p>Upgrade Your Plan to Unlock this Feature</p></center>");
//					textTempDark.setAlert_content("<center style=\"color: #919191;\"><p>Upgrade Your Plan to Unlock this Feature</p></center>");
//
//					textTemp.setTotal_cnt(0);
//					textTemp.setRemaining_cnt(0);
//
//					textTempDark.setTotal_cnt(0);
//					textTempDark.setRemaining_cnt(0);
//				}

//				if(!appNotifyEnable) {
//					appNotifyTemp.setAlert_content("<center><p>Upgrade Your Plan to Unlock this Feature</p></center>");
//					appNotifyTempDark.setAlert_content("<center style=\"color: #919191;\"><p>Upgrade Your Plan to Unlock this Feature</p></center>");
//
//					appNotifyTemp.setTotal_cnt(0);
//					appNotifyTemp.setRemaining_cnt(0);
//
//					appNotifyTempDark.setTotal_cnt(0);
//					appNotifyTempDark.setRemaining_cnt(0);
//				}

				for (int i = 0; i < size; i++) {
					Object[] alertObj = res.get(i);
					String alertname = (String) alertObj[0];
					if (alertname.equalsIgnoreCase("Device Not Reporting"))
						alertname = "Network";
					else
						alertname = alertname.replace(" Alert", "");
					int remaining_cnt = ((BigInteger) alertObj[1]).intValue();
					int total_cnt = ((BigInteger) alertObj[2]).intValue();
					long alert_type = ((BigInteger) alertObj[3]).longValue();
					unlimited = (boolean) alertObj[4];
					String feature_code = (String) alertObj[5];

					// work arround for buddy customer. if txn limit > 1000 ; then instead show remaining, unlimited text will be displayed
					if((feature_code.contains("SMS") )&&(total_cnt >= ul_check_limit))
						unlimited = true;
					
					if((alert_type == 1 || alert_type == 2 || alert_type == 3 || alert_type == 4
							|| alert_type == 11 || alert_type == 14 || alert_type == 17 || alert_type == 18 || alert_type == 19) && aqigatewayid > 0) {
						
						alertContent = new JAlertContent();
						alertContent.setAlertname(alertname);
						alertContent.setTotal(total_cnt);
						alertContent.setLeft(remaining_cnt);
						alertContent.setIcon_url(getAlertIcon(alert_type));

						if (feature_code.contains("MAIL") && !emailUnlimitedCredits && emailEnable) {
							emailTemp.setUnlimited(unlimited);
							emailTempDark.setUnlimited(unlimited);
							
							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
							emailTemp.setAlert_content(emailTemp.getAlert_content() + result);
							emailTempDark.setAlert_content( emailTempDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));

							if(!unlimited) {
								
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									emailTemp.setTotal_cnt(emailTemp.getTotal_cnt() + total_cnt);
									emailTemp.setRemaining_cnt(emailTemp.getRemaining_cnt() + remaining_cnt);

									emailTempDark.setTotal_cnt(emailTempDark.getTotal_cnt() + total_cnt);
									emailTempDark.setRemaining_cnt(emailTempDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							} else {
								upgrade_flag = false;
								emailUnlimitedCredits = true;
							}
							emailAlertList.add(alertContent);
						} else if (feature_code.contains("SMS") && !textUnlimitedCredits && smsEnable) {
							textTemp.setUnlimited(unlimited);
							textTempDark.setUnlimited(unlimited);
							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
							textTemp.setAlert_content(textTemp.getAlert_content() + result);
							textTempDark.setAlert_content(textTempDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));
							
							if(!unlimited) {
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									textTemp.setTotal_cnt(textTemp.getTotal_cnt() + total_cnt);
									textTemp.setRemaining_cnt(textTemp.getRemaining_cnt() + remaining_cnt);

									textTempDark.setTotal_cnt(textTempDark.getTotal_cnt() + total_cnt);
									textTempDark.setRemaining_cnt(textTempDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							} else {
								upgrade_flag = false;
								textUnlimitedCredits = true;
							}
							textAlertList.add(alertContent);
						} else if (enable_appnotify && feature_code.contains("NOTIFY") && !appNotifyUnlimitedCredits && appNotifyEnable) {
							appNotifyTemp.setUnlimited(unlimited);
							appNotifyTempDark.setUnlimited(unlimited);
							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
							appNotifyTemp.setAlert_content(appNotifyTemp.getAlert_content() + result);
							appNotifyTempDark.setAlert_content(appNotifyTempDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));
							
							if(!unlimited) {
								
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									appNotifyTemp.setTotal_cnt(appNotifyTemp.getTotal_cnt() + total_cnt);
									appNotifyTemp.setRemaining_cnt(appNotifyTemp.getRemaining_cnt() + remaining_cnt);

									appNotifyTempDark.setTotal_cnt(appNotifyTempDark.getTotal_cnt() + total_cnt);
									appNotifyTempDark.setRemaining_cnt(appNotifyTempDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							} else {
								upgrade_flag = false;
								appNotifyUnlimitedCredits = true;
							}
							inAppAlertList.add(alertContent);
						} else if(feature_code.contains("COUNT") && alert_based && !alertTemplate.isUnlimited()) {

							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);

							alertTemplate.setAlert_content(alertTemplate.getAlert_content() + result);
							alertTemplateDark.setAlert_content( alertTemplateDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));

							if(unlimited) {
								upgrade_flag = false;
								alertTemplate.setUnlimited(unlimited);
								alertTemplateDark.setUnlimited(unlimited);
							} else {
																
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									alertTemplate.setTotal_cnt(alertTemplate.getTotal_cnt() + total_cnt);
									alertTemplate.setRemaining_cnt(alertTemplate.getRemaining_cnt() + remaining_cnt);
									alertTemplateDark.setTotal_cnt(alertTemplateDark.getTotal_cnt() + total_cnt);
									alertTemplateDark.setRemaining_cnt(alertTemplateDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							}
							
							commonAlertList.add(alertContent);
						}
					}else if(alert_type == 1 || alert_type == 2 || alert_type == 3 || alert_type == 4
							|| alert_type == 11 || alert_type == 14 || alert_type == 17) {

						
						alertContent = new JAlertContent();
						alertContent.setAlertname(alertname);
						alertContent.setTotal(total_cnt);
						alertContent.setLeft(remaining_cnt);
						alertContent.setIcon_url(getAlertIcon(alert_type));
						if (feature_code.contains("MAIL") && !emailUnlimitedCredits && emailEnable) {
							emailTemp.setUnlimited(unlimited);
							emailTempDark.setUnlimited(unlimited);
							
							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
							emailTemp.setAlert_content(emailTemp.getAlert_content() + result);
							emailTempDark.setAlert_content( emailTempDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));

							if(!unlimited) {
								
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									emailTemp.setTotal_cnt(emailTemp.getTotal_cnt() + total_cnt);
									emailTemp.setRemaining_cnt(emailTemp.getRemaining_cnt() + remaining_cnt);

									emailTempDark.setTotal_cnt(emailTempDark.getTotal_cnt() + total_cnt);
									emailTempDark.setRemaining_cnt(emailTempDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							} else {
								upgrade_flag = false;
								emailUnlimitedCredits = true;
							}
							emailAlertList.add(alertContent);
						} else if (feature_code.contains("SMS") && !textUnlimitedCredits && smsEnable) {
							textTemp.setUnlimited(unlimited);
							textTempDark.setUnlimited(unlimited);
							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
							textTemp.setAlert_content(textTemp.getAlert_content() + result);
							textTempDark.setAlert_content(textTempDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));
							
							if(!unlimited) {
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									textTemp.setTotal_cnt(textTemp.getTotal_cnt() + total_cnt);
									textTemp.setRemaining_cnt(textTemp.getRemaining_cnt() + remaining_cnt);

									textTempDark.setTotal_cnt(textTempDark.getTotal_cnt() + total_cnt);
									textTempDark.setRemaining_cnt(textTempDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							} else {
								upgrade_flag = false;
								textUnlimitedCredits = true;
							}
							textAlertList.add(alertContent);
						} else if (enable_appnotify && feature_code.contains("NOTIFY") && !appNotifyUnlimitedCredits && appNotifyEnable) {
							appNotifyTemp.setUnlimited(unlimited);
							appNotifyTempDark.setUnlimited(unlimited);
							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);
							appNotifyTemp.setAlert_content(appNotifyTemp.getAlert_content() + result);
							appNotifyTempDark.setAlert_content(appNotifyTempDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));
							
							if(!unlimited) {
								
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									appNotifyTemp.setTotal_cnt(appNotifyTemp.getTotal_cnt() + total_cnt);
									appNotifyTemp.setRemaining_cnt(appNotifyTemp.getRemaining_cnt() + remaining_cnt);

									appNotifyTempDark.setTotal_cnt(appNotifyTempDark.getTotal_cnt() + total_cnt);
									appNotifyTempDark.setRemaining_cnt(appNotifyTempDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							} else {
								upgrade_flag = false;
								appNotifyUnlimitedCredits = true;
							}
							inAppAlertList.add(alertContent);
						} else if(feature_code.contains("COUNT") && alert_based && !alertTemplate.isUnlimited()) {

							String result = appendAlerts(unlimited, total_cnt, remaining_cnt,alertname);

							alertTemplate.setAlert_content(alertTemplate.getAlert_content() + result);
							alertTemplateDark.setAlert_content( alertTemplateDark.getAlert_content() + appendAlertsDark(unlimited, total_cnt, remaining_cnt,alertname));

							if(unlimited) {
								upgrade_flag = false;
								alertTemplate.setUnlimited(unlimited);
								alertTemplateDark.setUnlimited(unlimited);
							} else {
																
								totalCount += total_cnt;
								remainingCount += remaining_cnt;

								if(!result.isEmpty()) {
									alertTemplate.setTotal_cnt(alertTemplate.getTotal_cnt() + total_cnt);
									alertTemplate.setRemaining_cnt(alertTemplate.getRemaining_cnt() + remaining_cnt);
									alertTemplateDark.setTotal_cnt(alertTemplateDark.getTotal_cnt() + total_cnt);
									alertTemplateDark.setRemaining_cnt(alertTemplateDark.getRemaining_cnt() + remaining_cnt);
								}
								total_unlimited = false;
							}
							
							commonAlertList.add(alertContent);
						}
					
					}
				}
				
				if(alert_based) {
					double alertPercent = Math.round(((double)alertTemplate.getRemaining_cnt() / (double)alertTemplate.getTotal_cnt()) * 100);
					
					/** prepare alerts object **/
					alertTemplate.setColor_code(alertPercent>30 ? "#FF0085FF" : "#FFB47E57");
					alertTemplate.setRemaining_percent((int) alertPercent + "%");
					alertTemplateDark.setColor_code(alertPercent>30 ? "#FF0085FF" : "#FFB47E57");
					alertTemplateDark.setRemaining_percent((int) alertPercent + "%");
					if(emailEnable) {
						alertTemplate.setAlert_content(alertTemplate.getAlert_content() + alertsPageHtmlFooter());
						alertTemplateDark.setAlert_content(alertTemplateDark.getAlert_content() + alertsPageHtmlFooter());
					}
					alertTemplate.setAlert_content_new(commonAlertList);
					alertTemplateDark.setAlert_content_new(commonAlertList);
					alertList.add(alertTemplate);
					alertListDark.add(alertTemplateDark);
				} else {
					if (emailEnable) {
						double emailAlertPercent = Math.round(((double) emailTemp.getRemaining_cnt() /
								(double) emailTemp.getTotal_cnt()) * 100);

						/** prepare email object **/
						emailTemp.setColor_code(emailAlertPercent > 30 ? "#FF0085FF" : "#FFB47E57");
						emailTemp.setRemaining_percent((int) emailAlertPercent + "%");
						emailTempDark.setColor_code(emailAlertPercent > 30 ? "#FF0085FF" : "#FFB47E57");
						emailTempDark.setRemaining_percent((int) emailAlertPercent + "%");
						// if(emailEnable) {
						emailTemp.setAlert_content(emailTemp.getAlert_content() + alertsPageHtmlFooter());
						emailTempDark.setAlert_content(
								emailTempDark.getAlert_content() + alertsPageHtmlFooter());
						// }
						emailTemp.setAlert_content_new(emailAlertList);
						emailTempDark.setAlert_content_new(emailAlertList);
						emailTemp.setAlert_icon("email_icon");
						alertList.add(emailTemp);
						alertListDark.add(emailTempDark);
					}
					/** prepare text object **/
					if (smsEnable) {
						double textAlertPercent = Math.round(((double) textTemp.getRemaining_cnt() /
								(double) textTemp.getTotal_cnt()) * 100);

						textTemp.setColor_code(textAlertPercent > 30 ? "#FF0085FF" : "#FFB47E57");
						textTemp.setRemaining_percent((int) textAlertPercent + "%");
						textTempDark.setColor_code(textAlertPercent > 30 ? "#FF0085FF" : "#FFB47E57");
						textTempDark.setRemaining_percent((int) textAlertPercent + "%");
						// if(smsEnable) {
						textTemp.setAlert_content(textTemp.getAlert_content() + alertsPageHtmlFooter());
						textTempDark
								.setAlert_content(textTempDark.getAlert_content() + alertsPageHtmlFooter());
						// }
						textTemp.setAlert_content_new(textAlertList);
						textTempDark.setAlert_content_new(textAlertList);
						textTemp.setAlert_icon("text_icon");
						alertList.add(textTemp);
						alertListDark.add(textTempDark);
					}
					/** prepare appnotify object **/
					if (appNotifyEnable && enable_appnotify) {
						double appNotifyAlertPercent = Math.round(((double) appNotifyTemp.getRemaining_cnt() /
														(double) appNotifyTemp.getTotal_cnt()) * 100);

						appNotifyTemp.setColor_code(appNotifyAlertPercent > 30 ? "#FF0085FF" : "#FFB47E57");
						appNotifyTemp.setRemaining_percent((int) appNotifyAlertPercent + "%");
						appNotifyTempDark.setColor_code(appNotifyAlertPercent > 30 ? "#FF0085FF" : "#FFB47E57");
						appNotifyTempDark.setRemaining_percent((int) appNotifyAlertPercent + "%");
						// if(appNotifyEnable && enable_appnotify) {
						appNotifyTemp.setAlert_content(appNotifyTemp.getAlert_content() + alertsPageHtmlFooter());
						appNotifyTempDark.setAlert_content(
								appNotifyTempDark.getAlert_content() + alertsPageHtmlFooter());
						// }
						appNotifyTemp.setAlert_content_new(inAppAlertList);
						appNotifyTempDark.setAlert_content_new(inAppAlertList);
						appNotifyTemp.setAlert_icon("inapp_icon");
						alertList.add(appNotifyTemp);
						alertListDark.add(appNotifyTempDark);
					}
				}
				
				if ((!unlimited) && (totalCount * 0.20) >= remainingCount) {
					upgrade_flag = true;
					upgrade_label = "Upgrade";
				}
				
				if(totalCount > 0) {
					remainingAlertPercent = ((double) remainingCount / totalCount) * 100;
					if(remainingAlertPercent > 0.0 && remainingAlertPercent < 1.0)
						remainingAlertPercent = 1.0;
					else
						remainingAlertPercent = Math.round(remainingAlertPercent);
				}
				
				if(textUnlimitedCredits) {
					caution_desc = "";
					upgrade_flag = false;
				}else {
					if(remainingAlertPercent == 0.0) {
						caution_desc = "100% of Alerts Exhausted";
					} else if(remainingAlertPercent <= 10.0) {
						caution_desc = "90% of Alerts Exhausted";
					} else if(remainingAlertPercent <= 20.0) {
						caution_desc = "80% of Alerts Exhausted";
					}
				}

				String nextRenewalDate = crService.getNextRenewalDate(gateway_id);
				if( !nextRenewalDate.equalsIgnoreCase("NA") ) 
					response.put("next_alert_renewal_content", "Next Alert Cycle");
				
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				Properties prop = new Properties();
				prop.load(new FileInputStream(file));
				String unlimitedProp = prop.getProperty("total_unlimited");
				if(unlimitedProp.equalsIgnoreCase("true")) {
					total_unlimited = true;
				}

				response.put("next_renewal_date", nextRenewalDate);
				response.put("alert_based", alert_based);
				response.put("alert_list", alertList);
				response.put("alert_list_dark", alertListDark);
				response.put("total_label", "Total");
				response.put("left_label", "Left");
				response.put("upgrade_flag", upgrade_flag);
				response.put("upgrade_label", upgrade_label);
				response.put("remaining_alert_percent", (int) remainingAlertPercent + "%");
				response.put("total_alert_count", totalCount);
				response.put("caution_desc", caution_desc);
				response.put("remaining_alert_count", remainingCount);
				response.put("total_unlimited", total_unlimited);
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Please try after some time");
			}
			
			
		} catch (Exception e) {
			log.info("getalertslimitV5 : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}
		return response;

	}

	public int getDeviceConfigV4Gateway(long gatewayid,long planid) {
		log.info("Entered getDeviceConfigV4Gateway :: DAO");
		int cnt = 0;

		try {
			String qry = "SELECT txn_limit AS no_cnt FROM gateway_feature UF JOIN feature F ON UF.feature_id = F.id"
					+ " WHERE UF.`enable` = 1 And F.feature_code='N_DEVICE' AND UF.gateway_id=" + gatewayid+ ";";
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);

			List res = query.list();
			
			if(res.isEmpty()) {
				qry = "SELECT device_cnt FROM plan WHERE id=" + planid+ ";";
				query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
				res = query.list();
			}
			
			if (!res.isEmpty())
				cnt = (int)res.get(0);

			return cnt;
		}catch (Exception e) {
			log.error(e.getLocalizedMessage());
		}
		return cnt;
	}
	
	@Override
	public JPauseHistory getPauseHistoryRecordAllCBS(String cbsubid) {
		log.info("Entered into getPauseHistoryRecordAllCBS :: cbsubid :"+ cbsubid);
		JPauseHistory history = new JPauseHistory();
		try {
			String qry = "SELECT GF.resume_date,GF.pause_status FROM subscription_pause_history GF JOIN `all_chargebee_subscription` AP ON AP.subscription_id = GF.cb_sub_id WHERE GF.cb_sub_id=:cbsubid AND AP.subscription_started_at < GF.`updatedon` LIMIT 1;";
			
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("cbsubid", cbsubid);
			SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy");
			List<Object[]> gatewayIdList = (List<Object[]>) query.list();
			
			if( gatewayIdList.isEmpty() ) {
				log.info("There is no subscription_pause_history");
				return null;
			}
			
			Object[] obj = gatewayIdList.get(0);
			history.setResumeDate(dateFormat.format((Date) obj[0]));
			history.setStatus(Long.valueOf((Byte) obj[1]));
			return history;
		} catch (Exception e) {
			log.error("Error in getPauseHistoryRecordAllCBS :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}
	
	@Override
	public JGatewayFeature getGatewayFeatureByIdWithoutactive(Long gateway_id) {
		log.info("Entered into getGatewayFeatureById :: gatewayid :"+ gateway_id);
		JGatewayFeature gatewayFeature = new JGatewayFeature();
		try {
			String qry = "SELECT gateway_id,plan_id,sub_id,period_id FROM gateway_feature GF WHERE "
					+ "GF.gateway_id=:gatewayid limit 1;";
			
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("gatewayid", gateway_id);
			
			List<Object[]> gatewayIdList = (List<Object[]>) query.list();
			
			if( gatewayIdList.isEmpty() ) {
				log.info("There is no gateway feature");
				return gatewayFeature;
			}
			
			Object[] obj = gatewayIdList.get(0);
			gatewayFeature.setGateway_id(((BigInteger) obj[0]).longValue());
			gatewayFeature.setPlan_id(((BigInteger) obj[1]).intValue());
			if (obj[2] != null) {
				gatewayFeature.setSub_id((String) obj[2]);
			}
			gatewayFeature.setPeriod_id(((BigInteger) obj[3]).intValue());
			return gatewayFeature;
		} catch (Exception e) {
			log.error("Error in getGatewayFeatureById :: Error : "+ e.getLocalizedMessage());
		}
		return gatewayFeature;
	}
	
	public boolean getMeariPlanExpired(long userID, long gatewayId, int period_id) {
		log.info("Entered into getMeariKey :: userid : "+ userID);
		try {
			String qry = "SELECT key_name FROM meari_subscription where gateway_id=:gatewayid AND is_activated = 1 AND "
					+ "UTC_TIMESTAMP() > activated_date AND UTC_TIMESTAMP() < expiry_date AND period_id=:periodid ";
			
			Query query = null;
			if (userID != 0) {
				query = sessionFactory.getCurrentSession().createSQLQuery(qry + "AND user_id=:userid");
				query.setParameter("gatewayid", gatewayId);
				query.setParameter("periodid", period_id);
				query.setParameter("userid", userID);
			} else {
				query = sessionFactory.getCurrentSession().createSQLQuery(qry);
				query.setParameter("gatewayid", gatewayId);
				query.setParameter("periodid", period_id);
			}

			List<String> meariKeyList = (List<String>) query.list();

			if( meariKeyList.isEmpty() ) {
				log.info("There is no key found");
				return false;
			}

			return true;
		} catch (Exception e) {
			log.error("Error in getMeariKey :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}
	
	public FlexiPlanHistory getFlexiplandetailsbySubid(String sub_id) {
		log.info("Entered into getFlexiplandetailsbySubid :: sub_id : "+sub_id);
		try {
			List flexiplandetail = sessionFactory.getCurrentSession().createCriteria( FlexiPlanHistory.class ).add( Restrictions.eq("subscription_id", sub_id) ).list();
			if( flexiplandetail.isEmpty() ) {
				log.info("Flexi plan detail is not found");
				return null;
			}
			return (FlexiPlanHistory) flexiplandetail.get(0);
		} catch (Exception e) {
			log.error("Error in getFlexiplandetailsbySubid :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}
	
	public boolean saveorupdateflexiplanhistory(FlexiPlanHistory flexiPlan) {
		log.info("Entered saveorupdateflexiplanhistory :: DAO");
		boolean isSuccess = false;
		try {
			sessionFactory.getCurrentSession().merge(flexiPlan);
			isSuccess = true;
		}catch (Exception e) {
			log.error("saveOrUpdate saveorupdateflexiplanhistory : ",e.getLocalizedMessage());
			isSuccess = false;
		}
		return isSuccess;
	}
	
	@Override
	public ArrayList<JVetPlanDetails> getVetPlanTerms(String plan_type) {
		log.info("Entered into getVetPlanTerms ");
		ArrayList<JVetPlanDetails> vetPlanList = new ArrayList<JVetPlanDetails>();
		try {
			String qry = "SELECT id,`plan_name`,`feature_list`,`feature_list_ui_new` FROM plan WHERE plan_type=:plan_type";
			
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("plan_type", plan_type);
			
			List<Object[]> planList = (List<Object[]>) query.list();
			
			if( planList.isEmpty() ) {
				log.info("There is no gateway feature");
				return vetPlanList;
			}
			for (Object[] obj : planList) {
				JVetPlanDetails vetPlans = new JVetPlanDetails();

			//Object[] obj = planList.get(0);
			vetPlans.setPlan_id(((BigInteger) obj[0]).longValue());
			vetPlans.setPlan_name( (String)obj[1]);
			if (obj[2] != null) {
				try {						
					JPlanDetail jplandetail = new Gson().fromJson((String)obj[2], JPlanDetail.class);	
					vetPlans.setPlans(jplandetail.getPlans());
					vetPlans.setCover(jplandetail.getCover());
					vetPlans.setDon_cover(jplandetail.getDon_cover());
				} catch (Exception err) {
					log.error("jplandetail:"+err.getLocalizedMessage());
				}
			}
			if (obj[3] != null) {
				try {						
					JTerms terms = new Gson().fromJson((String)obj[3], JTerms.class);	
					vetPlans.setTerm_list(terms.getTerm_list());
				} catch (Exception err) {
					log.error("jplan-terms:"+err.getLocalizedMessage());
				}
			}
			vetPlanList.add(vetPlans);
			}
		} catch (Exception e) {
			log.error("Error in getVetPlanTerms :: Error : "+ e.getLocalizedMessage());
		}
		return vetPlanList;
	}

	@Override
	public String getSubscriptionStatus(String subId) {

		log.info("Entered into getSubscriptionStatus :: subId : {}", subId);
		try {
			String qry = "SELECT subscription_status FROM all_product_subscription WHERE duplicate_subs =0 AND subscription_id = :subId limit 1";
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("subId", subId);

			return (String) query.uniqueResult() != null ? (String) query.uniqueResult() : "";
		} catch (Exception e) {
			log.error("Error in getSubscriptionStatus :: Error : "+ e.getLocalizedMessage());
		}
		return "";
	}


	@Override
	public String getCurrentSubStatusForChargebeeUser(String chargeBeeId) {

		log.info("Entered into isEligibleForUpgradeCouponOldDevice :: subId : {}", chargeBeeId);
		try {
			String qry = "SELECT subscription_status FROM all_chargebee_subscription WHERE duplicate_subs =0 AND is_deleted=0 AND chargebee_id = :chargeBeeId limit 1";
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("chargeBeeId", chargeBeeId);

			return (String) query.uniqueResult() != null ? (String) query.uniqueResult() : "";
		} catch (Exception e) {
			log.error("Error in isEligibleForUpgradeCouponOldDevice :: Error : "+ e.getLocalizedMessage());
		}
		return "";
	}

	@Override
	public boolean checkComboExists(String cbid) {
		log.info("Entered into checkComboExists :: cbid : "+cbid);
		int count = 0;
		boolean isExists = false;
		try {
			
			String qry = "SELECT COUNT(id) FROM all_product_subscription WHERE chargebee_id=:cbid AND (`subscription_status`='active' OR `subscription_status`='non_renewing') "
					+ " AND `plan_id` IN( SELECT chargebee_planid FROM `plan_to_period` pp JOIN plan p ON p.id=pp.plan_id AND p.plan_type='combo-plan') "
					+ " group by chargebee_id";
					
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("cbid", cbid);
			
			List res = query.list();

			if (!res.isEmpty())
				count = ((BigInteger)res.get(0)).intValue();
			if(count>0) {
				isExists = true;
			}
		} catch (Exception e) {
			log.error("Error in getPlan :: Error : "+ e.getLocalizedMessage());
		}
		return isExists;
	}

	@Override
	public List<ComboContent> getComboPlanContent(){
		log.info("Entered into getComboPlanContent  ");
		List<ComboContent> comboContentList = new ArrayList<>();
		try {

			comboContentList = sessionFactory.getCurrentSession().createCriteria(ComboContent.class).list();

			if( !comboContentList.isEmpty() )
				return comboContentList;

			return comboContentList;
		} catch (Exception e) {
			log.error("Error in getComboPlanContent :: Error : "+ e.getLocalizedMessage());
		}
		return comboContentList;

	}

	@Override
	public boolean checkComboExistsinallchargebee(String cbid) {
		log.info("Entered into checkComboExistsinallchargebee :: cbid : "+cbid);
		int count = 0;
		boolean isExists = false;
		try {

			String qry = "SELECT COUNT(id) FROM all_chargebee_subscription WHERE chargebee_id=:cbid AND (`subscription_status`='active'  OR `subscription_status`='non_renewing')"
					+ " AND `plan_id` IN( SELECT chargebee_planid FROM `plan_to_period` pp JOIN plan p ON p.id=pp.plan_id AND p.device_cnt > 1 ) "
					+ " group by chargebee_id";

			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("cbid", cbid);

			List res = query.list();

			if (!res.isEmpty())
				count = ((BigInteger)res.get(0)).intValue();
			if(count>0) {
				isExists = true;
			}
		} catch (Exception e) {
			log.error("Error in checkComboExistsinallchargebee :: Error : "+ e.getLocalizedMessage());
		}
		return isExists;
	}



	public List<GatewaytoFeature> listGatewaytoFeature(long gatewayId){
		try {
			log.info("Entered into listGatewaytoFeature :: gatewayId : "+gatewayId);
			List<GatewaytoFeature> gatewaytoFeatureList = (List<GatewaytoFeature>)this.sessionFactory.getCurrentSession().createCriteria(GatewaytoFeature.class)
					.add(Restrictions.eq("gateway_id", gatewayId)).list();
			return gatewaytoFeatureList;
		}catch (Exception e) {
			log.error("Error in listGatewaytoFeature :: Error : "+ e.getLocalizedMessage());
			throw e;
		}
	}

	@Override
	public boolean saveOrUpdateGatewaytoFeature(GatewaytoFeature plan) {
		try {
			log.info("Entered into saveOrUpdateGatewaytoFeature :: gatewayId : "+plan.getGateway_id());
			sessionFactory.getCurrentSession().merge(plan);
			return true;

		} catch (Exception e) {
			log.error("Error in saveOrUpdateGatewaytoFeature :: Error : "+ e.getLocalizedMessage());
			throw e;
		}
	}

	@Override
	public GatewaytoFeature getGFByGatewayFeature(long gatewayId, long featureid) {
		try {
			log.info("Entered into listGatewaytoFeature :: gatewayId : "+gatewayId);
			GatewaytoFeature gtf = (GatewaytoFeature)this.sessionFactory.getCurrentSession().createCriteria(GatewaytoFeature.class)
					.add(Restrictions.eq("gateway_id", gatewayId))
					.add(Restrictions.eq("feature_id.id", featureid)).list().get(0);
			return gtf;
		}catch (Exception e) {
			log.error("Error in getGFByGatewayFeature :: Error : "+ e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public String getplantypebychargebeeid(String planId) {

		log.info("Entered into getplantypebychargebeeid :: subId : {}", planId);
		try {
			String qry = "SELECT p.plan_type FROM plan p JOIN plan_to_period pp ON p.id = pp.plan_id WHERE pp.chargebee_planid =:chargeBeeId limit 1";
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("chargeBeeId", planId);

			return (String) query.uniqueResult() != null ? (String) query.uniqueResult() : "";
		} catch (Exception e) {
			log.error("Error in getplantypebychargebeeid :: Error : "+ e.getLocalizedMessage());
		}
		return "";
	}

	@Override
	public boolean deleteGatewaytoFeature(long id) {
		try {
			log.info("Entered into deleteGatewaytoFeature :: gatewayFeatureId : "+id);
			String qry = "delete from gateway_feature where id = :id";
			int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).
					setParameter("id",id).executeUpdate();
			return (resultVal == 1) ? true : false;

		}
		catch(Exception e){
			log.error("Error in deleteGatewaytoFeature :: Error : "+ e.getLocalizedMessage());
			return false;
		}

	}

	@Override
	public List<Feature> listFeatureByGatewayId(int id) {
		List<Feature> featureList= new ArrayList<>();
		try{
			log.info("Entered into listFeatureByGatewayId :: gatewayId : "+id);

			Long monitorTypeId = (long) getMonitorTypeByGatewayId(id);
			List<BigInteger> checkAlreadyFeaturesmapped = checkAlreadyFeaturesmapped(id);
			if(monitorTypeId.intValue() != 0) {
				featureList = (List<Feature>) this.sessionFactory.getCurrentSession().createCriteria(Feature.class)
						.add(Restrictions.eq("monitor_type_id", monitorTypeId)).list();

				if(!checkAlreadyFeaturesmapped.isEmpty()){
					featureList=featureList.stream().filter(feature->!checkAlreadyFeaturesmapped.contains(BigInteger.valueOf(feature.getId()))).collect(Collectors.toList());
				}

			}

		}catch (Exception e) {
			log.error("Error in listFeatureByGatewayId :: Error : "+ e.getLocalizedMessage());

		}
		finally {
			return featureList;
		}

	}

    private List<BigInteger> checkAlreadyFeaturesmapped(int id) {
		try {
			log.info("Entered into checkAlreadyFeaturesmapped :: gatewayId : "+id);
			String qry = "SELECT feature_id FROM gateway_feature gf WHERE gf.gateway_id= :gatewayId";
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("gatewayId", id);
			List<BigInteger> mappedIds =query.list();
			return mappedIds;
		} catch (Exception e) {
			log.error("Error in checkAlreadyFeaturesmapped :: Error : "+ e.getLocalizedMessage());
			return new ArrayList<>();
		}
	}

	private int getMonitorTypeByGatewayId(int id) {
		try {
			log.info("Entered into getMonitorTypeByGatewayId :: gatewayId : "+id);
			String qry = "SELECT monitor_type_id FROM gateway g INNER JOIN assetmodel am ON am.id=g.model_id WHERE g.id= :gatewayId";
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("gatewayId", id);
			BigInteger monitorType = (BigInteger) query.uniqueResult();
			return monitorType.intValue();
		} catch (Exception e) {
			log.error("Error in getMonitorTypeByGatewayId :: Error : "+ e.getLocalizedMessage());
			return 0;
		}

	}

    @Override
    public boolean isEligibleForVetChatFreeTrial(String chargebeeid) {

        log.info("Entered into isEligibleForVetChatFreeTrial :: chargebeeid : "+chargebeeid);
            try {
                String qry = "SELECT id FROM all_product_subscription WHERE chargebee_id=:chargebeeid AND monitor_type=11 AND gateway_id=0";
                Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
                query.setParameter("chargebeeid", chargebeeid);
                List list = query.list();
                if (list.isEmpty()) {
                    return true;
                }
                return false;
            } catch (Exception e) {
                log.error("Error in isEligibleForVetChatFreeTrial :: Error : "+ e.getLocalizedMessage());
                return false;
            }

    }

    @Override
    public PlanToPeriod getPlanToPeriodById(Long id) {
    log.info("Entered into getPlanToPeriodById :: planToPeriodId : "+id);
    PlanToPeriod planToPeriod = new PlanToPeriod();
    try{
        planToPeriod = (PlanToPeriod) this.sessionFactory
                                .getCurrentSession().createCriteria(PlanToPeriod.class)
                                .add(Restrictions.eq("id", id)).list().get(0);

    } catch (Exception e) {
        log.error("Error in getPlanToPeriodById :: Error : "+ e.getLocalizedMessage());
    }
    return planToPeriod;
    }

    @Override
    public List<JVetChatFreeTrialContent> getVetChatFreeTrialContent() {
        log.info("Entered into getVetChatFreeTrialContent");
        List vetChatFreeTrialContentList = new ArrayList<>();
        try {
            vetChatFreeTrialContentList = this.sessionFactory
                                    .getCurrentSession().createCriteria(JVetChatFreeTrialContent.class)
                                    .add( Restrictions.eq("enable", true)).list();


        }
        catch (Exception e) {
            log.error("Error in getVetChatFreeTrialContent :: Error : "+ e.getLocalizedMessage());
        }
        return vetChatFreeTrialContentList;
    }
}
