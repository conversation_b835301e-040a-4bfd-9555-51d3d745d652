package com.nimble.irisservices.dao;

import com.nimble.irisservices.dto.*;
import com.nimble.irisservices.entity.DeviceReplaced;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.GatewayPendingEvent;
import com.nimble.irisservices.entity.GatewayStatus;
import com.nimble.irisservices.entity.GatewayToAlexa;
import com.nimble.irisservices.entity.NightVisionMode;
import com.nimble.irisservices.entity.PlThresholdStatus;
import com.nimble.irisservices.entity.RemoveGatewayRequest;
import com.nimble.irisservices.entity.RemoveGatewayRequestHistory;
import com.nimble.irisservices.entity.RemoveGatewayType;
import com.nimble.irisservices.entity.TempCalibStatus;
import com.nimble.irisservices.entity.UpgradeDeviceHistory;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

public interface IGatewayDaoV4 {

	public JResponse getJPetprofilesByUserv4(long userid, long gatewayid, int monitortype);

	public List<JGateway> getJGateway(String assetgroupid, String groupid, String subgroupid, String gatewayid,
			long userid, String meid);
	
	public JGatewayDetails getJGatewayDetails(String key,String value);

	public JResponse getGatewayListByFilter(String sKey, String sValue, String fType, String otype, long offset,
			long limit, String oKey, JResponse response);

	public JResponse getGatewayByIdWeb(String gatewayId, JResponse response);

	public JResponse listUserPetProfileWeb(long id, JResponse response);

	public long getUserIdForGateway(String meid, String username);
	
	public Gateway getGatewayByMeid(String meid);

	public boolean saveDeviceReplaced(DeviceReplaced deviceReplaced);

	public List<DeviceReplaced> getReplacedDeviceData();

	public boolean removeDeviceReplaced(long userId, String meid);

	public JUser getUsergatewaydetails(long gatewayId);
	
	public LinkedList<JGateway> getGatewaysByInstalledDate(long userid);
	
//	public boolean updateNewMeidInOrderMap(long user_id, String new_meid, String old_meid);

	public JResponse getUnmappedGatewayListByFilter(String sKey, String sValue, String fType, String otype, long offset,
			long limit, String oKey, JResponse response);

	public boolean removeNotComplitedDeviceReplaced(long userId, String meid);
	
	public String getGatewayImg(long gatewayid);

	public boolean checkRecallQRC(String qrcode);

	public boolean saveTempCalib(TempCalibStatus calibStatus);

	public boolean updateTempUnit(long cmpid, String temperatureunit);

	public boolean savePLDelayFreq(PlThresholdStatus plThresholdStatus);

	public boolean updateWarrantyPopup(long gateway_id, boolean popup);

	public List<RemoveGatewayType> getRemoveGatewayType();

	public RemoveGatewayType getRemoveGatewayTypeById(int remove_gateway_type_id);

	public RemoveGatewayRequest getRemoveGatewayRequest(long user_id);

	public RemoveGatewayRequest saveOrUpdateRemoveGatewayRequest(RemoveGatewayRequest removeGatewayRequest);
	
	public RemoveGatewayRequestHistory saveOrUpdateRemoveGatewayRequestHistory(RemoveGatewayRequestHistory removeGatewayRequest);

	public List<RemoveGatewayRequest> getValidRemovalGateway(long user_id);

	public List<JGatewayInfo> getJGatewayInfo(long user_id);

	public List<Object> getWCDeviceList(long userId, long gatewayId, long monitor_type_id);

	public int saveOrUpdateGatewayProfile(long gatewayId, long profileId);

	public long getGatewayProfile(long gatewayId);

	public boolean updateGatewayProfile(long gateway_id, long profile_id);

	public List<WCDeviceList> getSubDeviceList(String username);

	public int getSubUserCount(long user_id);

	public GatewayToAlexa getGatewayToAlexaByUserId(long user_id);

	public GatewayToAlexa saveOrUpdateGatewayToAlexa(GatewayToAlexa gatewayToAlexa);
	
	public String getGatewayImgv5(long gatewayid);
	
	public List<WCDeviceListWeb> getWCamDeviceList( long userID);

	public GatewayStatus getGatewayStatus(long gatewayId);

	public GatewayStatus saveOrUpdateGatewayStatus(GatewayStatus gatewayStatus);

	public List<NightVisionMode> getNightVisionMode(long monitortype);

	public UpgradeDeviceHistory saveOrUpdateUpgradeDeviceHistory(UpgradeDeviceHistory upgradeDeviceHistory);

	public boolean deleteRemoveGatewayRequest(long user_id, long gateway_id);

	public boolean updateOldGatewayProfile(long old_gateway_id, long gateway_id);

	public boolean updatePetProfileGatewayId(long old_gateway_id, long gatewayId);

	public boolean updateGatewayNameByPetProfile(long gatewayId);

	public boolean updateTimeZoneLastGatewayReport(long gateway_id, String time_zone);

	public GatewayPendingEvent getGatewayPendingEvent(long gateway_id);

	public GatewayPendingEvent saveOrUpdateGatewayPendingEvent(GatewayPendingEvent gatewayPendingEvent);

	public boolean delGatewayFeature(long id);

	public ArrayList<JCategory> getProductCategory();
	
	public ArrayList<JSensorType> getSensorType();

	public List<Object> getSensorList(long userId, long gatewayId, long monitor_type_id, String reqVer);

	public List<Object> getSensorListByCode(long userId, long gatewayId, String sensorCode, String os);

	public ArrayList<JSensorTypeCode> getSensorTypeCode();
	
	public boolean checkOrderWithinRange(long gatewayId,String from, String to);
	
	public boolean insertDeviceSub(long userid,long gatewayid,String instal_date,long monitortypeid,long sales_channel,boolean isgps);
	
	public boolean getGnameExist(String gname,long gateway_id,long cmp_id);

	public boolean updateMeidForGateway(long gateway_id);

	public List<JPetmonitorHistory> getPetMonitorHistory(long gatewayId, String tempunit, String timezone);

	public boolean isGpsDevice(long gateway_id);
	
	public boolean isDeviceConfigured(long userId);

	public List<JGatewayInfo> getJGatewayInfo(long user_id, long monitorType);

	public boolean updateDevicebasedSubGatewayId(long old_gateway_id, long gatewayId);

	List<JPetprofileFlutter> getPetprofileList(long user_id);

	boolean saveUserDeviceSpot(long userId, String gatewayId, String devicePlace, String rvType);

	List<Object[]> getUserDeviceSpot();

	boolean getUserDeviceSpotByUserId(long userId, long gatewayId);

	FotaUpdate getFotaUpdate(String gatewayId);

	boolean updateLatestFotaVersion(String gatewayId, String updatedFotaVersionNumber);

	boolean saveMinicamshippingaddres(Minicamshipping jminicamshipping);
	
	boolean checkN12fotaupdateAvailable(String fotaUpdateVersion, Long gatewayId);

	List<JGatewayInfo> getJGatewayInfoV1(long userId, long monitorTypeId, long gateWayId);

    ArrayList<JProductWithSubCategory> getProductList();
	boolean saveBleFotaDebug(JBleFotaDebug jBleFotaDebug);

	List<String> getBleFotaCommands(long gatewayId);

	boolean deleteBleFotaCommands(long gatewayId);

	Minicamshipping getExistingMiniCamShippingInfo(long userId, long gatewayId);

}
