package com.nimble.irisservices.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

import com.nimble.irisservices.dto.*;
import com.nimble.irisservices.entity.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.ResourceUtils;

import com.chargebee.Environment;
import com.chargebee.ListResult;
import com.chargebee.Result;
import com.chargebee.filters.enums.SortOrder;
import com.chargebee.models.Card;
import com.chargebee.models.CreditNote;
import com.chargebee.models.CreditNote.ReasonCode;
import com.chargebee.models.Customer;
import com.chargebee.models.Customer.PaymentMethod;
import com.chargebee.models.HostedPage;
import com.chargebee.models.HostedPage.CheckoutExistingRequest;
import com.chargebee.models.HostedPage.CheckoutNewRequest;
import com.chargebee.models.Invoice;
import com.chargebee.models.PaymentSource;
import com.chargebee.models.Plan;
import com.chargebee.models.Subscription;
import com.chargebee.models.Subscription.Addon;
import com.chargebee.models.Subscription.CreateForCustomerRequest;
import com.chargebee.models.Subscription.Status;
import com.chargebee.models.Subscription.SubscriptionListRequest;
import com.chargebee.models.Transaction;
import com.chargebee.models.enums.AutoCollection;
import com.chargebee.models.enums.CreditOptionForCurrentTermCharges;
import com.nimble.irisservices.dao.IChargebeeDao;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IAvatarService;
import com.nimble.irisservices.service.ICancelService;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IRechargeService;
import com.nimble.irisservices.service.IReportServiceV4;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

import freemarker.template.Template;

@Service
@Transactional
public class ChargebeeServiceImpl implements IChargebeeService {

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	@Value("${chargebee.coupon}")
	private String cbCoupon;

	@Value("${cb.recharge.coupon}")
	private String reCoupon;
	
	@Value("${cancel_recharge}")
	private boolean cancel_recharge;

	@Value("${minus_refund_amount}")
	private String minus_refund_amount;

	@Value("${refund_by_prorated}")
	private String refund_by_prorated;
	
	@Value("${orderdate_trial}")
	private String orderdate_trial;

	@Value("${offer_days}")
	private int offer_days;
	
	@Value("${coupon_img_update}")
	private String coupon_img_update;
	
	@Value("${coupon_desc_update}")
	private String coupon_desc_update;
	
	@Value("${coupon_code_update}")
	private String coupon_code_update;
	
	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	IAvatarService iAvatarService;

	Helper _helper = new Helper();

	@Autowired
	@Lazy
	IChargebeeService cbService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;

	@Autowired
	@Lazy
	IChargebeeDao cbDao;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	IRechargeService reService;

	@Autowired
	IExternalConfigService externalConfigService;
	
	@Autowired
	freemarker.template.Configuration templates;

	@Value("${wagglehooks_url}")
	private String wagglehooks_url;

	@Value("${immediate_cancel_note}")
	private String immediate_cancel_note;

	@Value("${upcomingrenewal_cancel_note}")
	private String upcomingrenewal_cancel_note;

	@Value("${freeplan}")
	private String freeplan;

	@Value("${omitplan}")
	private String omitplan;

	@Value("${vpmplan}")
	private String vpmplan;

	@Value("${addonplan}")
	private String addonplan;

	@Value("${embedupdate}")
	private boolean embedupdate;

	@Value("#{${chargebee.addonid}}")
	private Map<String, String> activationAddonId;

	@Value("#{${chargebee.updateaddonid}}")
	private Map<String, String> updateAddonId;

	@Value("#{${chargebee.reactivationid}}")
	private Map<String, String> reactivateAddonId;
	
	@Value("#{${chargebee.trialaddonid}}")
	private Map<String, String> trialAddonId;
	

	@Value("#{${chargebee.downgradeaddonid}}")
	private Map<String, String> downgradeAddonId;

	@Value("${redirtPetUrl}")
	private String redirtPetUrl;

	@Value("${checkoutversion}")
	private String checkoutversion;

	@Value("${show_offer}")
	private boolean show_offer = true;

	@Value("${device_count_config}")
	private int device_count_config;

	@Value("${orderid.later.popup}")
	private boolean orderid_later_popup_config = false;

	@Value("${orderid.later.popup.content}")
	private String orderid_later_popup_content;

	@Value("${show_later_btn_warranty_popup}")
	private boolean show_later_btn_warranty_popup = false;

	@Value("${warranty_msg}")
	private String warranty_msg;

	@Value("${warranty_msg_v2}")
	private String warranty_msg_v2;
	
	@Value("${warranty_msg_v2_content}")
	private String warranty_msg_v2_content;
	
	@Value("${warranty_msg_v2_image}")
	private String warranty_msg_v2_image;

	@Value("${redirect.checkout.page.content}")
	private String check_out_page_content;
	
	@Value("${redirect.checkout.page.image}")
	private String check_out_page_image;
	
	@Value("${redirect.checkout.page.image.dark}")
	private String check_out_page_image_dark;

	@Value("${show.checkout.page.popup}")
	private boolean show_check_out_page;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Value("#{${cancel_sub_url}}")
	private Map<String, String> cancel_sub_url;

	@Value("${cancel_inapp_redirect}")
	private boolean cancel_inapp_redirect = false;
	
	@Value("${stay_with_us_img}")
	private String stay_with_us_img;
	
	@Value("${stay_with_us_content}")
	private String stay_with_us_content;
	
	@Value("${addition_benefits_cancel_reward_title}")
	private String addition_benefits_cancel_reward_title;
	
	@Value("${cancel_reward_url}")
	private String cancel_reward_url;
	
	@Value("${switch_plan_note_content_within_7_days}")
	private String switch_plan_note_content_within_7_days;
	
	@Value("${switch_plan_note_content_after_7_days}")
	private String switch_plan_note_content_after_7_days;
	
	@Value("${show_cancel_basedon_user_cancel_feedback}")
	private boolean show_cancel_basedon_user_cancel_feedback;
	
	@Value("${minicam_max_count}")
	private long minicam_max_count;
	
	@Autowired
	ICancelService cancelService;
	
	@Value("${show_nextrenewal_popup}")
	private boolean show_nextrenewal_popup;
	
	@Value("${days_tohandle_nextrenewal}")
	private int days_tohandle_nextrenewal;
	
	@Autowired
	IReportServiceV4 iReportServiceV4;

	@Value("${bundle_contact_us_content}")
	private String bundle_contact_us_content;
	
	@Value("${priorityemail}")
	private String priorityemail;
	
	@Value("${priorityphone}")
	private String priorityphone;
	
	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;
	
	@Value("${pause_enable}")
	private boolean pause_enable = true;

	@Value("${flexi_plan_activate_content_1}")
	private String flexi_plan_activate_content_1;

	@Value("${flexi_plan_activate_content_2}")
	private String flexi_plan_activate_content_2;

	@Value("${flexi_plan_start_content}")
	private String flexi_plan_start_content;

	@Value("${flexi_plan_pause_content}")
	private String flexi_plan_pause_content;

	@Value("${free_minicam_startdate}")
	private String free_minicam_startdate;

	@Value("${coupon_img_upgrade}")
	private String coupon_img_upgrade;
	@Value("${coupon_desc_upgrade}")
	private String coupon_desc_upgrade;
	@Value("${coupon_code_upgrade}")
	private String coupon_code_upgrade;

	@Value("${isFreeDeviceShippingAvailable}")
	private boolean isFreeDeviceShippingAvailable;

	private long refund_window_for_renewal = 2;

	private static final Logger log = LogManager.getLogger(ChargebeeServiceImpl.class);

	public Subscription createDefaultSubsPlan(String cb_id) {
		Subscription subscription = null;
		try {
//			subscription = createDefaultSubsPlan1(cb_id);
//			if (cb_id != null && !cb_id.equalsIgnoreCase("NA")) {
//				int plan_id = 1;
//				int period_id=1;
//				
//				String[] cbPlanAndTrialPeriod = crService.getChargebeePlanById(plan_id, period_id);
//				String cb_planid = cbPlanAndTrialPeriod[0];
//
//				Thread.sleep(2000); // 2seconds
//
//				// check chum plan is already created for that customer
//				ListResult result = Subscription.list().customerId().is(cb_id).planId().is(cb_planid).status()
//						.in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL).sortByUpdatedAt(SortOrder.DESC)
//						.request();
//
//				if (!result.isEmpty())
//					subscription = result.get(0).subscription();
//				else {
//					Result res = Subscription.createForCustomer(cb_id).planId(cb_planid)
//							.autoCollection(AutoCollection.ON).request();
//					subscription = res.subscription();
//					log.info("create Default Subs Plan:" + cb_id + " : " + cb_planid);
//				}
//			}

		} catch (Exception e) {
//			e.printStackTrace();
			log.error("createDefaultSubsPlan:" + e.getMessage());
		}
		return subscription;
	}

	public Subscription createDefaultSubsPlan1(String cb_id) {
		Subscription subscription = null;
		try {
			if (cb_id != null && !cb_id.equalsIgnoreCase("NA")) {
				int plan_id = 1;
				int period_id = 1;

				String[] cbPlanAndTrialPeriod = crService.getChargebeePlanById(plan_id, period_id, null);
				String cb_planid = cbPlanAndTrialPeriod[0];

				ListResult result = Subscription.list().customerId().is(cb_id).status()
						.in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL, Status.CANCELLED)
						.sortByUpdatedAt(SortOrder.DESC).request();

				if (!result.isEmpty()) {
					int ssize = result.size();
					for (ListResult.Entry subs : result) {
						if (ssize == 1 && !omitplan.contains(subs.subscription().planId())) {
							subscription = subs.subscription();
							if (subscription.status().name().equalsIgnoreCase("CANCELLED")) {
								Result res = Subscription.update(subscription.id()).planId(cb_planid)
										.addonId(0, reactivateAddonId.get("US")).addonQuantity(0, 1)
										.addonBillingCycles(0, 1).replaceAddonList(true).forceTermReset(true).request();
								subscription = res.subscription();
							}
							return subscription;
						} else {
							if ("ACTIVE,NON_RENEWING,IN_TRIAL".contains(subs.subscription().status().name())
									&& !omitplan.contains(subs.subscription().planId())) {
								subscription = subs.subscription();
								return subscription;
							}
						}
					}

					if (subscription == null) {
						for (ListResult.Entry subs : result) {

							subscription = subs.subscription();
							if (subscription.status().name().equalsIgnoreCase("CANCELLED")
									&& !omitplan.contains(subs.subscription().planId())) {
								Result res = Subscription.update(subscription.id()).planId(cb_planid)
										.addonId(0, updateAddonId.get("US")).addonQuantity(0, 1)
										.addonBillingCycles(0, 1).replaceAddonList(true).forceTermReset(true).request();
								subscription = res.subscription();
								return subscription;
							}

						}
					}
				} else {
					Result res = Subscription.createForCustomer(cb_id).planId(cb_planid)
							.autoCollection(AutoCollection.ON).request();
					subscription = res.subscription();
					log.info("create Default Subs Plan:" + cb_id + " : " + cb_planid);
					return subscription;
				}
			}

		} catch (Exception e) {
			log.error("createDefaultSubsPlan:" + e.getMessage());
		}
		return subscription;
	}

	public void updateCBCustomerDetails(User user) {
		try {
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);

			Result result1 = Customer.update(user.getChargebeeid()).firstName(user.getFirstname())
					.lastName(user.getLastname()).email(user.getEmail()).phone(user.getMobileno()).request();

		} catch (Exception e) {
			log.error(" update CB customer detail:" + e.getMessage());
		}

	}

	@Override
	public JResponse getSubscriptionFromDB(UserV4 user, int inapp_redirect) {
//		String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
//		String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);
//		String vpmplan = _helper.getExternalConfigValue("vpmplan", externalConfigService);

		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);

		try {
			String cbId = "NA";
//			if (user.getChargebeeid().equalsIgnoreCase("NA")) {
//				cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
//						user.getMobileno(), user.getUsername(), 0, "NA");
//				user.setChargebeeid(cbId);
//			}
			if (user.getChargebeeid().equalsIgnoreCase("NA")) {
				ListResult rs = null;
				int i = 1;
				Loop: while (rs == null || i <= 3) {
					i = i + 1;
					Thread.sleep(2000);
					rs = Customer.list().email().is(user.getEmail()).sortByCreatedAt(SortOrder.ASC).request();

					if (!rs.isEmpty()) {
						for (ListResult.Entry entry : rs) {
							Customer customer = entry.customer();
							user.setChargebeeid(customer.id());
						}
						break Loop;
					}
				}
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
							user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}
			}

			com.chargebee.models.Subscription subscripCbee = null;
			com.chargebee.models.Subscription vpmSubscripCbee = null;

			AllSubscription subscrip = null;
			AllSubscription vpmSubscrip = null;
			boolean payment_due = false;
			boolean isPaidPlan = false;
			boolean vpm_addon = false;
			if (!user.getChargebeeid().equalsIgnoreCase("NA")) {

				List<AllSubscription> allSubscription = cbService.getSubscriptionByChargebeeId(user.getChargebeeid());
				int ssize = 0;

				if (allSubscription != null) {
					ssize = allSubscription.size();
					for (AllSubscription subs : allSubscription) {
						String subs_planid = subs.getPlanId();
						if (ssize == 1 && !omitplan.contains(subs_planid) && !vpmplan.contains(subs_planid)
								&& !addonplan.contains(subs_planid)) {

							subscrip = subs;

							if (!freeplan.contains(subs_planid)) {
								isPaidPlan = true;

								if (subs.getAddons().contains("vetchat")) {
									vpm_addon = true;
									log.info(subs_planid + ": vpm included with plan");
									break;
								}

								break;
							}

						} else if (!freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
								&& !vpmplan.contains(subs_planid) && !addonplan.contains(subs_planid)) {

							subscrip = subs;
							if (subs.getAddons().contains("vetchat")) {
								vpm_addon = true;
								log.info(subs_planid + ": vpm included with plan");
								break;
							}
							break;

						}
					}

					if (subscrip == null) {
						for (AllSubscription subs : allSubscription) {
							if (freeplan.contains(subs.getPlanId())) {
								subscrip = subs;
								break;
							}
						}
					}

					if (vpmSubscrip == null) {
						for (AllSubscription subs : allSubscription) {
							if (vpmplan.contains(subs.getPlanId())) {
								vpmSubscrip = subs;
								break;
							}
						}
					}
				}

				if (allSubscription == null && (inapp_redirect != 2)) {
					// ios inapp user.here checking is there any CB subscription available
					log.info("CB sub_create : getSubscription : userid : " + user.getId());
					subscripCbee = cbService.createDefaultSubsPlan(user.getChargebeeid());

//					if (subscripCbee == null) {
//						ListResult resultSet = Customer.list().id().is(user.getChargebeeid()).request();
//						if (resultSet.isEmpty()) {
//							user = createOrupdateChargebeeid(user);
//							subscripCbee = cbService.createDefaultSubsPlan(user);
//						}
//					}
					subscrip = chargebeeSubObjToEntity(subscripCbee);
				}

			}

			// initialize values
			JVpmSubscription vpmSubs = new JVpmSubscription();
			String strprice1 = "NA";
			int availCnt = 0;
			int totalCnt = 0;
			int usedCnt = 0;

			String billingPeriod = "NA";
			String periodUnit = "MONTH";
			String planid = "chum";
			String planname = "Chum";
			String availCredit = "0";
			int period = 0;

			float price = (float) 0.0;
			String strprice = "$0.0";
			String status = "ACTIVE";
			int daysBetween = -1;
			int days_remaining = -1;
			boolean show_nextrenewal = false;
			String startedAt = "NA";
			String nextPaymentDate = "NA";
			String createDate = "NA";
			String updateDate = "NA";
			String cbSubId = "NA";
			String cbSubStatus = "NA";
			boolean setupAutoRenewal = false;
			String autoRenewalStatus = "NA";
			boolean cancel = false;
			boolean vetchat_cancel = false;
			String cbvet_cancelId = "";
			String cbvet_planId = "";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date dateobj = new Date();
			Date nextpaymentTS = new Date();
			;
			boolean cb_vetchat = false;

			int iris_splan = 1;
			int iris_speriod = 1;
			String desc = "Free Plan";
			JSubscriptionPlanReport rpt = new JSubscriptionPlanReport();
			boolean vpm_enable = companyService.getVetCallStaus(user.getCmpId());
			int substatus_code = 0;

			if (subscrip != null || vpmSubscrip != null) {
				if (subscrip != null) {

					Credits credits = cbService.getChargebeeUserCredits(subscrip.getCustomerId());
					if (credits != null) {
						availCredit = String
								.valueOf(credits.getPromotional_credits() + credits.getRefundable_credits());
					}

					JPlan jPlan = cbService.getPlanDesc(subscrip.getPlanId());

					planid = subscrip.getPlanId();
					billingPeriod = jPlan.getPeriodUnit();
					planname = jPlan.getPlanName() + " " + billingPeriod;

					price = (float) subscrip.getPlanAmount() / 100;
					strprice = "$" + String.valueOf(price);
					status = subscrip.getSubscriptionStatus().toUpperCase();

					if ((status.equalsIgnoreCase("ACTIVE")) || (status.equalsIgnoreCase("IN_TRIAL"))) {
						sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
						if (status.equalsIgnoreCase("IN_TRIAL")) {
							cancel = true;
						}
						substatus_code = 1;

						nextpaymentTS = sdf.parse(subscrip.getNextBillingAt());
						nextPaymentDate = sdf.format(nextpaymentTS.getTime());

						Date nextPaymentDate1 = sdf.parse(subscrip.getNextBillingAt());
						Date todayDate = sdf.parse(sdf.format(dateobj));
						Date nextPaymentDateNew = sdf.parse(subscrip.getNextBillingAt());

						long difference = nextPaymentDate1.getTime() - todayDate.getTime();
						daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
						days_remaining = daysBetween;

						sdf = new SimpleDateFormat("yyyy-MM-dd");
						// sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
						nextPaymentDate = sdf.format(nextPaymentDateNew.getTime());
						status = "ACTIVE";
						autoRenewalStatus = "Enabled";

						if (freeplan.contains(subscrip.getPlanId())) {
							nextPaymentDate = "NA";
							days_remaining = -1;
							autoRenewalStatus = "NA";
							billingPeriod = "NA";
						}

					} else if (status.equalsIgnoreCase("NON_RENEWING")) {
						sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
						status = "ACTIVE";
						nextPaymentDate = sdf.format(sdf.parse(subscrip.getSubscriptionCancelledAt()).getTime());
						Date cancelledAt = sdf.parse(nextPaymentDate);
						Date todayDate = sdf.parse(sdf.format(dateobj));

						long difference = cancelledAt.getTime() - todayDate.getTime();
						daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
						days_remaining = daysBetween;

						sdf = new SimpleDateFormat("yyyy-MM-dd");
						sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
						nextPaymentDate = sdf.format(sdf.parse(subscrip.getSubscriptionCancelledAt()).getTime());
						substatus_code = 1;
						autoRenewalStatus = "Disabled";
					}

					if (daysBetween < 0)
						days_remaining = -1;

					ArrayList<Integer> ids = crService.getPlanAndPeriod(planid);
					boolean alert_setting = true;
					if (!ids.isEmpty()) {
						iris_splan = ids.get(0);
						iris_speriod = ids.get(1);

						SubscriptionPlan splan = crService.getSubsPlanById(ids.get(0));
						desc = splan.getDescription();
						planname = splan.getPlan_name();
						alert_setting = splan.isAlert_setting();
					}

					// safety
					try {
						sdf = new SimpleDateFormat("yyyy-MM-dd");
						updateDate = sdf.format(sdf.parse(subscrip.getUpdatedDate()).getTime());
						sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
						createDate = sdf.format(sdf.parse(subscrip.getSubscriptionCreatedAt()).getTime());
						startedAt = sdf.format(sdf.parse(subscrip.getSubscriptionStartedAt()).getTime());
						cbSubId = subscrip.getSubscriptionId();
						cbSubStatus = subscrip.getSubscriptionStatus().toUpperCase();
					} catch (Exception e) {
						log.error("subs dates: ", e.getLocalizedMessage());
					}

					List<JGatewaySubSetup> setupList = crService.checkDeviceConfigStatusV2(iris_splan, user.getId(),
							days_remaining);

					rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
							days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus, createDate,
							updateDate, cbSubId, cbSubStatus, availCredit, alert_setting, user.getChargebeeid(),
							startedAt, planid, String.valueOf(iris_speriod), cancel, substatus_code, payment_due);

				} else {
					List<JGatewaySubSetup> setupList = crService.checkDeviceConfigStatusV2(iris_splan, user.getId(),
							days_remaining);

					rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
							days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus, createDate,
							updateDate, cbSubId, cbSubStatus, availCredit, true, user.getChargebeeid(), startedAt, "",
							String.valueOf(iris_speriod), cancel, substatus_code, payment_due);
				}
				int vpmstatus_code = 0;
				String str_total_cnt = "0";
				float price1 = 0;

				if (vpm_addon) {
					vpmstatus_code = 1;
					str_total_cnt = "Unlimited";
					vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
							str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod, vetchat_cancel,
							cbvet_cancelId, cbvet_planId, cb_vetchat);

				} else if (vpmSubscrip != null) {
					price1 = (float) vpmSubscrip.getPlanAmount() / 100;
					status = vpmSubscrip.getSubscriptionStatus();
					if (vpmSubscrip.getPlanId().equalsIgnoreCase("vet-chat")) {
						strprice1 = "$" + String.valueOf(price1);
						nextPaymentDate = "NA";
						autoRenewalStatus = "Disabled";
						billingPeriod = "NA";
						vpmstatus_code = 1;
					} else {
						sdf = new SimpleDateFormat("yyyy-MM-dd");
						sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
						cb_vetchat = true;

						if (status.equalsIgnoreCase("ACTIVE") || (status.equalsIgnoreCase("IN_TRIAL"))) {
							if (status.equalsIgnoreCase("IN_TRIAL")) {
								vetchat_cancel = true;
								cbvet_cancelId = vpmSubscrip.getSubscriptionId();
								cbvet_planId = vpmSubscrip.getPlanId();
							}

							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							try {
								autoRenewalStatus = "Enabled";
								nextpaymentTS = sdf.parse(vpmSubscrip.getNextBillingAt());
//
//								if (nextpaymentTS == null)
//									nextpaymentTS = vpmSubscrip.currentTermEnd();

							} catch (Exception ex) {
//								nextpaymentTS = vpmSubscrip.currentTermEnd();
							}

							nextPaymentDate = sdf.format(nextpaymentTS.getTime());

							Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
							Date todayDate = sdf.parse(sdf.format(dateobj));

							long difference = nextPaymentDate1.getTime() - todayDate.getTime();
							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
							days_remaining = daysBetween;
							autoRenewalStatus = "Enabled";
							vpmstatus_code = 1;

						} else if (status.equalsIgnoreCase("NON_RENEWING")) {
							autoRenewalStatus = "Disabled";
							status = "ACTIVE";
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							nextPaymentDate = sdf.format(vpmSubscrip.getSubscriptionCancelledAt());
							Date cancelledAt = sdf.parse(nextPaymentDate);
							Date todayDate = sdf.parse(sdf.format(dateobj));

							long difference = cancelledAt.getTime() - todayDate.getTime();
							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
							days_remaining = daysBetween;
							vpmstatus_code = 1;
							autoRenewalStatus = "Disabled";
						} else {
							status = "INACTIVE";
							vpmstatus_code = 0;
							strprice1 = "$0.0";
						}
						billingPeriod = "Monthly";
						strprice1 = "$" + String.valueOf(price1);
						str_total_cnt = "Unlimited";
					}

					ArrayList<Integer> vpmlist = crService.getVPMAvailabilty(user.getId(), "VPM", 0,
							vpmSubscrip.getPlanId());

					if (!vpmlist.isEmpty()) {
						availCnt = vpmlist.get(0);
						totalCnt = vpmlist.get(1);
						usedCnt = totalCnt - availCnt;

						if (availCnt == 0) {
							status = "INACTIVE";
							vpmstatus_code = 0;
							strprice1 = "$0.0";
							nextPaymentDate = "NA";
							billingPeriod = "NA";
							str_total_cnt = "0";
						}
						if (vpmSubscrip.getPlanId().equalsIgnoreCase("vet-chat") && totalCnt > 0)
							str_total_cnt = totalCnt + "";
					}

					vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
							str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod, vetchat_cancel,
							cbvet_cancelId, cbvet_planId, cb_vetchat);
				} else {

					if (vpm_enable) {
						ArrayList<Integer> vpmlist = crService.getVPMAvailabilty(user.getId(), "VPM", 0, "");
						availCnt = vpmlist.get(0);
						totalCnt = vpmlist.get(1);
						usedCnt = totalCnt - availCnt;
						status = "ACTIVE";
						vpmstatus_code = 1;
						str_total_cnt = availCnt + "";

						if (availCnt == 0) {
							status = "INACTIVE";
							vpmstatus_code = 0;
							strprice1 = "$0.0";
							nextPaymentDate = "NA";
							billingPeriod = "NA";
							str_total_cnt = "0";
						}
					} else {
						availCnt = 0;
						totalCnt = 0;
						usedCnt = 0;
						status = "INACTIVE";
						vpmstatus_code = 0;
						strprice1 = "$0.0";
						nextPaymentDate = "NA";
						billingPeriod = "NA";
						str_total_cnt = "0";
					}
					autoRenewalStatus = "Disabled";
					nextPaymentDate = "NA";
					billingPeriod = "NA";
					strprice1 = "$0.0";
					vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
							str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod, vetchat_cancel,
							cbvet_cancelId, cbvet_planId, cb_vetchat);
				}

			} else {

				List<JGatewaySubSetup> setupList = crService.checkDeviceConfigStatusV2(iris_splan, user.getId(),
						days_remaining);

				rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
						days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus, createDate,
						updateDate, cbSubId, cbSubStatus, availCredit, true, user.getChargebeeid(), startedAt, "",
						String.valueOf(iris_speriod), cancel, substatus_code, payment_due);

				availCnt = 0;
				totalCnt = 0;
				usedCnt = 0;
				status = "INACTIVE";
				autoRenewalStatus = "Disabled";
				nextPaymentDate = "NA";
				int vpmstatus_code = 0;
				String str_total_cnt = "0";
				billingPeriod = "NA";
				strprice1 = "0";

				vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
						str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod, vetchat_cancel, cbvet_cancelId,
						cbvet_planId, cb_vetchat);

			}

			boolean is_upgrade = true;
			int redirect = user.getInapp_purchase(); // default 1
			String upgrade_msg = "Success";

			if (isPaidPlan == true && (inapp_redirect == 2 || user.getInapp_purchase() == 2)) {
				inapp_redirect = 1;
				redirect = inapp_redirect;
				user.setInapp_purchase(inapp_redirect);
				// call user update method
				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());

			} else if ((isPaidPlan == false && inapp_redirect == 2 && user.getInapp_purchase() == 1)
					|| (isPaidPlan == false && inapp_redirect == 1 && user.getInapp_purchase() == 2)) {
				// update CB purchase status to 2[inapp] in user table
				user.setInapp_purchase(inapp_redirect);
				redirect = inapp_redirect;
				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());
			}

			redirect = user.getInapp_purchase();
	
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("planid", iris_splan);
			response.put("periodid", iris_speriod);
			response.put("subscriptionplan", rpt);
			response.put("vpm_enable", vpm_enable);
			response.put("vpmsubs", vpmSubs);
			response.put("immediate_cancel_note", immediate_cancel_note);
			response.put("upcomingrenewal_cancel_note", upcomingrenewal_cancel_note);
			response.put("is_upgrade", is_upgrade);
			response.put("redirect_inapp", redirect);
			response.put("upgrade_msg", upgrade_msg);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error while getting Subscription plan");
			log.error("getSubscription:" + e.getMessage());
		}

		return response;
	}

	@Override
	public JResponse getIosSubscriptionV2(UserV4 user, int inapp_redirect) {
		// String vpmplan = _helper.getExternalConfigValue("vpmplan",
		// externalConfigService);

		JResponse response = new JResponse();
		// Environment.configure(chargebeeSiteName, chargebeeSiteKey);

		try {

			// initialize values
			JVpmSubscription vpmSubs = new JVpmSubscription();
			String strprice1 = "NA";
			int availCnt = 0;
			int totalCnt = 0;
			int usedCnt = 0;

			int iris_splan = 1;
			int iris_speriod = 1;
			boolean vpm_enable = companyService.getVetCallStaus(user.getCmpId());

			JSubscriptionPlanReport rpt = crService.getInappSubscriptionByUser(user.getId());

			if (rpt != null) {

				iris_splan = Integer.valueOf(rpt.getPlanid());
				iris_speriod = Integer.valueOf(rpt.getPeriodid());

				List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(Integer.parseInt(rpt.getPlanid()),
						user.getId(), rpt.getDays_remaining(), "NA",0,0,0,0,"");
				rpt.setListJGatewaySubSetup(setupList);

			} else {
				response = cbService.getSubscriptionFromDB(user, inapp_redirect);
				return response;
			}

			if (vpm_enable) {
				ArrayList<Integer> vpmlist = crService.getVPMAvailabilty(user.getId(), "VPM", 0, "NA");
				availCnt = vpmlist.get(0);
				totalCnt = vpmlist.get(1);
				usedCnt = totalCnt - availCnt;
			}

			vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, "NA", "NA", "NA", 0, "NA", "NA",
					false, "", "", false);

			boolean is_upgrade = true;
			int redirect = user.getInapp_purchase();
			String upgrade_msg = "Success";

			if (iris_splan != 1 && user.getInapp_purchase() == 2 && inapp_redirect == 1) {
				is_upgrade = false;
				redirect = user.getInapp_purchase();
				upgrade_msg = " You have purchased in App Store. Pls cancel subscription in app store and then purchase in android ";
			} else if (iris_splan == 1 && user.getInapp_purchase() == 2 && inapp_redirect == 1) {
				user.setInapp_purchase(inapp_redirect);
				redirect = inapp_redirect;
				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("planid", iris_splan);
			response.put("periodid", iris_speriod);
			response.put("subscriptionplan", rpt);
			response.put("vpm_enable", vpm_enable);
			response.put("immediate_cancel_note", immediate_cancel_note);
			response.put("upcomingrenewal_cancel_note", upcomingrenewal_cancel_note);
			response.put("vpmsubs", vpmSubs);
			response.put("is_upgrade", is_upgrade);
			response.put("redirect_inapp", redirect);
			response.put("upgrade_msg", upgrade_msg);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error while getting Subscription plan");
			log.error("getSubscription:" + e.getMessage());
		}

		return response;
	}

	public List<JGatewaySubSetup> checkDeviceConfigStatus(long planid, long userid, int days_remaining, String status,
			int exMo,int exYr, int subMo,int subYr,String paymenttype) {
		log.info("Entering checkDeviceConfigStatusV2:");
		List<JGatewaySubSetup> setupList = new ArrayList<JGatewaySubSetup>();

		long add_device_cnt = 0;
		int remaindays = -1;
		int maxDevCnt = 0;
		boolean setupActivate = false;
		String showNextRenewal_withContent = "";

		try {

			if (planid > 0) {
				maxDevCnt = crService.getDeviceConfigV4(userid, planid);
			}

			LinkedList<JGateway> gatewayList = gatewayServiceV4.getGatewaysByInstalledDate(userid);

			for (JGateway gateway : gatewayList) {

				if (planid == 1 || status.equalsIgnoreCase("INACTIVE") || status.equalsIgnoreCase("PAUSED")) {
					setupActivate = true;
					remaindays = -1;
				} else {
					if (planid > 0) {
						if (maxDevCnt == 0) {
							setupActivate = true;
						} else {
							if (maxDevCnt > add_device_cnt) {
								setupActivate = false;
								add_device_cnt = add_device_cnt + 1;
								remaindays = days_remaining;
							} else {
								setupActivate = true;
								remaindays = -1;
							}
						}
					} else {
						setupActivate = true;
						remaindays = -1;
					}
				}

				if((remaindays < days_tohandle_nextrenewal) && (remaindays > 0) && show_nextrenewal_popup
						&&((exMo>0 && exMo < subMo && exYr==subYr) || exYr>0 && exYr<subYr) 
						&&(!paymenttype.equalsIgnoreCase("PAYPAL_EXPRESS_CHECKOUT"))) {
					showNextRenewal_withContent = "Your card is about to expire. Please update it to ensure uninterrupted service.";

					//showNextRenewal_withContent = "Subscription renews in " + remaindays + " days";
				}

				JGatewaySubSetup setup = new JGatewaySubSetup(gateway.getId(), setupActivate, remaindays,
						gateway.getMeid(), gateway.getName(), showNextRenewal_withContent);

				setupList.add(setup);
			}
		} catch (Exception e) {
			log.info("checkDeviceConfigStatusV2 : " + e.getLocalizedMessage());
		}
		return setupList;
	}

	private AllSubscription chargebeeSubObjToEntity(Subscription subsChargebee) {

		AllSubscription subscription = null;
		try {

			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			sdf.setTimeZone(TimeZone.getTimeZone("UTC"));

			String addons = "NA";
			try {
				for (Addon add : subsChargebee.addons()) {
					addons = add.id() + ",";
					addons += add.quantity() + ",";
				}
			} catch (Exception e) {
				addons = "NA";
			}

			int isDeleted = 0;
			String createdAt = "1753-01-01 00:00:00";
			String startedAt = "1753-01-01 00:00:00";
			String activatedAt = "1753-01-01 00:00:00";
			String cancelledAt = "1753-01-01 00:00:00";
			String trialStart = "1753-01-01 00:00:00";
			String trialEnd = "1753-01-01 00:00:00";
			String nextBillingAt = "1753-01-01 00:00:00";
			String updatedAt = "1753-01-01 00:00:00";
			String metaData = "";
			String planPeriod = "NA";

			planPeriod = cbService.getPlanDesc(subsChargebee.planId()).getPeriodUnit();

			if (subsChargebee.createdAt() != null)
				createdAt = sdf.format(subsChargebee.createdAt());

			if (subsChargebee.startedAt() != null)
				startedAt = sdf.format(subsChargebee.startedAt());

			if (subsChargebee.activatedAt() != null)
				activatedAt = sdf.format(subsChargebee.activatedAt());

			if (subsChargebee.cancelledAt() != null)
				cancelledAt = sdf.format(subsChargebee.cancelledAt());

			if (subsChargebee.trialStart() != null)
				trialStart = sdf.format(subsChargebee.trialStart());

			if (subsChargebee.trialEnd() != null)
				trialEnd = sdf.format(subsChargebee.trialEnd());

			if (subsChargebee.nextBillingAt() != null)
				nextBillingAt = sdf.format(subsChargebee.nextBillingAt());

			if (subsChargebee.updatedAt() != null)
				updatedAt = sdf.format(subsChargebee.updatedAt());

			if (subsChargebee.deleted())
				isDeleted = 1;

			if (subsChargebee.metaData() != null)
				metaData = subsChargebee.metaData().toString();

			subscription = new AllSubscription(subsChargebee.id(), subsChargebee.planId(),
					subsChargebee.status().toString(), createdAt, startedAt, activatedAt, cancelledAt,
					((double) subsChargebee.planAmount()) / 100, subsChargebee.customerId(), "NA", addons, trialStart,
					trialEnd, nextBillingAt, _helper.getCurrentTimeinUTC(), isDeleted, metaData, 1, updatedAt,
					planPeriod);
			return subscription;
		} catch (Exception e) {
			return null;
		}

	}

	@Override
	public List<AllSubscription> getSubscriptionByChargebeeId(String chargebeeid) {
		return cbDao.getSubscriptionByChargebeeId(chargebeeid);
	}

	@Override
	public JPlan getPlanDesc(String planId) {
		return cbDao.getPlanDesc(planId);
	}

	@Override
	public Credits getChargebeeUserCredits(String chargebeeId) {
		return cbDao.getChargebeeUserCredits(chargebeeId);
	}

	@Override
	public boolean createPaidSubscription(String chargebeeId, JProductSubscription jProductSubscription,
			Date purchasedDate) {
		log.info("Entered into createPaidSubscription :: chargebeeID : " + chargebeeId);
		boolean isPaidPlan = false;
		try {

			Environment.configure(chargebeeSiteName, chargebeeSiteKey);

			isPaidPlan = checkExistingPaidPlan(chargebeeId);

			log.info("is Paid plan : " + isPaidPlan);
			if (!isPaidPlan) {
				Result res = Subscription.createForCustomer(chargebeeId).planId(jProductSubscription.getPlan_id())
						.coupon(cbCoupon).startDate(new Timestamp(purchasedDate.getTime()))
						.autoCollection(AutoCollection.ON).request();
				Subscription subscription = res.subscription();
				log.info("Subscription created cbId: " + chargebeeId + " : " + subscription.planId());
			}

		} catch (Exception e) {
			log.error("createPaidSubscription :  " + e.getLocalizedMessage());
			return false;
		}
		return true;
	}

	@Override
	public void createVetChatSubscription(String chargebeeId, String couponCode) {
		log.info("Entered into createVetChatSubscription :: chargebeeID : " + chargebeeId);
		boolean isVetChatPlan = false;
		try {
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);

			String planId = "vetchat-yearly";

			isVetChatPlan = checkExistingVetChatPlan(chargebeeId, planId);

			String currTime = _helper.getCurrentTimeinUTC();
			Date purchasedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(currTime);

			log.info("is paid vetchat plan : " + isVetChatPlan);
			if (!isVetChatPlan) {
				Result res = Subscription.createForCustomer(chargebeeId).planId(planId)
						.coupon(couponCode).startDate(new Timestamp(purchasedDate.getTime()))
						.autoCollection(AutoCollection.OFF)
						.billingCycles(1)
						.request();
				Subscription subscription = res.subscription();
				log.info("Subscription created cbId: " + chargebeeId + " : " + subscription.planId());
			}
		} catch (Exception e) {
			log.error("createVetChatSubscription :  " + e.getLocalizedMessage());
		}
	}

	@Override
	public ComboPlanInfo checkActiveComboPlan(String chargebeeId) {
		return cbDao.checkActiveComboPlan(chargebeeId);
	}

	@Override
	public void AssignGatewayFeatureForComboPlan(String chargebeeId, long userId, long gatewayId) {
		log.info("Entered into AssignGatewayFeatureForComboPlan :: user_id : "+userId+" :: gateway_id : "+gatewayId+" :: chargebee_id : "+chargebeeId);
		try {
			ComboPlanInfo comboPlanInfo = cbService.checkActiveComboPlan(chargebeeId);
			if( comboPlanInfo != null ) {
				log.info("User have active combo plan, calling wagglehooks to check and assign gateway feature and all_product subscription");
				String waggleHooksURL = wagglehooks_url + "assigncomboplan?user_id="+userId+"&gateway_id="+gatewayId+"&chargebee_planid="+comboPlanInfo.getPlan_name()+"&subscription_id="+comboPlanInfo.getSubscription_id();
				log.info("wagglehooks assigncomboplan url : "+waggleHooksURL);
				String res = _helper.httpPOSTRequest(waggleHooksURL, "");
				log.info("wagglehooks assigncomboplan response : "+res);
			}
		} catch (Exception e) {
			log.error("Error in AssignGatewayFeatureForComboPlan :: Error : "+e.getLocalizedMessage());
		}

	}

	public boolean checkExistingVetChatPlan(String chargebeeId, String planName) {
		log.info("Entered into checkExistingPaidPlan :: chargebee ID : " + chargebeeId);
		try {

			Environment.configure(chargebeeSiteName, chargebeeSiteKey);
			SubscriptionListRequest subList = com.chargebee.models.Subscription.list().customerId().is(chargebeeId)
					.status().in(Status.ACTIVE);

			subList = subList.sortByUpdatedAt(SortOrder.DESC);
			ListResult result = subList.request();

			if (!result.isEmpty()) {
				for (ListResult.Entry subs : result) {
					String subs_planid = subs.subscription().planId();
					if (planName.contains(subs_planid)) {
						return true;
					}
				}
			}
			return false;
		} catch (Exception e) {
			log.error("Error in checkExistingPaidPlan :: Error : " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public boolean checkCurrentPaidPlan(String chargebeeId, long monitor_type_id) {
		log.info("Entered into checkCurrentPaidPlan :: chargebee ID : " + chargebeeId);
		try {

			Environment.configure(chargebeeSiteName, chargebeeSiteKey);
			SubscriptionListRequest subList = com.chargebee.models.Subscription.list().customerId().is(chargebeeId)
					.status().in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL, Status.PAUSED);

			subList = subList.sortByUpdatedAt(SortOrder.DESC);
			
			ListResult result = subList.request();

			if (!result.isEmpty()) {
				for (ListResult.Entry subs : result) {
					String subs_planid = subs.subscription().planId();
					if (!freeplan.contains(subs_planid)) {
						long planMonitorTypeId = crService.getMonitorTypeByCBPlan( subs_planid );
						if( monitor_type_id == planMonitorTypeId )
							return true;
					}
				}
			}
			return false;
		} catch (Exception e) {
			log.error("Error in checkCurrentPaidPlan :: Error : " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public boolean checkExistingPaidPlan(String chargebeeId) {
		log.info("Entered into checkExistingPaidPlan :: chargebee ID : " + chargebeeId);
		try {

			Environment.configure(chargebeeSiteName, chargebeeSiteKey);
			SubscriptionListRequest subList = com.chargebee.models.Subscription.list().customerId().is(chargebeeId)
					.status().in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL, Status.PAUSED, Status.CANCELLED);

			subList = subList.sortByUpdatedAt(SortOrder.DESC);
			
			ListResult result = subList.request();

			if (!result.isEmpty()) {
				for (ListResult.Entry subs : result) {
					String subs_planid = subs.subscription().planId();
					if (!freeplan.contains(subs_planid)) {
						return true;
					}
				}
			}
			return false;
		} catch (Exception e) {
			log.error("Error in checkExistingPaidPlan :: Error : " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public JResponse getSubscriptionFromCB(UserV4 user, int inapp_redirect, String os, String app_ver, String timezone,
			boolean create_benefit, Long gateway_id) {

		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		try {
			boolean ver_map_cancel = false;

			String cbId = "NA";
//			if (user.getChargebeeid().equalsIgnoreCase("NA")) {
//				cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
//						user.getMobileno(), user.getUsername(), 0, "NA");
//				user.setChargebeeid(cbId);
//			}
			if (user.getChargebeeid().equalsIgnoreCase("NA")) {
				ListResult rs = null;
				int i = 1;
				Loop: while (rs == null || i <= 3) {
					i = i + 1;
					rs = Customer.list().email().is(user.getEmail()).sortByCreatedAt(SortOrder.ASC).request();

					if (!rs.isEmpty()) {
						for (ListResult.Entry entry : rs) {
							Customer customer = entry.customer();
							user.setChargebeeid(customer.id());
						}
						break Loop;
					}
					Thread.sleep(2000);
				}
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
							user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}
			}
			com.chargebee.models.Subscription subscrip = null;
			Card card = null;
			PaymentMethod ps = null;
			com.chargebee.models.Subscription vpmSubscrip = null;
			boolean validForBundleSub = true;
			boolean isPaidPlan = false;
			boolean isPaused = false;
			// boolean vpm_addon = false;
			boolean isFreetrial = false;
			String cbSubId = "NA";
			String plans = "bite-monthly,bite-yearly,woof-monthly,woof-yearly,paw-monthly,paw-yearly,bark-monthly,bark-yearly,"+addonplan + "," + vpmplan;
			boolean cancelSub = false;
			String cancelSubUrl = cancel_sub_url.get("US");
			boolean show_alertlimit = false;
			// Append email to URL
			if (!user.getEmail().equalsIgnoreCase("NA"))
				cancelSubUrl = cancelSubUrl + "?email=" + user.getEmail();
			else
				cancelSubUrl = cancelSubUrl + "?email=";

			// Append full name to URL
			if (!user.getFirstname().equalsIgnoreCase("NA") && !user.getLastname().equalsIgnoreCase("NA"))
				cancelSubUrl = cancelSubUrl + "&fullname="
						+ (user.getFirstname() + " " + user.getLastname()).replace(' ', '$');
			else if (!user.getFirstname().equalsIgnoreCase("NA") && user.getLastname().equalsIgnoreCase("NA"))
				cancelSubUrl = cancelSubUrl + "&fullname=" + (user.getFirstname()).replace(' ', '$');
			else if (user.getFirstname().equalsIgnoreCase("NA") && !user.getLastname().equalsIgnoreCase("NA"))
				cancelSubUrl = cancelSubUrl + "&fullname=" + (user.getLastname()).replace(' ', '$');
			else
				cancelSubUrl = cancelSubUrl + "&fullname=";

			// Append phone to URL
			if (!user.getMobileno().equalsIgnoreCase("NA"))
				cancelSubUrl = cancelSubUrl + "&phone=" + user.getMobileno();
			else
				cancelSubUrl = cancelSubUrl + "&phone=";

			String[] planLis = plans.split(",");

			if (!app_ver.equalsIgnoreCase("na") && !os.equalsIgnoreCase("na")) {
				VersionMapping verObj = crService.getVersionMapping(app_ver, os);
				if (verObj != null) {
					cancelSub = verObj.isShow_cancel_sub();
					ver_map_cancel = verObj.isShow_cancel_sub();
				}
			}

			if (!user.getChargebeeid().equalsIgnoreCase("NA")) {
				SubscriptionListRequest subList = com.chargebee.models.Subscription.list().customerId()
						.is(user.getChargebeeid()).status()
						.in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL, Status.PAUSED, Status.CANCELLED);
				//for (String plan : planLis)
					subList = subList.planId().notIn(planLis);

				subList = subList.sortByUpdatedAt(SortOrder.DESC);
				ListResult result = subList.request();

				int ssize = 0;

				if (!result.isEmpty()) {
					ssize = result.size();

//					for( ListResult.Entry subs : result ) {
//						if(subs.subscription().status().toString().equalsIgnoreCase("cancelled")) {
//							validForBundleSub = false;
//							continue;
//						}
//					}

					for (ListResult.Entry subs : result) {

						if (subs.subscription().status().toString().equalsIgnoreCase("cancelled")) {
							validForBundleSub = false;
							continue;
						}

						String subs_planid = subs.subscription().planId();
						// && !vpmplan.contains(subs_planid)&& !addonplan.contains(subs_planid)
						if (ssize == 1 && !omitplan.contains(subs_planid)) {
							subscrip = subs.subscription();
							ps = subs.customer().paymentMethod();
							card = subs.card();
							if (!freeplan.contains(subs_planid)
//									&& !vpmplan.contains(subs.subscription().planId())
//									&& !addonplan.contains(subs.subscription().planId())
							) {
								isPaidPlan = true;

//								List<Addon> addonList = subscrip.addons();
//								for (Addon addon : addonList) {
//									if (addon.id().contains("vetchat")) {
//										vpm_addon = true;
//										log.info(subs_planid + ": vpm included with plan");
//										break;
//									}
//								}
								break;
							}

						} else if (!freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
						// && !vpmplan.contains(subs_planid) && !addonplan.contains(subs_planid)
								&& !subs.subscription().status().name().equalsIgnoreCase("PAUSED")) {
							subscrip = subs.subscription();
							card = subs.card();
							isPaidPlan = true;
							isFreetrial = false;
							isPaused = false;
							break;
						} else if (!freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
						// && !vpmplan.contains(subs_planid) && !addonplan.contains(subs_planid)
								&& subs.subscription().status().name().equalsIgnoreCase("PAUSED")) {
							subscrip = subs.subscription();
							card = subs.card();
							isPaidPlan = true;
							isFreetrial = false;
							isPaused = true;
//							List<Addon> addonList = subscrip.addons();
//							for (Addon addon : addonList) {
//								if (addon.id().contains("vetchat")) {
//									vpm_addon = true;
//									log.info(subs_planid + ": vpm included with plan");
//									break;
//								}
//							}
						}
					}

					if (subscrip == null) {
						for (ListResult.Entry subs : result) {
							if (freeplan.contains(subs.subscription().planId())) {
								subscrip = subs.subscription();
								card = subs.card();
								break;
							}
						}
					}
//					if (vpmSubscrip == null && !vpm_addon) {
//						for (ListResult.Entry subs : result) {
//							if (vpmplan.contains(subs.subscription().planId())) {
//								vpmSubscrip = subs.subscription();
//								break;
//							}
//						}
//					}
				}

				if (!user.getChargebeeid().equalsIgnoreCase("NA") && result.isEmpty()) {
					ListResult resultSet = Customer.list().id().is(user.getChargebeeid()).request();

					if (resultSet.isEmpty()) {
						boolean stat = userServiceV4.recreateUserInChargebee(user.getFirstname(), user.getLastname(),
								user.getEmail(), user.getMobileno(), user.getChargebeeid());
					}
				}

				if (subscrip != null && !freeplan.contains(subscrip.planId())) {
					cbSubId = subscrip.id();
				} else {
					cbSubId = "NA";
				}

				if (subscrip == null || !isPaidPlan || (freeplan.contains(subscrip.planId()))) {

					ListResult res = com.chargebee.models.Subscription.list().customerId().is(user.getChargebeeid())
							.status().in(Status.CANCELLED).planId().notIn(planLis).request();

					if (!res.isEmpty()) {
						for (ListResult.Entry subs : res) {
							if (!freeplan.contains(subs.subscription().planId())
									&& !omitplan.contains(subs.subscription().planId())
							// && !vpmplan.contains(subs.subscription().planId()) &&
							// !addonplan.contains(subs.subscription().planId())
							) {
								isFreetrial = false;
								cbSubId = subs.subscription().id();

								break;
							} else if (freeplan.contains(subs.subscription().planId())) {
								isFreetrial = true;
								cbSubId = subs.subscription().id();
							}
						}
					} else {
						isFreetrial = true;
					}
				}

				if ((result.isEmpty() || subscrip == null) && (inapp_redirect != 2)) {
					// ios inapp user.here checking is there any CB subscription available
					log.info("CB sub_create  : getSubscriptionv2 : userid : " + user.getId());
					// subscrip = cbService.createDefaultSubsPlan(user.getChargebeeid());

				}
			}
			int expiryMonth = 0;
			int expiryYear = 0;
			int renewMonth = 0;
			int renewYear = 0;
			if(subscrip != null) {
				
			}
			// initialize values
			JVpmSubscription vpmSubs = new JVpmSubscription();
			String strprice1 = "NA";
			int availCnt = 0;
			int totalCnt = 0;
			int usedCnt = 0;

			String billingPeriod = "NA";
			String periodUnit = "MONTH";
			String planid = "chum";
			String planname = "Chum";
			String availCredit = "0";
			int period = 0;

			float price = (float) 0.0;
			String strprice = "$0.0";
			String status = "ACTIVE";
			int daysBetween = -1;
			int days_remaining = -1;
			String startedAt = "NA";
			String nextPaymentDate = "NA";
			String createDate = "NA";
			String updateDate = "NA";
			String cbSubStatus = "NA";
			boolean setupAutoRenewal = false;
			String autoRenewalStatus = "NA";
			boolean cancel = false;
			boolean vetchat_cancel = false;
			String cbvet_cancelId = "";
			String cbvet_planId = "";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			SimpleDateFormat abfweb = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
			Date dateobj = new Date();
			Timestamp nextpaymentTS;
			boolean cb_vetchat = false;

			int iris_splan = 1;
			int iris_speriod = 1;
			String desc = "Free Plan";
			JSubscriptionPlanReport rpt = new JSubscriptionPlanReport();
			boolean vpm_enable = false;// companyService.getVetCallStaus(user.getCmpId());
			int substatus_code = 1;
			boolean payment_due = false;
			String cur_feature = "";
			boolean is_freeplan = false;
			boolean show_planoffer = show_offer;
			String cur_feature_flutter = "";
			String cur_feature_flutter_dark = "";
			String cur_feature_ui_new = "";
			boolean immediate_cancel = false;
			String switch_plan_note_content = switch_plan_note_content_after_7_days;
			String dueDtSite = "";
			String btn_label = "Upgrade";
			String btn_desc = "<p> <b style='font-size: 16px;'>Upgrade</b> <br> Upgrade and Unlock Pet Protection.</p>";
			String sub_label = "Subscription Renews in";
			boolean non_renu_status = false;
			if (subscrip != null || vpmSubscrip != null) {

				if (subscrip != null) {
					
					if(card != null) {
						expiryMonth = card.expiryMonth();
						expiryYear = card.expiryYear();
					}
					
					CreditNote cr = null;
					ListResult crLst = CreditNote.list().customerId().is(user.getChargebeeid()).request();
					cr = crLst.isEmpty() ? null : crLst.get(0).creditNote();
					ListResult planRes = Plan.list().id().is(subscrip.planId()).request();
					availCredit = (cr != null) ? String.valueOf(cr.amountAvailable() / 100.0) : "0";

					int invoice_due = subscrip.dueInvoicesCount();
					if (invoice_due > 0) {
						ListResult invoiceList = Invoice.list().customerId().is(user.getChargebeeid()).subscriptionId()
								.is(subscrip.id()).status().is(com.chargebee.models.Invoice.Status.PAYMENT_DUE)
								.request();
						if (invoiceList.isEmpty()) {
							invoice_due = 0; // no pending due
						} else {
							invoice_due = 1; // pending due is there
						}
//						for( ListResult.Entry invoiceObj : invoiceList) {
//							Result result = Invoice.voidInvoice(invoiceObj.invoice().id()).request();
//							Invoice invo = result.invoice();
//							log.info("voidInvoice-id: "+invo.id());
//						}
					}
//					if(invoice_due > 0) {
//						List<JGatewaySubSetup> setupList = checkDeviceConfigStatus((long)iris_splan, user.getId(),
//								days_remaining);
//						status = "Payment Pending";
//						substatus_code = 0;
//						rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
//								days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus, createDate,
//								updateDate, cbSubId, cbSubStatus, availCredit, true, user.getChargebeeid(), startedAt, "",
//								String.valueOf(iris_speriod), cancel, substatus_code);
//					} 
//					else {
					for (ListResult.Entry planR : planRes) {
						Plan plan = planR.plan();
						period = plan.period();
						periodUnit = plan.periodUnit().name();
						planid = plan.id();
						planname = plan.name();

						if (periodUnit.equalsIgnoreCase("YEAR")) {
							if (period == 2)
								billingPeriod = "2 Year";
							else if (period >= 5)
								billingPeriod = planname;
							else
								billingPeriod = "Yearly";
						} else if (periodUnit.equalsIgnoreCase("MONTH")) {
							if (period == 3)
								billingPeriod = "Quarterly";
							else if (period == 6)
								billingPeriod = "Half-Yearly";
							else if (period == 12)
								billingPeriod = "Yearly";
							else if (period == 24)
								billingPeriod = "2 Year";
							else
								billingPeriod = "Monthly";
						} else if (periodUnit.equalsIgnoreCase("DAY")) {
							billingPeriod = "Daily";
						} else if (periodUnit.equalsIgnoreCase("WEEK")) {
							billingPeriod = "Weekly";
						}

						price = (float) subscrip.planUnitPrice() / 100;
						strprice = "$" + String.valueOf(price);
						status = subscrip.status().name();

						if ((status.equalsIgnoreCase("ACTIVE")) || (status.equalsIgnoreCase("IN_TRIAL"))) {
							if (invoice_due > 0) {
								status = "Payment Due";
								substatus_code = 0;
								payment_due = true;
								btn_label = "Upgrade";
								btn_desc = "<p> <b style='font-size: 16px;'>Upgrade</b> <br> Upgrade and Unlock Pet Protection.</p>";
								sub_label = "Payment Due";

							} else {
								sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
								if (status.equalsIgnoreCase("IN_TRIAL")) {
									cancel = true;
									if (ver_map_cancel)
										cancelSub = true;
								}
								substatus_code = 1;
								try {
									nextpaymentTS = subscrip.currentTermEnd();

									if (nextpaymentTS == null)
										nextpaymentTS = subscrip.nextBillingAt();

								} catch (Exception ex) {
									nextpaymentTS = subscrip.currentTermEnd();
								}
								nextPaymentDate = sdf.format(nextpaymentTS.getTime());
								dueDtSite = abfweb.format(nextpaymentTS.getTime());

								Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
								Date todayDate = sdf.parse(sdf.format(dateobj));
								
								Calendar calendar = Calendar.getInstance();
								calendar.setTime(nextPaymentDate1);
								renewYear = calendar.get(Calendar.YEAR);
								//Add one to month {0 - 11}
								renewMonth = calendar.get(Calendar.MONTH) + 1;
								
								long difference = nextPaymentDate1.getTime() - todayDate.getTime();
								daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
								days_remaining = daysBetween;

								sdf = new SimpleDateFormat("dd-MMM-yyyy");
								sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
								nextPaymentDate = sdf.format(nextpaymentTS.getTime());
								// status = "ACTIVE";
								autoRenewalStatus = "Enabled";
								btn_label = "Upgrade";
								btn_desc = "<p> <b style='font-size: 16px;'>Upgrade</b> <br> Upgrade and Unlock Pet Protection.</p>";
								sub_label = "Subscription Renews in";

								if (freeplan.contains(subscrip.planId())) {
									nextPaymentDate = "NA";
									days_remaining = -1;
									autoRenewalStatus = "NA";
									billingPeriod = "NA";
								}
							}
						} else if (status.equalsIgnoreCase("NON_RENEWING")) {
							sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
							status = "ACTIVE";
							nextPaymentDate = sdf.format(subscrip.cancelledAt().getTime());
							dueDtSite = abfweb.format(subscrip.cancelledAt().getTime());

							Date cancelledAt = sdf.parse(nextPaymentDate);
							Date todayDate = sdf.parse(sdf.format(dateobj));

							long difference = cancelledAt.getTime() - todayDate.getTime();
							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
							days_remaining = daysBetween;

							sdf = new SimpleDateFormat("dd-MMM-yyyy"); // yyyy-MM-dd
							sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
							nextPaymentDate = sdf.format(subscrip.cancelledAt().getTime());
							substatus_code = 1;
							autoRenewalStatus = "Disabled";
							btn_label = "Upgrade";
							btn_desc = "<p> <b style='font-size: 16px;'>Upgrade</b> <br> Upgrade and Unlock Pet Protection.</p>";
							sub_label = "Subscription Expires in";
							non_renu_status = true;

							if (ver_map_cancel)
								cancelSub = false;

						} else if (status.equalsIgnoreCase("PAUSED")) {
							status = "PAUSED";
							substatus_code = 2;
							days_remaining = -1;
							btn_label = "Resume";
							btn_desc = "<p> <b style='font-size: 16px;'>Resume</b> <br> Resume and Unlock Pet Protection.</p>";
							sub_label = "Subscription Paused";
						}

						if (daysBetween < 0)
							days_remaining = -1;

						ArrayList<Integer> ids = crService.getPlanAndPeriod(planid);
						boolean alert_setting = true;
						if (!ids.isEmpty()) {
							iris_splan = ids.get(0);
							iris_speriod = ids.get(1);

							SubscriptionPlan splan = crService.getSubsPlanById(ids.get(0));
							desc = splan.getDescription();
							planname = splan.getPlan_name();
							alert_setting = splan.isAlert_setting();
							cur_feature = splan.getCur_feature();
							cur_feature_flutter = splan.getCur_feature_flutter();
							cur_feature_flutter_dark = splan.getCur_feature_flutter_dark();
							cur_feature_ui_new = splan.getCur_feature_ui_new();

							if (splan.isCustom())
								show_planoffer = false;

							if (cur_feature == null || cur_feature.equalsIgnoreCase("null"))
								cur_feature = "";

							if (cur_feature_flutter == null || cur_feature_flutter.equalsIgnoreCase("null"))
								cur_feature_flutter = "";

							if (cur_feature_flutter_dark == null || cur_feature_flutter_dark.equalsIgnoreCase("null"))
								cur_feature_flutter_dark = "";
							
							if (cur_feature_ui_new == null || cur_feature_ui_new.equalsIgnoreCase("null"))
								cur_feature_ui_new = "";
						}

						// safety
						String paymenttype="";
						try {
							sdf = new SimpleDateFormat("yyyy-MM-dd");
							createDate = sdf.format(subscrip.createdAt().getTime());
							updateDate = sdf.format(subscrip.updatedAt().getTime());
							startedAt = sdf.format(subscrip.startedAt().getTime());
							// cbSubId = subscrip.id();
							cbSubStatus = subscrip.status().name();
							paymenttype = (ps!=null) ? ps.gateway().name() : "";
						} catch (Exception e) {
							log.error("subs dates: ", e.getLocalizedMessage());
						}
												
						List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(iris_splan, user.getId(),
								days_remaining, status,expiryMonth,expiryYear,renewMonth,renewYear,paymenttype);

						rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
								days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus,
								createDate, updateDate, cbSubId, cbSubStatus, availCredit, alert_setting,
								user.getChargebeeid(), startedAt, planid, String.valueOf(iris_speriod), cancel,
								substatus_code, payment_due);
					}

					sdf.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
					Date curStart = sdf.parse(new Helper().getCurrentTimeinUTC());

					if (!status.equalsIgnoreCase("IN_TRIAL"))
						curStart = sdf.parse(sdf.format(subscrip.currentTermStart().getTime()));
					Date today = sdf.parse(new Helper().getCurrentTimeinUTC());

					long difference = today.getTime() - curStart.getTime();
					double daysBetween_active = Math.ceil((difference / (1000 * 60 * 60 * 24)));
					double hoursBetween_active = (difference / (1000 * 60 * 60));

					if (hoursBetween_active <= 24 && billingPeriod.equalsIgnoreCase("monthly"))
						immediate_cancel = true;
					else if (daysBetween_active <= 7 && !billingPeriod.equalsIgnoreCase("monthly"))
						immediate_cancel = true;
					// }
					
					if (daysBetween_active <= 7)
						switch_plan_note_content = switch_plan_note_content_within_7_days;
						
				} else {
					List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(iris_splan, user.getId(), days_remaining,
							status,0,0,0,0,"");

					rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
							days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus, createDate,
							updateDate, cbSubId, cbSubStatus, availCredit, true, user.getChargebeeid(), startedAt, "",
							String.valueOf(iris_speriod), cancel, substatus_code, payment_due);
				}
				int vpmstatus_code = 0;
				String str_total_cnt = "0";
				float price1 = 0;
				availCnt = 0;
				totalCnt = 0;
				usedCnt = 0;
				status = "INACTIVE";
				vpmstatus_code = 0;
				strprice1 = "$0.0";
				nextPaymentDate = "NA";
				billingPeriod = "NA";
				str_total_cnt = "0";
				autoRenewalStatus = "Disabled";
				nextPaymentDate = "NA";
				billingPeriod = "NA";
				strprice1 = "$0.0";

				vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
						str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod, vetchat_cancel, cbvet_cancelId,
						cbvet_planId, cb_vetchat);

			} else {
				List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(iris_splan, user.getId(), days_remaining,
						status,0,0,0,0,"");

				rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
						days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus, createDate,
						updateDate, cbSubId, cbSubStatus, availCredit, true, user.getChargebeeid(), startedAt, "",
						String.valueOf(iris_speriod), cancel, substatus_code, payment_due);

				availCnt = 0;
				totalCnt = 0;
				usedCnt = 0;
				status = "INACTIVE";
				autoRenewalStatus = "Disabled";
				nextPaymentDate = "NA";
				int vpmstatus_code = 0;
				String str_total_cnt = "0";
				billingPeriod = "NA";
				strprice1 = "0";
				vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
						str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod, vetchat_cancel, cbvet_cancelId,
						cbvet_planId, cb_vetchat);

			}

			boolean is_upgrade = true;

			if (isPaused) {
				is_upgrade = false;
			}

			int redirect = user.getInapp_purchase(); // default 1
			String upgrade_msg = "Success";

			if (isPaidPlan == true && (inapp_redirect == 2 || user.getInapp_purchase() == 2)) {
				inapp_redirect = 1;
				redirect = inapp_redirect;
				user.setInapp_purchase(inapp_redirect);
				// call user update method
				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());

			} else if ((isPaidPlan == false && inapp_redirect == 2 && user.getInapp_purchase() == 1)
					|| (isPaidPlan == false && inapp_redirect == 1 && user.getInapp_purchase() == 2)) {
				// update CB purchase status to 2[inapp] in user table
				user.setInapp_purchase(inapp_redirect);
				redirect = inapp_redirect;
				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());
			}

			redirect = user.getInapp_purchase();

			if (iris_splan == 1)
				is_freeplan = true;

			if (rpt.getPlanname().equals("Chum"))
				sub_label = "Subscription Inactive";

			rpt.setFreeplan(is_freeplan);
			rpt.setFreetrial(isFreetrial);
			rpt.setCur_feature(cur_feature);
			rpt.setBtn_label(btn_label);
			rpt.setBtn_desc(btn_desc);
			rpt.setSub_label(sub_label);
			rpt.setCur_feature_flutter(cur_feature_flutter);
			rpt.setCur_feature_flutter_dark(cur_feature_flutter_dark);
			rpt.setCur_feature_ui_new(cur_feature_ui_new);

			response.put("show_offer", show_planoffer);
			if (crService.getDeviceCountByUser(user.getId(), 1) > device_count_config) {
				CompanyConfig companyConfig = companyService.getCompanyConfig(user.getCmpId());
				if (companyConfig != null && companyConfig.isCustom_plan_enable()) {
					response.put("show_offer", false);
				}

			}

			List<JGateway> jGatewayList = gatewayService.getJGatewayByUser(user.getId(), "1");
			boolean show_warranty_popup = false;
			boolean show_check_out_page = this.show_check_out_page;
			String check_out_page_url = "NA";
			boolean show_orderid_later_popup = orderid_later_popup_config;
			String gateway_name = "Pet";
			
			if (jGatewayList != null) {

				for (JGateway gateway : jGatewayList) {
					
					if (!gateway.isShowOrderId() && !gateway.isPurchased_from_others() && gateway.getMonitorTypeId()==1) {
						show_warranty_popup = true;
						break;
					}
				}

			}
			
			if( jGatewayList != null && !jGatewayList.isEmpty() && jGatewayList.size() == 1 )
				gateway_name = jGatewayList.get(0).getName();

			if (!show_warranty_popup) {
				show_orderid_later_popup = false;
			}

			boolean show_support = false; // this key is to show contact support for recharge bundle subscription not created during ordermap bcoz of email not matched 
			response.put("show_check_out_popup", false);
			ProductSubscription productSubscription = cbService.getProductSubscription(user.getId());
			boolean isRecharge = false;
			if(productSubscription != null) {
				validForBundleSub = true;
				if(productSubscription.getPlan_id().contains("rech")) {
					isRecharge = true;
				}
			}
			log.info("valid_bundle_subs : "+ validForBundleSub +" :: show_check_out_page : "+ show_check_out_page);
			if (show_check_out_page && !show_warranty_popup && productSubscription != null
					&& !productSubscription.isIs_subscription_activated()) {

				if(isRecharge) {
					show_support = true;
					show_check_out_page = false;
				}
				
				if (validForBundleSub && !isPaidPlan && !isRecharge) {
					check_out_page_url = checkOutURLForBundleSubs(productSubscription, user.getChargebeeid());
				}

				if( productSubscription.isActive_subscription() ) {
					response.put("show_bundle_contact_us", true);
					response.put("bundle_contact_us_content", bundle_contact_us_content);
				}
				
				if (check_out_page_url.equalsIgnoreCase("NA")) {
					show_check_out_page = false;
				} else {
					show_check_out_page = true;
					JPlan planDesc = cbService.getPlanDesc(productSubscription.getPlan_id());
					String subsPeriod = getSubscriptionPeriod(planDesc.getPeriodUnit());
					String check_out_page = check_out_page_content;
					check_out_page = check_out_page.replace("$$", subsPeriod);
					log.info("sub period : " + subsPeriod + " :: checkout content : " + check_out_page);
					response.put("show_check_out_popup", show_check_out_page);
					response.put("check_out_popup_content", check_out_page);
					response.put("check_out_popup_image", check_out_page_image);
					response.put("check_out_popup_image_dark", check_out_page_image_dark);
				}

			}

			if (isPaidPlan) {
				show_check_out_page = false;
				validForBundleSub = false;
				show_alertlimit = true;
			} else {
				cancelSub = false;
				show_alertlimit = false;
			}

			if (show_warranty_popup || productSubscription == null) {
				show_check_out_page = false;
			}

			String country = user.getCountry().toUpperCase();
			if (country == null || country.isEmpty() || country.equalsIgnoreCase("US") || country.equalsIgnoreCase("NA")
					|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in")) {
				country = "US";
			}

			cancelSubUrl = cancel_sub_url.get(country);
			
			if(cancelSubUrl == null) {
				cancelSubUrl = cancel_sub_url.get("US");
			}

			if (cancelSubUrl.equalsIgnoreCase("NA") && !cancel_inapp_redirect) {
				cancelSub = false;
			}

			boolean show_benefits = false;
			boolean benefits_created = false;
			boolean is_expired = true;
			boolean isAvail = false;//iAvatarService.checkBenefitsAvail(rpt.getPeriodid());
			 // currently not using additional benefits feature
			if (create_benefit) {
				if (isAvail) {
					show_benefits = crService.checkAdditionalBenifits(user.getEmail());
					if (show_benefits) {
						benefits_created = crService.checkAdditionalBenifitsCreated(user.getEmail(),
								Integer.parseInt(rpt.getPeriodid()));

						if (!benefits_created) {
							if (Integer.parseInt(rpt.getPeriodid()) >= 3
									&& rpt.getAutoRenewalStatus().equalsIgnoreCase("enabled")
									&& rpt.getStatus_code() == 1) {

								if (rpt.getNextrenew_date().equalsIgnoreCase("NA")) {
									Calendar fromCal = Calendar.getInstance();
									
									int mCnt = 6;
									if(Integer.parseInt(rpt.getPeriodid())==4)
										mCnt = 12;
									else if(Integer.parseInt(rpt.getPeriodid())==5)
										mCnt = 24;
									
									fromCal.set(Calendar.MONTH, (fromCal.get(Calendar.MONTH) + mCnt));

									dueDtSite = abfweb.format(fromCal.getTime());
								}

								JCouponData couponObj = new JCouponData(user.getId(), user.getEmail(),
										rpt.getPeriodid(), user.getFirstname(), user.getLastname(), dueDtSite);

								JResponse resp1 = iAvatarService.generateCoupon(couponObj,"plan-purchase");

								if ((int) resp1.get("Status") == 1) {
									benefits_created = true;
									is_expired = false; // this for newly generated users
								}
							}
						} else
							benefits_created = true;
					}
				}
			}
			// cancel sub contents
			LinkedList<String> reasonList = new LinkedList<String>();
			JCancelSubInfo cancelInfo = new JCancelSubInfo();

			if (cancel_inapp_redirect) {
				reasonList.add("Currently not using");
				reasonList.add("Not happy with the service");
				reasonList.add("Costs too much");
				reasonList.add("Others");

				String can_label1 = "Unsubscribing?";
				String can_label2 = "Don't miss out on \r\n unwavering pet protection and \r\n guaranteed peace of mind.";
				String btn1 = "Never Mind";
				String btn2 = "Stay with Waggle";
				String can_label_newflow = "Take advantage of additional benefits";

				List can_label2_newflow = crService.listPlanBenefits(iris_speriod);

				if (can_label2_newflow.isEmpty())
					can_label_newflow = "Sad to see you go!";

				cancelInfo = new JCancelSubInfo(can_label1, can_label2, btn1, btn2, reasonList);
				cancelInfo.setCan_label_newflow(can_label_newflow);
				cancelInfo.setCan_label2_newflow(can_label2_newflow);

				if (rpt.getAutoRenewalStatus().equalsIgnoreCase("Disabled"))
					cancelSub = false;
				
				boolean is_can_applicable = true;
				String can_msg = "";
				
				if( show_cancel_basedon_user_cancel_feedback ) {
					
					boolean isvalid = crService.checkRenewalDateInUserRetained( user.getId() );
					//if valid = true means already coupon applied, cancel will not be shown
					if( isvalid ) 
						cancelSub = false;
					
					if( cancelSub ) {
						UserCancelFeedBack userCancelFeedback = cancelService.getUserCancelFeedBackByShowCancel( user.getId() , false );
						if( userCancelFeedback != null ) {
							cancelSub = false;
						}	
					}	
					
				}
				
				response.put("show_cancel_reward", false);
				AdditionBenefitsCancelReward additionBenefitsCancelReward = cancelService.getAdditionBenefitsCancelReward( user.getId() );
				if( additionBenefitsCancelReward != null ) {
					response.put("cancel_reward_code", additionBenefitsCancelReward.getCode());
					
					Date endDate = sdf.parse( additionBenefitsCancelReward.getEnd_date() );
					Date curDate = new Date(System.currentTimeMillis());
					
					if( endDate.after(curDate) ) {
						response.put("cancel_reward_enddate", new SimpleDateFormat("dd-MMM-yyyy").format(endDate));
						response.put("show_cancel_reward", true);
						response.put("cancel_reward_title", addition_benefits_cancel_reward_title);	
						response.put("cancel_reward_url", cancel_reward_url);
					}
					
				}
				
				// Checking additional benefit validity
				if (is_expired) // this is already benefits created user
					is_expired = iAvatarService.checkBenefitsExpired(user.getId());

				if (!is_expired && isAvail) {
					is_can_applicable = false;
					can_msg = "To cancel the subscription and additional benefits, please reach out to our Customer Delight team.";
					cancelInfo.setIs_can_applicable(is_can_applicable);
					cancelInfo.setCan_msg(can_msg);
				}
			}

			// end of cancel content
			rpt.setShow_benefits(benefits_created);

			CancelCustomerRetain cancelCustomerRetain = crService.getCancelCustomerRetain( user.getId() );
			if( cancelCustomerRetain != null ) {
				SimpleDateFormat sdfCancel = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				Date cancelCustomerRetainDate =  sdfCancel.parse( cancelCustomerRetain.getUpdated_on() );
				Date currentDate = sdfCancel.parse( _helper.getCurrentTimeinUTC() );
				Calendar c = Calendar.getInstance();
				c.setTime(cancelCustomerRetainDate);
				c.add(Calendar.DATE, 8);
				cancelCustomerRetainDate = c.getTime();
				if (currentDate.before( cancelCustomerRetainDate ) ) {
					cancelSub = false;
				}
				
			}
			
			rpt.setCancelSubUrl(cancelSubUrl);
			rpt.setShow_cancel_sub(cancelSub);
			rpt.setCancel_inapp_redirect(cancel_inapp_redirect);

			Minicamshipping minicamshipping = gatewayServiceV4.getExistingMiniCamShippingInfo(user.getId(), 0L);
			if (minicamshipping != null) {
				rpt.setShowBillingPopup(minicamshipping.getAddress1().equalsIgnoreCase("NA"));
			}
			JPauseHistory pauseHis = null;
			
			if(cbSubId != null)
				 pauseHis = crService.getPauseHistoryRecordAllCBS(cbSubId);
			
			if((pauseHis == null || pauseHis.getStatus() == 1) && iris_speriod == 1 && isPaidPlan)
			{
				response.put("showpausesub", true);
			}else {
				response.put("showpausesub", false);
			}
			
			response.put("nextrenew_date_old", rpt.getNextrenew_date());
			
			if(pauseHis != null && pauseHis.getResumeDate() != null) {
				if(pauseHis.getStatus() == 1) {
					rpt.setShow_cancel_sub(false);
					rpt.setAutoRenewalStatus("Disable");
					rpt.setNextrenew_date(pauseHis.getResumeDate());
				}
					
				response.put("resumeDate", pauseHis.getResumeDate());
				response.put("pauseStatus", pauseHis.getStatus() == 1 ? true : false);
				response.put("pausetitle", "Your pet's tail will wag for this!");
				response.put("pausecontent", "Your subscription will renewal on ");
			} else {
				response.put("resumeDate", "");
				response.put("pauseStatus", false);
				response.put("pausetitle", "Your pet's tail will wag for this!");
				response.put("pausecontent", "Your subscription will renewal on");
			}
			if(isFreeDeviceShippingAvailable) {
				boolean show_cancel_sub = !gatewayService.minicamaddresavilable(user.getId()) && rpt.isShow_cancel_sub();
				rpt.setShow_cancel_sub(show_cancel_sub);
			}
			response.put("cancel_button_text", "Cancel Protection");
			response.put("paydue_info", "Update payment details and attempt again.");
			response.put("show_alertlimit", show_alertlimit);
			response.put("planid", iris_splan);
			response.put("periodid", iris_speriod);
			response.put("subscriptionplan", rpt);
			response.put("vpm_enable", vpm_enable);
			response.put("vpmsubs", vpmSubs);
			
			response.put("show_pauseoption",pause_enable);
			
			if(non_renu_status) {
				response.put("show_pauseoption", false);
			}

			response.put("is_upgrade", is_upgrade);
			response.put("redirect_inapp", redirect);
			response.put("upgrade_msg", upgrade_msg);
			response.put("show_warranty_popup", show_warranty_popup);
			response.put("show_warranty_popup_content", warranty_msg_v2);
			response.put("show_warranty_popup_v2_image", warranty_msg_v2_image);
			response.put("show_warranty_popup_v2_content", warranty_msg_v2_content);
			response.put("show_orderid_later_popup", show_orderid_later_popup);
			response.put("orderid_later_popup_content", orderid_later_popup_content);
			response.put("warranty_popup_later_btn", show_later_btn_warranty_popup);
			response.put("immediate_cancel_note", immediate_cancel_note);
			response.put("upcomingrenewal_cancel_note", upcomingrenewal_cancel_note);

			response.put("bundle_check_out_url", check_out_page_url);
			response.put("valid_bundle_subs", validForBundleSub);
			response.put("cancelInfo", cancelInfo);
			response.put("immediate_cancel", immediate_cancel);
			
			response.put("stay_with_us_img", stay_with_us_img);
			response.put("stay_with_us_content", gateway_name);
			response.put("switch_plan_note_content", switch_plan_note_content);
			response.put("show_support", show_support);
			
			LinkedList<String> stayList = new LinkedList<String>();
			stayList.add("Instant temperature alerts");
			stayList.add("Power outage warnings");
			stayList.add("GPS tracking & Safe Zone alerts");
			stayList.add("Extended warranty for active plans");
			
			response.put("stay_with_us_list", stayList);
			
			String allProd = cbService.getProductSubscriptionByChargebee(user.getChargebeeid());
			boolean isActiveyearplan = false;
			if(allProd != null) {
				isActiveyearplan = true;
			}
			response.put("is_priorityuser", isActiveyearplan);
			response.put("priorityemail", priorityemail);
			response.put("priorityphone", priorityphone);
			
			response.put("Status", 1);
			response.put("Msg", "Success");

			async.saveCurrentPlanDetails(user.getId(), user.getChargebeeid(), iris_splan, iris_speriod, rpt.getPlanid(),
					response);

		} catch (Exception e) {
			// e.printStackTrace();
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Please try again.");
			log.error("getSubscription:" + e.getMessage());
		}

		return response;
	}

	public JProductSubRes getProductSubscriptionFromDBV1(UserV4 user, String os, String app_ver, String timezone,
			Long gateway_id, Long monitor_id) {

		JProductSubRes prodSub = new JProductSubRes();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		try {
			String cbId = "NA";
			if (user.getChargebeeid().equalsIgnoreCase("NA")) {
				ListResult rs = null;
				int i = 1;
				Loop: while (rs == null || i <= 3) {
					i = i + 1;
					Thread.sleep(2000);
					rs = Customer.list().email().is(user.getEmail()).sortByCreatedAt(SortOrder.ASC).request();

					if (!rs.isEmpty()) {
						for (ListResult.Entry entry : rs) {
							Customer customer = entry.customer();
							user.setChargebeeid(customer.id());
						}
						break Loop;
					}
				}
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					log.info("create user in chargebee : chargebeeid : "+user.getChargebeeid());
					cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
							user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}
			}
			AllProductSubscription subscrip1 = null;
			boolean isPaused = false;
			boolean isFreetrial = false;
			boolean isFreePlan = false;
			String cbSubId = "NA";
			boolean show_cancel_sub = false;
			String status = "INACTIVE";
			String billingPeriod = "NA";
			String planname = "NA";
			int iris_splan = 0;
			int iris_speriod = 0;
			boolean show_alertlimit = false;
			boolean immediate_cancel = false;
			boolean payment_due = false;
			Card card = null;
			PaymentMethod ps = null;
			String cur_feature_ui_new = "";
			int substatus_code = 1;
			
			AllProductSubscription sub = cbService.getProductSubscriptionByGatewayId(gateway_id, user.getChargebeeid(),monitor_id);
			
			JGatewayFeature gatewayFeature = crService.getGatewayFeatureById(gateway_id);
			
			SubscriptionPlan splan = crService.getSubsPlanById(gatewayFeature.getPlan_id());

			if (!user.getChargebeeid().equalsIgnoreCase("NA")) {
				if (sub != null) {
					String subs_planid = sub.getPlanId();
					
					cbSubId = sub.getSubscriptionId();
					
					Result result = null;
					try {
						result = Subscription.retrieve(cbSubId).request();
						card = result.card();
						ps = result.customer().paymentMethod();
					}catch(Exception e) {
						log.error("getSubscription:" + e.getMessage());
					}
		            
					if (monitor_id > 1 && !freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
							&& sub.getSubscriptionStatus().equalsIgnoreCase("cancelled")) {
						isFreetrial = false;
						isFreePlan = true;
						isPaused = false;
						status = "ACTIVE";
						billingPeriod = "Free";
						planname = splan.getPlan_name();
						iris_splan = gatewayFeature.getPlan_id();
						iris_speriod = gatewayFeature.getPeriod_id();
					}else if (!freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
							&& !sub.getSubscriptionStatus().equalsIgnoreCase("PAUSED") && !sub.getSubscriptionStatus().equalsIgnoreCase("CANCELLED")) {
						subscrip1 = sub;
						isFreetrial = false;
						isFreePlan = false;
						isPaused = false;
						iris_speriod = gatewayFeature.getPeriod_id();
					} else if (!freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
							&& sub.getSubscriptionStatus().equalsIgnoreCase("PAUSED")) {
						subscrip1 = sub;
						isFreetrial = false;
						isFreePlan = false;
						status = "PAUSED";
						isPaused = true;
						JGatewayFeature gatewayFeatureNew = crService.getGatewayFeatureByIdWithoutactive(gateway_id);
						iris_splan = gatewayFeatureNew!= null && gatewayFeatureNew.getPlan_id() > 0 ? gatewayFeatureNew.getPlan_id() : 0;
						iris_speriod = gatewayFeatureNew!= null && gatewayFeatureNew.getPlan_id() > 0 ? gatewayFeatureNew.getPeriod_id() : 0;
						splan = crService.getSubsPlanById(gatewayFeatureNew.getPlan_id());
					}
					if (subscrip1 == null) {
						if (freeplan.contains(sub.getPlanId())) {
							subscrip1 = sub;
							isFreePlan = true;
						}
					} else if(sub.getSubscriptionStatus().equalsIgnoreCase("IN_TRIAL")) {
						isFreetrial = true;
					}
				} else {
					// Free plan check
					if (gatewayFeature.getPlan_id() > 0 && gatewayFeature.getSub_id().equalsIgnoreCase("NA")) {
						log.info("Free plan found");
						status = "ACTIVE";
						billingPeriod = "Free";
						planname = splan.getPlan_name();
						iris_splan = gatewayFeature.getPlan_id();
						iris_speriod = gatewayFeature.getPeriod_id();
						isFreePlan = true;
					}
					
					if(monitor_id == 1) {
						String allSubscription = cbService.getSubscriptionByChargebeeIdNotinProductsub(user.getChargebeeid());
						cbSubId = allSubscription;
					}
				}
			}

			String periodUnit = "NA";
			String planid = "NA";

			float price = (float) 0.0;
			int daysBetween = -1;
			int days_remaining = -1;
			String nextPaymentDate = "NA";
			String autoRenewalStatus = "NA";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date dateobj = new Date();
			
			int expiryMonth = 0;
			int expiryYear = 0;
			int renewMonth = 0;
			int renewYear = 0;

			JSubscriptionPlanReportV2 rpt = new JSubscriptionPlanReportV2();
			boolean show_manage_sub = false;
//			long subQuantity = 0;
			String cancel_popup = "If you cancel, your plan stays active until the next renewal";
			boolean warranty_claimed = false;
			if (subscrip1 != null && splan != null) {
				
				if(card != null) {
					expiryMonth = card.expiryMonth();
					expiryYear = card.expiryYear();
				}
				
				periodUnit = subscrip1.getPlanPeriod();
				planid = subscrip1.getPlanId();

				if (periodUnit.contains("HALF")) {
					billingPeriod = "Half Yearly";
				} else if (periodUnit.contains("MONTH")) {
					billingPeriod = "Monthly";
				} else if (periodUnit.equalsIgnoreCase("2-YEAR")) {
					billingPeriod = "2 Year";
				} else if (periodUnit.equalsIgnoreCase("5-YEAR")) {
					billingPeriod = "5 Year";
				} else if (periodUnit.contains("QUARTER")) {
					billingPeriod = "Quarter";
				} else if (periodUnit.contains("YEAR")) {
					billingPeriod = "Yearly";
				}

				price = (float) subscrip1.getPlanAmount() / 100;
				String strprice = "$" + String.valueOf(price);
				status = subscrip1.getSubscriptionStatus().toUpperCase();

				if ((status.equalsIgnoreCase("ACTIVE")) || (status.equalsIgnoreCase("IN_TRIAL"))) {
					boolean ispaymentDue = cbService.checkDueInvoice(cbSubId);
					payment_due = ispaymentDue;
					if (ispaymentDue) {
						status = "PAYMENT DUE";
						autoRenewalStatus = "NA";
					} else {
						sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
						try {
							nextPaymentDate = subscrip1.getNextBillingAt();
							if (nextPaymentDate.equalsIgnoreCase("1753-01-01 00:00:00"))
								nextPaymentDate = subscrip1.getTrialEnd();
						} catch (Exception ex) {
							nextPaymentDate = subscrip1.getTrialEnd();
						}

						Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
						Date todayDate = sdf.parse(sdf.format(dateobj));

						long difference = nextPaymentDate1.getTime() - todayDate.getTime();
						daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
						
						days_remaining = daysBetween;

						sdf = new SimpleDateFormat("dd-MMM-yyyy");
						sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
						nextPaymentDate = sdf.format(nextPaymentDate1);
						autoRenewalStatus = "Enabled";
						if (freeplan.contains(subscrip1.getPlanId())) {
							nextPaymentDate = "NA";
							days_remaining = -1;
							autoRenewalStatus = "NA";
						}
						
						Calendar calendar = Calendar.getInstance();
						calendar.setTime(nextPaymentDate1);
						renewYear = calendar.get(Calendar.YEAR);
						//Add one to month {0 - 11}
						renewMonth = calendar.get(Calendar.MONTH) + 1;
						substatus_code = 1;
					}
				} else if (status.equalsIgnoreCase("NON_RENEWING")) {
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					status = "ACTIVE";
					autoRenewalStatus = "Disabled";
					nextPaymentDate = subscrip1.getSubscriptionCancelledAt();

					Date cancelledAt = sdf.parse(nextPaymentDate);
					Date todayDate = sdf.parse(sdf.format(dateobj));

					long difference = cancelledAt.getTime() - todayDate.getTime();
					daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
					days_remaining = daysBetween;

					sdf = new SimpleDateFormat("dd-MMM-yyyy"); // yyyy-MM-dd
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					nextPaymentDate = sdf.format(cancelledAt);
					substatus_code = 1;
				} else if (status.equalsIgnoreCase("PAUSED")) {
					status = "PAUSED";
					days_remaining = -1;
					autoRenewalStatus = "Disabled";
					substatus_code = 2;
					
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					try {
						nextPaymentDate = subscrip1.getNextBillingAt();
						if (nextPaymentDate.equalsIgnoreCase("1753-01-01 00:00:00"))
							nextPaymentDate = subscrip1.getTrialEnd();
					} catch (Exception ex) {
						nextPaymentDate = subscrip1.getTrialEnd();
					}

					Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
					Date todayDate = sdf.parse(sdf.format(dateobj));

					long difference = nextPaymentDate1.getTime() - todayDate.getTime();
					daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
					
					days_remaining = daysBetween;

					sdf = new SimpleDateFormat("dd-MMM-yyyy");
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					nextPaymentDate = sdf.format(nextPaymentDate1);
					autoRenewalStatus = "Enabled";
					if (freeplan.contains(subscrip1.getPlanId())) {
						nextPaymentDate = "NA";
						days_remaining = -1;
						autoRenewalStatus = "NA";
					}
					
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(nextPaymentDate1);
					renewYear = calendar.get(Calendar.YEAR);
					//Add one to month {0 - 11}
					renewMonth = calendar.get(Calendar.MONTH) + 1;
					substatus_code = 1;
				
				}

				if (daysBetween < 0)
					days_remaining = -1;
				
				if (splan != null && splan.getId() > 0) {

					cur_feature_ui_new = splan.getCur_feature_ui_new();

					if (cur_feature_ui_new == null || cur_feature_ui_new.equalsIgnoreCase("null"))
						cur_feature_ui_new = "";
				}
				
				if (monitor_id != null && monitor_id == 1) {
					sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					Date curStart = sdf.parse(sdf.format(sdf.parse(subscrip1.getSubscriptionStartedAt()).getTime()));
					Date today = sdf.parse(new Helper().getCurrentTimeinUTC());

					long difference = today.getTime() - curStart.getTime();
					double daysBetween_active = Math.ceil((difference / (1000 * 60 * 60 * 24)));
					double hoursBetween_active = (difference / (1000 * 60 * 60));

					if (hoursBetween_active <= 24 && billingPeriod.equalsIgnoreCase("monthly"))
						immediate_cancel = true;
					else if (daysBetween_active <= 7 && !billingPeriod.equalsIgnoreCase("monthly"))
						immediate_cancel = true;
				}
				if(!sub.getSubscriptionStatus().equalsIgnoreCase("PAUSED") && !sub.getSubscriptionStatus().equalsIgnoreCase("CANCELLED")) {
					iris_splan = gatewayFeature.getPlan_id();
					iris_speriod = gatewayFeature.getPeriod_id();
				}
				
				planname = splan.getPlan_name();
				if (monitor_id == 0) {
					monitor_id = splan.getMonitor_type();
				}
				
				if (!app_ver.equalsIgnoreCase("na") && !os.equalsIgnoreCase("na")) {
					VersionMapping verObj = crService.getVersionMapping(app_ver, os);
					if (verObj != null) {
						if (verObj.isShow_cancel_sub() && subscrip1.getSubscriptionStatus().equalsIgnoreCase("active")
								&& !isFreePlan)
							show_cancel_sub = true;
							show_alertlimit = true;
					}
				}
				
				if( show_cancel_sub && monitor_id == 1) {
					UserCancelFeedBack userCancelFeedback = cancelService.getUserCancelFeedBackByShowCancel( user.getId() , false );
					if( userCancelFeedback != null ) {
						show_cancel_sub = false;
					}	
				}

				rpt = new JSubscriptionPlanReportV2(planid, planname,  billingPeriod, nextPaymentDate,
						days_remaining, status, autoRenewalStatus, cbSubId,  
						user.getChargebeeid(), isFreetrial, show_cancel_sub, isFreePlan, payment_due,strprice);

			} else {
				rpt = new JSubscriptionPlanReportV2(planid, planname, billingPeriod, nextPaymentDate,
						days_remaining, status, autoRenewalStatus, cbSubId,  user.getChargebeeid(), 
						isFreetrial, show_cancel_sub, isFreePlan,payment_due,"NA");
			}
			
			String meariSubKey = "NA";

			if(monitor_id == 4 || monitor_id == 5 || monitor_id == 8 || monitor_id == 12) {
				meariSubKey = crService.getMeariKey(0, gateway_id, iris_speriod);
			}
			boolean validForBundleSub = true;
			
			boolean is_upgrade = crService.isUpgradeAvailable(iris_splan, iris_speriod);
			if (isPaused) {
				is_upgrade = false;
			} else if (status.equalsIgnoreCase("cancelled")) {
				is_upgrade = true;
				validForBundleSub = false;
			}

			rpt.setFreetrial(isFreetrial);
			
			boolean show_warranty_popup = false;
			boolean show_check_out_page = this.show_check_out_page;
			String check_out_page_url = "NA";
			String gateway_name = "Pet";
			
			JGateway gateway = gatewayService.getJGatewayByGateway(gateway_id);
			if (gateway != null) {
					if (!gateway.isShowOrderId() && !gateway.isPurchased_from_others()) {
						warranty_claimed = true;
						show_warranty_popup = true;
					}
			}
			JResponse response1 = new JResponse();
			
			Map<String,Object> deviceList = new HashMap<>();
			try {
			// get device list by gatewayid
			deviceList = iReportServiceV4.getLastGatewayReportV6(gateway_id, user.getId(), monitor_id, user.getCountry(), 
					os, "V2", response1);
			prodSub.setDeviceList(deviceList);
			}catch(Exception e) {
				log.info("deviceList : "+ e.getMessage());
			}
			
			rpt.setShow_cancel_sub(show_cancel_sub);
			prodSub.setCancel_button_text("Cancel Plan");
			
			
			
			JPauseHistory pauseHis = null;
			
			if(sub != null && sub.getSubscriptionId() != null)
				 pauseHis = crService.getPauseHistoryRecord( sub.getSubscriptionId());
			
			if((pauseHis == null || pauseHis.getStatus() == 1) && iris_speriod == 1 && !isFreePlan && monitor_id == 1)
			{
				prodSub.setShowpausesub(true);
			}else {
				prodSub.setShowpausesub(false);
			}
				
			prodSub.setNextrenew_date_old(rpt.getNextrenew_date());
			
			if(pauseHis != null && pauseHis.getResumeDate() != null) {
				if(pauseHis.getStatus() == 1 && monitor_id == 1 ) {
					rpt.setShow_cancel_sub(false);
					rpt.setAutoRenewalStatus("Disable");
					rpt.setNextrenew_date(pauseHis.getResumeDate());
				}
					
				prodSub.setResumeDate(pauseHis.getResumeDate());
				prodSub.setPauseStatus(pauseHis.getStatus() == 1 ? true : false);
				prodSub.setPausetitle("Your pet's tail will wag for this!");
				prodSub.setPausecontent("Your subscription will renewal on ");
			} else {
				prodSub.setResumeDate("");
				prodSub.setPauseStatus(false);
				prodSub.setPausetitle("Your pet's tail will wag for this!");
				prodSub.setPausecontent("Your subscription will renewal on ");
			}
			
			if( gateway != null ) {
				gateway_name = gateway.getName();
				prodSub.setName(gateway.getName());
				prodSub.setGateway_id(gateway.getId());
			}
			
			prodSub.setShow_pauseoption(pause_enable);
			
			if(sub != null && sub.getSubscriptionId() != null && sub.getSubscriptionStatus().equalsIgnoreCase("non_renewing")) {
				prodSub.setShow_pauseoption(false);
			}
			
			if(monitor_id != null && monitor_id == 1) {		
				String paymenttype = (ps!=null) ? ps.gateway().name() : "";
				
				 checkDeviceConfigStatusByGateway(iris_splan, gateway,
					days_remaining, status,expiryMonth,expiryYear,renewMonth,renewYear,user.getChargebeeid(),paymenttype,prodSub);
				
				prodSub.setShow_check_out_popup(false);
				ProductSubscription productSubscription = cbService.getProductSubscription(user.getId());
				boolean isRecharge = false;
				if(productSubscription != null) {
						validForBundleSub = true;
						if (productSubscription.getPlan_id().contains("rech")) {
							isRecharge = true;
						}
				}
				
				int redirect = user.getInapp_purchase(); // default 1
				int inapp_redirect = 1;

				if (!isFreePlan == true && (inapp_redirect == 2 || user.getInapp_purchase() == 2)) {
					inapp_redirect = 1;
					redirect = inapp_redirect;
					user.setInapp_purchase(inapp_redirect);
					// call user update method
					userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());

				} else if ((!isFreePlan == false && inapp_redirect == 2 && user.getInapp_purchase() == 1)
						|| (!isFreePlan == false && inapp_redirect == 1 && user.getInapp_purchase() == 2)) {
					// update CB purchase status to 2[inapp] in user table
					user.setInapp_purchase(inapp_redirect);
					redirect = inapp_redirect;
					userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());
				}

				redirect = user.getInapp_purchase();
				
				log.info("valid_bundle_subs : "+ validForBundleSub +" :: show_check_out_page : "+ show_check_out_page);
				if (show_check_out_page && !show_warranty_popup && productSubscription != null
						&& !productSubscription.isIs_subscription_activated()) {

					if(isRecharge) {
						show_check_out_page = false;
					}
					
					Inventory inventory = niomDbservice.getInventoryByMeid(gateway.getMeid());
					
					if (validForBundleSub && !isRecharge &&  (inventory != null && inventory.getOrderid() != null && inventory.getOrderid()
							.equalsIgnoreCase(String.valueOf(productSubscription.getOrder_id())))) {
						check_out_page_url = checkOutURLForBundleSubs(productSubscription, user.getChargebeeid());
					}

					if( productSubscription.isActive_subscription() &&  (inventory != null && inventory.getOrderid() != null && inventory.getOrderid()
							.equalsIgnoreCase(String.valueOf(productSubscription.getOrder_id())))) {
						prodSub.setShow_bundle_contact_us(true);
						prodSub.setBundle_contact_us_content(bundle_contact_us_content);
					}
					
					if (check_out_page_url.equalsIgnoreCase("NA")) {
						show_check_out_page = false;
					} else {
						show_check_out_page = true;
						JPlan planDesc = cbService.getPlanDesc(productSubscription.getPlan_id());
						String subsPeriod = getSubscriptionPeriod(planDesc.getPeriodUnit());
						String check_out_page = check_out_page_content;
						check_out_page = check_out_page.replace("$$", subsPeriod);
						log.info("sub period : " + subsPeriod + " :: checkout content : " + check_out_page);
						prodSub.setShow_check_out_popup(show_check_out_page);
						prodSub.setCheck_out_popup_content(check_out_page);
						prodSub.setCheck_out_popup_image(check_out_page_image);
						prodSub.setCheck_out_popup_image_dark(check_out_page_image_dark);
					}

				}
				
				if (splan != null && !splan.isIs_freeplan()) {
					validForBundleSub = false;
				} 
				
				// cancel sub contents
				
				boolean cancelSub = false;
				String cancelSubUrl = cancel_sub_url.get("US");
				// Append email to URL
				if (!user.getEmail().equalsIgnoreCase("NA"))
					cancelSubUrl = cancelSubUrl + "?email=" + user.getEmail();
				else
					cancelSubUrl = cancelSubUrl + "?email=";

				// Append full name to URL
				if (!user.getFirstname().equalsIgnoreCase("NA") && !user.getLastname().equalsIgnoreCase("NA"))
					cancelSubUrl = cancelSubUrl + "&fullname="
							+ (user.getFirstname() + " " + user.getLastname()).replace(' ', '$');
				else if (!user.getFirstname().equalsIgnoreCase("NA") && user.getLastname().equalsIgnoreCase("NA"))
					cancelSubUrl = cancelSubUrl + "&fullname=" + (user.getFirstname()).replace(' ', '$');
				else if (user.getFirstname().equalsIgnoreCase("NA") && !user.getLastname().equalsIgnoreCase("NA"))
					cancelSubUrl = cancelSubUrl + "&fullname=" + (user.getLastname()).replace(' ', '$');
				else
					cancelSubUrl = cancelSubUrl + "&fullname=";

				// Append phone to URL
				if (!user.getMobileno().equalsIgnoreCase("NA"))
					cancelSubUrl = cancelSubUrl + "&phone=" + user.getMobileno();
				else
					cancelSubUrl = cancelSubUrl + "&phone=";
				
				String country = user.getCountry().toUpperCase();
				country = "US";
				
				cancelSubUrl = cancel_sub_url.get(country);
				
				if(cancelSubUrl == null) {
					cancelSubUrl = cancel_sub_url.get("US");
				}

				if (cancelSubUrl.equalsIgnoreCase("NA") && !cancel_inapp_redirect) {
					cancelSub = false;
				}

				boolean is_expired = true;
				boolean isAvail = false;
				
				LinkedList<String> reasonList = new LinkedList<String>();
				JCancelSubInfo cancelInfo = new JCancelSubInfo();

				if (cancel_inapp_redirect) {
					reasonList.add("Currently not using");
					reasonList.add("Not happy with the service");
					reasonList.add("Costs too much");
					reasonList.add("Others");

					String can_label1 = "Unsubscribing?";
					String can_label2 = "Don't miss out on \r\n unwavering pet protection and \r\n guaranteed peace of mind.";
					String btn1 = "Never Mind";
					String btn2 = "Stay with Waggle";
					String can_label_newflow = "Take advantage of additional benefits";

					List can_label2_newflow = crService.listPlanBenefits(iris_speriod);

					if (can_label2_newflow.isEmpty())
						can_label_newflow = "Sad to see you go!";

					cancelInfo = new JCancelSubInfo(can_label1, can_label2, btn1, btn2, reasonList);
					cancelInfo.setCan_label_newflow(can_label_newflow);
					cancelInfo.setCan_label2_newflow(can_label2_newflow);

					if (rpt.getAutoRenewalStatus().equalsIgnoreCase("Disabled"))
						cancelSub = false;
					
					boolean is_can_applicable = true;
					String can_msg = "";
					
					if( show_cancel_basedon_user_cancel_feedback ) {
						
						boolean isvalid = crService.checkRenewalDateInUserRetained( user.getId() );
						//if valid = true means already coupon applied, cancel will not be shown
						if( isvalid ) 
							cancelSub = false;
						
						if( cancelSub ) {
							UserCancelFeedBack userCancelFeedback = cancelService.getUserCancelFeedBackByShowCancel( user.getId() , false );
							if( userCancelFeedback != null ) {
								cancelSub = false;
							}	
						}	
						
					}
					
					prodSub.setShow_cancel_reward(false);
					AdditionBenefitsCancelReward additionBenefitsCancelReward = cancelService.getAdditionBenefitsCancelReward( user.getId() );
					if( additionBenefitsCancelReward != null ) {
						prodSub.setCancel_reward_code(additionBenefitsCancelReward.getCode());
						
						Date endDate = sdf.parse( additionBenefitsCancelReward.getEnd_date() );
						Date curDate = new Date(System.currentTimeMillis());
						
						if( endDate.after(curDate) ) {
							prodSub.setCancel_reward_enddate(new SimpleDateFormat("dd-MMM-yyyy").format(endDate));
							prodSub.setShow_cancel_reward(true);
							prodSub.setCancel_reward_title(addition_benefits_cancel_reward_title);
							prodSub.setCancel_reward_url(cancel_reward_url);
						}
						
					}
					
					// Checking additional benefit validity
					if (is_expired) // this is already benefits created user
						is_expired = iAvatarService.checkBenefitsExpired(user.getId());

					if (!is_expired && isAvail) {
						is_can_applicable = false;
						can_msg = "To cancel the subscription and additional benefits, please reach out to our Customer Delight team.";
						cancelInfo.setIs_can_applicable(is_can_applicable);
						cancelInfo.setCan_msg(can_msg);
					}
				}
				prodSub.setCancelSubUrl(cancelSubUrl);
				prodSub.setCancel_inapp_redirect(cancel_inapp_redirect);
				prodSub.setCancelInfo(cancelInfo);
				prodSub.setStay_with_us_content(gateway_name);
				
				LinkedList<String> stayList = new LinkedList<String>();
				stayList.add("Instant temperature alerts");
				stayList.add("Power outage warnings");
				stayList.add("GPS tracking & Safe Zone alerts");
				stayList.add("Extended warranty for active plans");
				
				prodSub.setStay_with_us_list(stayList);
				
				prodSub.setBundle_check_out_url(check_out_page_url);
				prodSub.setShow_warranty_popup(show_warranty_popup);
				prodSub.setShow_warranty_popup_content(warranty_msg_v2);
				prodSub.setShow_warranty_popup_v2_image(warranty_msg_v2_image);
				prodSub.setShow_warranty_popup_v2_content(warranty_msg_v2_content);
				prodSub.setWarranty_popup_later_btn(show_later_btn_warranty_popup);
				prodSub.setImmediate_cancel_note(immediate_cancel_note);
				prodSub.setUpcomingrenewal_cancel_note(upcomingrenewal_cancel_note);
				prodSub.setOrderid_later_popup_content(orderid_later_popup_content);
				prodSub.setRedirect_inapp(redirect);
				prodSub.setValid_bundle_subs(validForBundleSub);
				prodSub.setImmediate_cancel(immediate_cancel);
				prodSub.setCur_feature_ui_new(cur_feature_ui_new);
			}
			
			if (subscrip1 != null && !subscrip1.getSubscriptionId().equalsIgnoreCase("NA") && subscrip1.getSubscriptionId().substring(0, 3).equalsIgnoreCase("RE-")) {
				prodSub.setIs_rec(true);
			}
			prodSub.setPlanid(iris_splan);
			prodSub.setPeriodid(iris_speriod);
			prodSub.setSubscriptionplan(rpt);
			prodSub.setMonitor_id(monitor_id.intValue());
			prodSub.setWarranty_claimed(warranty_claimed);
			prodSub.setMeariSubKey(meariSubKey);
//			response.put("sub_quantity", subQuantity);
//			response.put("mapped_quantity", mappedQuantity);
			prodSub.setShow_alertlimit(show_alertlimit);
			prodSub.setShow_upgrade(is_upgrade);
			prodSub.setShow_manage_sub(show_manage_sub);
			prodSub.setCancel_popup_content(cancel_popup);			

			async.saveCurrentPlanDetails(user.getId(), user.getChargebeeid(), iris_splan, iris_speriod, rpt.getCbPlanId(),
					null);

		} catch (Exception e) {
			log.error("getSubscription:" + e.getMessage());
		}

		return prodSub;
	}

	
	@Override
	public String checkOutURLForBundleSubs(ProductSubscription productSubscription, String chargebeeId) {
		log.info("Entered into checkOutURLForBundleSubs :: user_id : " + productSubscription.getUser_id()
				+ " :: plan_id : " + productSubscription.getPlan_id());
		try {
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);
			HashMap<String, Object> metaData = new HashMap<String, Object>();

			metaData.put("regarding", "bundle_subscription");
			metaData.put("user_id", productSubscription.getUser_id());
			metaData.put("order_id", productSubscription.getOrder_id());

			com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
			CheckoutNewRequest checkoutNewRequest = HostedPage.checkoutNew()
					.subscriptionPlanId(productSubscription.getPlan_id()).subscriptionAutoCollection(AutoCollection.ON)
					.subscriptionPlanQuantity(1).subscriptionCoupon(cbCoupon).addonId(0, activationAddonId.get("US"))
					.addonQuantity(0, 1).addonBillingCycles(0, 1).customerId(chargebeeId);

			return checkoutNewRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString()).embed(embedupdate)
					.request().hostedPage().url();
		} catch (Exception e) {
			log.error("Error in checkOutURLForBundleSubs :: Error : " + e.getLocalizedMessage());
			return "NA";
		}

	}

	@Override
	public String generatePaymentURL(String chargebeeid) {
		try {
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);

			ListResult resultSet = Customer.list().id().is(chargebeeid).request();

			if (resultSet.size() > 0) {
				HostedPage hostedPage = null;

				if (checkoutversion.equalsIgnoreCase("v2")) {
					// v2
					Result res = HostedPage.updatePaymentMethod().customerId(chargebeeid).embed(false).request();
					hostedPage = res.hostedPage();
				} else {
					// v3
					Result res = HostedPage.managePaymentSources().customerId(chargebeeid).request();
					hostedPage = res.hostedPage();
				}

				return hostedPage.url();

			} else {
				return null;
			}

		} catch (Exception e) {
			return null;
		}
	}

	@Override
	public void cancelCbSubscription(String sub_id, String chargebeeId, int count) {
		log.info("Entered cancelCbSubscription : " + sub_id);
		try {
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);

			try {
				while (count > 0) {
					Card.deleteCardForCustomer(chargebeeId).request();
					log.info("Card deleted");
				}
			} catch (Exception e) {
				log.error("Delete card for customer : " + e.getMessage());
			}

			Result res = Subscription.cancel(sub_id).request();
			log.info("Subscription id : " + sub_id);
			log.info("cancel status : " + res);

		} catch (Exception e) {
			log.error("cancelCbSubscription : " + e.getLocalizedMessage());
		}
	}

	@Override
	public void deleteCbCustomer(String chargebeeid) {
		log.info("Entered deleteCbCustomer : " + chargebeeid);
		try {
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);

			Result res = Customer.delete(chargebeeid).request();
			log.info("Chargebee id : " + chargebeeid);
			log.info("Delete status : " + res);

		} catch (Exception e) {
			log.error("deleteCbSubscription : " + e.getLocalizedMessage());
		}

	}

	@Override
	public boolean checkCardDetails(String chargebeeid) {
		log.info("Entered into checkCardDetails :: chargebee id : " + chargebeeid);
		boolean isPaymentSrcAvail = false;
		try {
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);
			ListResult result = PaymentSource.list().customerId().is(chargebeeid).request();
			for (ListResult.Entry entry : result) {
				PaymentSource paymentSource = entry.paymentSource();
				isPaymentSrcAvail = true;
			}

		} catch (Exception e) {
			log.info("Error in checkCardDetails :: Error : " + e.getLocalizedMessage());
		}
		return isPaymentSrcAvail;
	}

	@Override
	public ProductSubscription getProductSubscription(long user_id) {
		return cbDao.getProductSubscription(user_id);
	}

	@Override
	public String getSubscriptionPeriod(String periodUnit) {
		log.info("Entered into getSubscriptionPeriod :: period : " + periodUnit);
		try {

			switch (periodUnit) {
			case "Monthly": {
				return "1 Month";
			}
			case "Quarterly": {
				return "2 Months";
			}
			case "Half-Yearly": {
				return "6 Months";
			}
			case "Yearly": {
				return "1 Year";
			}
			case "2-Year": {
				return "2 Years";
			}
			case "5-Year": {
				return "5 Years";
			}
			default: {
				return "";
			}
			}
		} catch (Exception e) {
			log.error("Error in getSubscriptionPeriod :: Error : " + e.getLocalizedMessage());
			return "";
		}
	}

	@Override
	public List<UnpaidInvoices> getUnpaidInvoice(String type) {
		return cbDao.getUnpaidInvoice(type);
	}

	@Override
	public void saveCurrentPlanDetails(long user_id, String chargebee_id, int plan_id, int period_id,
			String chargebee_plan_id, JResponse response) {
		log.info("Entered into saveCurrentPlanDetails :: user_id : " + user_id + " :: chargebee_id : " + chargebee_id);
		try {
			JPlan planDesc = cbService.getPlanDesc(chargebee_plan_id);
			CbCurrentPlanDetails cbCurrentPlanDetails = new CbCurrentPlanDetails(planDesc.getPlanToPeriodId(), plan_id,
					period_id, user_id, chargebee_id, chargebee_plan_id, "NA", _helper.getCurrentTimeinUTC());
			boolean insertStatus = cbDao.saveCurrentPlanDetails(cbCurrentPlanDetails);
			log.info("Insert status of saveCurrentPlanDetails : " + insertStatus);
		} catch (Exception e) {
			log.error("Error in saveCurrentPlanDetails :: Error : " + e.getLocalizedMessage());
		}
	}

	@Override
	public String createCBSubsForRecharge(String subId, String cbID, String planId, String reSubId,
			String nextrenewal_at, String price) {
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		String status = "Success";

		try {

			String oneTimeAddonId = activationAddonId.get("US");
			int addonStatus = 0;
			String event_status;

			if (!subId.equalsIgnoreCase("NA")) {

				Result result = Subscription.retrieve(subId).request();
				Subscription subscription = result.subscription();
				String subStatus = subscription.status().name();

				if (freeplan.contains(subscription.planId())) {
					addonStatus = 0; // activation
				} else if (!freeplan.contains(subscription.planId()) && !(subStatus.equalsIgnoreCase("CANCELLED"))) {
					addonStatus = 1; // upgrade
				} else if (!freeplan.contains(subscription.planId()) && (subStatus.equalsIgnoreCase("CANCELLED"))) {
					addonStatus = 2; // re-activation
				}

				switch (addonStatus) {
				case 0:
					oneTimeAddonId = activationAddonId.get("US");
					break;

				case 1:
					oneTimeAddonId = updateAddonId.get("US");
					break;

				case 2:
					oneTimeAddonId = reactivateAddonId.get("US");
					break;
				}
				Result res = Subscription.update(subId).replaceAddonList(true).replaceCouponList(true).planId(planId)
						.addonId(0, oneTimeAddonId).addonQuantity(0, 1).addonBillingCycles(0, 1).couponIds(cbCoupon)
						.forceTermReset(true).request();
				event_status = "Sub_updated";
				status = "Success";
			} else {
				ListResult result = com.chargebee.models.Subscription.list().customerId().is(cbID).status()
						.in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL).sortByUpdatedAt(SortOrder.DESC)
						.request();

				int ssize = 0;
				boolean isPaidPlan = false;
				Subscription subscrip = null;
				if (!result.isEmpty()) {

					for (ListResult.Entry subs : result) {

						String subs_planid = subs.subscription().planId();
						if (ssize == 1 && !omitplan.contains(subs_planid)) {
							subscrip = subs.subscription();
							if (!freeplan.contains(subs_planid)) {
								long planMonitorTypeId = crService.getMonitorTypeByCBPlan( subs_planid );
								if( planMonitorTypeId == 1) {
									isPaidPlan = true;
									break;
								}
							}

						} else if (!freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)) {
							subscrip = subs.subscription();
							long planMonitorTypeId = crService.getMonitorTypeByCBPlan( subs_planid );
							if( planMonitorTypeId == 1) {
								isPaidPlan = true;
								break;
							}
						}
					}

					if (subscrip == null) {
						for (ListResult.Entry subs : result) {
							if (freeplan.contains(subs.subscription().planId())) {
								subscrip = subs.subscription();
								break;
							}
						}
					}

				}
				if (isPaidPlan && subscrip != null) {
					event_status = "Sub_Available";
					subId = subscrip.id();
					reSubId = "NA";
					status = "Available:" + subscrip.planId();
				} else {
					subId = "RE-" + String.valueOf(Calendar.getInstance().getTimeInMillis());

					// Result res =
					// Customer.update(cbID).autoCollection(AutoCollection.OFF).request();
					float priceCent = (Float.parseFloat(price)) * 100;
					log.info("priceCent:" + (int) priceCent);
					CreateForCustomerRequest sReq = Subscription.createForCustomer(cbID).id(subId).planId(planId)
							.planQuantity(1).addonId(0, oneTimeAddonId).addonBillingCycles(0, 1)
							.planUnitPrice((int) priceCent);

					if (!reCoupon.isEmpty() && !reCoupon.equalsIgnoreCase("NA"))
						sReq = sReq.couponIds(reCoupon);

					Result res = sReq.request();

					Subscription subNew = res.subscription();

					Timestamp termEndsAt = Timestamp.valueOf(nextrenewal_at);

					res = Subscription.changeTermEnd(subNew.id()).termEndsAt(termEndsAt).request();
					event_status = "Sub_Created";
					status = "Success";
				}
			}
			
			ReCBWebhookStatus reCbObj = new ReCBWebhookStatus(cbID, subId, reSubId, event_status);
			boolean stat = reService.saveRechargeCBSubStatus(reCbObj);
			log.info("saveRechargeCBSubStatus:" + stat);
			return status;
		} catch (Exception e) {
			status = "failed";
			ReCBWebhookStatus reCbObj = new ReCBWebhookStatus(cbID, subId, reSubId, status);
			boolean stat = reService.saveRechargeCBSubStatus(reCbObj);
			log.info("saveRechargeCBSubStatus:" + stat);

			log.error("saveRechargeCBSubStatus : Exce:" + e.getLocalizedMessage());
			// e.printStackTrace();
			return status;
		}

	}

	@Override
	public void updateSalesChannel(String user_id, String orderChannel, long gatewayId, String order_date, String order_id, int is_bundle) {
		cbDao.updateSalesChannel(user_id, orderChannel, gatewayId, order_date, order_id, is_bundle);
	}

	@Override
	public boolean saveCancelSubscription(JCancelSubDetail csDetail, long userid, String cancel_type,
			String cancelled_at, int refund_amount, double exchange_rate, String curr_code) {
		return cbDao.saveCancelSubscription(csDetail, userid, cancel_type, cancelled_at, refund_amount, exchange_rate,
				curr_code);
	}
	
//	@Override
//	public void updateCBSubsRenewalDt(String subId,String nxtRenewDt) {
//		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
//		try {
//			Timestamp termEndsAt = Timestamp.valueOf(nxtRenewDt);
//			
//			Result res1 = Subscription.update(subId).replaceCouponList(true).request();
//			
//			Result res2 = Subscription.changeTermEnd(subId).termEndsAt(termEndsAt).request();
//
//		}catch (Exception e) {
//			e.printStackTrace();
//		}	
//
//	}

	@Override
	public boolean generateAddiBenefits(UserV4 user, String period, String createdFrom) {
		boolean benefits_created = false;

		try {
			int periodid = Integer.parseInt(period);
			boolean show_benefits = crService.insertAddiUser(user.getEmail());
			benefits_created = crService.checkAdditionalBenifitsCreated(user.getEmail(),periodid);
			log.info("generateAddiBenefits:"+show_benefits+":"+benefits_created);
	
			if (!benefits_created) {
	
				Calendar fromCal = Calendar.getInstance();
				int mCnt = 6;
				if(periodid ==4)
					mCnt = 12;
				else if(periodid ==5)
					mCnt = 24;
				
				fromCal.set(Calendar.MONTH, (fromCal.get(Calendar.MONTH) +mCnt));
				SimpleDateFormat sdf1 = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
				String dueDate = sdf1.format(fromCal.getTime());
	
				JCouponData couponObj = new JCouponData(user.getId(), user.getEmail(), period, user.getFirstname(),
						user.getLastname(), dueDate);
				log.info("genAddiStart:"+sdf1.format(Calendar.getInstance().getTime()));
				JResponse resp1 = iAvatarService.generateCoupon(couponObj, createdFrom);
				log.info("generatedAddiBenefits:"+resp1.toString());
				log.info("genAddiEnd:"+sdf1.format(Calendar.getInstance().getTime()));
	
				if ((int) resp1.get("Status") == 1) {
					benefits_created = true;
				}
			} else
				benefits_created = true;
		} catch (Exception e) {
			log.error("err generateAddiBenefits:" + e.getLocalizedMessage());
		}
		return benefits_created;
	}

	@Override
	public AllProductSubscription getProductSubscriptionByGatewayId(long gateway_id, String chargebee_id, long monitor_type) {
		return cbDao.getProductSubscriptionByGatewayId(gateway_id, chargebee_id,monitor_type);
	}
	
	@Override
	public JResponse updateproductsub(UserV4 user, JProdSubReq subReq) {
		JResponse response = new JResponse();
		log.info("Entered into updateproductsub");
		try {
			if (user != null) {

				if (subReq.isFree_plan()) {
					ArrayList<Long> gateways = new ArrayList<Long>();
					gateways.add(subReq.getGateway_id());
					JSubManage subMap = new JSubManage(subReq.getPlan_id(), user.getChargebeeid(), "NA",
							1, gateways);
					boolean stat = crService.assignGatewayFeature(user, subMap);

					if (stat) {
						response.put("Status", 1);
						response.put("Msg", "Success");
					} else {
						response.put("Status", 0);
						response.put("Msg", "Activation Failed");
					}
				} else {
					Environment.configure(chargebeeSiteName, chargebeeSiteKey);

					if (user.getChargebeeid().equalsIgnoreCase("NA")) {
						String cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(),
								user.getEmail(), user.getMobileno(), user.getUsername(), 0, "NA");
						user.setChargebeeid(cbId);
					}

					int addonStatus = 0;
					String activationId = "";
					String reactivationId = "";
					String upgradeId = "";
					String trialId = "";
					String downgradeId = "";
					String cb_subid = subReq.getCbsubid();
					String country = user.getCountry().toUpperCase();
					country = "US";

					String[] cbPlanAndTrialPeriod = crService.getChargebeePlanById(subReq.getPlan_id(),
							subReq.getPeriod_id(), country);

					activationId = activationAddonId == null ? "setup_charges" : activationAddonId.get(country);
					reactivationId = reactivateAddonId == null ? "reactivation-charges-onetime"
							: reactivateAddonId.get(country);
					upgradeId = updateAddonId == null ? "upgrade_charges" : updateAddonId.get(country);
					trialId = trialAddonId == null ? "trial-charges-us" :  trialAddonId.get(country);
					downgradeId = downgradeAddonId == null ? "downgrade_charges" : downgradeAddonId.get(country);

					String cb_plan = cbPlanAndTrialPeriod[0];
					int freeTrialPeriod = Integer.parseInt(cbPlanAndTrialPeriod[1]);
//					String cb_coupon_id = cbPlanAndTrialPeriod[2];
//					String cb_addon_id = cbPlanAndTrialPeriod[3];
//					int plan_to_period_id = Integer.parseInt(cbPlanAndTrialPeriod[4]);

					if(subReq.getMonitortype_id()==6) {
						if(!orderdate_trial.equalsIgnoreCase("NA") &&!orderdate_trial.isEmpty())
						{
							String[] dtRange = orderdate_trial.split(":");
							
							boolean orderAvail = gatewayServiceV4.checkOrderWithinRange(subReq.getGateway_id(), dtRange[0], dtRange[1]);
							
							if(orderAvail)
								freeTrialPeriod = offer_days;
						}

					}else if(subReq.getMonitortype_id()==11) {
						if (cb_subid.equalsIgnoreCase("NA"))
							cb_subid  = cbService.getVetPlanSub(user.getChargebeeid(), subReq.getMonitortype_id());
					}
					
					String oneTimeAddonId = "";
					String subStatus = "";
					Subscription subscription = null;
					HashMap<String, Object> metaData = new HashMap<String, Object>();
					metaData.put("userid", user.getId());
					metaData.put("gatewayid", subReq.getGateway_id());

					if (cb_subid.isEmpty())
						cb_subid = "NA";

					if (!cb_plan.equalsIgnoreCase("NA")) {
						if (!cb_subid.equalsIgnoreCase("NA")) {
							
							Result result = Subscription.retrieve(cb_subid).request();
							subscription = result.subscription();
							subStatus = subscription.status().name();
							if (!(subStatus.equalsIgnoreCase("CANCELLED"))) {
								addonStatus = 1; // upgrade
							} else if ((subStatus.equalsIgnoreCase("CANCELLED"))) {
								addonStatus = 2; // re-activation
							}
						} else if(cb_subid.equalsIgnoreCase("NA") && subReq.isIs_trial()) {
							addonStatus = 3;
						}else {
							addonStatus = 0;
						}
						if( addonStatus==1) {
							ArrayList<Integer> ids = crService.getPlanAndPeriod(subscription.planId());
							long curPlanid = ids.get(0); // before updating plan
							long curPeriodid = ids.get(1); // before updating period

							if(subReq.getPeriod_id() < curPeriodid) {
								addonStatus = 4;
							}
						}
						switch (addonStatus) {
						case 0:
							oneTimeAddonId = activationId;
							break;

						case 1:
							oneTimeAddonId = upgradeId;
							break;

						case 2:
							oneTimeAddonId = reactivationId;
							break;
						case 3:
							oneTimeAddonId = trialId;
							break;
						case 4:
							oneTimeAddonId = downgradeId;
							break;
						}
						Timestamp trialEnd = null;

						// this for customer merge
						CheckoutNewRequest checkoutNewRequest;
						CheckoutExistingRequest checkoutExitingRequest;
						Result res;
						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);

						if (!cb_subid.equalsIgnoreCase("NA") && subReq.isIs_trial()) {
							log.info(
									"updateproductsub : !cb_subid.equalsIgnoreCase(\"NA\")&& freetrial && freeTrialPeriod >0");

							Timestamp trialEndDate = new Timestamp(
									Instant.now().plus(freeTrialPeriod, ChronoUnit.DAYS).toEpochMilli());

							if (!subStatus.equalsIgnoreCase("CANCELLED")) {
								Result res1 = Subscription.cancel(cb_subid)
										.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.PRORATE)
										.request();
							}

							checkoutExitingRequest = HostedPage.checkoutExisting().subscriptionId(cb_subid)
									.subscriptionPlanId(cb_plan).addonId(0, oneTimeAddonId).addonQuantity(0, 1)
									.addonBillingCycles(0, 1).replaceAddonList(true).redirectUrl(redirtPetUrl)
									.passThruContent(jsonObj.toString()).embed(embedupdate).forceTermReset(true)
									.subscriptionTrialEnd(trialEndDate);

							res = checkoutExitingRequest.request();

						} else if (cb_subid.equalsIgnoreCase("NA")) {
							log.info(
									"updateproductsub : cb_subid.equalsIgnoreCase(\"NA\")&& freetrial && freeTrialPeriod >0");


							checkoutNewRequest = HostedPage.checkoutNew().subscriptionPlanId(cb_plan)
									.subscriptionPlanQuantity(1)
									.addonId(0, oneTimeAddonId).addonQuantity(0, 1).addonBillingCycles(0, 1)
									.passThruContent(jsonObj.toString()).customerId(user.getChargebeeid());
							
							if (subReq.isIs_trial() && freeTrialPeriod > 0) {
								Timestamp trialEndDate = new Timestamp(
										Instant.now().plus(freeTrialPeriod, ChronoUnit.DAYS).toEpochMilli());
								checkoutNewRequest.subscriptionTrialEnd(trialEndDate);
							}
							
							res = checkoutNewRequest.redirectUrl(redirtPetUrl).request();

						} else if (!cb_subid.equalsIgnoreCase("NA")) {
							log.info("updateproductsub : !cb_subid.equalsIgnoreCase(\"NA\")");

							if(subReq.getMonitortype_id()==1 && subReq.isIs_flexi() && !subStatus.equalsIgnoreCase("CANCELLED")){
								Result res1 = Subscription.cancel(cb_subid)
										.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.NONE)
										.request();
							}

							checkoutExitingRequest = HostedPage.checkoutExisting().subscriptionId(cb_subid)
									.subscriptionPlanId(cb_plan).addonId(0, oneTimeAddonId).addonQuantity(0, 1)
									.addonBillingCycles(0, 1).replaceAddonList(true).redirectUrl(redirtPetUrl)
									.passThruContent(jsonObj.toString()).embed(embedupdate).forceTermReset(true);

							if (subReq.isIs_trial() && freeTrialPeriod > 0) {
								Timestamp trialEndDate = new Timestamp(
										Instant.now().plus(freeTrialPeriod, ChronoUnit.DAYS).toEpochMilli());
								checkoutExitingRequest.subscriptionTrialEnd(trialEndDate);
							}

							res = checkoutExitingRequest.request();

						} else {
							log.info("updateproductsub : none of the if case matched :inside else case block");

							checkoutNewRequest = HostedPage.checkoutNew().subscriptionPlanId(cb_plan)
									.subscriptionPlanQuantity(1).addonId(0, oneTimeAddonId).addonQuantity(0, 1)
									.addonBillingCycles(0, 1).customerId(user.getChargebeeid());

							if (subReq.isIs_trial() && freeTrialPeriod > 0) {
								Timestamp trialEndDate = new Timestamp(
										Instant.now().plus(freeTrialPeriod, ChronoUnit.DAYS).toEpochMilli());
								checkoutNewRequest.subscriptionTrialEnd(trialEndDate);
							}

							res = checkoutNewRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString())
									.embed(embedupdate).request();

						}

						HostedPage hostedPage = res.hostedPage();
						boolean updated = false;
						if(subReq.getMonitortype_id()>0) {
							String  hp_id = hostedPage.id();
							updated = cbService.saveTemp_sub_purchase(user.getId(), subReq.getGateway_id(), subReq.getMonitortype_id(), hp_id);
						}
						
						if (updated) {
							response.put("checkOutURL", hostedPage.url());
							response.put("Status", 1);
							response.put("Msg", "Success");
						} else {
							response.put("Status", 0);
							response.put("Msg", "Error occurred. Please try again later");
						}

					} else {
						response.put("Status", 0);
						response.put("Msg", "chargebee plan id not available in DB");
					}
				}
			}

		} catch (Exception e) {
			//e.printStackTrace();
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Failed to update subscription");
			log.error("Error updateproductsub:" + e.getMessage());
		}
		return response;
	}

	@Override
	public boolean checkDueInvoice(String cbSubId) {
		return cbDao.checkDueInvoice(cbSubId);
	}
	
	public JResponse cancelsubplanV5(UserV4 usr, JCancelSubDetail jCancelSubDetail, boolean end_term_cancel) {
		JResponse response = new JResponse();
		boolean refund = false;
		try {
			
			boolean isRecharge = false;
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);
			if (usr != null && !jCancelSubDetail.getCb_subid().equalsIgnoreCase("NA")) {
				if(cancel_recharge) {
					if( jCancelSubDetail.getCb_subid().toLowerCase().contains("re-") || !usr.getRecharge_custid().equalsIgnoreCase("NA") ) { 
						log.info("recharge subscriprion : true");
						isRecharge = crService.cancelRechargeSubscription( usr.getUsername() );
					} else { 
						log.info("recharge subscriprion : false");
					}
				}
				ListResult res1 = com.chargebee.models.Subscription.list().id().is(jCancelSubDetail.getCb_subid())
						.request();
				Subscription subscrip = null;

				for (ListResult.Entry subs : res1) {
					subscrip = subs.subscription();
				}
				int invoice_due = 0;
				if (subscrip != null) {
					invoice_due = subscrip.dueInvoicesCount();
					if (invoice_due > 0) {
						ListResult invoiceList = Invoice.list().customerId().is(usr.getChargebeeid()).subscriptionId()
								.is(subscrip.id()).status().is(com.chargebee.models.Invoice.Status.PAYMENT_DUE)
								.request();
						if (invoiceList.isEmpty()) {
							invoice_due = 0; // no pending due
						} else {
							invoice_due = 1; // pending due is there
						}
					}
				}
				Result res = null;
				String msg = "";
				String emailMsg = "", cancel_type = "NA", curr_code = "NA";
				double exchange_rate = 1;
				int refund_amount = 0;
				String cancelled_at = "1753-01-01 00:00:00";

				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				sdf.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
				Date curStart = sdf.parse(sdf.format(subscrip.currentTermStart().getTime()));
				Date today = sdf.parse(new Helper().getCurrentTimeinUTC());

				long difference = today.getTime() - curStart.getTime();
				double daysBetween = Math.ceil((difference / (1000 * 60 * 60 * 24)));
				String subStatus = subscrip.status().toString();

				msg = "Your plan has been cancelled.";
				//Other pet monitor all other subscription are end of the term cancel
				if(jCancelSubDetail.getMonitortype_id()>1) {
					daysBetween = 8;
					end_term_cancel = true;
					
//					int qty = subscrip.planQuantity();
					
//					if(qty>1) {
//						HashMap<String, Object> metaData = new HashMap<String, Object>();
//
//						metaData.put("userid", usr.getId());
//						metaData.put("gatewayid", jCancelSubDetail.getGateway_id());
//
//						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
//						res = Subscription.update(subscrip.id()).planQuantity(qty-1).metaData(jsonObj).request();
//						subscrip = res.subscription();
//						
//						response.put("Status", 1);
//						response.put("Msg", msg);
//						return response;
//					}
				}
				

				if(jCancelSubDetail.getMonitortype_id() == 1) {
					cbService.saveTemp_sub_purchase(usr.getId(), jCancelSubDetail.getGateway_id(), jCancelSubDetail.getMonitortype_id(), jCancelSubDetail.getCb_subid());
				}
				
				if (invoice_due > 0) {
					if( !isRecharge ) 
						res = Subscription.cancel(jCancelSubDetail.getCb_subid()).endOfTerm(false)
								.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.NONE).request();
					cancel_type = "immediate_cancel";
					cancelled_at = sdf.format(today);
						
				} else if (daysBetween <= 7) {
					if (end_term_cancel) {
						if( !isRecharge ) 
							res = Subscription.cancel(jCancelSubDetail.getCb_subid()).endOfTerm(true)
									.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.NONE).request();
						Date nextRenewDate = sdf.parse(sdf.format(subscrip.nextBillingAt().getTime()));
						cancel_type = "end_of_the_term";
						cancelled_at = sdf.format(nextRenewDate);
						
						msg = "Your plan has been cancelled.  It'll take effect at the end date of the plan period.";
					} else {
						if( !isRecharge ) 
							res = Subscription.cancel(jCancelSubDetail.getCb_subid()).endOfTerm(false)
									.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.NONE).request();
						cancel_type = "immediate_cancel";
						cancelled_at = sdf.format(today);

						Subscription cancelSubscription = res.subscription();
						subStatus = cancelSubscription.status().toString();

						if (subStatus.equalsIgnoreCase("cancelled") || subStatus.equalsIgnoreCase("non_renewing")) {
							ListResult invoiceList = Invoice.list().customerId().is(usr.getChargebeeid())
									.subscriptionId().is(subscrip.id()).sortByDate(SortOrder.DESC).limit(1).request();

							if (!invoiceList.isEmpty()) {
								for (ListResult.Entry inv : invoiceList) {
									JSONObject invResponse = new JSONObject();
									invResponse = new JSONObject(inv.invoice().toJson());

									exchange_rate = Double.parseDouble(invResponse.get("exchange_rate").toString());
									curr_code = inv.invoice().currencyCode();

									int amount_paid = inv.invoice().amountPaid();
									int refund_minus_amount = 0;
									boolean refundByProrated = Boolean.parseBoolean(refund_by_prorated);

									if (amount_paid > 0) {
										if (refundByProrated) {
											Date nextRenewDate = sdf
													.parse(sdf.format(subscrip.nextBillingAt().getTime()));
											long daysDiff = nextRenewDate.getTime() - curStart.getTime();
											double daysForRenewal = Math.ceil((daysDiff / (1000 * 60 * 60 * 24)));
											refund_minus_amount = (int) ((amount_paid / daysForRenewal) * daysBetween);
										} else
											refund_minus_amount = Integer.parseInt(minus_refund_amount);

										if (amount_paid < refund_minus_amount)
											refund_amount = amount_paid;
										else
											refund_amount = amount_paid - refund_minus_amount;

										try {
											Result result = Invoice.refund(inv.invoice().id())
													.refundAmount(refund_amount)
													.creditNoteReasonCode(ReasonCode.SERVICE_UNSATISFACTORY).request();
											Invoice invoice = result.invoice();
											Transaction transaction = result.transaction();
											CreditNote creditNote = result.creditNote();

											msg = "Waggle Subscription Cancelled. The refund will be processed in 24-48 hours.";
											refund = true;
										} catch (Exception e) {
											response.put("Refund", false);
											response.put("Error in refund", e.getLocalizedMessage());
											log.error("cancelsubplan : refund exception : " + e.getLocalizedMessage());
										}
									}
								}
							}
						}
					}
				} else if(daysBetween <= refund_window_for_renewal) {
					if( !isRecharge )
						res = Subscription.cancel(jCancelSubDetail.getCb_subid()).endOfTerm(false)
								.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.NONE).request();
					cancel_type = "immediate_cancel";
					cancelled_at = sdf.format(today);

					Subscription cancelSubscription = res.subscription();
					subStatus = cancelSubscription.status().toString();

					if (subStatus.equalsIgnoreCase("cancelled") || subStatus.equalsIgnoreCase("non_renewing")) {
						ListResult invoiceList = Invoice.list().customerId().is(usr.getChargebeeid())
								.subscriptionId().is(subscrip.id()).sortByDate(SortOrder.DESC).limit(1).request();

						if (!invoiceList.isEmpty()) {
							for (ListResult.Entry inv : invoiceList) {
								JSONObject invResponse = new JSONObject();
								invResponse = new JSONObject(inv.invoice().toJson());

								exchange_rate = Double.parseDouble(invResponse.get("exchange_rate").toString());
								curr_code = inv.invoice().currencyCode();

								int amount_paid = inv.invoice().amountPaid();
								int refund_minus_amount = 0;
								boolean refundByProrated = Boolean.parseBoolean(refund_by_prorated);

								if (amount_paid > 0) {
									if (refundByProrated) {
										Date nextRenewDate = sdf
												.parse(sdf.format(subscrip.nextBillingAt().getTime()));
										long daysDiff = nextRenewDate.getTime() - curStart.getTime();
										double daysForRenewal = Math.ceil((daysDiff / (1000 * 60 * 60 * 24)));
										refund_minus_amount = (int) ((amount_paid / daysForRenewal) * daysBetween);
									} else
										refund_minus_amount = Integer.parseInt(minus_refund_amount);

									if (amount_paid < refund_minus_amount)
										refund_amount = amount_paid;
									else
										refund_amount = amount_paid - refund_minus_amount;

									try {
										Result result = Invoice.refund(inv.invoice().id())
												.refundAmount(refund_amount)
												.creditNoteReasonCode(ReasonCode.SERVICE_UNSATISFACTORY).request();
										Invoice invoice = result.invoice();
										Transaction transaction = result.transaction();
										CreditNote creditNote = result.creditNote();

										msg = "Waggle Subscription Cancelled. The refund will be processed in 24-48 hours.";
										refund = true;
									} catch (Exception e) {
										response.put("Refund", false);
										response.put("Error in refund", e.getLocalizedMessage());
										log.error("cancelsubplan : refund exception : " + e.getLocalizedMessage());
									}
								}
							}
						}
					}
				}
				else {
					if( !isRecharge ) 
						res = Subscription.cancel(jCancelSubDetail.getCb_subid()).endOfTerm(true)
								.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.NONE).request();
					Date nextRenewDate = sdf.parse(sdf.format(subscrip.nextBillingAt().getTime()));
					cancel_type = "end_of_the_term";
					cancelled_at = sdf.format(nextRenewDate);
					msg = "Your plan has been cancelled.  It'll take effect at the end date of the plan period.";
				}

				Subscription cancelSubscription = res.subscription();
				subStatus = cancelSubscription.status().toString();
				
				if(jCancelSubDetail.getMonitortype_id()>0) {
					if(subStatus.equalsIgnoreCase("non_renewing"))
						crService.updateSubsStatus(subStatus, cancelSubscription.id());
				}

				if (subStatus.equalsIgnoreCase("cancelled") || subStatus.equalsIgnoreCase("non_renewing")) {
					boolean status = cbService.saveCancelSubscription(jCancelSubDetail, usr.getId(), cancel_type,
							cancelled_at, refund_amount, exchange_rate, curr_code);
					File file = ResourceUtils.getFile("classpath:iris3.properties");
					Properties prop = new Properties();
					prop.load(new FileInputStream(file));

					String toAddr = prop.getProperty("to_address");
					String ccAddr = prop.getProperty("cc_address");
					String bccAddr = prop.getProperty("bcc_address");

					String sub = "CB Cancel Subscription: " + usr.getUsername();
					String mailmsg = "";
					
					if(!usr.getRecharge_custid().equalsIgnoreCase("NA")) {
						sub = "Recharge Subscription Cancel: " + usr.getUsername();
						mailmsg = "Hi Team, " + "<br />" + emailMsg
								+ " Kindly Cancel the recharge subscription manually in the Recharge Portal <br /><b> Username :</b>" + usr.getUsername()
								+ "<br /><b> Recharge customer ID:</b> " + usr.getRecharge_custid()
								+ "<br /><b> CB customer ID:</b> " + usr.getChargebeeid()
								+ "<br /><b> CB Subscription ID:</b> " + jCancelSubDetail.getCb_subid();
						async.SendEmail_SES(toAddr, ccAddr, bccAddr, sub, mailmsg);
					}
					
					Template emailTemplate = (Template) templates.getTemplate("RefundEmail.ftl");
					Map<String, String> emailParams = new HashMap<>();
					if (refund) {
						emailTemplate = (Template) templates.getTemplate("RefundEmail.ftl");
						sub = "Confirmation of Waggle Pet Monitor Subscription Cancellation and Refund";
					} else if(!cancel_type.equalsIgnoreCase("immediate_cancel")) {
						emailTemplate = (Template) templates.getTemplate("ScheduledCancel.ftl");
						sub = "Confirmation: Cancelation Scheduled";
					}

					emailParams.put("Name", String.valueOf(usr.getFirstname()));
					emailParams.put("Year", String.valueOf(Calendar.getInstance().get(Calendar.YEAR)));

					ResponseEntity<String> newEmailContent = ResponseEntity
							.ok(FreeMarkerTemplateUtils.processTemplateIntoString(emailTemplate, emailParams));
					mailmsg = newEmailContent.getBody();
					async.SendEmail_SES(usr.getEmail(), null, bccAddr, sub, mailmsg);

                    String reasonType=jCancelSubDetail.getReason_type();

                    if(reasonType.equals("Others (please share)")){
                        reasonType="Others";
                    }
                    String reasonDescription=reasonType.equals("Others")?jCancelSubDetail.getReason_desc():"NA";

                    if(!reasonType.equals("Not happy with the services")) {
                        String planName = cbDao.getPlanName(jCancelSubDetail.getCb_subid());
                        String mailSub = "Reg Cancel Feedback : " + usr.getUsername();
                        String mailContent = "<p>Hi Team,</p>" + "<p>Find the user cancel feedback request</p>";
                        mailContent += "<p>Email           : " + usr.getEmail() + "</p>";
                        mailContent += "<p>Chargebee Id    : " + usr.getChargebeeid() + "</p>";
                        if (!planName.isEmpty()) {
                            mailContent += "<p>Current Plan    : " + planName + "</p>";
                        }
                        mailContent += "<p>Reason          : " + reasonType + "</p>";
                        mailContent += "<p>Customer Review : " + reasonDescription + "</p>";
                        mailContent += "<p>Offer Provide   : "+ "NA" +"</p>";
                        mailContent += "<p>Offer Device   : " + "NA" + "</p>";

                        mailContent = mailContent + "<br><br>Thanks,<br> Irisservice ";
                        async.SendEmail_SES(toAddr, ccAddr, bccAddr, mailSub, mailContent);

                    }
					response.put("Status", 1);
					response.put("Msg", msg);
					response.put("Refund", refund);
				} else {
					response.put("Status", 0);
					response.put("Msg", "Failed to cancel subscription!!!");
				}
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured");
			response.put("Error", e.getLocalizedMessage());
			log.error("cancelsubplan : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@Override
	public String getMeariSubCode(long userid, long gatewayid,long period_id,boolean isSolar) {
		String keyname =  cbDao.getMeariSubCode(userid, gatewayid,period_id, isSolar);
		if(!keyname.isEmpty())
			updateMeariSubStatus(userid, gatewayid,0, "inprogress", keyname);
		return keyname;
		
	}

	@Override
	public boolean updateMeariSubStatus(long userid, long gatewayid, long period_id,String type,String keyname) {
		return cbDao.updateMeariSubStatus(userid, gatewayid,period_id, type, keyname);
	}

	@Override
	public boolean saveTemp_sub_purchase(long userid, long gatewayid, long monitortype_id, String hp_id) {
		return cbDao.saveTemp_sub_purchase(userid, gatewayid, monitortype_id, hp_id);
	}
	
	@Override
	public ProductSubscription getProductSubscriptionByOrderId(long order_id) {
		return cbDao.getProductSubscriptionByOrderId(order_id);
	}

	@Override
	public ProductSubscription saveOrUpdateProductSubscription(ProductSubscription productSubscription) {
		return cbDao.saveOrUpdateProductSubscription(productSubscription);
	}
	
	
	public JResponse cancelsubplanByreturn(long userId, String meid) {
		JResponse response = new JResponse();
		boolean refund = false;
		User user = null;
		Gateway gateway = null;
		boolean end_term_cancel = false;
		CancelsubHistory cansub = new CancelsubHistory();
		try {
			
			boolean isRecharge = false;
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);
			
			try {
				user = userService.getUserById(userId);
			} catch (Exception e) {
				log.info("Invalid userid : " + user.getId() + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Userid");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			
			if(meid != null)
				gateway = gatewayServiceV4.getGatewayByMeid(meid);
			
			Set<Gateway> gatewayList = user.getGateways();
			
			if (user != null && gateway != null && gatewayList.size() == 1) {
	
				ListResult res1 = com.chargebee.models.Subscription.list().customerId().is(user.getChargebeeid()).request();
				Subscription subscrip = null;

				for (ListResult.Entry subs : res1) {
					subscrip = subs.subscription();
				}
				int invoice_due = 0;
				if (subscrip != null) {
					
					if(cancel_recharge) {
						if( subscrip.id().toLowerCase().contains("re-") ) { 
							log.info("recharge subscriprion : true");
							isRecharge = crService.cancelRechargeSubscription( user.getUsername() );
						} else { 
							log.info("recharge subscriprion : false");
						}
					}
					
					invoice_due = subscrip.dueInvoicesCount();
					if (invoice_due > 0) {
						ListResult invoiceList = Invoice.list().customerId().is(user.getChargebeeid()).subscriptionId()
								.is(subscrip.id()).status().is(com.chargebee.models.Invoice.Status.PAYMENT_DUE)
								.request();
						if (invoiceList.isEmpty()) {
							invoice_due = 0; // no pending due
						} else {
							invoice_due = 1; // pending due is there
						}
					}
				}
				Result res = null;
				String msg = "";
				String emailMsg = "", cancel_type = "NA", curr_code = "NA";
				double exchange_rate = 1;
				int refund_amount = 0;
				String cancelled_at = "1753-01-01 00:00:00";

				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				sdf.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
				Date curStart = sdf.parse(sdf.format(subscrip.currentTermStart().getTime()));
				Date today = sdf.parse(new Helper().getCurrentTimeinUTC());

				long difference = today.getTime() - curStart.getTime();
				double daysBetween = Math.ceil((difference / (1000 * 60 * 60 * 24)));
				String subStatus = subscrip.status().toString();

				msg = "Your plan has been cancelled.";
				//Other pet monitor all other subscription are end of the term cancel
				
				AllSubscription allSubs = null;
				AllProductSubscription allProdSub = null;
				
				if(gateway.getModel().getMonitor_type().getId() == 1) {
					allSubs = cbService.getSubscriptionByGatewayId(subscrip.id());
				}else if(allSubs == null && gateway.getModel().getMonitor_type().getId() != 1) {
					allProdSub = cbService.getProductSubscriptionBySubId(subscrip.id());
				}
				
				if (allSubs != null || allProdSub != null) {
					
					cansub.setEmail(user.getEmail());
					cansub.setPlan_name(subscrip.planId());
					cansub.setSubsciption_id(subscrip.id());
					
					OrderMappingDetails order = userService.getOrderMappingByUser(user.getId());
					if(order != null) {
						cansub.setOrderid(order.getOrderid());
					}
					
					if(allProdSub != null && allProdSub.getMonitor_type() > 1) {
						daysBetween = 8;
						end_term_cancel = true;
					}

					if (invoice_due > 0 || (subStatus.equalsIgnoreCase("paused"))) {
						if (!isRecharge)
							res = Subscription.cancel(subscrip.id()).endOfTerm(false)
									.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.NONE)
									.request();
						cancel_type = "immediate_cancel";
						cancelled_at = sdf.format(today);

					} else if (daysBetween <= 7) {
						if (end_term_cancel) {
							if (!isRecharge)
								res = Subscription.cancel(subscrip.id()).endOfTerm(true)
										.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.NONE)
										.request();
							Date nextRenewDate = sdf.parse(sdf.format(subscrip.nextBillingAt().getTime()));
							cancel_type = "end_of_the_term";
							cancelled_at = sdf.format(nextRenewDate);

							msg = "Your plan has been cancelled.  It'll take effect at the end date of the plan period.";
						} else {
							if (!isRecharge)
								res = Subscription.cancel(subscrip.id()).endOfTerm(false)
										.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.NONE)
										.request();
							cancel_type = "immediate_cancel";
							cancelled_at = sdf.format(today);

							Subscription cancelSubscription = res.subscription();
							subStatus = cancelSubscription.status().toString();

							if (subStatus.equalsIgnoreCase("cancelled") || subStatus.equalsIgnoreCase("non_renewing")) {
								ListResult invoiceList = Invoice.list().customerId().is(user.getChargebeeid())
										.subscriptionId().is(subscrip.id()).sortByDate(SortOrder.DESC).limit(1)
										.request();

								if (!invoiceList.isEmpty()) {
									for (ListResult.Entry inv : invoiceList) {
										JSONObject invResponse = new JSONObject();
										invResponse = new JSONObject(inv.invoice().toJson());

										exchange_rate = Double.parseDouble(invResponse.get("exchange_rate").toString());
										curr_code = inv.invoice().currencyCode();

										int amount_paid = inv.invoice().amountPaid();
										int refund_minus_amount = 0;
										boolean refundByProrated = Boolean.parseBoolean(refund_by_prorated);

										if (amount_paid > 0) {
											if (refundByProrated) {
												Date nextRenewDate = sdf
														.parse(sdf.format(subscrip.nextBillingAt().getTime()));
												long daysDiff = nextRenewDate.getTime() - curStart.getTime();
												double daysForRenewal = Math.ceil((daysDiff / (1000 * 60 * 60 * 24)));
												refund_minus_amount = (int) ((amount_paid / daysForRenewal)
														* daysBetween);
											} else
												refund_minus_amount = Integer.parseInt(minus_refund_amount);

											if (amount_paid < refund_minus_amount)
												refund_amount = amount_paid;
											else
												refund_amount = amount_paid - refund_minus_amount;

											try {
												Result result = Invoice.refund(inv.invoice().id())
														.refundAmount(refund_amount)
														.creditNoteReasonCode(ReasonCode.SERVICE_UNSATISFACTORY)
														.request();
												Invoice invoice = result.invoice();
												Transaction transaction = result.transaction();
												CreditNote creditNote = result.creditNote();

												msg = "Waggle Subscription Cancelled. The refund will be processed in 24-48 hours.";
												refund = true;
											} catch (Exception e) {
												response.put("Refund", false);
												response.put("Error in refund", e.getLocalizedMessage());
												log.error("cancelsubplan : refund exception : "
														+ e.getLocalizedMessage());
											}
										}
									}
								}
							}
						}
					} else {
						if (!isRecharge)
							res = Subscription.cancel(subscrip.id()).endOfTerm(true)
									.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.NONE)
									.request();
						Date nextRenewDate = sdf.parse(sdf.format(subscrip.nextBillingAt().getTime()));
						cancel_type = "end_of_the_term";
						cancelled_at = sdf.format(nextRenewDate);
						msg = "Your plan has been cancelled.  It'll take effect at the end date of the plan period.";
					}

					Subscription cancelSubscription = res.subscription();
					subStatus = cancelSubscription.status().toString();
					
					if(allProdSub != null && allProdSub.getMonitor_type() >1) {
						if(subStatus.equalsIgnoreCase("non_renewing"))
							crService.updateSubsStatus(subStatus, cancelSubscription.id());
					}
					cansub.setStatus(subStatus);
					cansub.setOrderid("");
					cansub.setChargebee_id(user.getChargebeeid());
					cansub.setMeid(meid);

					if (subStatus.equalsIgnoreCase("cancelled") || subStatus.equalsIgnoreCase("non_renewing")) {
						boolean status = cbService.saveReturnCancelSubscription(subscrip.id(), "Return Product",
								"Retrun Product from web site auto cancellation", user.getId(), cancel_type,
								cancelled_at, refund_amount, exchange_rate, curr_code);
						File file = ResourceUtils.getFile("classpath:iris3.properties");
						Properties prop = new Properties();
						prop.load(new FileInputStream(file));

						String toAddr = prop.getProperty("to_address");
						String ccAddr = prop.getProperty("cc_address");
						String bccAddr = prop.getProperty("bcc_address");

						String sub = "CB Cancel Subscription: " + user.getUsername();
						String mailmsg = "Hi Team, " + "<br />" + emailMsg
								+ " for the below customer. <br /><b> Username :</b>" + user.getUsername()
								+ "<br /><b> CB customer ID:</b> " + user.getChargebeeid()
								+ "<br /><b> CB Subscription ID:</b> " + subscrip.id();

//					async.SendEmail_SES(toAddr, ccAddr, bccAddr, sub, mailmsg);
						Template emailTemplate = (Template) templates.getTemplate("RefundEmail.ftl");
						Map<String, String> emailParams = new HashMap<>();
						if (refund) {
							emailTemplate = (Template) templates.getTemplate("RefundEmail.ftl");
							sub = "Confirmation of Waggle Pet Monitor Subscription Cancellation and Refund";
						} else if (!cancel_type.equalsIgnoreCase("immediate_cancel")) {
							emailTemplate = (Template) templates.getTemplate("ScheduledCancel.ftl");
							sub = "Confirmation: Cancelation Scheduled";
						}

						emailParams.put("Name", String.valueOf(user.getFirstname()));
						emailParams.put("Year", String.valueOf(Calendar.getInstance().get(Calendar.YEAR)));

						ResponseEntity<String> newEmailContent = ResponseEntity
								.ok(FreeMarkerTemplateUtils.processTemplateIntoString(emailTemplate, emailParams));
						mailmsg = newEmailContent.getBody();
						async.SendEmail_SES(user.getEmail(), null, bccAddr, sub, mailmsg);

						response.put("Status", 1);
						response.put("Msg", msg);
						response.put("Refund", refund);
					} else {
						response.put("Status", 0);
						response.put("Msg", "Failed to cancel subscription!!!");
					}
					
					cbService.saveCancelSubHistory(cansub);
				}
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured");
			response.put("Error", e.getLocalizedMessage());
			log.error("cancelsubplan : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	

	@Override
	public boolean saveReturnCancelSubscription(String cbsubid, String reasonType, String reasonDesc, long userid, String cancel_type,
			String cancelled_at, int refund_amount, double exchange_rate, String curr_code){
		return cbDao.saveReturnCancelSubscription(cbsubid, reasonType, reasonDesc, userid, cancel_type, cancelled_at, refund_amount, exchange_rate,
				curr_code);
	}
	
	
	@Override
	public AllSubscription getSubscriptionByGatewayId(String subscrp_id) {
		return cbDao.getSubscriptionByGatewayId(subscrp_id);
	}
	
	public AllProductSubscription getProductSubscriptionBySubId(String subscrp_id) {
		return cbDao.getProductSubscriptionBySubId(subscrp_id);
	}

	@Override
	public boolean saveCancelSubHistory(CancelsubHistory canSub) {
		return cbDao.saveCancelSubHistory(canSub);
	}
	
	public String getSubscriptionByChargebeeIdNotinProductsub(String chargebeeid) {
		return cbDao.getSubscriptionByChargebeeIdNotinProductsub(chargebeeid);
	}
	

	public JProductSubRes checkDeviceConfigStatusByGateway(long planid, JGateway gateway, int days_remaining, String status,
			int exMo,int exYr, int subMo,int subYr,String userCB,String paymenttype, JProductSubRes prodSub) {
		log.info("Entering checkDeviceConfigStatusV2:");

		String showNextRenewal_withContent = "";

		try {
				String sub = cbService.getProductSubscriptionStatus(gateway.getId(), userCB);
				
				JGatewayFeature gatewayFeature = crService.getGatewayFeatureById(gateway.getId());
				
				long add_device_cnt = 0;
				int remaindays = -1;
				int maxDevCnt = 0;
				boolean setupActivate = false;
				if (gatewayFeature.getPlan_id() > 0) {
					maxDevCnt = crService.getDeviceConfigV4Gateway(gateway.getId(), gatewayFeature.getPlan_id());
				}


				if (sub.equalsIgnoreCase("NA") || gatewayFeature.getPlan_id() == 1 || sub.equalsIgnoreCase("INACTIVE") || sub.equalsIgnoreCase("PAUSED") || sub.equalsIgnoreCase("CANCELLED")) {
					setupActivate = true;
					remaindays = -1;
				}  else {
					if (gatewayFeature.getPlan_id() > 0) {
						if (maxDevCnt == 0) {
							setupActivate = true;
						} else {
							if (maxDevCnt > add_device_cnt) {
								setupActivate = false;
								add_device_cnt = add_device_cnt + 1;
								remaindays = days_remaining;
							} else {
								setupActivate = true;
								remaindays = -1;
							}
						}
					} else {
						setupActivate = true;
						remaindays = -1;
					}
				}

				if((remaindays < days_tohandle_nextrenewal) && (remaindays > 0) && show_nextrenewal_popup
						&&((exMo>0 && exMo < subMo && exYr==subYr) || exYr>0 && exYr<subYr) 

						&&(!paymenttype.equalsIgnoreCase("PAYPAL_EXPRESS_CHECKOUT"))) {

					showNextRenewal_withContent = "Your card is about to expire. Please update it to ensure uninterrupted service.";

					//showNextRenewal_withContent = "Subscription renews in " + remaindays + " days";
				}

				
				prodSub.setSetupActivate(setupActivate);
				prodSub.setDays_remaining(remaindays);
				prodSub.setMeid(gateway.getMeid());
				prodSub.setShow_nextrenewal_popup_content(showNextRenewal_withContent);
			
		} catch (Exception e) {
			log.info("checkDeviceConfigStatusV2 : " + e.getLocalizedMessage());
		}
		return prodSub;
	}
	
	@Override
	public String getProductSubscriptionStatus(long gateway_id, String chargebee_id) {
		return cbDao.getProductSubscriptionStatus(gateway_id,chargebee_id);
	}
	
	public String getPlanVersionbyplanname(String planid) {
		return cbDao.getPlanVersionbyplanname(planid);
	}
	
	@Override
	public String generatePaymentURLForProductsub(ProductSubscription productSubscription, String chargebeeId,
			long userId, long gatewayId, long monitorType) {
		log.info("Entered into generatePaymentURLForProductsub :: user_id : " + productSubscription.getUser_id()
				+ " :: plan_id : " + productSubscription.getPlan_id());
		Result res;
		try {
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);
			HashMap<String, Object> metaData = new HashMap<String, Object>();

			metaData.put("regarding", "bundle_subscription");
			metaData.put("user_id", productSubscription.getUser_id());
			metaData.put("order_id", productSubscription.getOrder_id());

			com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
			CheckoutNewRequest checkoutNewRequest = HostedPage.checkoutNew()
					.subscriptionPlanId(productSubscription.getPlan_id()).subscriptionAutoCollection(AutoCollection.ON)
					.subscriptionPlanQuantity(1).subscriptionCoupon(cbCoupon).addonId(0, activationAddonId.get("US"))
					.addonQuantity(0, 1).addonBillingCycles(0, 1).customerId(chargebeeId);
			
			if(monitorType == 1) {
				HostedPage hostedPage = checkoutNewRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString()).embed(embedupdate)
						.request().hostedPage();
				
				cbService.saveTemp_sub_purchase(userId, gatewayId, monitorType, hostedPage.id());
			}

			return checkoutNewRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString()).embed(embedupdate)
					.request().hostedPage().url();
		} catch (Exception e) {
			log.error("Error in generatePaymentURLForProductsub :: Error : " + e.getLocalizedMessage());
			return "NA";
		}

	}
	@Override
	public String createCBSubsForRechargePlanv3(String subId, String cbID, String planId, String reSubId,
			String nextrenewal_at, String price) {
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		String status = "Success";

		try {

			String oneTimeAddonId = activationAddonId.get("US");
			int addonStatus = 0;
			String event_status;

			if (!subId.equalsIgnoreCase("NA")) {

				Result result = Subscription.retrieve(subId).request();
				Subscription subscription = result.subscription();
				String subStatus = subscription.status().name();

				if (freeplan.contains(subscription.planId())) {
					addonStatus = 0; // activation
				} else if (!freeplan.contains(subscription.planId()) && !(subStatus.equalsIgnoreCase("CANCELLED"))) {
					addonStatus = 1; // upgrade
				} else if (!freeplan.contains(subscription.planId()) && (subStatus.equalsIgnoreCase("CANCELLED"))) {
					addonStatus = 2; // re-activation
				}

				switch (addonStatus) {
				case 0:
					oneTimeAddonId = activationAddonId.get("US");
					break;

				case 1:
					oneTimeAddonId = updateAddonId.get("US");
					break;

				case 2:
					oneTimeAddonId = reactivateAddonId.get("US");
					break;
				}
				Result res = Subscription.update(subId).replaceAddonList(true).replaceCouponList(true).planId(planId)
						.addonId(0, oneTimeAddonId).addonQuantity(0, 1).addonBillingCycles(0, 1).couponIds(cbCoupon)
						.forceTermReset(true).request();
				event_status = "Sub_updated";
				status = "Success";
			} else {

				subId = "RE-" + String.valueOf(Calendar.getInstance().getTimeInMillis());

				// Result res =
				// Customer.update(cbID).autoCollection(AutoCollection.OFF).request();
				float priceCent = (Float.parseFloat(price)) * 100;
				log.info("priceCent:" + (int) priceCent);
				CreateForCustomerRequest sReq = Subscription.createForCustomer(cbID).id(subId).planId(planId)
						.planQuantity(1).addonId(0, oneTimeAddonId).addonBillingCycles(0, 1)
						.planUnitPrice((int) priceCent);

				if (!reCoupon.isEmpty() && !reCoupon.equalsIgnoreCase("NA"))
					sReq = sReq.couponIds(reCoupon);

				Result res = sReq.request();

				Subscription subNew = res.subscription();

				Timestamp termEndsAt = Timestamp.valueOf(nextrenewal_at);

				res = Subscription.changeTermEnd(subNew.id()).termEndsAt(termEndsAt).request();
				event_status = "Sub_Created";
				status = "Success";
			}
			
			ReCBWebhookStatus reCbObj = new ReCBWebhookStatus(cbID, subId, reSubId, event_status);
			boolean stat = reService.saveRechargeCBSubStatus(reCbObj);
			log.info("saveRechargeCBSubStatus:" + stat);
			return status;
		} catch (Exception e) {
			status = "failed";
			ReCBWebhookStatus reCbObj = new ReCBWebhookStatus(cbID, subId, reSubId, status);
			boolean stat = reService.saveRechargeCBSubStatus(reCbObj);
			log.info("saveRechargeCBSubStatus:" + stat);

			log.error("saveRechargeCBSubStatus : Exce:" + e.getLocalizedMessage());
			// e.printStackTrace();
			return status;
		}

	}
	
	@Override
	public String getProductSubscriptionByChargebee(String chargebee_id) {
		return cbDao.getProductSubscriptionByChargebee(chargebee_id);
	}

	//used in txn service for feature mapping
	public JResponse getProductSubscriptionFromDB(UserV4 user, String os, String app_ver, String timezone,
			Long gateway_id, Long monitor_id) {

		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		try {
			String cbId = "NA";
			if (user.getChargebeeid().equalsIgnoreCase("NA")) {
				ListResult rs = null;
				int i = 1;
				Loop: while (rs == null || i <= 3) {
					i = i + 1;
					Thread.sleep(2000);
					rs = Customer.list().email().is(user.getEmail()).sortByCreatedAt(SortOrder.ASC).request();

					if (!rs.isEmpty()) {
						for (ListResult.Entry entry : rs) {
							Customer customer = entry.customer();
							user.setChargebeeid(customer.id());
						}
						break Loop;
					}
				}
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					log.info("create user in chargebee : chargebeeid : "+user.getChargebeeid());
					cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
							user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}
			}
			AllProductSubscription subscrip1 = null;
			boolean isPaused = false;
			boolean isFreetrial = false;
			boolean isFreePlan = false;
			String cbSubId = "NA";
			boolean show_cancel_sub = false;
			String status = "INACTIVE";
			String billingPeriod = "NA";
			String planname = "NA";
			int iris_splan = 0;
			int iris_speriod = 0;
			boolean show_alertlimit = false;
			boolean immediate_cancel = false;
			boolean payment_due = false;
			Card card = null;
			PaymentMethod ps = null;
			String cur_feature_ui_new = "";
			int substatus_code = 1;
			
			AllProductSubscription sub = cbService.getProductSubscriptionByGatewayId(gateway_id, user.getChargebeeid(),monitor_id);
			
			JGatewayFeature gatewayFeature = crService.getGatewayFeatureById(gateway_id);
			
			SubscriptionPlan splan = crService.getSubsPlanById(gatewayFeature.getPlan_id());

			if (!user.getChargebeeid().equalsIgnoreCase("NA")) {
				if (sub != null) {
					String subs_planid = sub.getPlanId();
					
					cbSubId = sub.getSubscriptionId();
					Result result = null;
					try {
						result = Subscription.retrieve(cbSubId).request();
						card = result.card();
						ps = result.customer().paymentMethod();
					}catch(Exception e) {
						response.put("Error", e.getLocalizedMessage());
						log.error("getSubscription:" + e.getMessage());
					}
		            
					if (monitor_id > 1 && !freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
							&& sub.getSubscriptionStatus().equalsIgnoreCase("cancelled")) {
						isFreetrial = false;
						isFreePlan = true;
						isPaused = false;
						status = "ACTIVE";
						billingPeriod = "Free";
						planname = splan.getPlan_name();
						iris_splan = gatewayFeature.getPlan_id();
						iris_speriod = gatewayFeature.getPeriod_id();
					}else if (!freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
							&& !sub.getSubscriptionStatus().equalsIgnoreCase("PAUSED")) {
						subscrip1 = sub;
						isFreetrial = false;
						isFreePlan = false;
						isPaused = false;
						iris_speriod = gatewayFeature.getPeriod_id();
					} else if (!freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
							&& sub.getSubscriptionStatus().equalsIgnoreCase("PAUSED")) {
						subscrip1 = sub;
						isFreetrial = false;
						isFreePlan = false;
						status = "PAUSED";
						isPaused = true;
						JGatewayFeature gatewayFeatureNew = crService.getGatewayFeatureByIdWithoutactive(gateway_id);
						iris_splan = gatewayFeatureNew!= null && gatewayFeatureNew.getPlan_id() > 0 ? gatewayFeatureNew.getPlan_id() : 0;
						iris_speriod = gatewayFeatureNew!= null && gatewayFeatureNew.getPlan_id() > 0 ? gatewayFeatureNew.getPeriod_id() : 0;
						splan = crService.getSubsPlanById(gatewayFeatureNew.getPlan_id());
					}
					if (subscrip1 == null) {
						if (freeplan.contains(sub.getPlanId())) {
							subscrip1 = sub;
							isFreePlan = true;
						}
					} else if(sub.getSubscriptionStatus().equalsIgnoreCase("IN_TRIAL")) {
						isFreetrial = true;
						
					}
				} else {
					// Free plan check
					if (gatewayFeature.getPlan_id() > 0 && gatewayFeature.getSub_id().equalsIgnoreCase("NA")) {
						log.info("Free plan found");
						status = "ACTIVE";
						billingPeriod = "Free";
						planname = splan.getPlan_name();
						iris_splan = gatewayFeature.getPlan_id();
						iris_speriod = gatewayFeature.getPeriod_id();
						isFreePlan = true;
					}
					
					if(monitor_id == 1) {
						String allSubscription = cbService.getSubscriptionByChargebeeIdNotinProductsub(user.getChargebeeid());
						cbSubId = allSubscription;
					}
				}
			}

			String periodUnit = "NA";
			String planid = "NA";

			float price = (float) 0.0;
			int daysBetween = -1;
			int days_remaining = -1;
			String nextPaymentDate = "NA";
			String autoRenewalStatus = "NA";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date dateobj = new Date();
			
			int expiryMonth = 0;
			int expiryYear = 0;
			int renewMonth = 0;
			int renewYear = 0;

			JSubscriptionPlanReportV2 rpt = new JSubscriptionPlanReportV2();
			boolean show_manage_sub = false;
//			long subQuantity = 0;
			String cancel_popup = "If you cancel, your plan stays active until the next renewal";
			boolean warranty_claimed = false;
			if (subscrip1 != null && splan != null) {
				
				if(card != null) {
					expiryMonth = card.expiryMonth();
					expiryYear = card.expiryYear();
				}
				
				periodUnit = subscrip1.getPlanPeriod();
				planid = subscrip1.getPlanId();

				if (periodUnit.contains("HALF")) {
					billingPeriod = "Half Yearly";
				} else if (periodUnit.contains("MONTH")) {
					billingPeriod = "Monthly";
				} else if (periodUnit.equalsIgnoreCase("2-YEAR")) {
					billingPeriod = "2 Year";
				} else if (periodUnit.equalsIgnoreCase("5-YEAR")) {
					billingPeriod = "5 Year";
				} else if (periodUnit.contains("QUARTER")) {
					billingPeriod = "Quarter";
				} else if (periodUnit.contains("YEAR")) {
					billingPeriod = "Yearly";
				}

				price = (float) subscrip1.getPlanAmount() / 100;
				String strprice = "$" + String.valueOf(price);
				status = subscrip1.getSubscriptionStatus().toUpperCase();

				if ((status.equalsIgnoreCase("ACTIVE")) || (status.equalsIgnoreCase("IN_TRIAL"))) {
					boolean ispaymentDue = cbService.checkDueInvoice(cbSubId);
					payment_due = ispaymentDue;
					if (ispaymentDue) {
						status = "PAYMENT DUE";
						autoRenewalStatus = "NA";
					} else {
						sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
						try {
							nextPaymentDate = subscrip1.getNextBillingAt();
							if (nextPaymentDate.equalsIgnoreCase("1753-01-01 00:00:00"))
								nextPaymentDate = subscrip1.getTrialEnd();
						} catch (Exception ex) {
							nextPaymentDate = subscrip1.getTrialEnd();
						}

						Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
						Date todayDate = sdf.parse(sdf.format(dateobj));

						long difference = nextPaymentDate1.getTime() - todayDate.getTime();
						daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
						
						days_remaining = daysBetween;

						sdf = new SimpleDateFormat("dd-MMM-yyyy");
						sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
						nextPaymentDate = sdf.format(nextPaymentDate1);
						autoRenewalStatus = "Enabled";
						if (freeplan.contains(subscrip1.getPlanId())) {
							nextPaymentDate = "NA";
							days_remaining = -1;
							autoRenewalStatus = "NA";
						}
						
						Calendar calendar = Calendar.getInstance();
						calendar.setTime(nextPaymentDate1);
						renewYear = calendar.get(Calendar.YEAR);
						//Add one to month {0 - 11}
						renewMonth = calendar.get(Calendar.MONTH) + 1;
						substatus_code = 1;
					}
				} else if (status.equalsIgnoreCase("NON_RENEWING")) {
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					status = "ACTIVE";
					autoRenewalStatus = "Disabled";
					nextPaymentDate = subscrip1.getSubscriptionCancelledAt();

					Date cancelledAt = sdf.parse(nextPaymentDate);
					Date todayDate = sdf.parse(sdf.format(dateobj));

					long difference = cancelledAt.getTime() - todayDate.getTime();
					daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
					days_remaining = daysBetween;

					sdf = new SimpleDateFormat("dd-MMM-yyyy"); // yyyy-MM-dd
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					nextPaymentDate = sdf.format(cancelledAt);
					substatus_code = 1;
				} else if (status.equalsIgnoreCase("PAUSED")) {
					status = "PAUSED";
					days_remaining = -1;
					autoRenewalStatus = "Disabled";
					substatus_code = 2;
					
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					try {
						nextPaymentDate = subscrip1.getNextBillingAt();
						if (nextPaymentDate.equalsIgnoreCase("1753-01-01 00:00:00"))
							nextPaymentDate = subscrip1.getTrialEnd();
					} catch (Exception ex) {
						nextPaymentDate = subscrip1.getTrialEnd();
					}

					Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
					Date todayDate = sdf.parse(sdf.format(dateobj));

					long difference = nextPaymentDate1.getTime() - todayDate.getTime();
					daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
					
					days_remaining = daysBetween;

					sdf = new SimpleDateFormat("dd-MMM-yyyy");
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					nextPaymentDate = sdf.format(nextPaymentDate1);
					autoRenewalStatus = "Enabled";
					if (freeplan.contains(subscrip1.getPlanId())) {
						nextPaymentDate = "NA";
						days_remaining = -1;
						autoRenewalStatus = "NA";
					}
					
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(nextPaymentDate1);
					renewYear = calendar.get(Calendar.YEAR);
					//Add one to month {0 - 11}
					renewMonth = calendar.get(Calendar.MONTH) + 1;
					substatus_code = 1;
				
				}

				if (daysBetween < 0)
					days_remaining = -1;
				
				if (splan != null && splan.getId() > 0) {

					cur_feature_ui_new = splan.getCur_feature_ui_new();

					if (cur_feature_ui_new == null || cur_feature_ui_new.equalsIgnoreCase("null"))
						cur_feature_ui_new = "";
				}
				
				if (monitor_id != null && monitor_id == 1) {
					sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					Date curStart = sdf.parse(sdf.format(sdf.parse(subscrip1.getSubscriptionStartedAt()).getTime()));
					Date today = sdf.parse(new Helper().getCurrentTimeinUTC());

					long difference = today.getTime() - curStart.getTime();
					double daysBetween_active = Math.ceil((difference / (1000 * 60 * 60 * 24)));
					double hoursBetween_active = (difference / (1000 * 60 * 60));

					if (hoursBetween_active <= 24 && billingPeriod.equalsIgnoreCase("monthly"))
						immediate_cancel = true;
					else if (daysBetween_active <= 7 && !billingPeriod.equalsIgnoreCase("monthly"))
						immediate_cancel = true;
				}
				if(!sub.getSubscriptionStatus().equalsIgnoreCase("PAUSED")) {
					iris_splan = gatewayFeature.getPlan_id();
					iris_speriod = gatewayFeature.getPeriod_id();
				}
				
				planname = splan.getPlan_name();
				if (monitor_id == 0) {
					monitor_id = splan.getMonitor_type();
				}
				
				if (!app_ver.equalsIgnoreCase("na") && !os.equalsIgnoreCase("na")) {
					VersionMapping verObj = crService.getVersionMapping(app_ver, os);
					if (verObj != null) {
						if (verObj.isShow_cancel_sub() && subscrip1.getSubscriptionStatus().equalsIgnoreCase("active")
								&& !isFreePlan)
							show_cancel_sub = true;
							show_alertlimit = true;
					}
				}

				rpt = new JSubscriptionPlanReportV2(planid, planname,  billingPeriod, nextPaymentDate,
						days_remaining, status, autoRenewalStatus, cbSubId,  
						user.getChargebeeid(), isFreetrial, show_cancel_sub, isFreePlan, payment_due,strprice);
				
				
			} else {
				rpt = new JSubscriptionPlanReportV2(planid, planname, billingPeriod, nextPaymentDate,
						days_remaining, status, autoRenewalStatus, cbSubId,  user.getChargebeeid(), 
						isFreetrial, show_cancel_sub, isFreePlan,payment_due,"NA");
			}
			
			String meariSubKey = "NA";

			if(monitor_id == 4 || monitor_id == 5) {
				meariSubKey = crService.getMeariKey(0, gateway_id, iris_speriod);
			}
			boolean validForBundleSub = true;
			
			boolean is_upgrade = crService.isUpgradeAvailable(iris_splan, iris_speriod);
			if (isPaused) {
				is_upgrade = false;
			} else if (status.equalsIgnoreCase("cancelled")) {
				is_upgrade = true;
				validForBundleSub = false;
			}

			rpt.setFreetrial(isFreetrial);
			
			boolean show_warranty_popup = false;
			boolean show_check_out_page = this.show_check_out_page;
			String check_out_page_url = "NA";
			String gateway_name = "Pet";
			String meid = "NA";
			JGateway gateway = gatewayService.getJGatewayByGateway(gateway_id);
			if (gateway != null) {
				if (!gateway.isShowOrderId() && !gateway.isPurchased_from_others()) {
					warranty_claimed = true;
					show_warranty_popup = true;
				}
				meid = gateway.getMeid();
			}
			response.put("meid",meid);
			
			// get device list by gatewayid
			response = iReportServiceV4.getLastGatewayReportV8(gateway_id, user.getId(), monitor_id, user.getCountry(), 
					os, "V2", response);
			
			rpt.setShow_cancel_sub(show_cancel_sub);

			if(isFreeDeviceShippingAvailable) {
				show_cancel_sub = cbDao.getCancelSub(gateway_id, user.getChargebeeid()) > 0 ? false :show_cancel_sub ;
				rpt.setShow_cancel_sub(show_cancel_sub);
			}

			response.put("cancel_button_text", "Cancel Plan");
//			if (monitor_id == 1) {
//				response.put("cancel_button_text", "Cancel Protection");
//			}
			response.put("planid", iris_splan);
			response.put("periodid", iris_speriod);
			response.put("subscriptionplan", rpt);
			response.put("monitor_id", monitor_id);
			response.put("warranty_claimed", warranty_claimed);
			response.put("meariSubKey", meariSubKey);
//			response.put("sub_quantity", subQuantity);
//			response.put("mapped_quantity", mappedQuantity);
			response.put("show_alertlimit", show_alertlimit);
			response.put("show_upgrade", is_upgrade);
			response.put("show_manage_sub", show_manage_sub);
			response.put("cancel_popup_content", cancel_popup);
			
			response.put("Status", 1);
			response.put("Msg", "Success");

			async.saveCurrentPlanDetails(user.getId(), user.getChargebeeid(), iris_splan, iris_speriod, rpt.getCbPlanId(),
					response);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Please try again.");
			log.error("getSubscription:" + e.getMessage());
		}

		return response;
	}
	
	@Override
	public AllProductSubscription getProductSubscriptionByGateway(long id, String chargebeeid) {
		return cbDao.getProductSubscriptionByGateway(id,chargebeeid);
	}

	public JProductSubResV2 getProductSubscriptionplanV2FromDB(UserV4 user, String os, String app_ver, String timezone,
			Long gateway_id, Long monitor_type) {

		JProductSubResV2 prodSub = new JProductSubResV2();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		try {
			String cbId = "NA";
			if (user.getChargebeeid().equalsIgnoreCase("NA")) {
				ListResult rs = null;
				int i = 1;
				Loop: while (rs == null || i <= 3) {
					i = i + 1;
					Thread.sleep(2000);
					rs = Customer.list().email().is(user.getEmail()).sortByCreatedAt(SortOrder.ASC).request();

					if (!rs.isEmpty()) {
						for (ListResult.Entry entry : rs) {
							Customer customer = entry.customer();
							user.setChargebeeid(customer.id());
						}
						break Loop;
					}
				}
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					log.info("create user in chargebee : chargebeeid : "+user.getChargebeeid());
					cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
							user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}
			}
			AllProductSubscription subscrip1 = null;
			boolean isPaused = false;
			boolean isFreetrial = false;
			boolean isFreePlan = false;
			String cbSubId = "NA";
			boolean show_cancel_sub = false;
			String status = "INACTIVE";
			String billingPeriod = "NA";
			String planname = "NA";
			int iris_splan = 0;
			int iris_speriod = 0;
			boolean show_alertlimit = false;
			boolean immediate_cancel = false;
			boolean payment_due = false;
			boolean hasReactivationCharge = false;
			boolean hasFullAmountPaid = false;
			Card card = null;
			PaymentMethod ps = null;
			String cur_feature_ui_new = "";
			int substatus_code = 1;
			long planid_gf = 0;
			long periodid_gf=0;
			String subid_gf="";
			AllProductSubscription sub = cbService.getProductSubscriptionByGatewayId(gateway_id, user.getChargebeeid(),monitor_type);
			
			if(monitor_type !=11) {
				JGatewayFeature gatewayFeature = crService.getGatewayFeatureById(gateway_id);
				planid_gf = gatewayFeature.getPlan_id();
				periodid_gf = gatewayFeature.getPeriod_id();
				subid_gf = gatewayFeature.getSub_id();
			}else {
				if (sub != null) {
					ArrayList<Integer> ids = crService.getPlanAndPeriod(sub.getPlanId());
					planid_gf = ids.get(0);
					periodid_gf = ids.get(1);
					subid_gf = sub.getSubscriptionId();
				}
			}
			
			SubscriptionPlan splan = crService.getSubsPlanById(planid_gf);

			if (!user.getChargebeeid().equalsIgnoreCase("NA")) {
				if (sub != null && splan != null) {
					if (sub.getPlanId().contains("flexi")){
						prodSub = crService.getFlexiPlanHistory(gateway_id, sub.getSubscriptionId());
						if (prodSub == null){
							prodSub = new JProductSubResV2();
							prodSub.setIs_paused(true);
							prodSub.setMonth_left("3/3");
							prodSub.setRemaining_month(3);
						} else {
							String currTime = _helper.getCurrentTimeinUTC();
							int days_left = (int) _helper.getDaysBetweenDate(currTime, prodSub.getFlexi_next_billing_date());
							int hours_left = (int) _helper.getDaysBetweenDate(currTime, prodSub.getFlexi_next_billing_date());
							prodSub.setIs_started(prodSub.getRemaining_month() > 0);
							prodSub.setRemaining_month(3 - prodSub.getRemaining_month());
							prodSub.setMonth_left(prodSub.getRemaining_month() + "/3");
							if(days_left <= 0 && prodSub.getRemaining_month() == 0) {
								prodSub.setRemaining_month(-1);
								prodSub.setIs_paused(true);
								prodSub.setIs_started(false);
							}
							prodSub.setDays_left(days_left < 0 ? 0 : days_left);

							if (prodSub.getRemaining_month() > 0 && !prodSub.getIs_paused()) {
								prodSub.setShow_flexi_pause(true);
							}

							if (days_left > 0 && prodSub.getIs_paused()) {
								prodSub.setIs_paused(false);
								prodSub.setShow_flexi_pause(false);
								prodSub.setPaused_date_content(_helper.getCurrentTimeInFormat(prodSub.getFlexi_next_billing_date(), "yyyy-MM-dd HH:mm:ss", "dd MMM yyyy"));
							}

							if (prodSub.getPaused_count() == 2) {
								prodSub.setShow_flexi_pause(false);
							}

							prodSub.setFlexi_next_billing_date(_helper.getCurrentTimeInFormat(prodSub.getFlexi_next_billing_date(), "yyyy-MM-dd HH:mm:ss", "dd MMM yyyy"));
							prodSub.setFlexi_plan_start_date(_helper.getCurrentTimeInFormat(prodSub.getFlexi_plan_start_date(), "yyyy-MM-dd HH:mm:ss", "dd MMM yyyy"));
						}
						prodSub.setIs_flexi(true);
					}
					String subs_planid = sub.getPlanId();
					cbSubId = sub.getSubscriptionId();

					Result result = null;
					try {
						result = Subscription.retrieve(cbSubId).request();
						card = result.card();
						ps = result.customer().paymentMethod();
						if(periodid_gf >= 4 && monitor_type == 1) {
							try {
								ListResult invoiceList = Invoice.list().customerId().is(user.getChargebeeid()).subscriptionId()
										.is(cbSubId).status().is(Invoice.Status.PAID).sortByDate(SortOrder.DESC).limit(1)
										.request();
								if (!invoiceList.isEmpty()) {
									Invoice invoice = invoiceList.get(0).invoice();

									hasReactivationCharge = invoice.lineItems().stream()
											.anyMatch(item -> "reactivation-charges-onetime".equals(item.entityId()) || "reactivation-charges-cad".equals(item.entityId()));

									int planAmount = invoice.lineItems().stream()
											.filter(item -> item.entityType() != null && "plan".equalsIgnoreCase(String.valueOf(item.entityType())))
											.mapToInt(item -> item.amount())
											.findFirst()
											.orElse(0);

									hasFullAmountPaid = invoice.amountPaid() >= planAmount;
								}
							}catch(Exception e){
								log.error("error getInvoice detail in reacivation:" + e.getMessage());
							}
						}
					}catch(Exception e) {
						log.error("getSubscription:" + e.getMessage());
					}

					try {
						String planType = crService.getplantypebychargebeeid(subs_planid);

						if (planType != null && planType.equalsIgnoreCase("combo-plan") && sub.getSubscriptionStatus().equalsIgnoreCase("cancelled")) {
							cbSubId = "NA";
						}
					} catch (Exception e) {
						log.error("getplan type:" + e.getMessage());
					}
		            
					if (monitor_type > 1 && !freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
							&& sub.getSubscriptionStatus().equalsIgnoreCase("cancelled")) {
						isFreetrial = false;
						isFreePlan = true;
						isPaused = false;
						status = "ACTIVE";
						billingPeriod = "Free";
						planname = splan.getPlan_name();
						iris_splan = (int)planid_gf;
						iris_speriod =(int)periodid_gf;
					}else if (!freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
							&& !sub.getSubscriptionStatus().equalsIgnoreCase("PAUSED") && !sub.getSubscriptionStatus().equalsIgnoreCase("CANCELLED")) {
						subscrip1 = sub;
						isFreetrial = false;
						isFreePlan = false;
						isPaused = false;
						iris_speriod = (int)periodid_gf;
					} else if (!freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
							&& sub.getSubscriptionStatus().equalsIgnoreCase("PAUSED")) {
						subscrip1 = sub;
						isFreetrial = false;
						isFreePlan = false;
						status = "PAUSED";
						isPaused = true;
						JGatewayFeature gatewayFeatureNew = crService.getGatewayFeatureByIdWithoutactive(gateway_id);
						iris_splan = gatewayFeatureNew!= null && gatewayFeatureNew.getPlan_id() > 0 ? gatewayFeatureNew.getPlan_id() : 0;
						iris_speriod = gatewayFeatureNew!= null && gatewayFeatureNew.getPlan_id() > 0 ? gatewayFeatureNew.getPeriod_id() : 0;
						splan = crService.getSubsPlanById(gatewayFeatureNew.getPlan_id());
					}
					if (subscrip1 == null) {
						if (freeplan.contains(sub.getPlanId())) {
							subscrip1 = sub;
							isFreePlan = true;
						}
					} else if(sub.getSubscriptionStatus().equalsIgnoreCase("IN_TRIAL")) {
						isFreetrial = true;
					}

					if(sub.getPlanId().contains("combo")) {
						prodSub.setIs_combo(true);
					}
				} else {
					// Free plan check
					if (planid_gf > 0 && subid_gf.equalsIgnoreCase("NA")) {
						log.info("Free plan found");
						status = "ACTIVE";
						billingPeriod = "Free";
						planname = splan.getPlan_name();
						iris_splan = (int)planid_gf;
						iris_speriod = (int)periodid_gf;
						isFreePlan = true;
					}
					
					if(monitor_type == 1) {
						String allSubscription = cbService.getSubscriptionByChargebeeIdNotinProductsub(user.getChargebeeid());
						cbSubId = allSubscription;
					}
				}

				if(sub != null) {
					cbSubId = sub.getSubscriptionId();
				}
			}

			String periodUnit = "NA";
			String planid = "NA";

			float price = (float) 0.0;
			int daysBetween = -1;
			int days_remaining = -1;
			String nextPaymentDate = "NA";
			String autoRenewalStatus = "NA";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date dateobj = new Date();
			
			int expiryMonth = 0;
			int expiryYear = 0;
			int renewMonth = 0;
			int renewYear = 0;

			JSubscriptionPlanReportV2 rpt = new JSubscriptionPlanReportV2();
			boolean show_manage_sub = false;
//			long subQuantity = 0;
			String cancel_popup = "If you cancel, your plan stays active until the next renewal";
			boolean warranty_claimed = false;
			if (subscrip1 != null && splan != null) {
				
				if(card != null) {
					expiryMonth = card.expiryMonth();
					expiryYear = card.expiryYear();
				}
				
				periodUnit = subscrip1.getPlanPeriod();
				planid = subscrip1.getPlanId();

				if (periodUnit.contains("HALF")) {
					billingPeriod = "Half Yearly";
				} else if (periodUnit.contains("MONTH")) {
					billingPeriod = "Monthly";
				} else if (periodUnit.equalsIgnoreCase("2-YEAR")) {
					billingPeriod = "2 Year";
				} else if (periodUnit.equalsIgnoreCase("5-YEAR")) {
					billingPeriod = "5 Year";
				} else if (periodUnit.contains("QUARTER")) {
					billingPeriod = "Quarter";
				} else if (periodUnit.contains("YEAR")) {
					billingPeriod = "Yearly";
				}

				price = (float) subscrip1.getPlanAmount() / 100;
				String strprice = "$" + String.valueOf(price);
				status = subscrip1.getSubscriptionStatus().toUpperCase();

				if ((status.equalsIgnoreCase("ACTIVE")) || (status.equalsIgnoreCase("IN_TRIAL"))) {
					boolean ispaymentDue = cbService.checkDueInvoice(cbSubId);
					payment_due = ispaymentDue;
					if (ispaymentDue) {
						status = "PAYMENT DUE";
						autoRenewalStatus = "NA";
					} else {
						sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
						try {
							nextPaymentDate = subscrip1.getNextBillingAt();
							if (nextPaymentDate.equalsIgnoreCase("1753-01-01 00:00:00"))
								nextPaymentDate = subscrip1.getTrialEnd();
						} catch (Exception ex) {
							nextPaymentDate = subscrip1.getTrialEnd();
						}

						Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
						Date todayDate = sdf.parse(sdf.format(dateobj));

						long difference = nextPaymentDate1.getTime() - todayDate.getTime();
						daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
						
						days_remaining = daysBetween;

						sdf = new SimpleDateFormat("dd-MMM-yyyy");
						sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
						nextPaymentDate = sdf.format(nextPaymentDate1);
						autoRenewalStatus = "Enabled";
						if (freeplan.contains(subscrip1.getPlanId())) {
							nextPaymentDate = "NA";
							days_remaining = -1;
							autoRenewalStatus = "NA";
						}
						
						Calendar calendar = Calendar.getInstance();
						calendar.setTime(nextPaymentDate1);
						renewYear = calendar.get(Calendar.YEAR);
						//Add one to month {0 - 11}
						renewMonth = calendar.get(Calendar.MONTH) + 1;
						substatus_code = 1;
					}
				} else if (status.equalsIgnoreCase("NON_RENEWING")) {
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					status = "ACTIVE";
					autoRenewalStatus = "Disabled";
					nextPaymentDate = subscrip1.getSubscriptionCancelledAt();

					Date cancelledAt = sdf.parse(nextPaymentDate);
					Date todayDate = sdf.parse(sdf.format(dateobj));

					long difference = cancelledAt.getTime() - todayDate.getTime();
					daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
					days_remaining = daysBetween;

					sdf = new SimpleDateFormat("dd-MMM-yyyy"); // yyyy-MM-dd
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					nextPaymentDate = sdf.format(cancelledAt);
					substatus_code = 1;
				} else if (status.equalsIgnoreCase("PAUSED")) {
					status = "PAUSED";
					days_remaining = -1;
					autoRenewalStatus = "Disabled";
					substatus_code = 2;
					
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					try {
						nextPaymentDate = subscrip1.getNextBillingAt();
						if (nextPaymentDate.equalsIgnoreCase("1753-01-01 00:00:00"))
							nextPaymentDate = subscrip1.getTrialEnd();
					} catch (Exception ex) {
						nextPaymentDate = subscrip1.getTrialEnd();
					}

					Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
					Date todayDate = sdf.parse(sdf.format(dateobj));

					long difference = nextPaymentDate1.getTime() - todayDate.getTime();
					daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
					
					days_remaining = daysBetween;

					sdf = new SimpleDateFormat("dd-MMM-yyyy");
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					nextPaymentDate = sdf.format(nextPaymentDate1);
					autoRenewalStatus = "Enabled";
					if (freeplan.contains(subscrip1.getPlanId())) {
						nextPaymentDate = "NA";
						days_remaining = -1;
						autoRenewalStatus = "NA";
					}
					
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(nextPaymentDate1);
					renewYear = calendar.get(Calendar.YEAR);
					//Add one to month {0 - 11}
					renewMonth = calendar.get(Calendar.MONTH) + 1;
					substatus_code = 1;
				
				}

				if (daysBetween < 0)
					days_remaining = -1;
				
				if (splan != null && splan.getId() > 0) {

					cur_feature_ui_new = splan.getCur_feature_ui_new();

					if (cur_feature_ui_new == null || cur_feature_ui_new.equalsIgnoreCase("null"))
						cur_feature_ui_new = "";
				}
				
				if (monitor_type != null && monitor_type == 1) {
					sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					Date curStart = sdf.parse(sdf.format(sdf.parse(subscrip1.getSubscriptionStartedAt()).getTime()));
					Date today = sdf.parse(new Helper().getCurrentTimeinUTC());

					long difference = today.getTime() - curStart.getTime();
					double daysBetween_active = Math.ceil((difference / (1000 * 60 * 60 * 24)));
					double hoursBetween_active = (difference / (1000 * 60 * 60));

					if (hoursBetween_active <= 24 && billingPeriod.equalsIgnoreCase("monthly"))
						immediate_cancel = true;
					else if (daysBetween_active <= 7 && !billingPeriod.equalsIgnoreCase("monthly"))
						immediate_cancel = true;
				}
				if(!sub.getSubscriptionStatus().equalsIgnoreCase("PAUSED") && !sub.getSubscriptionStatus().equalsIgnoreCase("CANCELLED")) {
					iris_splan = (int)planid_gf;
					iris_speriod = (int)periodid_gf;
				}
				
				planname = splan.getPlan_name();
				if (monitor_type == 0) {
					monitor_type = splan.getMonitor_type();
				}
				
				if (!app_ver.equalsIgnoreCase("na") && !os.equalsIgnoreCase("na")) {
					VersionMapping verObj = crService.getVersionMapping(app_ver, os);
					if (verObj != null) {
						if (verObj.isShow_cancel_sub() && subscrip1.getSubscriptionStatus().equalsIgnoreCase("active")
								&& !isFreePlan)
							show_cancel_sub = true;
							show_alertlimit = true;
					}
				}
				
				if( show_cancel_sub && monitor_type == 1) {
					UserCancelFeedBack userCancelFeedback = cancelService.getUserCancelFeedBackByShowCancel( user.getId() , false );
					if( userCancelFeedback != null ) {
						show_cancel_sub = false;
					}	
				}

				rpt = new JSubscriptionPlanReportV2(planid, planname,  billingPeriod, nextPaymentDate,
						days_remaining, status, autoRenewalStatus, cbSubId,  
						user.getChargebeeid(), isFreetrial, show_cancel_sub, isFreePlan, payment_due,strprice);

			} else {
				rpt = new JSubscriptionPlanReportV2(planid, planname, billingPeriod, nextPaymentDate,
						days_remaining, status, autoRenewalStatus, cbSubId,  user.getChargebeeid(), 
						isFreetrial, show_cancel_sub, isFreePlan,payment_due,"NA");
			}
			
			String meariSubKey = "NA";

			if(monitor_type == 4 || monitor_type == 5 || monitor_type == 8 || monitor_type == 12) {
				meariSubKey = crService.getMeariKey(0, gateway_id, iris_speriod);
			}
			boolean validForBundleSub = true;
			
			boolean is_upgrade = crService.isUpgradeAvailable(iris_splan, iris_speriod);
			if (isPaused) {
				is_upgrade = false;
			} else if (status.equalsIgnoreCase("cancelled")) {
				is_upgrade = true;
				validForBundleSub = false;
			}

			if(prodSub.getIs_flexi() && !status.equalsIgnoreCase("cancelled")){
				if (prodSub.getRemaining_month() <= 0 && prodSub.getDays_left() <= 0) {

				} else {
					is_upgrade = false;
				}

				if (prodSub.getRemaining_month() > 0){
					show_cancel_sub = false;
					prodSub.setFlexi_plan_list_hide(true);
				}
			}

			rpt.setFreetrial(isFreetrial);
			
			boolean show_warranty_popup = false;
			boolean show_check_out_page = this.show_check_out_page;
			String check_out_page_url = "NA";
			String gateway_name = "Pet";
			
			JGateway gateway = gatewayService.getJGatewayByGateway(gateway_id);
			if (gateway != null) {
					if (!gateway.isShowOrderId() && !gateway.isPurchased_from_others()) {
						warranty_claimed = true;
						show_warranty_popup = true;
					}
			}
			JResponse response1 = new JResponse();
			
			Map<String,Object> deviceList = new HashMap<>();
			try {
			// get device list by gatewayid
			deviceList = iReportServiceV4.getLastGatewayReportV6(gateway_id, user.getId(), monitor_type, user.getCountry(), 
					os, "V2", response1);
			prodSub.setDeviceList(deviceList);
			}catch(Exception e) {
				log.info("deviceList : "+ e.getMessage());
			}
			
			rpt.setShow_cancel_sub(show_cancel_sub);
			prodSub.setCancel_button_text("Cancel Plan");
			
			
			
			JPauseHistory pauseHis = null;
			
			if(sub != null && sub.getSubscriptionId() != null)
				 pauseHis = crService.getPauseHistoryRecord( sub.getSubscriptionId());
			
			if((pauseHis == null || pauseHis.getStatus() == 1) && iris_speriod == 1 && !isFreePlan && monitor_type == 1)
			{
				prodSub.setShowpausesub(true);
			}else {
				prodSub.setShowpausesub(false);
			}
				
			prodSub.setNextrenew_date_old(rpt.getNextrenew_date());
			
			if(pauseHis != null && pauseHis.getResumeDate() != null) {
				if(pauseHis.getStatus() == 1 && monitor_type == 1 ) {
					rpt.setShow_cancel_sub(false);
					rpt.setAutoRenewalStatus("Disable");
					rpt.setNextrenew_date(pauseHis.getResumeDate());
				}
					
				prodSub.setResumeDate(pauseHis.getResumeDate());
				prodSub.setPauseStatus(pauseHis.getStatus() == 1 ? true : false);
				prodSub.setPausetitle("Your pet's tail will wag for this!");
				prodSub.setPausecontent("Your subscription will renewal on ");
			} else {
				prodSub.setResumeDate("");
				prodSub.setPauseStatus(false);
				prodSub.setPausetitle("Your pet's tail will wag for this!");
				prodSub.setPausecontent("Your subscription will renewal on ");
			}

			if(isFreeDeviceShippingAvailable && monitor_type != 11) {
				show_cancel_sub = cbDao.getCancelSub(gateway_id, user.getChargebeeid()) > 0 ? false : show_cancel_sub;
				rpt.setShow_cancel_sub(show_cancel_sub);
			}

			if( gateway != null ) {
				gateway_name = gateway.getName();
				prodSub.setName(gateway.getName());
				prodSub.setGateway_id(gateway.getId());
			}
			try {
				if (monitor_type == 1 && !status.equalsIgnoreCase("INACTIVE") && !splan.getPlan_type().equalsIgnoreCase("Combo-Plan")) {
					boolean addrAvil = gatewayService.minicamaddresavilable(user.getId());

					Date startedDate = sdf.parse(sdf.format(sdf.parse(subscrip1.getSubscriptionStartedAt()).getTime()));
					Date now = sdf.parse(free_minicam_startdate);

					if (startedDate.after(now) && !addrAvil && hasReactivationCharge && hasFullAmountPaid) {
						prodSub.setShowAddressPopup(true);
					}
				}
			}catch(Exception e){
				log.info("deviceList : "+ e.getMessage());
			}
			prodSub.setShow_pauseoption(pause_enable);
			
			if(sub != null && sub.getSubscriptionId() != null && sub.getSubscriptionStatus().equalsIgnoreCase("non_renewing")) {
				prodSub.setShow_pauseoption(false);
			}

			if(monitor_type != null && (monitor_type == 1 || monitor_type == 5 || monitor_type == 6 || monitor_type == 8 || monitor_type == 12)) {
				String paymenttype = (ps!=null) ? ps.gateway().name() : "";

				checkDeviceConfigStatusByGatewayPlanv2(iris_splan, gateway,
					days_remaining, status,expiryMonth,expiryYear,renewMonth,renewYear,user.getChargebeeid(),paymenttype,prodSub);

				prodSub.setShow_check_out_popup(false);
				ProductSubscription productSubscription = cbService.getProductSubscription(user.getId());
				boolean isRecharge = false;
				if(productSubscription != null) {
						validForBundleSub = true;
						if (productSubscription.getPlan_id().contains("rech")) {
							isRecharge = true;
						}
				}

				int redirect = user.getInapp_purchase(); // default 1
				int inapp_redirect = 1;

				if (!isFreePlan == true && (inapp_redirect == 2 || user.getInapp_purchase() == 2)) {
					inapp_redirect = 1;
					redirect = inapp_redirect;
					user.setInapp_purchase(inapp_redirect);
					// call user update method
					userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());

				} else if ((!isFreePlan == false && inapp_redirect == 2 && user.getInapp_purchase() == 1)
						|| (!isFreePlan == false && inapp_redirect == 1 && user.getInapp_purchase() == 2)) {
					// update CB purchase status to 2[inapp] in user table
					user.setInapp_purchase(inapp_redirect);
					redirect = inapp_redirect;
					userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());
				}

				redirect = user.getInapp_purchase();
				
				log.info("valid_bundle_subs : "+ validForBundleSub +" :: show_check_out_page : "+ show_check_out_page);
				if (show_check_out_page && !show_warranty_popup && productSubscription != null
						&& !productSubscription.isIs_subscription_activated()) {

					if(isRecharge) {
						show_check_out_page = false;
					}
					
					Inventory inventory = niomDbservice.getInventoryByMeid(gateway.getMeid());
					
					if (validForBundleSub && !isRecharge &&  (inventory != null && inventory.getOrderid() != null && inventory.getOrderid()
							.equalsIgnoreCase(String.valueOf(productSubscription.getOrder_id())))) {
						check_out_page_url = checkOutURLForBundleSubs(productSubscription, user.getChargebeeid());
					}

					if( productSubscription.isActive_subscription() &&  (inventory != null && inventory.getOrderid() != null && inventory.getOrderid()
							.equalsIgnoreCase(String.valueOf(productSubscription.getOrder_id())))) {
						prodSub.setShow_bundle_contact_us(true);
						prodSub.setBundle_contact_us_content(bundle_contact_us_content);
					}
					
					if (check_out_page_url.equalsIgnoreCase("NA")) {
						show_check_out_page = false;
					} else {
						show_check_out_page = true;
						JPlan planDesc = cbService.getPlanDesc(productSubscription.getPlan_id());
						String subsPeriod = getSubscriptionPeriod(planDesc.getPeriodUnit());
						String check_out_page = check_out_page_content;
						check_out_page = check_out_page.replace("$$", subsPeriod);
						log.info("sub period : " + subsPeriod + " :: checkout content : " + check_out_page);
						prodSub.setShow_check_out_popup(show_check_out_page);
						prodSub.setCheck_out_popup_content(check_out_page);
						prodSub.setCheck_out_popup_image(check_out_page_image);
						prodSub.setCheck_out_popup_image_dark(check_out_page_image_dark);
					}

				}
				
				if (splan != null && !splan.isIs_freeplan()) {
					validForBundleSub = false;
				} 
				
				// cancel sub contents
				
				boolean cancelSub = false;
				String cancelSubUrl = cancel_sub_url.get("US");
				// Append email to URL
				if (!user.getEmail().equalsIgnoreCase("NA"))
					cancelSubUrl = cancelSubUrl + "?email=" + user.getEmail();
				else
					cancelSubUrl = cancelSubUrl + "?email=";

				// Append full name to URL
				if (!user.getFirstname().equalsIgnoreCase("NA") && !user.getLastname().equalsIgnoreCase("NA"))
					cancelSubUrl = cancelSubUrl + "&fullname="
							+ (user.getFirstname() + " " + user.getLastname()).replace(' ', '$');
				else if (!user.getFirstname().equalsIgnoreCase("NA") && user.getLastname().equalsIgnoreCase("NA"))
					cancelSubUrl = cancelSubUrl + "&fullname=" + (user.getFirstname()).replace(' ', '$');
				else if (user.getFirstname().equalsIgnoreCase("NA") && !user.getLastname().equalsIgnoreCase("NA"))
					cancelSubUrl = cancelSubUrl + "&fullname=" + (user.getLastname()).replace(' ', '$');
				else
					cancelSubUrl = cancelSubUrl + "&fullname=";

				// Append phone to URL
				if (!user.getMobileno().equalsIgnoreCase("NA"))
					cancelSubUrl = cancelSubUrl + "&phone=" + user.getMobileno();
				else
					cancelSubUrl = cancelSubUrl + "&phone=";
				
				String country = user.getCountry().toUpperCase();
				country = "US";
				
				cancelSubUrl = cancel_sub_url.get(country);
				
				if(cancelSubUrl == null) {
					cancelSubUrl = cancel_sub_url.get("US");
				}

				if (cancelSubUrl.equalsIgnoreCase("NA") && !cancel_inapp_redirect) {
					cancelSub = false;
				}

				boolean is_expired = true;
				boolean isAvail = false;
				
				LinkedList<String> reasonList = new LinkedList<String>();
				JCancelSubInfo cancelInfo = new JCancelSubInfo();

				if (cancel_inapp_redirect) {
					reasonList.add("Currently not using");
					reasonList.add("Not happy with the service");
					reasonList.add("Costs too much");
					reasonList.add("Others");

					String can_label1 = "Unsubscribing?";
					String can_label2 = "Don't miss out on \r\n unwavering pet protection and \r\n guaranteed peace of mind.";
					String btn1 = "Never Mind";
					String btn2 = "Stay with Waggle";
					String can_label_newflow = "Take advantage of additional benefits";

					List can_label2_newflow = crService.listPlanBenefits(iris_speriod);

					if (can_label2_newflow.isEmpty())
						can_label_newflow = "Sad to see you go!";

					cancelInfo = new JCancelSubInfo(can_label1, can_label2, btn1, btn2, reasonList);
					cancelInfo.setCan_label_newflow(can_label_newflow);
					cancelInfo.setCan_label2_newflow(can_label2_newflow);

					if (rpt.getAutoRenewalStatus().equalsIgnoreCase("Disabled"))
						cancelSub = false;
					
					boolean is_can_applicable = true;
					String can_msg = "";
					
					if( show_cancel_basedon_user_cancel_feedback ) {
						
						boolean isvalid = crService.checkRenewalDateInUserRetained( user.getId() );
						//if valid = true means already coupon applied, cancel will not be shown
						if( isvalid ) 
							cancelSub = false;
						
						if( cancelSub ) {
							UserCancelFeedBack userCancelFeedback = cancelService.getUserCancelFeedBackByShowCancel( user.getId() , false );
							if( userCancelFeedback != null ) {
								cancelSub = false;
							}	
						}	
						
					}
					
					prodSub.setShow_cancel_reward(false);
					AdditionBenefitsCancelReward additionBenefitsCancelReward = cancelService.getAdditionBenefitsCancelReward( user.getId() );
					if( additionBenefitsCancelReward != null ) {
						prodSub.setCancel_reward_code(additionBenefitsCancelReward.getCode());
						
						Date endDate = sdf.parse( additionBenefitsCancelReward.getEnd_date() );
						Date curDate = new Date(System.currentTimeMillis());
						
						if( endDate.after(curDate) ) {
							prodSub.setCancel_reward_enddate(new SimpleDateFormat("dd-MMM-yyyy").format(endDate));
							prodSub.setShow_cancel_reward(true);
							prodSub.setCancel_reward_title(addition_benefits_cancel_reward_title);
							prodSub.setCancel_reward_url(cancel_reward_url);
						}
						
					}
					
					// Checking additional benefit validity
					if (is_expired) // this is already benefits created user
						is_expired = iAvatarService.checkBenefitsExpired(user.getId());

					if (!is_expired && isAvail) {
						is_can_applicable = false;
						can_msg = "To cancel the subscription and additional benefits, please reach out to our Customer Delight team.";
						cancelInfo.setIs_can_applicable(is_can_applicable);
						cancelInfo.setCan_msg(can_msg);
					}
				}
				prodSub.setCancelSubUrl(cancelSubUrl);
				prodSub.setCancel_inapp_redirect(cancel_inapp_redirect);
				prodSub.setCancelInfo(cancelInfo);
				prodSub.setStay_with_us_content(gateway_name);
				
				LinkedList<String> stayList = new LinkedList<String>();
				stayList.add("Instant temperature alerts");
				stayList.add("Power outage warnings");
				stayList.add("GPS tracking & Safe Zone alerts");
				stayList.add("Extended warranty for active plans");
				
				prodSub.setStay_with_us_list(stayList);
				
				prodSub.setBundle_check_out_url(check_out_page_url);
				prodSub.setShow_warranty_popup(show_warranty_popup);
				prodSub.setShow_warranty_popup_content(warranty_msg_v2);
				prodSub.setShow_warranty_popup_v2_image(warranty_msg_v2_image);
				prodSub.setShow_warranty_popup_v2_content(warranty_msg_v2_content);
				prodSub.setWarranty_popup_later_btn(show_later_btn_warranty_popup);
				prodSub.setImmediate_cancel_note(immediate_cancel_note);
				prodSub.setUpcomingrenewal_cancel_note(upcomingrenewal_cancel_note);
				prodSub.setOrderid_later_popup_content(orderid_later_popup_content);
				prodSub.setRedirect_inapp(redirect);
				prodSub.setValid_bundle_subs(validForBundleSub);
				prodSub.setImmediate_cancel(immediate_cancel);
				prodSub.setCur_feature_ui_new(cur_feature_ui_new);
				prodSub.setActivated_content(flexi_plan_activate_content_2);
				if(prodSub.getIs_flexi() && prodSub.getRemaining_month() == 3) {
					prodSub.setActivated_content(flexi_plan_activate_content_1);
				}
				prodSub.setStart_content(flexi_plan_start_content);
				prodSub.setPause_content(flexi_plan_pause_content);
			}

			if(monitor_type == 11) {
				boolean setupActivate = false;
                setupActivate = !cbService.getVetchatSetUpActivate(user.getEmail());
				prodSub.setSetupActivate(setupActivate);
			}
			
			if (subscrip1 != null && !subscrip1.getSubscriptionId().equalsIgnoreCase("NA") && subscrip1.getSubscriptionId().substring(0, 3).equalsIgnoreCase("RE-")) {
				prodSub.setIs_rec(true);
			}
			boolean show_coupon = false;
			if(iris_speriod<5 && status.equals("ACTIVE") && !isFreePlan) {
				show_coupon = true;
								 
				JCouponInfo coupon_info = new JCouponInfo(coupon_img_upgrade, coupon_desc_upgrade, coupon_code_upgrade, "Continue");
				prodSub.setShow_coupon(show_coupon);
				prodSub.setCoupon_info(coupon_info);
			}
			
			prodSub.setPlanid(iris_splan);
			prodSub.setPeriodid(iris_speriod);
			if (meariSubKey != null && meariSubKey.equalsIgnoreCase("NA") && status.equalsIgnoreCase("in_trial")) {
				prodSub.setPeriodid(1);
			}
			prodSub.setSubscriptionplan(rpt);
			prodSub.setMonitor_id(monitor_type.intValue());
			prodSub.setWarranty_claimed(warranty_claimed);
			prodSub.setMeariSubKey(meariSubKey);
//			response.put("sub_quantity", subQuantity);
//			response.put("mapped_quantity", mappedQuantity);
			prodSub.setShow_alertlimit(show_alertlimit);
			prodSub.setShow_upgrade(is_upgrade);
			prodSub.setShow_manage_sub(show_manage_sub);
			prodSub.setCancel_popup_content(cancel_popup);
			prodSub.setPlan_ver(splan.getPlan_ver());

			async.saveCurrentPlanDetails(user.getId(), user.getChargebeeid(), iris_splan, iris_speriod, rpt.getCbPlanId(),
					null);

		} catch (Exception e) {
			log.error("getProductSubscriptionplanV2:" + e.getMessage());
		}

		return prodSub;
	}
	public JProductSubResV2 getVetplanFromDB(UserV4 user, String os, String app_ver, String timezone,
			Long gateway_id, Long monitor_id) {

		JProductSubResV2 prodSub = new JProductSubResV2();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		try {
			String cbId = "NA";
			if (user.getChargebeeid().equalsIgnoreCase("NA")) {
				ListResult rs = null;
				int i = 1;
				Loop: while (rs == null || i <= 3) {
					i = i + 1;
					Thread.sleep(2000);
					rs = Customer.list().email().is(user.getEmail()).sortByCreatedAt(SortOrder.ASC).request();

					if (!rs.isEmpty()) {
						for (ListResult.Entry entry : rs) {
							Customer customer = entry.customer();
							user.setChargebeeid(customer.id());
						}
						break Loop;
					}
				}
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					log.info("create user in chargebee : chargebeeid : "+user.getChargebeeid());
					cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
							user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}
			}
			AllProductSubscription subscrip1 = null;
			boolean isPaused = false;
			boolean isFreetrial = false;
			boolean isFreePlan = false;
			String cbSubId = "NA";
			boolean show_cancel_sub = false;
			String status = "INACTIVE";
			String billingPeriod = "NA";
			String planname = "NA";
			int iris_splan = 0;
			int iris_speriod = 0;
			boolean show_alertlimit = false;
			boolean immediate_cancel = false;
			boolean payment_due = false;
			Card card = null;
			PaymentMethod ps = null;
			String cur_feature_ui_new = "";
			int substatus_code = 1;
			
			AllProductSubscription sub = cbService.getProductSubscriptionByGatewayId(gateway_id, user.getChargebeeid(),monitor_id);
			
			JGatewayFeature gatewayFeature = crService.getGatewayFeatureById(gateway_id);
			
			SubscriptionPlan splan = crService.getSubsPlanById(gatewayFeature.getPlan_id());

			if (!user.getChargebeeid().equalsIgnoreCase("NA")) {
				if (sub != null) {
					if (sub.getPlanId().contains("flexi")){
						prodSub = crService.getFlexiPlanHistory(gateway_id, sub.getSubscriptionId());
						if (prodSub == null){
							prodSub = new JProductSubResV2();
							prodSub.setIs_paused(true);
							prodSub.setMonth_left("3/3");
							prodSub.setRemaining_month(3);
						} else {
							String currTime = _helper.getCurrentTimeinUTC();
							int days_left = (int) _helper.getDaysBetweenDate(currTime, prodSub.getFlexi_next_billing_date());
							int hours_left = (int) _helper.getDaysBetweenDate(currTime, prodSub.getFlexi_next_billing_date());;
							prodSub.setIs_started(prodSub.getRemaining_month() > 0);
							prodSub.setRemaining_month(3 - prodSub.getRemaining_month());
							prodSub.setMonth_left(prodSub.getRemaining_month() + "/3");
							if(days_left <= 0 && prodSub.getRemaining_month() == 0) {
								prodSub.setRemaining_month(-1);
								prodSub.setIs_paused(true);
								prodSub.setIs_started(false);
							}
							prodSub.setDays_left(days_left < 0 ? 0 : days_left);

							if (prodSub.getRemaining_month() > 0 && !prodSub.getIs_paused()) {
								prodSub.setShow_flexi_pause(true);
							}

							if (days_left > 0 && prodSub.getIs_paused()){
								prodSub.setIs_paused(false);
								prodSub.setShow_flexi_pause(false);
								prodSub.setPaused_date_content(_helper.getCurrentTimeInFormat(prodSub.getFlexi_next_billing_date(), "yyyy-MM-dd HH:mm:ss", "dd MMM yyyy"));
							}

							if(prodSub.getPaused_count()==2){
								prodSub.setShow_flexi_pause(false);
							}

							prodSub.setFlexi_next_billing_date(_helper.getCurrentTimeInFormat(prodSub.getFlexi_next_billing_date(), "yyyy-MM-dd HH:mm:ss", "dd MMM yyyy"));
							prodSub.setFlexi_plan_start_date(_helper.getCurrentTimeInFormat(prodSub.getFlexi_plan_start_date(), "yyyy-MM-dd HH:mm:ss", "dd MMM yyyy"));
						}
						prodSub.setIs_flexi(sub.getSubscriptionStatus().contains("cancel") ? false : true);
					}
					String subs_planid = sub.getPlanId();

					cbSubId = sub.getSubscriptionId();
					
					Result result = null;
					try {
						result = Subscription.retrieve(cbSubId).request();
						card = result.card();
						ps = result.customer().paymentMethod();
					}catch(Exception e) {
						log.error("getSubscription:" + e.getMessage());
					}
		            
					if (monitor_id > 1 && !freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
							&& sub.getSubscriptionStatus().equalsIgnoreCase("cancelled")) {
						isFreetrial = false;
						isFreePlan = true;
						isPaused = false;
						status = "ACTIVE";
						billingPeriod = "Free";
						planname = splan.getPlan_name();
						iris_splan = gatewayFeature.getPlan_id();
						iris_speriod = gatewayFeature.getPeriod_id();
					}else if (!freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
							&& !sub.getSubscriptionStatus().equalsIgnoreCase("PAUSED") && !sub.getSubscriptionStatus().equalsIgnoreCase("CANCELLED")) {
						subscrip1 = sub;
						isFreetrial = false;
						isFreePlan = false;
						isPaused = false;
						iris_speriod = gatewayFeature.getPeriod_id();
					} else if (!freeplan.contains(subs_planid) && !omitplan.contains(subs_planid)
							&& sub.getSubscriptionStatus().equalsIgnoreCase("PAUSED")) {
						subscrip1 = sub;
						isFreetrial = false;
						isFreePlan = false;
						status = "PAUSED";
						isPaused = true;
						JGatewayFeature gatewayFeatureNew = crService.getGatewayFeatureByIdWithoutactive(gateway_id);
						iris_splan = gatewayFeatureNew!= null && gatewayFeatureNew.getPlan_id() > 0 ? gatewayFeatureNew.getPlan_id() : 0;
						iris_speriod = gatewayFeatureNew!= null && gatewayFeatureNew.getPlan_id() > 0 ? gatewayFeatureNew.getPeriod_id() : 0;
						splan = crService.getSubsPlanById(gatewayFeatureNew.getPlan_id());
					}
					if (subscrip1 == null) {
						if (freeplan.contains(sub.getPlanId())) {
							subscrip1 = sub;
							isFreePlan = true;
						}
					} else if(sub.getSubscriptionStatus().equalsIgnoreCase("IN_TRIAL")) {
						isFreetrial = true;
					}
				} else {
					// Free plan check
					if (gatewayFeature.getPlan_id() > 0 && gatewayFeature.getSub_id().equalsIgnoreCase("NA")) {
						log.info("Free plan found");
						status = "ACTIVE";
						billingPeriod = "Free";
						planname = splan.getPlan_name();
						iris_splan = gatewayFeature.getPlan_id();
						iris_speriod = gatewayFeature.getPeriod_id();
						isFreePlan = true;
					}
					
					if(monitor_id == 1) {
						String allSubscription = cbService.getSubscriptionByChargebeeIdNotinProductsub(user.getChargebeeid());
						cbSubId = allSubscription;
					}
				}
			}

			String periodUnit = "NA";
			String planid = "NA";

			float price = (float) 0.0;
			int daysBetween = -1;
			int days_remaining = -1;
			String nextPaymentDate = "NA";
			String autoRenewalStatus = "NA";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date dateobj = new Date();
			
			int expiryMonth = 0;
			int expiryYear = 0;
			int renewMonth = 0;
			int renewYear = 0;

			JSubscriptionPlanReportV2 rpt = new JSubscriptionPlanReportV2();
			boolean show_manage_sub = false;
//			long subQuantity = 0;
			String cancel_popup = "If you cancel, your plan stays active until the next renewal";
			boolean warranty_claimed = false;
			if (subscrip1 != null && splan != null) {
				
				if(card != null) {
					expiryMonth = card.expiryMonth();
					expiryYear = card.expiryYear();
				}
				
				periodUnit = subscrip1.getPlanPeriod();
				planid = subscrip1.getPlanId();

				if (periodUnit.contains("HALF")) {
					billingPeriod = "Half Yearly";
				} else if (periodUnit.contains("MONTH")) {
					billingPeriod = "Monthly";
				} else if (periodUnit.equalsIgnoreCase("2-YEAR")) {
					billingPeriod = "2 Year";
				} else if (periodUnit.equalsIgnoreCase("5-YEAR")) {
					billingPeriod = "5 Year";
				} else if (periodUnit.contains("QUARTER")) {
					billingPeriod = "Quarter";
				} else if (periodUnit.contains("YEAR")) {
					billingPeriod = "Yearly";
				}

				price = (float) subscrip1.getPlanAmount() / 100;
				String strprice = "$" + String.valueOf(price);
				status = subscrip1.getSubscriptionStatus().toUpperCase();

				if ((status.equalsIgnoreCase("ACTIVE")) || (status.equalsIgnoreCase("IN_TRIAL"))) {
					boolean ispaymentDue = cbService.checkDueInvoice(cbSubId);
					payment_due = ispaymentDue;
					if (ispaymentDue) {
						status = "PAYMENT DUE";
						autoRenewalStatus = "NA";
					} else {
						sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
						try {
							nextPaymentDate = subscrip1.getNextBillingAt();
							if (nextPaymentDate.equalsIgnoreCase("1753-01-01 00:00:00"))
								nextPaymentDate = subscrip1.getTrialEnd();
						} catch (Exception ex) {
							nextPaymentDate = subscrip1.getTrialEnd();
						}

						Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
						Date todayDate = sdf.parse(sdf.format(dateobj));

						long difference = nextPaymentDate1.getTime() - todayDate.getTime();
						daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
						
						days_remaining = daysBetween;

						sdf = new SimpleDateFormat("dd-MMM-yyyy");
						sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
						nextPaymentDate = sdf.format(nextPaymentDate1);
						autoRenewalStatus = "Enabled";
						if (freeplan.contains(subscrip1.getPlanId())) {
							nextPaymentDate = "NA";
							days_remaining = -1;
							autoRenewalStatus = "NA";
						}
						
						Calendar calendar = Calendar.getInstance();
						calendar.setTime(nextPaymentDate1);
						renewYear = calendar.get(Calendar.YEAR);
						//Add one to month {0 - 11}
						renewMonth = calendar.get(Calendar.MONTH) + 1;
						substatus_code = 1;
					}
				} else if (status.equalsIgnoreCase("NON_RENEWING")) {
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					status = "ACTIVE";
					autoRenewalStatus = "Disabled";
					nextPaymentDate = subscrip1.getSubscriptionCancelledAt();

					Date cancelledAt = sdf.parse(nextPaymentDate);
					Date todayDate = sdf.parse(sdf.format(dateobj));

					long difference = cancelledAt.getTime() - todayDate.getTime();
					daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
					days_remaining = daysBetween;

					sdf = new SimpleDateFormat("dd-MMM-yyyy"); // yyyy-MM-dd
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					nextPaymentDate = sdf.format(cancelledAt);
					substatus_code = 1;
				} else if (status.equalsIgnoreCase("PAUSED")) {
					status = "PAUSED";
					days_remaining = -1;
					autoRenewalStatus = "Disabled";
					substatus_code = 2;
					
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					try {
						nextPaymentDate = subscrip1.getNextBillingAt();
						if (nextPaymentDate.equalsIgnoreCase("1753-01-01 00:00:00"))
							nextPaymentDate = subscrip1.getTrialEnd();
					} catch (Exception ex) {
						nextPaymentDate = subscrip1.getTrialEnd();
					}

					Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
					Date todayDate = sdf.parse(sdf.format(dateobj));

					long difference = nextPaymentDate1.getTime() - todayDate.getTime();
					daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
					
					days_remaining = daysBetween;

					sdf = new SimpleDateFormat("dd-MMM-yyyy");
					sdf.setTimeZone(TimeZone.getTimeZone("UTC" + timezone));
					nextPaymentDate = sdf.format(nextPaymentDate1);
					autoRenewalStatus = "Enabled";
					if (freeplan.contains(subscrip1.getPlanId())) {
						nextPaymentDate = "NA";
						days_remaining = -1;
						autoRenewalStatus = "NA";
					}
					
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(nextPaymentDate1);
					renewYear = calendar.get(Calendar.YEAR);
					//Add one to month {0 - 11}
					renewMonth = calendar.get(Calendar.MONTH) + 1;
					substatus_code = 1;
				
				}

				if (daysBetween < 0)
					days_remaining = -1;
				
				if (splan != null && splan.getId() > 0) {

					cur_feature_ui_new = splan.getCur_feature_ui_new();

					if (cur_feature_ui_new == null || cur_feature_ui_new.equalsIgnoreCase("null"))
						cur_feature_ui_new = "";
				}
				
				if (monitor_id != null && monitor_id == 1) {
					sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					Date curStart = sdf.parse(sdf.format(sdf.parse(subscrip1.getSubscriptionStartedAt()).getTime()));
					Date today = sdf.parse(new Helper().getCurrentTimeinUTC());

					long difference = today.getTime() - curStart.getTime();
					double daysBetween_active = Math.ceil((difference / (1000 * 60 * 60 * 24)));
					double hoursBetween_active = (difference / (1000 * 60 * 60));

					if (hoursBetween_active <= 24 && billingPeriod.equalsIgnoreCase("monthly"))
						immediate_cancel = true;
					else if (daysBetween_active <= 7 && !billingPeriod.equalsIgnoreCase("monthly"))
						immediate_cancel = true;
				}
				if(!sub.getSubscriptionStatus().equalsIgnoreCase("PAUSED") && !sub.getSubscriptionStatus().equalsIgnoreCase("CANCELLED")) {
					iris_splan = gatewayFeature.getPlan_id();
					iris_speriod = gatewayFeature.getPeriod_id();
				}
				
				planname = splan.getPlan_name();
				if (monitor_id == 0) {
					monitor_id = splan.getMonitor_type();
				}
				
				if (!app_ver.equalsIgnoreCase("na") && !os.equalsIgnoreCase("na")) {
					VersionMapping verObj = crService.getVersionMapping(app_ver, os);
					if (verObj != null) {
						if (verObj.isShow_cancel_sub() && subscrip1.getSubscriptionStatus().equalsIgnoreCase("active")
								&& !isFreePlan)
							show_cancel_sub = true;
							show_alertlimit = true;
					}
				}
				
				if( show_cancel_sub && monitor_id == 1) {
					UserCancelFeedBack userCancelFeedback = cancelService.getUserCancelFeedBackByShowCancel( user.getId() , false );
					if( userCancelFeedback != null ) {
						show_cancel_sub = false;
					}	
				}

				rpt = new JSubscriptionPlanReportV2(planid, planname,  billingPeriod, nextPaymentDate,
						days_remaining, status, autoRenewalStatus, cbSubId,  
						user.getChargebeeid(), isFreetrial, show_cancel_sub, isFreePlan, payment_due,strprice);

			} else {
				rpt = new JSubscriptionPlanReportV2(planid, planname, billingPeriod, nextPaymentDate,
						days_remaining, status, autoRenewalStatus, cbSubId,  user.getChargebeeid(), 
						isFreetrial, show_cancel_sub, isFreePlan,payment_due,"NA");
			}
			
			String meariSubKey = "NA";

			if(monitor_id == 4 || monitor_id == 5 || monitor_id == 8 || monitor_id == 12) {
				meariSubKey = crService.getMeariKey(0, gateway_id, iris_speriod);
			}
			boolean validForBundleSub = true;


			
			boolean is_upgrade = crService.isUpgradeAvailable(iris_splan, iris_speriod);

			if (isPaused) {
				is_upgrade = false;
			} else if (status.equalsIgnoreCase("cancelled")) {
				is_upgrade = true;
				validForBundleSub = false;
			}

			if(prodSub.getIs_flexi() && !status.equalsIgnoreCase("cancelled")){
				if (prodSub.getRemaining_month() <= 0 && prodSub.getDays_left() <= 0) {

				} else {
					is_upgrade = false;
				}

				if (prodSub.getRemaining_month() > 0){
					show_cancel_sub = false;
				}
			}

			rpt.setFreetrial(isFreetrial);
			
			boolean show_warranty_popup = false;
			boolean show_check_out_page = this.show_check_out_page;
			String check_out_page_url = "NA";
			String gateway_name = "Pet";
			
			JGateway gateway = gatewayService.getJGatewayByGateway(gateway_id);
			if (gateway != null) {
					if (!gateway.isShowOrderId() && !gateway.isPurchased_from_others()) {
						warranty_claimed = true;
						show_warranty_popup = true;
					}
			}
			JResponse response1 = new JResponse();
			
			Map<String,Object> deviceList = new HashMap<>();
			try {
			// get device list by gatewayid
			deviceList = iReportServiceV4.getLastGatewayReportV6(gateway_id, user.getId(), monitor_id, user.getCountry(), 
					os, "V2", response1);
			prodSub.setDeviceList(deviceList);
			}catch(Exception e) {
				log.info("deviceList : "+ e.getMessage());
			}
			
			rpt.setShow_cancel_sub(show_cancel_sub);
			prodSub.setCancel_button_text("Cancel Plan");
			
			
			
			JPauseHistory pauseHis = null;
			
			if(sub != null && sub.getSubscriptionId() != null)
				 pauseHis = crService.getPauseHistoryRecord( sub.getSubscriptionId());
			
			if((pauseHis == null || pauseHis.getStatus() == 1) && iris_speriod == 1 && !isFreePlan && monitor_id == 1)
			{
				prodSub.setShowpausesub(true);
			}else {
				prodSub.setShowpausesub(false);
			}
				
			prodSub.setNextrenew_date_old(rpt.getNextrenew_date());
			
			if(pauseHis != null && pauseHis.getResumeDate() != null) {
				if(pauseHis.getStatus() == 1 && monitor_id == 1 ) {
					rpt.setShow_cancel_sub(false);
					rpt.setAutoRenewalStatus("Disable");
					rpt.setNextrenew_date(pauseHis.getResumeDate());
				}
					
				prodSub.setResumeDate(pauseHis.getResumeDate());
				prodSub.setPauseStatus(pauseHis.getStatus() == 1 ? true : false);
				prodSub.setPausetitle("Your pet's tail will wag for this!");
				prodSub.setPausecontent("Your subscription will renewal on ");
			} else {
				prodSub.setResumeDate("");
				prodSub.setPauseStatus(false);
				prodSub.setPausetitle("Your pet's tail will wag for this!");
				prodSub.setPausecontent("Your subscription will renewal on ");
			}
			
			if( gateway != null ) {
				gateway_name = gateway.getName();
				prodSub.setName(gateway.getName());
				prodSub.setGateway_id(gateway.getId());
			}
			
			prodSub.setShow_pauseoption(pause_enable);
			
			if(sub != null && sub.getSubscriptionId() != null && sub.getSubscriptionStatus().equalsIgnoreCase("non_renewing")) {
				prodSub.setShow_pauseoption(false);
			}
			
			if(monitor_id != null && monitor_id == 1) {
				String paymenttype = (ps!=null) ? ps.gateway().name() : "";
				
				checkDeviceConfigStatusByGatewayPlanv2(iris_splan, gateway,
					days_remaining, status,expiryMonth,expiryYear,renewMonth,renewYear,user.getChargebeeid(),paymenttype,prodSub);
				
				prodSub.setShow_check_out_popup(false);
				ProductSubscription productSubscription = cbService.getProductSubscription(user.getId());
				boolean isRecharge = false;
				if(productSubscription != null) {
						validForBundleSub = true;
						if (productSubscription.getPlan_id().contains("rech")) {
							isRecharge = true;
						}
				}
				
				int redirect = user.getInapp_purchase(); // default 1
				int inapp_redirect = 1;

				if (!isFreePlan == true && (inapp_redirect == 2 || user.getInapp_purchase() == 2)) {
					inapp_redirect = 1;
					redirect = inapp_redirect;
					user.setInapp_purchase(inapp_redirect);
					// call user update method
					userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());

				} else if ((!isFreePlan == false && inapp_redirect == 2 && user.getInapp_purchase() == 1)
						|| (!isFreePlan == false && inapp_redirect == 1 && user.getInapp_purchase() == 2)) {
					// update CB purchase status to 2[inapp] in user table
					user.setInapp_purchase(inapp_redirect);
					redirect = inapp_redirect;
					userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());
				}

				redirect = user.getInapp_purchase();
				
				log.info("valid_bundle_subs : "+ validForBundleSub +" :: show_check_out_page : "+ show_check_out_page);
				if (show_check_out_page && !show_warranty_popup && productSubscription != null
						&& !productSubscription.isIs_subscription_activated()) {

					if(isRecharge) {
						show_check_out_page = false;
					}
					
					Inventory inventory = niomDbservice.getInventoryByMeid(gateway.getMeid());
					
					if (validForBundleSub && !isRecharge &&  (inventory != null && inventory.getOrderid() != null && inventory.getOrderid()
							.equalsIgnoreCase(String.valueOf(productSubscription.getOrder_id())))) {
						check_out_page_url = checkOutURLForBundleSubs(productSubscription, user.getChargebeeid());
					}

					if( productSubscription.isActive_subscription() &&  (inventory != null && inventory.getOrderid() != null && inventory.getOrderid()
							.equalsIgnoreCase(String.valueOf(productSubscription.getOrder_id())))) {
						prodSub.setShow_bundle_contact_us(true);
						prodSub.setBundle_contact_us_content(bundle_contact_us_content);
					}
					
					if (check_out_page_url.equalsIgnoreCase("NA")) {
						show_check_out_page = false;
					} else {
						show_check_out_page = true;
						JPlan planDesc = cbService.getPlanDesc(productSubscription.getPlan_id());
						String subsPeriod = getSubscriptionPeriod(planDesc.getPeriodUnit());
						String check_out_page = check_out_page_content;
						check_out_page = check_out_page.replace("$$", subsPeriod);
						log.info("sub period : " + subsPeriod + " :: checkout content : " + check_out_page);
						prodSub.setShow_check_out_popup(show_check_out_page);
						prodSub.setCheck_out_popup_content(check_out_page);
						prodSub.setCheck_out_popup_image(check_out_page_image);
						prodSub.setCheck_out_popup_image_dark(check_out_page_image_dark);
					}

				}
				
				if (splan != null && !splan.isIs_freeplan()) {
					validForBundleSub = false;
				} 
				
				// cancel sub contents
				
				boolean cancelSub = false;
				String cancelSubUrl = cancel_sub_url.get("US");
				// Append email to URL
				if (!user.getEmail().equalsIgnoreCase("NA"))
					cancelSubUrl = cancelSubUrl + "?email=" + user.getEmail();
				else
					cancelSubUrl = cancelSubUrl + "?email=";

				// Append full name to URL
				if (!user.getFirstname().equalsIgnoreCase("NA") && !user.getLastname().equalsIgnoreCase("NA"))
					cancelSubUrl = cancelSubUrl + "&fullname="
							+ (user.getFirstname() + " " + user.getLastname()).replace(' ', '$');
				else if (!user.getFirstname().equalsIgnoreCase("NA") && user.getLastname().equalsIgnoreCase("NA"))
					cancelSubUrl = cancelSubUrl + "&fullname=" + (user.getFirstname()).replace(' ', '$');
				else if (user.getFirstname().equalsIgnoreCase("NA") && !user.getLastname().equalsIgnoreCase("NA"))
					cancelSubUrl = cancelSubUrl + "&fullname=" + (user.getLastname()).replace(' ', '$');
				else
					cancelSubUrl = cancelSubUrl + "&fullname=";

				// Append phone to URL
				if (!user.getMobileno().equalsIgnoreCase("NA"))
					cancelSubUrl = cancelSubUrl + "&phone=" + user.getMobileno();
				else
					cancelSubUrl = cancelSubUrl + "&phone=";
				
				String country = user.getCountry().toUpperCase();
				country = "US";
				
				cancelSubUrl = cancel_sub_url.get(country);
				
				if(cancelSubUrl == null) {
					cancelSubUrl = cancel_sub_url.get("US");
				}

				if (cancelSubUrl.equalsIgnoreCase("NA") && !cancel_inapp_redirect) {
					cancelSub = false;
				}

				boolean is_expired = true;
				boolean isAvail = false;
				
				LinkedList<String> reasonList = new LinkedList<String>();
				JCancelSubInfo cancelInfo = new JCancelSubInfo();

				if (cancel_inapp_redirect) {
					reasonList.add("Currently not using");
					reasonList.add("Not happy with the service");
					reasonList.add("Costs too much");
					reasonList.add("Others");

					String can_label1 = "Unsubscribing?";
					String can_label2 = "Don't miss out on \r\n unwavering pet protection and \r\n guaranteed peace of mind.";
					String btn1 = "Never Mind";
					String btn2 = "Stay with Waggle";
					String can_label_newflow = "Take advantage of additional benefits";

					List can_label2_newflow = crService.listPlanBenefits(iris_speriod);

					if (can_label2_newflow.isEmpty())
						can_label_newflow = "Sad to see you go!";

					cancelInfo = new JCancelSubInfo(can_label1, can_label2, btn1, btn2, reasonList);
					cancelInfo.setCan_label_newflow(can_label_newflow);
					cancelInfo.setCan_label2_newflow(can_label2_newflow);

					if (rpt.getAutoRenewalStatus().equalsIgnoreCase("Disabled"))
						cancelSub = false;
					
					boolean is_can_applicable = true;
					String can_msg = "";
					
					if( show_cancel_basedon_user_cancel_feedback ) {
						
						boolean isvalid = crService.checkRenewalDateInUserRetained( user.getId() );
						//if valid = true means already coupon applied, cancel will not be shown
						if( isvalid ) 
							cancelSub = false;
						
						if( cancelSub ) {
							UserCancelFeedBack userCancelFeedback = cancelService.getUserCancelFeedBackByShowCancel( user.getId() , false );
							if( userCancelFeedback != null ) {
								cancelSub = false;
							}	
						}	
						
					}
					
					prodSub.setShow_cancel_reward(false);
					AdditionBenefitsCancelReward additionBenefitsCancelReward = cancelService.getAdditionBenefitsCancelReward( user.getId() );
					if( additionBenefitsCancelReward != null ) {
						prodSub.setCancel_reward_code(additionBenefitsCancelReward.getCode());
						
						Date endDate = sdf.parse( additionBenefitsCancelReward.getEnd_date() );
						Date curDate = new Date(System.currentTimeMillis());
						
						if( endDate.after(curDate) ) {
							prodSub.setCancel_reward_enddate(new SimpleDateFormat("dd-MMM-yyyy").format(endDate));
							prodSub.setShow_cancel_reward(true);
							prodSub.setCancel_reward_title(addition_benefits_cancel_reward_title);
							prodSub.setCancel_reward_url(cancel_reward_url);
						}
						
					}
					
					// Checking additional benefit validity
					if (is_expired) // this is already benefits created user
						is_expired = iAvatarService.checkBenefitsExpired(user.getId());

					if (!is_expired && isAvail) {
						is_can_applicable = false;
						can_msg = "To cancel the subscription and additional benefits, please reach out to our Customer Delight team.";
						cancelInfo.setIs_can_applicable(is_can_applicable);
						cancelInfo.setCan_msg(can_msg);
					}
				}
				prodSub.setCancelSubUrl(cancelSubUrl);
				prodSub.setCancel_inapp_redirect(cancel_inapp_redirect);
				prodSub.setCancelInfo(cancelInfo);
				prodSub.setStay_with_us_content(gateway_name);
				
				LinkedList<String> stayList = new LinkedList<String>();
				stayList.add("Instant temperature alerts");
				stayList.add("Power outage warnings");
				stayList.add("GPS tracking & Safe Zone alerts");
				stayList.add("Extended warranty for active plans");
				
				prodSub.setStay_with_us_list(stayList);
				
				prodSub.setBundle_check_out_url(check_out_page_url);
				prodSub.setShow_warranty_popup(show_warranty_popup);
				prodSub.setShow_warranty_popup_content(warranty_msg_v2);
				prodSub.setShow_warranty_popup_v2_image(warranty_msg_v2_image);
				prodSub.setShow_warranty_popup_v2_content(warranty_msg_v2_content);
				prodSub.setWarranty_popup_later_btn(show_later_btn_warranty_popup);
				prodSub.setImmediate_cancel_note(immediate_cancel_note);
				prodSub.setUpcomingrenewal_cancel_note(upcomingrenewal_cancel_note);
				prodSub.setOrderid_later_popup_content(orderid_later_popup_content);
				prodSub.setRedirect_inapp(redirect);
				prodSub.setValid_bundle_subs(validForBundleSub);
				prodSub.setImmediate_cancel(immediate_cancel);
				prodSub.setCur_feature_ui_new(cur_feature_ui_new);
				prodSub.setActivated_content(flexi_plan_activate_content_2);
				if (prodSub.getIs_flexi() && prodSub.getRemaining_month() == 3){
					prodSub.setActivated_content(flexi_plan_activate_content_1);
				}
				prodSub.setStart_content(flexi_plan_start_content);
				prodSub.setPause_content(flexi_plan_pause_content);
			}
			
			if (subscrip1 != null && !subscrip1.getSubscriptionId().equalsIgnoreCase("NA") && subscrip1.getSubscriptionId().substring(0, 3).equalsIgnoreCase("RE-")) {
				prodSub.setIs_rec(true);
			}
			prodSub.setPlanid(iris_splan);
			prodSub.setPeriodid(iris_speriod);
			prodSub.setSubscriptionplan(rpt);
			prodSub.setMonitor_id(monitor_id.intValue());
			prodSub.setWarranty_claimed(warranty_claimed);
			prodSub.setMeariSubKey(meariSubKey);
//			response.put("sub_quantity", subQuantity);
//			response.put("mapped_quantity", mappedQuantity);
			prodSub.setShow_alertlimit(show_alertlimit);
			prodSub.setShow_upgrade(is_upgrade);
			prodSub.setShow_manage_sub(show_manage_sub);
			prodSub.setCancel_popup_content(cancel_popup);			

			async.saveCurrentPlanDetails(user.getId(), user.getChargebeeid(), iris_splan, iris_speriod, rpt.getCbPlanId(),
					null);

		} catch (Exception e) {
			log.error("getProductSubscriptionplanV2:" + e.getMessage());
		}

		return prodSub;
	}

	public JProductSubResV2 checkDeviceConfigStatusByGatewayPlanv2(long planid, JGateway gateway, int days_remaining, String status,
			int exMo,int exYr, int subMo,int subYr,String userCB,String paymenttype, JProductSubResV2 prodSub) {
		log.info("Entering checkDeviceConfigStatusV2:");

		String showNextRenewal_withContent = "";

		try {
				String sub = cbService.getProductSubscriptionStatus(gateway.getId(), userCB);
				
				JGatewayFeature gatewayFeature = crService.getGatewayFeatureById(gateway.getId());
				
				long add_device_cnt = 0;
				int remaindays = -1;
				int maxDevCnt = 0;
				boolean setupActivate = false;
				if (gatewayFeature.getPlan_id() > 0) {
					maxDevCnt = crService.getDeviceConfigV4Gateway(gateway.getId(), gatewayFeature.getPlan_id());
				}


				if (sub.equalsIgnoreCase("NA") || gatewayFeature.getPlan_id() == 1 || sub.equalsIgnoreCase("INACTIVE") || sub.equalsIgnoreCase("PAUSED") || sub.equalsIgnoreCase("CANCELLED")) {
					setupActivate = true;
					remaindays = -1;
				}  else {
					if (gatewayFeature.getPlan_id() > 0) {
						if (maxDevCnt == 0) {
							setupActivate = true;
						} else {
							if (maxDevCnt > add_device_cnt) {
								setupActivate = false;
								add_device_cnt = add_device_cnt + 1;
								remaindays = days_remaining;
							} else {
								setupActivate = true;
								remaindays = -1;
							}
						}
					} else {
						setupActivate = true;
						remaindays = -1;
					}
				}

				if((remaindays < days_tohandle_nextrenewal) && (remaindays > 0) && show_nextrenewal_popup
						&&((exMo>0 && exMo < subMo && exYr==subYr) || exYr>0 && exYr<subYr) 

						&&(!paymenttype.equalsIgnoreCase("PAYPAL_EXPRESS_CHECKOUT"))) {

					showNextRenewal_withContent = "Your card is about to expire. Please update it to ensure uninterrupted service.";

					//showNextRenewal_withContent = "Subscription renews in " + remaindays + " days";
				}

				
				prodSub.setSetupActivate(setupActivate);
				prodSub.setDays_remaining(remaindays);
				prodSub.setMeid(gateway.getMeid());
				prodSub.setShow_nextrenewal_popup_content(showNextRenewal_withContent);
			
		} catch (Exception e) {
			log.info("checkDeviceConfigStatusV2 : " + e.getLocalizedMessage());
		}
		return prodSub;
	}

	@Override
	public boolean updategatewaybysubcriptionId(long gatewayId,String subId) {
		return cbDao.updategatewaybysubcriptionId(gatewayId,subId);
	}

	@Override
	public boolean isVetPlanAvailable(String chargebeeid, long monitortype) {
		return cbDao.isVetPlanAvailable(chargebeeid, monitortype);
	}

	@Override
	public String getVetPlanSub(String chargebeeid,long monitortype) {
		return cbDao.getVetPlanSub(chargebeeid, monitortype);
	}

	@Override
	public long getCurrentVetPlanid(String chargebeeid, long monitortype) {
		return cbDao.getCurrentVetPlanid(chargebeeid, monitortype);
	}

	@Override
	public List<AllProductSubscription> getproductSubscriptions(String chargebeeid, long gateway_id) {
		return cbDao.getproductSubscriptions(chargebeeid, gateway_id);
	}

	@Override
	public String getUnmappedPlaninprodSubscription(String chargebeeid, long monitorType) {
		return cbDao.getUnmappedPlaninprodSubscription(chargebeeid, monitorType);
	}

	@Override
	public long getGatewayFeatureBySubId(String subId) {
		return cbDao.getGatewayFeatureBySubId(subId);
	}
	@Override
	public void sendCBFailedMail(String fName, String lName, String email, String phoneNo, String userName){
		try {
			Properties prop = new Properties();
			File file = ResourceUtils.getFile("classpath:iris3.properties");
			prop.load(new FileInputStream(file));

			String to_address = prop.getProperty("to_address");
			String cc_address = prop.getProperty("cc_address");
			String bcc_address = prop.getProperty("bcc_address");

			String mailSub = "Reg Failed CBCustomer creation : " + userName;
			String mailContent = "<p>Hi Team,</p>" + "<p>Find the CB customer creation failed details</p>";
			mailContent += "<p>First Name   : " + fName+ "</p>";
			mailContent += "<p>Last Name    : " + lName + "</p>";
			mailContent += "<p>Email        : " + email + "</p>";
			mailContent += "<p>Mobile       : " + phoneNo + "</p>";

			mailContent = mailContent + "<br><br>Thanks,<br> Irisservice ";
			async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);
		}
		catch (Exception e) {
			log.error("Reg Failed CBCustomer creation : " + userName+":"+e.getLocalizedMessage());
		}
	}

	@Override
	public List<ManageList> getManageList() {
		return cbDao.getManageList();
	}

	@Override
	public boolean getVetchatSetUpActivate(String email) {
		return cbDao.getVetchatSetUpActivate(email);
	}

	@Override
	public SubscriptionWithoutDevice getSubscriptionWithoutDevice(long userId, String email) {
		return cbDao.getSubscriptionWithoutDevice(userId, email);
	}
}
