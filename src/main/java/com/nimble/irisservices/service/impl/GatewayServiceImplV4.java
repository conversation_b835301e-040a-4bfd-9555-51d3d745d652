package com.nimble.irisservices.service.impl;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dao.IGatewayDaoV4;
import com.nimble.irisservices.dao.IUserDaoV4;
import com.nimble.irisservices.dto.*;
import com.nimble.irisservices.entity.DeviceReplaced;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.GatewayPendingEvent;
import com.nimble.irisservices.entity.GatewayStatus;
import com.nimble.irisservices.entity.GatewayToAlexa;
import com.nimble.irisservices.entity.NightVisionMode;
import com.nimble.irisservices.entity.PlThresholdStatus;
import com.nimble.irisservices.entity.RemoveGatewayRequest;
import com.nimble.irisservices.entity.RemoveGatewayRequestHistory;
import com.nimble.irisservices.entity.RemoveGatewayType;
import com.nimble.irisservices.entity.TempCalibStatus;
import com.nimble.irisservices.entity.UpgradeDeviceHistory;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IPetSpeciesServicesV4;
import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class GatewayServiceImplV4 implements IGatewayServiceV4 {
	
	private static final Logger log = LogManager.getLogger(GatewayServiceImplV4.class);

	@Autowired
	IGatewayDaoV4 gatewayDaoV4;

	@Autowired
	IUserDaoV4 userDao;

	@Autowired
	private NiomDatabaseImpl niomDao;

	@Value("${path.postfix.device}")
	private String path_postfix_device;
	
	@Value("${path.postfix.mobile}")
	private String path_postfix_mobile;
	
	@Value("${wowza.api.admin.dns}")
	private String wowza_api_admin_dns;
	
	@Value("${wowza.admin.passcode}")
	private String wowza_admin_passcode;
	
	@Value("${wowza.thumbnail.url}")
	private String wowza_thumbnail_url;

	@Autowired
	@Lazy
	IPetSpeciesServicesV4 petSpeciesServicesv4;
	
	@Override
	public JGatewayDetails getJGatewayDetails(String key, String value) {
		return gatewayDaoV4.getJGatewayDetails(key, value);
	}

	@Override
	public JResponse getJPetprofilesByUserV4(long userid, long gatewayid, int monitortype) {
		long startTime1 = System.currentTimeMillis();
		JResponse response = gatewayDaoV4.getJPetprofilesByUserv4(userid, gatewayid, monitortype);
		log.info(">> getJPetprofilesByUserV4 ElapsTime :@serviceimpl:  "
				+ ((System.currentTimeMillis() - startTime1) / 1000.0));
		;

		return response;
	}

	@Override
	public List<JGateway> getGatewayV4(String assetgroupid, String groupid, String subgroupid, String gatewayid,
			long userid, String meid) {
		return gatewayDaoV4.getJGateway(assetgroupid, groupid, subgroupid, gatewayid, userid, meid);
	}

	@Override
	public int saveorupdatePetprofileV4(JPetprofile jpetprofiles, Long userid, boolean gatewayAva, String birthday,
			long speciesid) {

		String update_qry = "UPDATE pet_profile PP SET PP.name = '" + jpetprofiles.getName() + "', \r\n"
				+ "PP.birth_date ='" + birthday + "', PP.sex ='" + jpetprofiles.getSex() + "',\r\n" + " PP.breed='"
				+ jpetprofiles.getBreed() + "' , PP.height='" + jpetprofiles.getHeight() + "' , \r\n" + " PP.weight = '"
				+ jpetprofiles.getWeight() + "' , PP.remarks='" + jpetprofiles.getRemarks() + "', \r\n"
				+ " PP.imageurl='" + jpetprofiles.getImageurl() + "' ,\r\n" + " PP.speciesid='" + speciesid
				+ "', PP.enable=" + jpetprofiles.isEnable() + ", PP.user_id=" + userid + " WHERE PP.gateway_id = '"
				+ jpetprofiles.getGateway_id() + "' ;";

		String create_qry = null;

		if (gatewayAva)
			create_qry = "INSERT INTO pet_profile (gateway_id,NAME,birth_date,sex,breed,height,weight,remarks,"
					+ "imageurl,speciesid,enable,user_id) VALUES"
					+ "('" + jpetprofiles.getGateway_id() + "','" + jpetprofiles.getName() + "','" + birthday + "','"
					+ jpetprofiles.getSex() + "','" + jpetprofiles.getBreed() + "'" + ",'" + jpetprofiles.getHeight()
					+ "','" + jpetprofiles.getWeight() + "','" + jpetprofiles.getRemarks() + "','"
					+ jpetprofiles.getImageurl() + "','" + speciesid + "'," + jpetprofiles.isEnable() + ","+userid+");";

		int updateStatus = userDao.executeQuery(update_qry);

		if (updateStatus > 0) {
			log.info("User device info updated successful");
			return 1;
		} else if (gatewayAva) {
			int createStatus = userDao.executeQuery(create_qry);
			log.info("User device info inserted successful");
			if (createStatus > 0)
				return 1;
		}
		return 0;

	}
	
	@Override
	public int saveorupdatePetprofileV5(JPetprofile jpetprofiles, Long userid, String birthday, long speciesid) {

		log.info("Entered saveorupdatePetprofileV5 ::");
		try {
			if (jpetprofiles.getId() == 0) {
				String create_qry = "INSERT INTO pet_profile (gateway_id,NAME,birth_date,sex,breed,height,weight,remarks,"
						+ "imageurl,speciesid,enable,user_id, intact, structure) VALUES"
						+ "('" + jpetprofiles.getGateway_id() + "','" + jpetprofiles.getName() + "','" + birthday + "','"
						+ jpetprofiles.getSex() + "','" + jpetprofiles.getBreed() + "'" + ",'" + jpetprofiles.getHeight()
						+ "','" + jpetprofiles.getWeight() + "','" + jpetprofiles.getRemarks() + "','"
						+ jpetprofiles.getImageurl() + "','" + speciesid + "'," + jpetprofiles.isEnable() + ","+userid+", "+ jpetprofiles.isIntact() +", '"+ jpetprofiles.getStructure() +"');";
				log.info("pet_profile insert qry : " + create_qry );
				int updateStatus = userDao.executeQuery(create_qry);
				if (updateStatus > 0) {
					log.info("pet profile inserted successful");
					return 1;
				}
				return 0;
			} else {
				String update_qry = "UPDATE pet_profile PP SET PP.name = '" + jpetprofiles.getName() + "', "
						+ "PP.birth_date ='" + birthday + "', PP.sex ='" + jpetprofiles.getSex() + "'," + " PP.breed='"
						+ jpetprofiles.getBreed() + "' , PP.height='" + jpetprofiles.getHeight() + "' , " + " PP.weight = '"
						+ jpetprofiles.getWeight() + "' , PP.remarks='" + jpetprofiles.getRemarks() + "', "
						+ " PP.imageurl='" + jpetprofiles.getImageurl() + "' ," + " PP.speciesid='" + speciesid
						+ "', PP.enable=" + jpetprofiles.isEnable() + ", PP.user_id=" + userid + ", PP.gateway_id = '"
						+ jpetprofiles.getGateway_id() + "', PP.intact="+ jpetprofiles.isIntact() +","
						+ "PP.structure='"+ jpetprofiles.getStructure() +"' where PP.id="+jpetprofiles.getId()+" ;";
				log.info("pet_profile update qry : " + update_qry );
				int updateStatus = userDao.executeQuery(update_qry);
//				if (updateStatus > 0) {
//					log.info("petprofile updated successful");
//					return 1;
//				}
				return 1;
			}
			
		} catch (Exception e) {
			log.error("Error on saveorupdatePetprofileV5 :"+e.getLocalizedMessage());
		}
		return 0;
	}

	@Override
	public JResponse getGatewayListByFilter(String sKey, String sValue, String fType, String otype, long offset,
			long limit, String oKey, JResponse response) {
		return gatewayDaoV4.getGatewayListByFilter(sKey, sValue, fType, otype, offset, limit, oKey, response);
	}

	@Override
	public JResponse getGatewayByIdWeb(String gatewayId, JResponse response) {
		return gatewayDaoV4.getGatewayByIdWeb(gatewayId, response);
	}

	@Override
	public JResponse listUserPetProfileWeb(long id, JResponse response) {
		return gatewayDaoV4.listUserPetProfileWeb(id, response);
	}

	@Override
	public long getUserIdForGateway(String meid, String username) {
		return gatewayDaoV4.getUserIdForGateway(meid, username);
	}

	@Override
	public Gateway getGatewayByMeid(String meid) {
		return gatewayDaoV4.getGatewayByMeid(meid);
	}

	@Override
	public boolean saveDeviceReplaced(DeviceReplaced deviceReplaced) {
		return gatewayDaoV4.saveDeviceReplaced(deviceReplaced);
	}

	@Override
	public List<GatewayV4Web> setReplacedDevice(List<GatewayV4Web> gatewayV4WebList) {
		log.info(" Entered into setReplacedDevice :: GatewayServiceImplV4 ");
		
		try {
			List<DeviceReplaced> deviceReplacedList = gatewayDaoV4.getReplacedDeviceData();

			if (deviceReplacedList == null || deviceReplacedList.isEmpty()) {
				return gatewayV4WebList;
			}
			
//			Map<String, DeviceReplaced> deviceReplacedMap = deviceReplacedList.stream()
//					.collect(Collectors.toMap(DeviceReplaced::getMeid, Function.identity()));

			Map<String, Integer> deviceReplacedMap = new HashMap<String, Integer>();
			
			for( DeviceReplaced device : deviceReplacedList ) {
				if( device.getIsReplaced()==1 || device.getIsReplaced()==2 )
					deviceReplacedMap.put(device.getMeid(), device.getIsReplaced());
			}
			
			for (GatewayV4Web gatewayV4Web : gatewayV4WebList) {
				String meid = gatewayV4Web.getMeid();

				if (deviceReplacedMap.containsKey(meid)) {
					int deviceStatus = deviceReplacedMap.get(meid);
					gatewayV4Web.setDeviceReplaced(deviceStatus);
				}
			}
			return gatewayV4WebList;
		} catch (Exception e) {
			log.error("list DeviceReplaced : " + e.getLocalizedMessage());
			return gatewayV4WebList;
		}
	}

	@Override
	public LinkedList<JGateway> getGatewaysByInstalledDate(long userid){
		return gatewayDaoV4.getGatewaysByInstalledDate(userid);
	}
	@Override
	public boolean removeDeviceReplaced(long userId, String meid) {
		return gatewayDaoV4.removeDeviceReplaced(userId, meid);
	}

	@Override
	public JUser getUsergatewaydetails(long gatewayId) {
		return gatewayDaoV4.getUsergatewaydetails(gatewayId);
	}
	
	@Override
	public JResponse getUnmappedGatewayListByFilter(String sKey, String sValue, String fType, String otype, long offset,
			long limit, String oKey, JResponse response) {
		return gatewayDaoV4.getUnmappedGatewayListByFilter(sKey, sValue, fType, otype, offset, limit, oKey, response);
	}

	@Override
	public String getGatewayImg(long gatewayid) {
		return gatewayDaoV4.getGatewayImg(gatewayid);
	}

	@Override
	public String getGatewayImgv5(long gatewayid) {
		return gatewayDaoV4.getGatewayImgv5(gatewayid);
	}
	
	@Override
	public boolean removeNotComplitedDeviceReplaced(long userId, String meid) {
		return gatewayDaoV4.removeNotComplitedDeviceReplaced(userId, meid);
	}

	@Override
	public boolean checkRecallQRC(String qrcode) {
		return gatewayDaoV4.checkRecallQRC(qrcode);
	}

	@Override
	public boolean saveTempCalib(TempCalibStatus calibStatus) {
		return gatewayDaoV4.saveTempCalib(calibStatus);
	}

	@Override
	public boolean updateTempUnit(long cmpid, String temperatureunit) {
		return gatewayDaoV4.updateTempUnit(cmpid, temperatureunit);
	}
	
	@Override
	public boolean savePLDelayFreq(PlThresholdStatus plThresholdStatus) {
		return gatewayDaoV4.savePLDelayFreq(plThresholdStatus);
	}

	@Override
	public boolean updateWarrantyPopup(long gateway_id, boolean popup) {
		return gatewayDaoV4.updateWarrantyPopup(gateway_id,popup);
	}

	@Override
	public List<RemoveGatewayType> getRemoveGatewayType() {
		return gatewayDaoV4.getRemoveGatewayType();
	}

	@Override
	public RemoveGatewayType getRemoveGatewayTypeById(int remove_gateway_type_id) {
		return gatewayDaoV4.getRemoveGatewayTypeById(remove_gateway_type_id);
	}

	@Override
	public RemoveGatewayRequest getRemoveGatewayRequest(long user_id) {
		return gatewayDaoV4.getRemoveGatewayRequest(user_id);
	}

	@Override
	public RemoveGatewayRequest saveOrUpdateRemoveGatewayRequest(RemoveGatewayRequest removeGatewayRequest) {
		return gatewayDaoV4.saveOrUpdateRemoveGatewayRequest(removeGatewayRequest);
	}
	
	@Override
	public RemoveGatewayRequestHistory saveOrUpdateRemoveGatewayRequestHistory(RemoveGatewayRequestHistory removeGatewayRequest) {
		return gatewayDaoV4.saveOrUpdateRemoveGatewayRequestHistory(removeGatewayRequest);
	}

	@Override
	public List<RemoveGatewayRequest> getValidRemovalGateway(long user_id) {
		return gatewayDaoV4.getValidRemovalGateway(user_id);
	}

	@Override
	public List<JGatewayInfo> getJGatewayInfo(long user_id) {
		return gatewayDaoV4.getJGatewayInfo(user_id);
	}

	@Override
	public List<Object> getWCDeviceList(long userId, long gatewayId, long monitor_type_id) {
		return gatewayDaoV4.getWCDeviceList(userId, gatewayId, monitor_type_id);
	}

//	@Override
//	public boolean updateNewMeidInOrderMap(long user_id, String new_meid, String old_meid) {
//		return gatewayDaoV4.updateNewMeidInOrderMap(user_id, new_meid, old_meid);
//	}

	@Override
	public int saveOrUpdateGatewayProfile(long gatewayId, long profileId) {
		return gatewayDaoV4.saveOrUpdateGatewayProfile(gatewayId, profileId);
	}

	@Override
	public long getGatewayProfile(long gatewayId) {
		return gatewayDaoV4.getGatewayProfile(gatewayId);
	}

	@Override
	public boolean updateGatewayProfile(long gateway_id, long profile_id) {
		return gatewayDaoV4.updateGatewayProfile(gateway_id, profile_id);
	}

	@Override
	public List<WCDeviceList> getSubDeviceList( String username) {
		return gatewayDaoV4.getSubDeviceList( username);
	}

	@Override
	public int getSubUserCount(long user_id) {
		return gatewayDaoV4.getSubUserCount(user_id);
	}

	@Override
	public GatewayToAlexa getGatewayToAlexaByUserId(long user_id) {
		return gatewayDaoV4.getGatewayToAlexaByUserId(user_id);
	}

	@Override
	public GatewayToAlexa saveOrUpdateGatewayToAlexa(GatewayToAlexa gatewayToAlexa) {
		return gatewayDaoV4.saveOrUpdateGatewayToAlexa(gatewayToAlexa);
	}

	@Override
	public List<WCDeviceListWeb> getWCamDeviceList(long userID) {
		return gatewayDaoV4.getWCamDeviceList(userID);
	}

	@Override
	public String getWC2Thumbnail(String meid) {
		log.info("Entered into getWC2Thumbnail :: meid : "+ meid);
		try {
			
			String url = wowza_thumbnail_url;
			url = url.replace( "meid", meid);
			url = url.replace( "wowzapasscode", wowza_admin_passcode);
			url = url.replace( "wowzaadmindns", wowza_api_admin_dns);
			
			Unirest.setTimeouts(0, 0);
			HttpResponse<String> response = Unirest.get(url)
			  .asString();
			
			byte[] imageBytes = IOUtils.toByteArray( response.getRawBody() );

			if( response.getStatus() != 200 )
				return "NA";
			
			return Base64.getEncoder().encodeToString(imageBytes);
			
		} catch (Exception e) {
			log.error("Error in getWC2Thumbnail :: Error : "+ e.getLocalizedMessage());
		}
		return "NA";
	}
	
	@Override
	public int saveorupdatePetprofileV6(JPetprofile jpetprofiles, Long userid, String birthday, long speciesid) {

		log.info("Entered saveorupdatePetprofileV5 ::");
		try {
			String cutUtc = IrisservicesUtil.getCurrentTimeUTC();
			
			if (jpetprofiles.getId() == 0) {
				
				String ActivityLel = petSpeciesServicesv4.getFindActivitylevel( jpetprofiles.getActivitylevel());
				
				String create_qry = "INSERT INTO pet_profile (gateway_id,NAME,birth_date,sex,breed,height,weight,remarks,"
						+ "imageurl,speciesid,enable,user_id, intact, structure,find_now,activitylevel) VALUES"
						+ "('" + jpetprofiles.getGateway_id() + "','" + jpetprofiles.getName() + "','" + birthday + "','"
						+ jpetprofiles.getSex() + "','" + jpetprofiles.getBreed() + "'" + ",'" + jpetprofiles.getHeight()
						+ "','" + jpetprofiles.getWeight() + "','" + jpetprofiles.getRemarks() + "','"
						+ jpetprofiles.getImageurl() + "','" + speciesid + "'," + jpetprofiles.isEnable() + ","+userid+", "+ jpetprofiles.isIntact() +", '"+ jpetprofiles.getStructure() +"', "+jpetprofiles.getFind_now()+",'"+ActivityLel+"');";
				log.info("pet_profile insert qry : " + create_qry );
				int updateStatus = userDao.executeQuery(create_qry);
				if (updateStatus > 0) {
					log.info("pet profile inserted successful");
					return 1;
				}
				return 0;
			} else {
				String ActivityLel = petSpeciesServicesv4.getFindActivitylevel( jpetprofiles.getActivitylevel());
				
				String update_qry = "UPDATE pet_profile PP SET PP.name = '" + jpetprofiles.getName() + "', "
						+ "PP.birth_date ='" + birthday + "', PP.sex ='" + jpetprofiles.getSex() + "'," + " PP.breed='"
						+ jpetprofiles.getBreed() + "' , PP.height='" + jpetprofiles.getHeight() + "' , " + " PP.weight = '"
						+ jpetprofiles.getWeight() + "' , PP.remarks='" + jpetprofiles.getRemarks() + "', "
						+ " PP.imageurl='" + jpetprofiles.getImageurl() + "' ," + " PP.speciesid='" + speciesid
						+ "', PP.enable=" + jpetprofiles.isEnable() + ", PP.user_id=" + userid + ", PP.gateway_id = '"
						+ jpetprofiles.getGateway_id() + "', PP.intact="+ jpetprofiles.isIntact() +","
						+ "PP.structure='"+ jpetprofiles.getStructure() +"', "
						+ "PP.find_now="+ jpetprofiles.getFind_now() +", "
						+ "PP.updated_on= '"+cutUtc +"',PP.activitylevel = '"+ActivityLel+"' where PP.id="+jpetprofiles.getId()+" ;";
				log.info("pet_profile update qry : " + update_qry );
				int updateStatus = userDao.executeQuery(update_qry);
//				if (updateStatus > 0) {
//					log.info("petprofile updated successful");
//					return 1;
//				}
				return 1;
			}
			
		} catch (Exception e) {
			log.error("Error on saveorupdatePetprofileV5 :"+e.getLocalizedMessage());
		}
		return 0;
	}
	
	@Override
	public int saveorupdatePetprofileV7(JPetprofileFlutter jpetprofiles, Long userid, String birthday, long speciesid) {

		log.info("Entered saveorupdatePetprofileV7 ::");
		try {
			String cutUtc = IrisservicesUtil.getCurrentTimeUTC();
			
			if (jpetprofiles.getId() == 0) {
				
				String ActivityLel = petSpeciesServicesv4.getFindActivitylevel( jpetprofiles.getActivitylevel());
				
				String create_qry = "INSERT INTO pet_profile (`name`,birth_date,sex,breed,height,weight,"
						+ "imageurl,speciesid,enable,user_id, intact, structure,find_now,activitylevel) VALUES"
						+ "('" + jpetprofiles.getName() + "','" + birthday + "','"
						+ jpetprofiles.getSex() + "','" + jpetprofiles.getBreed() + "'" + ",'" + jpetprofiles.getHeight()
						+ "','" + jpetprofiles.getWeight() + "','"
						+ jpetprofiles.getImageurl() + "','" + speciesid + "'," + jpetprofiles.isEnable() + ","+userid+", "+ jpetprofiles.isIntact() +", '"+ jpetprofiles.getStructure() +"', "+jpetprofiles.getFind_now()+",'"+ActivityLel+"');";
				log.info("pet_profile insert qry : " + create_qry );
				int generatedId = userDao.executePetProfileQuery(create_qry);				 
				if (generatedId > 0) {
					log.info("pet profile inserted successful");
					return generatedId;
				}
				return 0;
			} else {
				String ActivityLel = petSpeciesServicesv4.getFindActivitylevel( jpetprofiles.getActivitylevel());
				
				String update_qry = "UPDATE pet_profile PP SET PP.name = '" + jpetprofiles.getName() + "', "
						+ "PP.birth_date ='" + birthday + "', PP.sex ='" + jpetprofiles.getSex() + "'," + " PP.breed='"
						+ jpetprofiles.getBreed() + "' , PP.height='" + jpetprofiles.getHeight() + "' , " + " PP.weight = '"
						+ jpetprofiles.getWeight() + "' , "
						+ " PP.imageurl='" + jpetprofiles.getImageurl() + "' ," + " PP.speciesid='" + speciesid
						+ "', PP.enable=" + jpetprofiles.isEnable() + ", PP.user_id=" + userid + ", "
						+ "PP.intact="+ jpetprofiles.isIntact() +","
						+ "PP.structure='"+ jpetprofiles.getStructure() +"', "
						+ "PP.find_now="+ jpetprofiles.getFind_now() +", "
						+ "PP.updated_on= '"+cutUtc +"',PP.activitylevel = '"+ActivityLel+"' where PP.id="+jpetprofiles.getId()+" ;";
				log.info("pet_profile update qry : " + update_qry );
				int generatedId = userDao.executePetProfileQuery(update_qry);
				return (int) jpetprofiles.getId();
			}
			
		} catch (Exception e) {
			log.error("Error on saveorupdatePetprofileV7 :"+e.getLocalizedMessage());
		}
		return 0;
	}

	@Override
	public GatewayStatus getGatewayStatus(long gatewayId) {
		return gatewayDaoV4.getGatewayStatus(gatewayId);
	}

	@Override
	public GatewayStatus saveOrUpdateGatewayStatus(GatewayStatus gatewayStatus) {
		return gatewayDaoV4.saveOrUpdateGatewayStatus(gatewayStatus);
	}

	@Override
	public List<NightVisionMode> getNightVisionMode(long monitortype) {
		return gatewayDaoV4.getNightVisionMode(monitortype);
	}

	@Override
	public UpgradeDeviceHistory saveOrUpdateUpgradeDeviceHistory(UpgradeDeviceHistory upgradeDeviceHistory) {
		return gatewayDaoV4.saveOrUpdateUpgradeDeviceHistory(upgradeDeviceHistory);
	}

	@Override
	public boolean deleteRemoveGatewayRequest(long user_id, long gateway_id) {
		return gatewayDaoV4.deleteRemoveGatewayRequest(user_id, gateway_id);
	}

	@Override
	public boolean updateOldGatewayProfile(long old_gateway_id, long gateway_id) {
		return gatewayDaoV4.updateOldGatewayProfile(old_gateway_id, gateway_id);
	}

	@Override
	public boolean updatePetProfileGatewayId(long old_gateway_id, long gatewayId) {
		return gatewayDaoV4.updatePetProfileGatewayId(old_gateway_id, gatewayId);
	}

	@Override
	public boolean updateGatewayNameByPetProfile(long gatewayId) {
		return gatewayDaoV4.updateGatewayNameByPetProfile(gatewayId);
	}

	@Override
	public boolean updateTimeZoneLastGatewayReport(long gateway_id, String time_zone) {
		return gatewayDaoV4.updateTimeZoneLastGatewayReport(gateway_id, time_zone);
	}

	@Override
	public GatewayPendingEvent getGatewayPendingEvent(long gateway_id) {
		return gatewayDaoV4.getGatewayPendingEvent(gateway_id);
	}

	@Override
	public GatewayPendingEvent saveOrUpdateGatewayPendingEvent(GatewayPendingEvent gatewayPendingEvent) {
		return gatewayDaoV4.saveOrUpdateGatewayPendingEvent(gatewayPendingEvent);
	}

	@Override
	public boolean delGatewayFeature(long id) {
		return gatewayDaoV4.delGatewayFeature(id);
	}

	@Override
	public ArrayList<JCategory> getProductCategory() {
		return gatewayDaoV4.getProductCategory();
	}

	@Override
	public ArrayList<JSensorType> getSensorType() {
		return gatewayDaoV4.getSensorType();
	}
	
	@Override
	public List<Object> getSensorList(long userId, long gatewayId, long monitor_type_id, String reqVer) {
		return gatewayDaoV4.getSensorList(userId, gatewayId, monitor_type_id, reqVer);
	}
	
	@Override
	public List<Object> getSensorListByCode(long userId, long gatewayId, String sensorCode, String os) {
		return gatewayDaoV4.getSensorListByCode(userId, gatewayId, sensorCode, os);
	}
	
	@Override
	public ArrayList<JSensorTypeCode> getSensorTypeCode() {
		return gatewayDaoV4.getSensorTypeCode();
	}

	@Override
	public boolean checkOrderWithinRange(long gatewayId, String from, String to) {
		return gatewayDaoV4.checkOrderWithinRange(gatewayId, from, to);
	}

	@Override
	public boolean insertDeviceSub(long userid, long gatewayid, String instal_date,long monitortypeid,long sales_channel,boolean isgps) {
		return gatewayDaoV4.insertDeviceSub(userid, gatewayid, instal_date,monitortypeid,sales_channel,isgps);
	}

	@Override
	public boolean getGnameExist(String gname, long gateway_id,long cmp_id) {
		return gatewayDaoV4.getGnameExist(gname, gateway_id,cmp_id);
	}

	@Override
	public boolean updateMeidForGateway(long gateway_id) {
		return gatewayDaoV4.updateMeidForGateway(gateway_id);
	}
	
	@Override
	public List<JPetmonitorHistory> getPetMonitorHistory(long gatewayId, String tempunit, String timezone) {
		return gatewayDaoV4.getPetMonitorHistory(gatewayId, tempunit, timezone);
	}

	@Override
	public boolean isGpsDevice(long gateway_id) {
		return gatewayDaoV4.isGpsDevice(gateway_id);
	}

	@Override
	public boolean isDeviceConfigured(long userId) {
		return gatewayDaoV4.isDeviceConfigured(userId);
	}
	
	@Override
	public List<JGatewayInfo> getJGatewayInfo(long user_id,long monitorType) {
		return gatewayDaoV4.getJGatewayInfo(user_id,monitorType);
	}
	
	@Override
	public boolean updateDevicebasedSubGatewayId(long old_gateway_id, long gatewayId) {
		return gatewayDaoV4.updateDevicebasedSubGatewayId(old_gateway_id,gatewayId);
	}

	@Override
	public List<JPetprofileFlutter> getPetprofileList(long user_id) {

		return gatewayDaoV4.getPetprofileList(user_id);
	}

	@Override
	public boolean saveUserDeviceSpot(long userId, String gatewayId, String devicePlaceType, String devicePlace) {

		return gatewayDaoV4.saveUserDeviceSpot(userId, gatewayId, devicePlaceType, devicePlace);
	}

	@Override
	public List<Map<String, Object>> getUserDeviceSpot() {

		List<Object[]> results = gatewayDaoV4.getUserDeviceSpot();

		Map<Long, Map<String, Object>> categoryMap = new HashMap<>();

		for (Object[] row : results) {
			Long categoryId = ((Number) row[0]).longValue();
			String categoryTitle = (String) row[1];
			Long typeId = ((Number) row[2]).longValue();
			String typeValue = (String) row[3];

			categoryMap.computeIfAbsent(categoryId, k -> {
				Map<String, Object> category = new HashMap<>();
				category.put("title", categoryTitle);
				category.put("types", new ArrayList<Map<String, Object>>());
				return category;
			});

			Map<String, Object> type = new HashMap<>();
			type.put("id", typeId);
			type.put("value", typeValue);

			((List<Map<String, Object>>) categoryMap.get(categoryId).get("types")).add(type);
		}

		for (Map<String, Object> category : categoryMap.values()) {
			Map<String, Object> othersType = new HashMap<>();
			othersType.put("id", 0);
			othersType.put("value", "others");

			((List<Map<String, Object>>) category.get("types")).add(othersType);
		}

		return new ArrayList<>(categoryMap.values());
	}

	@Override
	public boolean getUserDeviceSpotByUserId(long userId, long gatewayId) {

		return gatewayDaoV4.getUserDeviceSpotByUserId(userId, gatewayId);
	}

	@Override
	public FotaUpdate getFotaUpdate(String gatewayId) {

		FotaUpdate fotaUpdate = gatewayDaoV4.getFotaUpdate(gatewayId);

		if(fotaUpdate.getCurrentFotaVersion().equalsIgnoreCase("na")) {
			Inventory inventory = niomDao.getInventoryByMeid(fotaUpdate.getMeid());
			fotaUpdate.setCurrentFotaVersion(inventory.getCurr_fota_version());
		}

		return fotaUpdate;
	}

	@Override
	public boolean updateLatestFotaVersion(String gatewayId, String updatedFotaVersionNumber){
		return gatewayDaoV4.updateLatestFotaVersion(gatewayId,updatedFotaVersionNumber);

	}

	@Override
	public boolean saveMinicamshippingaddres(Minicamshipping jminicamshipping) {
		return gatewayDaoV4.saveMinicamshippingaddres(jminicamshipping);
	}

	@Override
	public boolean checkN12fotaupdateAvailable(String fotaUpdateVersion, Long gatewayId) {
		return gatewayDaoV4.checkN12fotaupdateAvailable(fotaUpdateVersion,gatewayId);
	}

	@Override
	public ArrayList<JProductWithSubCategory> getProductList() {
		return gatewayDaoV4.getProductList();
	}


	@Override
	public List<JGatewayInfo> getJGatewayInfoV1(long userId, long monitorTypeId, long gateWayId){

		return gatewayDaoV4.getJGatewayInfoV1(userId, monitorTypeId, gateWayId);
	}


	@Override
	public boolean saveBleFotaDebug(JBleFotaDebug jBleFotaDebug) {

		return gatewayDaoV4.saveBleFotaDebug(jBleFotaDebug);
	}

	@Override
	public List<String> getBleFotaCommands(long gatewayId) {

		return gatewayDaoV4.getBleFotaCommands(gatewayId);
	}

	@Override
	public boolean deleteBleFotaCommands(long gatewayId) {

		return gatewayDaoV4.deleteBleFotaCommands(gatewayId);
	}

	@Override
	public Minicamshipping getExistingMiniCamShippingInfo(long userId, long gatewayId) {

		return gatewayDaoV4.getExistingMiniCamShippingInfo(userId, gatewayId);
	}

	@Override
	public void populateShowBillingPopupKeyForGateways(List<JProductSubResV2> gatewayList) {

        log.info("Entered into populateShowBillingPopupKeyForGateways :: gatewayList size : {}", gatewayList.size());

		for (JProductSubResV2 gateway : gatewayList) {
			Minicamshipping minicamshipping = gatewayDaoV4.getExistingMiniCamShippingInfo(0L, gateway.getGateway_id());
			gateway.setShowBillingPopup(minicamshipping.getAddress1().equalsIgnoreCase("NA"));
		}
	}
}
