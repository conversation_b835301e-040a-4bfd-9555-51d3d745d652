package com.nimble.irisservices.service.impl;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimble.irisservices.dto.*;
import com.nimble.irisservices.entity.*;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IChargebeeService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.gson.Gson;
import com.nimble.irisservices.dao.ICreditSystemDao;
import com.nimble.irisservices.dto.JTerms.Terms;
import com.nimble.irisservices.helper.HttpRequest;
import com.nimble.irisservices.service.IAvatarService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IGatewayServiceV4;

@Service
@Transactional
public class CreditSystemServiceImpl implements ICreditSystemService {
	@Autowired
	ICreditSystemDao crDao;

	@Autowired
	IAvatarService iAvatarService;
	
	@Value("${recharge.url}")
	private String recharge_url;
	
	@Value("${schedule_url}")
	private String schedule_url;
	
	@Value("${recharge.access-token}")
	private String access_token;
	
	@Value("${orderdate_trial}")
	private String orderdate_trial;
	
	@Value("${offer_days}")
	private int offer_days;
	
	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportcontactnumber;

	@Value("#{${supportemail}}")
	private Map<String,String> supportemail;
	
	@Value("#{${flexi_plan_details}}")
	private List<String> flexi_plan_details;

	@Value("${compare_image_other_products}")
	private String compare_image_other_products;
	
	@Value("${coupon_img_new}")
	private String coupon_img_new;
	@Value("${coupon_desc_new}")
	private String coupon_desc_new;
	@Value("${coupon_code_new}")
	private String coupon_code_new;
	
	@Value("${coupon_img_update}")
	private String coupon_img_update;
	@Value("${coupon_desc_update}")
	private String coupon_desc_update;
	@Value("${coupon_code_update}")
	private String coupon_code_update;

	@Value("${coupon_img_upgrade}")
	private String coupon_img_upgrade;
	@Value("${coupon_desc_upgrade}")
	private String coupon_desc_upgrade;
	@Value("${coupon_code_upgrade}")
	private String coupon_code_upgrade;

	@Value("${coupon_note}")
	private String coupon_note;

	@Autowired
	@Lazy
	ICreditSystemService crService;
	
	@Autowired
	IGatewayServiceV4 gatewayServiceV4;
	
	@Autowired
	HttpRequest httpRequest;

	@Autowired
	Helper helper;

	@Autowired
	@Lazy
	IChargebeeService cbService;

	private static final Logger log = LogManager.getLogger(CreditSystemServiceImpl.class);

	public boolean createSubscriptionPlan(SubscriptionPlan plan) throws Exception {
		return crDao.saveOrUpdateSubscriptionPlan(plan);
	}

	public List<SubscriptionPlan> listSubscriptionPlan()throws Exception{
		return crDao.listSubscriptionPlan();
	}
	
	public boolean deleteSubscriptionPlan(long id) throws Exception {
		return crDao.deleteSubscriptionPlan(id);
	}

	public boolean createPlanToMonitorType(PlanToMonitorType plan) throws Exception {
		return crDao.saveOrUpdatePlanToMonitorType(plan);
	}
	
	public PlanToMonitorType viewPlanToMonitorType(long id)throws Exception{
		return crDao.getPlanToMonitorTypeById(id);
	}
	
	public List<PlanToMonitorType> listPlanToMonitorType(){
		return crDao.listPlanToMonitorType();
	}

	public boolean deletePlanToMonitorType(long id) throws Exception {
		return crDao.deletePlanToMonitorType(id);
	}
	
	public boolean createPlanToPeriod(PlanToPeriod plan) throws Exception {
		return crDao.saveOrUpdatePlanToPeriod(plan);
	}
	
	public PlanToPeriod viewPlanToPeriod(long id)throws Exception{
		return crDao.getPlanToPeriodById(id);
	}
	
	public List<PlanToPeriod> listPlanToPeriod() throws Exception{
		return crDao.listPlanToPeriod();
	}

	public boolean deletePlanToPeriod(long id) throws Exception {
		return crDao.deletePlanToPeriod(id);
	}

	public int getPlanToPeriodByName(String chargebee_planid) {
		return crDao.getPlanToPeriodByName(chargebee_planid);
	}
	
	public int getPlanFreeTrialPeriodByName(String chargebee_planid) {
		return crDao.getPlanFreeTrialPeriodByName(chargebee_planid);
	}
	
	public SubscriptionPlan getSubsPlanById(long id) {
		return crDao.getSubsPlanById(id);
	}
	
	
	public SubscriptionPeriod getSubsPeriodById(long id) {
		return crDao.getSubsPeriodById(id);
	}

	
	public List<SubscriptionPeriod> listSubPeriod() throws Exception{
		return crDao.listSubPeriod();
	}
	
	
	public boolean createPlanToUpgrade(PlanToUpgrade plan) throws Exception {
		return crDao.saveOrUpdatePlanToUpgrade(plan);
	}
	
	 
	public List<PlanToUpgrade> listPlanToUpgrade() throws Exception{
		return crDao.listPlanToUpgrade();
	}

	
	public boolean deletePlanToUpgrade(long id) throws Exception {
		return crDao.deletePlanToUpgrade(id);
	}
	
	
	public Map<String, Object> getAvailUpgradePlans(long planid, long periodid,long userid,String country) {
		return crDao.getAvailUpgradePlans(planid, periodid,userid,country);
	}

	public Map<String, Object> getIosAvailUpgradePlan(long planid, long periodid,long userid,long monitortype,String country) {
		return crDao.getIosAvailUpgradePlan(planid, periodid, userid, monitortype, country);
	}
	
	public Map<String, Object> getAvailUpgradePlanNew(long planid, long periodid,long userid,long monitortype,String country){
		return crDao.getAvailUpgradePlanNew(planid, periodid, userid,monitortype,country);
	}
	
	
	public Map<String, Object> getAvailUpgradePlanV4(long planid, long periodid,long userid,long monitortype,String country){
		return crDao.getAvailUpgradePlanV4(planid, periodid, userid,monitortype,country);
	}

	
	public ArrayList<Integer> getPlanAndPeriod(String chargebee_planid) {
		return crDao.getPlanAndPeriod(chargebee_planid);
	}

	
	public String[] getChargebeePlanById(long planid, long periodid, String country_code) {
		return crDao.getChargebeePlanById(planid, periodid, country_code);
	}

	
	public ArrayList<String> getDeviceConfig(long planid) {
		return crDao.getDeviceConfig(planid);
	}
	
	
	public int getDeviceConfigV4(long userid,long planid) {
		return crDao.getDeviceConfigV4(userid,planid);
	}

	public int getDeviceConfigV5(long userid,long planid) {
		return crDao.getDeviceConfigV4(userid,planid);
	}

	
	public Map<String, Object> getplanlist(String qry, String country) {
		return crDao.getplanlist(qry,null,0,0,country);
	}

	
	public long getDeviceCountByUser(long userid, long monitor_id) {
		return crDao.getDeviceCountByUser(userid, monitor_id);
	}

	
	public List<Object> getDeviceCountByMonitorType(long userid, long monitortype) {
		return crDao.getDeviceCountByMonitorType(userid, monitortype);
	}
	
	
	public LinkedHashMap<String, Gateway> getGatewaysByReportTime(long userid, int monitortype) {
		return crDao.getGatewaysByReportTime(userid, monitortype);
	}

	@Override
	public FeatureType viewFeatureType(long id) throws Exception {
		return crDao.viewFeatureType(id);
	}

	@Override
	public List<FeatureType> listFeatureType() throws Exception {
		return crDao.listFeatureType();
	}

	
	public boolean createFeature(Feature feature) throws Exception {

		return crDao.saveOrUpdateFeature(feature);
	}
	
	public boolean deleteFeature(long id) throws Exception {
		return crDao.deleteFeature(id);
	}
	
	public Feature viewFeature(long id) throws Exception {
		return crDao.getFeatureById(id);
	}
	
	public Feature viewFeatureByname(String name) {
		return crDao.getFeatureByName(name);
	}
	
	public long getFeatureId(String fName) {
		return crDao.getFeatureId(fName);
	}
	
	public List<Feature> listFeature() throws Exception {
		return crDao.listFeature();
	}
	
	public boolean upateCredits(CompanyCreditMonitor ccm) throws Exception {
		return crDao.upateCredits(ccm);
	}

	public boolean upadteTransactionSummary(CompanyTransactionSummary cts) throws Exception {
		return crDao.upadteTransactionSummary(cts);
	}
	
	public CompanyCreditMonitor getCompanyCreditMonitorByCmpy(long cmp_id) {
		return crDao.getCompanyCreditMonitorByCmpy(cmp_id);
	}
	
	public boolean createUsertoFeature(UsertoFeature userfeature) throws Exception {
		return crDao.saveOrUpdateUsertoFeature(userfeature);
	}
	
	public boolean deleteUsertoFeature(long id) throws Exception {
		return crDao.deleteUsertoFeature(id);
	}
	
	public List<UsertoFeature> listUsertoFeature(long user_id) throws Exception {
		return crDao.listUsertoFeature(user_id);
	}
	
	public boolean createPlantoFeature(PlanToFeature plan) throws Exception {
		return crDao.saveOrUpdatePlantoFeature(plan);
	}
	
	public boolean deletePlantoFeature(long id) throws Exception {
		return crDao.deletePlantoFeature(id);
	}
	
	public ArrayList<String> getFeatureList(long userid, long ptp_id){
		return crDao.getFeatureList(userid, ptp_id);
	}
	
	public int getUserFeatureAvailabilty(long userid, String fName,long plan_id) {
		return crDao.getUserFeatureAvailabilty(userid, fName, plan_id);
	}
	
	public ArrayList<Integer> getVPMAvailabilty(long userid, String fCode,long plan_id,String cb_planid) {
		return crDao.getVPMAvailabilty(userid, fCode, plan_id,cb_planid);
	}
	public List<PlanToFeature> listPlantoFeature(long planid) throws Exception {
		return crDao.listPlantoFeature(planid);
	}

	public PlanToFeature getPFByPlanFeature(long planid, long featureid) {
		return crDao.getPFByPlanFeature(planid, featureid);
	}
	
	public UsertoFeature getUFByUserFeature(long userid, long featureid) {
		return crDao.getUFByUserFeature(userid, featureid);
	}
	
	public boolean updateUserTransaction(UserTransaction uTxn) throws Exception{
		return crDao.updateUserTransaction(uTxn);
	}

	public boolean updateUserTransactionHistory(UserTransactionHistory uTxnHistory) throws Exception{
		return crDao.updateUserTransactionHistory(uTxnHistory);
	}
	
	public UserTransaction getUserTransactionByUserFeature(long user_id,String feature) {
		return crDao.getUserTransactionByUserFeature(user_id, feature);
	}
//	
//	public List<UserTransaction> getUserTransactionByUser(long user_id){
//		return crDao.getUserTransactionByUser(user_id);
//	}
	
	public UserTransactionHistory getUserTransactionHistoryByUserFeature(long user_id, String feature,String date) {
		return crDao.getUserTransactionHistoryByUserFeature(user_id, feature,date);
	}
//	
//	public List<UserTransactionHistory> getUserTransactionHistoryByUser(long user_id){
//		return crDao.getUserTransactionHistoryByUser(user_id);
//	}
	
	@Override
	public ResetType viewResetType(long id) throws Exception{
		return crDao.viewResetType(id);
	}
	
	@Override
	public List<ResetType> listResetType() throws Exception {
		return crDao.listResetType();
	}
	
	@Override
	public boolean saveOrUpdateUserTxn(long user_id,long feature_id) {
		return crDao.saveOrUpdateUserTxn(user_id, feature_id);
	}
	
	@Override
	public boolean saveOrUpdateUserTxnHistory(long user_id,long feature_id) {
		return crDao.saveOrUpdateUserTxnHistory(user_id, feature_id);
	}
	
	@Override
	public float getIosPlanPrice(long planid,long periodid) {
		return crDao.getIosPlanPrice(planid,periodid);
	}
	
	@Override
	public VersionMapping getVersionMapping(String app_version, String os_type) {
		return crDao.getVersionMapping(app_version, os_type);
	}
	
	@Override
	public JSubscriptionPlanReport getInappSubscriptionByUser(long userid) {
		return crDao.getInappSubscriptionByUser(userid);
	}

	@Override
	public List<JVPMPlan> getVPMPlanList(String plantype) {
		return crDao.getVPMPlanList(plantype);
	}

	@Override
	public int getVPMPlanTxnCount(String cb_planid) {
		return crDao.getVPMPlanTxnCount(cb_planid);
	}
	
	@Override
	public List<PlanToPeriod> listPlanToPeriodByPlanId(long plan_id) {
		return crDao.listPlanToPeriodByPlanId(plan_id);
	}

	@Override
	public String[] getCouponId(String cbPlanid) {
		return crDao.getCouponId(cbPlanid);
	}

	@Override
	public JResponse upgradePlanList(long userid, long plan_id, long period_id,boolean freetrial,String country) {
		return crDao.upgradePlanList(userid, plan_id, period_id,freetrial,country);

	}

	@Override
	public String getPlanVersion(String cb_planid) {
		return crDao.getPlanVersion(cb_planid);
	}

	@Override
	public ArrayList<JFeatureCredit> getSettingFeatures(long user_id) {
		return crDao.getSettingFeatures(user_id);
	}
	
	@Override
	public List<JAddonPlan> getAddonPlanList(long plan_id, long period_id) {
		return crDao.getAddonPlanList(plan_id,period_id);
	}
	
	@Override
	public ArrayList<JAlertRemaining> getalertslimit(long user_id,String alertlimit_basedon) {
		return crDao.getalertslimit(user_id,alertlimit_basedon);
	}

	@Override
	public boolean saveOrUpdateUserFeatureCount(long user_id, long feature_id) {
		return crDao.saveOrUpdateUserFeatureCount(user_id, feature_id);
	}
	
	@Override
	public List<JGatewaySubSetup> checkDeviceConfigStatusV2(long planid, long userid, int days_remaining) {
		return crDao.checkDeviceConfigStatusV2(planid, userid, days_remaining) ;
	}

	@Override
	public JProductSubscription getCBPlan(String order_id) {
		return crDao.getCBPlan(order_id);
	}
	
	@Override
	public boolean updateSubStatus(String order_id,String user_id) {
		return crDao.updateSubStatus(order_id,user_id);
	}
	
	@Override
	public boolean updateReSubStatus(String order_id,String cb_sub_status) {
		return crDao.updateReSubStatus(order_id,cb_sub_status);
	}

	@Override
	public JResponse getalertslimitV2(long user_id, String alertlimit_basedon,boolean enable_appnotify,String reqVer) {
		return crDao.getalertslimitV2(user_id, alertlimit_basedon, enable_appnotify,reqVer);
	}
	
	@Override
	public JResponse getplanoffers(String plan_ver,long planid,long periodid, String country) {
		return crDao.getplanoffers(plan_ver,planid,periodid, country);
	}
	
	@Override
	public String[] getOfferStatus(String chargebee_planid) {
		return crDao.getOfferStatus(chargebee_planid);
	}

	@Override
	public boolean updateUserIdInProsuctSubs(String valueOf, long user_id) {
		return crDao.updateUserIdInProsuctSubs(valueOf, user_id);
	}

	@Override
	public String getNextRenewalDate(long user_id) {
		return crDao.getNextRenewalDate(user_id);
	}

	@Override
	public JResponse getalertslimitV5(long id, String alertlimit_basedon, boolean enable_appnotify, String reqVer,
			boolean smsEnable, boolean emailEnable, boolean appNotifyEnable, long gatewayId) {
		return crDao.getalertslimitV5(id, alertlimit_basedon, enable_appnotify,reqVer, smsEnable, emailEnable, appNotifyEnable, gatewayId);
	}

	@Override
	public JResponse upgradePlanList_v5(UserV4 user, long plan_id, long period_id,boolean freetrial,String country, String type) {
		return crDao.upgradePlanList_v5(user, plan_id, period_id,freetrial,country, type);
	}
	
	@Override
	public int getMaxDeviceCount(long planid) {
		return crDao.getMaxDeviceCount(planid);
	}

	@Override
	public LinkedList<String> listPlanBenefits(long periodid) {
		return crDao.listPlanBenefits(periodid);
	}

	@Override
	public boolean checkAdditionalBenifits(String user_name) {
		return crDao.checkAdditionalBenifits(user_name);
	}

	@Override
	public boolean checkAdditionalBenifitsCreated(String user_name,int periodId) {
		return crDao.checkAdditionalBenifitsCreated(user_name,periodId);
	}
	
	@Override
	public boolean insertAddiUser(String username) {
		return crDao.insertAddiUser(username);
	}
	
	@Override
	public JResponse generateSubCoupon(UserV4 user, String period, String due_date) {
		log.info("Entered generateCancelCoupon...");
		boolean benefits_created = false;
		JResponse resp1 = new JResponse();
		try {
			if (!benefits_created) {
//				Calendar fromCal = Calendar.getInstance();
//				int mCnt = 6;
//				if(periodid ==4)
//					mCnt = 12;
//				else if(periodid ==5)
//					mCnt = 24;
//				
//				fromCal.set(Calendar.MONTH, (fromCal.get(Calendar.MONTH) +mCnt));
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				Date prevFormat = sdf.parse(due_date);
				SimpleDateFormat sdf1 = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
				String dueDate = sdf1.format(prevFormat.getTime());
	
				JCouponData couponObj = new JCouponData(user.getId(), user.getEmail(), period, user.getFirstname(),
						user.getLastname(), dueDate);
				
				SubscriptionPeriod subPeriod = getSubsPeriodById(Long.parseLong(period));
				resp1 = iAvatarService.generateSubCoupon(couponObj, subPeriod.getPeriod_name());
				log.info("generatedAddiBenefits:"+resp1.toString());
	
				if ((int) resp1.get("Status") == 1) {
					benefits_created = true;
				}
			} else
				benefits_created = true;
		} catch (Exception e) {
			log.error("err generateAddiBenefits:" + e.getLocalizedMessage());
		}
		return resp1;
	}

	@Override
	public JPlanToUpgrade getPlanToUpgrade(int upgrade_plan_to_period_id, int cur_plan_to_period_id) {
		return crDao.getPlanToUpgrade(upgrade_plan_to_period_id, cur_plan_to_period_id);
	}

	@Override
	public JPlanInfo getPlan(long plan_id) {
		return crDao.getPlan(plan_id);
	}

	@Override
	public UserRetained getUserRetainedById(long id) {
		return crDao.getUserRetainedById(id);
	}

	@Override
	public UserRetained getUserRetainedByUserId(long user_id) {
		return crDao.getUserRetainedByUserId(user_id);
	}

	@Override
	public UserRetained saveOrUpdateUserRetained(UserRetained userRetained) {
		return crDao.saveOrUpdateUserRetained(userRetained);
	}

	@Override
	public boolean checkRenewalDateInUserRetained(long user_id) {
		return crDao.checkRenewalDateInUserRetained(user_id);
	}

	@Override
	public boolean cancelRechargeSubscription(String username) {
		log.info("Entered into cancelRechargeSubscription :: username : "+ username);
		try {
			boolean status = false;
			String subsId = crService.getRechargeSubscriptionId( username );
			
			if( subsId.equalsIgnoreCase("NA") )
				return false;
			
			String url = recharge_url + "/subscriptions/"+ subsId +"/cancel"; 
			
			Map<String, String> header = new HashMap<>();
			Map<String, Object> jsonBody = new HashMap<>();
			
			header.put("X-Recharge-Version", "2021-11");
			header.put("X-Recharge-Access-Token", access_token);
			
			jsonBody.put("cancellation_reason", "other reason");
			jsonBody.put("send_email", false);
			
			HttpRequest http = new HttpRequest();
			HttpResponse response = http.httpReq("Recharge Cancel Sub", "POST", url, header, jsonBody);
			log.info("recharge cancel response :: res : "+ response.toString());
			
			if( response.getResponse_code() == 200 )
				return true;
		} catch (Exception e) {
			log.error("Error in cancelRechargeSubscription :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public String getRechargeSubscriptionId(String username) {
		return crDao.getRechargeSubscriptionId(username);
	}
	
	@Override
	public PlanMigration getCurrentValidPlan(long planid, long periodid) {
		return crDao.getCurrentValidPlan(planid, periodid);
	}
	@Override
	public long findaqiqndcoenabledevice(long user_id) {
		return crDao.findaqiqndcoenabledevice(user_id);
	}

	@Override
	public JResponse getupgradesubplansV5(UserV4 user, long curplan_id, long curperiod_id, long monitor_type,
			String country, boolean defaultFreePlan,long gatewayid, String type) {
		return crDao.getupgradesubplansV5(user, curplan_id, curperiod_id, monitor_type, country, defaultFreePlan,gatewayid,type);
	}

	@Override
	public long getplanByCountryCode(long planID, String Country, long monitorType) {
		return crDao.getPlanIdByCountry(planID, Country, monitorType);
	}
	@Override
	public JGatewayFeature getGatewayFeatureById(Long gateway_id) {
		return crDao.getGatewayFeatureById(gateway_id);
	}

	@Override
	public List<JGatewayFeature> getGatewayFeaturesByPlanId(int plan_id, String sub_id) {
		return crDao.getGatewayFeaturesByPlanId(plan_id, sub_id);
	}
	
	@Override
	public boolean assignGatewayFeature(UserV4 user, JSubManage jSubDetail) {
		try {
			if (jSubDetail != null) {

				String msurl = schedule_url + "/v5.0/gatewayfeature";

				HashMap<String, String> headers = new HashMap<String, String>();
				headers.put("auth", user.getAuthKey());

				Map<String, Object> subMap = new HashMap<>();
				subMap.put("planid", jSubDetail.getPlanid());
				subMap.put("chargebeeid", jSubDetail.getChargebeeid());
				subMap.put("cbsubid", jSubDetail.getCbsubid());
				subMap.put("periodid", jSubDetail.getPeriodid());
				subMap.put("gatewayid", jSubDetail.getGatewayid());
				
				
				HttpResponse response = httpRequest.httpReq("GatewayMapping", "POST", msurl, headers, subMap);

				
				log.info("Plan Url update : " + response);
			}
			return true;
		} catch (Exception e) {
			log.error("Error assignGatewayFeature:" + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public boolean assignGatewayFeatureForPetMonitor(UserV4 user, JSubManage jSubDetail,
													 String eventType, boolean is_flexi_paused) {
		try {
			if (jSubDetail != null) {

				String msurl = schedule_url + "/v4.0/gatewayfeaturewithmonitor?chargbeeid="+jSubDetail.getChargebeeid()
						+"&subId="+jSubDetail.getCbsubid()+"&gatewayid="+jSubDetail.getGatewayid().get(0)
						+"&cbplan="+jSubDetail.getCbPlan()+"&eventtype="+eventType
						+"&cbemail="+user.getEmail()+"&isupgrade=false" +
						"&is_flexi_paused="+is_flexi_paused;

				HashMap<String, String> headers = new HashMap<String, String>();
				headers.put("auth", user.getAuthKey());

				Map<String, Object> subMap = null;

				HttpResponse response = httpRequest.httpReq("GatewayMapping", "POST", msurl, headers, subMap);


				log.info("Plan Url update : " + response);
			}
			return true;
		} catch (Exception e) {
			log.error("Error assignGatewayFeature:" + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public String getMeariKey(long userID, long gatewayId, int period_id) {
		return crDao.getMeariKey(userID, gatewayId, period_id);
	}

	@Override
	public boolean isUpgradeAvailable(int plan_id, int period_id) {
		return crDao.isUpgradeAvailable(plan_id, period_id);
	}

	@Override
	public long getMonitorTypeByCBPlan(String subs_planid) {
		return crDao.getMonitorTypeByCBPlan(subs_planid);
	}

	@Override
	public boolean updateSubsStatus(String status, String subId) {
		return crDao.updateSubStatus(status,subId);
	}

	@Override
	public CancelCustomerRetain getCancelCustomerRetain(long user_id) {
		return crDao.getCancelCustomerRetain(user_id);
	}

	@Override
	public CancelCustomerRetain saveOrUpdateCancelCustomerRetain(CancelCustomerRetain cancelCustomerRetain) {
		return crDao.saveOrUpdateCancelCustomerRetain(cancelCustomerRetain);
	}

	@Override
	public boolean saveOrUpdatePauseHistory(String cbsubid, long userId, String date,int status, String feedbackid, String review) {
		return crDao.saveOrUpdatePauseHistory(cbsubid, userId, date, status, feedbackid, review);
	}

	@Override
	public JPauseHistory getPauseHistoryRecord(String cbsubid) {
		return crDao.getPauseHistoryRecord(cbsubid);
	}
	
	@Override
	public ArrayList<JFeatureCredit> getSettingGatewayFeatures(long gateway_id) {
		return crDao.getSettingGatewayFeatures(gateway_id);
	}
	
	@Override
	public long findaqiqndcoenabledevicebyGateway(long gateway_id) {
		return crDao.findaqiqndcoenabledevicebyGateway(gateway_id);
	}

	@Override
	public JResponse getalertslimitV5ByGateway(long id, String alertlimit_basedon, boolean enable_appnotify, String reqVer,
			boolean smsEnable, boolean emailEnable, boolean appNotifyEnable, long gatewayId) {
		return crDao.getalertslimitV5ByGateway(id, alertlimit_basedon, enable_appnotify, reqVer, smsEnable, emailEnable, appNotifyEnable, gatewayId);
	}
	
	@Override
	public int getDeviceConfigV4Gateway(long gatewayid,long planid) {
		return crDao.getDeviceConfigV4Gateway(gatewayid,planid);
	}
	
	@Override
	public JPauseHistory getPauseHistoryRecordAllCBS(String cbsubid) {
		return crDao.getPauseHistoryRecordAllCBS(cbsubid);
	}
	
	@Override
	public JGatewayFeature getGatewayFeatureByIdWithoutactive(Long gateway_id) {
		return crDao.getGatewayFeatureByIdWithoutactive(gateway_id);
	}
	
	@Override
	public boolean getMeariPlanExpired(long userID, long gatewayId, int iris_speriod) {
		return crDao.getMeariPlanExpired(userID,gatewayId,iris_speriod);
	}

	@Override
	public JResponse getUpgradeSubPlanV6(UserV4 user, long curplan_id, long curperiod_id, long monitor_type, String country,
										 boolean defaultFreePlan, long gatewayid, String type, String plantype, String sub_id, boolean restrict_flexi_plan_period, boolean is_flexi) {
		log.info("Entered into upgradePlanList_v5 :: user_id : " + user.getId());
		JResponse response = new JResponse();
		try {
			JPlanInfo plan = crService.getPlan(curplan_id);
			if (plan == null) {
				plan = new JPlanInfo();
			}
			List<Object[]> planResp = crDao.getUpgradeSubPlanV6(user, curplan_id, curperiod_id, monitor_type,
					country, defaultFreePlan, gatewayid, type,plantype, is_flexi);
			if (planResp != null) {
				long plan_id = 0;
				long per_id = 0;
				String content1 = "";
				String period_name = "";
				String free_buynow="";
				String strike_price="";
				String plan_price = "";
				String display_price="";
				boolean is_free = false;
				List<JSubPlanDetailsV2> planDetailList = new ArrayList<JSubPlanDetailsV2>();
				String plan_name ="";
				int free_trial_days = 0;
				String display_msg="";
				String billed_price = "";
				String month_price = "";
				boolean is_best_deal = false;
				boolean has_free = false;

				outerLoop : for(int j=0;j<planResp.size();j++) {
					int size = planResp.size();
					log.info("upgradePlanList : size:" + size);
					JSubPlanDetailsV2 planDetail = new JSubPlanDetailsV2();
					Object[] tuple = (Object[]) planResp.get(0);

					long free_plan = 0;
					long free_period = 0;
					boolean isfree_eligible = false;
					JPlanDetail free_features=null;
					JSubPeriodDetail periodObj = new JSubPeriodDetail();
					ArrayList<JSubPeriodDetail> periodlist = new ArrayList<JSubPeriodDetail>();

					boolean orderAvail = false;
					if(monitor_type == 6 && gatewayid>0 && defaultFreePlan) {
						String[] dtRange = orderdate_trial.split(":");

						orderAvail = gatewayServiceV4.checkOrderWithinRange(gatewayid, dtRange[0], dtRange[1]);
					}
					if (size == 1) {
						plan_id = ((BigInteger) tuple[0]).longValue();
						plan_name = (String) tuple[1];
						per_id = ((BigInteger) tuple[2]).longValue();
						String feature_list = (String) tuple[3];
						JPlanDetail jplandetail = new JPlanDetail();

						if(restrict_flexi_plan_period && per_id <= curperiod_id){
							continue;
						}
						try {

							jplandetail = new Gson().fromJson(feature_list, JPlanDetail.class);

						} catch (JSONException err) {
							log.error("jplandetail:"+err.getLocalizedMessage());
						}

						content1 = (String) tuple[5];
						is_free = (boolean)tuple[6];
						period_name = (String) tuple[7];
						strike_price= ((int)tuple[8] >0) ? "$"+tuple[8]: "";
						plan_price = ((String) tuple[9])==null ? "" : (String) tuple[9] ;
						display_price=(String) tuple[10];
						display_msg = (String) tuple[12];
						billed_price = (String) tuple[13];
						month_price = (String) tuple[14];
						is_best_deal = (boolean) tuple[15];
						String terms_list = (String) tuple[3];

						ArrayList<Terms> term_list = new ArrayList<Terms>();

						if (tuple[16] != null) {
							try {
								JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);
								term_list = terms.getTerm_list();
							} catch (Exception err) {
								log.error("jplan-terms:"+err.getLocalizedMessage());
							}
						}

						if(is_free) {
							isfree_eligible = true;
							free_plan = plan_id;
							free_period =per_id;
							free_features = jplandetail;
							//long freeplan_id, long period_id, String free_buynow, long paidplan_id,
							//String period_name,	String paid_buynow
							periodObj = new JSubPeriodDetail(free_plan, free_period, content1, 0,period_name, "",
									strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal);
							periodlist.add(periodObj);

							if(monitor_type == 6 && orderAvail && defaultFreePlan) {
								free_trial_days = offer_days;
							}else {
								free_trial_days = defaultFreePlan ? (int) tuple[11] : 0;
							}

						}else {
							periodObj = new JSubPeriodDetail( 0,per_id, "",plan_id,period_name,content1,
									strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal);
							periodlist.add(periodObj);
						}

						planDetail.setFeature_list(jplandetail.getPlans());
						planDetail.setTerm_list(term_list);
						planDetail.setPeriod_list(periodlist);
						planDetail.setPlan_name(plan_name);
						if (plan_name.contains("Flexi")) {
							planDetail.setIs_flexiplan(true);
							planDetail.setPlan_details(flexi_plan_details);
							planDetail.setIs_new_plan(true);
						} else {
							planDetail.setIs_flexiplan(false);
						}
						planDetailList.add(planDetail);
						break outerLoop;
					}

					innerLoop : for (int i = j; i < size; i++) {
						tuple = (Object[]) planResp.get(i);
						plan_id = ((BigInteger) tuple[0]).longValue();
						plan_name = (String) tuple[1];
						per_id = ((BigInteger) tuple[2]).longValue();
						String feature_list = (String) tuple[3];
						JPlanDetail jplandetail = new JPlanDetail();
						content1 = (String) tuple[5];
						//is_free = (boolean) tuple[6];
						is_free = Boolean.parseBoolean(tuple[6].toString());
						period_name = (String) tuple[7];
						strike_price= ((int)tuple[8] >0) ? "$"+tuple[8]: "";
						plan_price = ((String) tuple[9])==null ? "" : (String) tuple[9];

						display_price=(String) tuple[10];
						display_msg = (String) tuple[12];
						billed_price = (String) tuple[13];
						month_price = (String) tuple[14];
						is_best_deal = Boolean.parseBoolean(tuple[15].toString());;
						try {
							jplandetail = new Gson().fromJson(feature_list, JPlanDetail.class);
						} catch (JSONException err) {
							log.error("jplandetail:"+err.getLocalizedMessage());
						}

						ArrayList<Terms> term_list = new ArrayList<Terms>();

						if (tuple[16] != null) {
							try {
								JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);
								term_list = terms.getTerm_list();
							} catch (Exception err) {
								log.error("jplan-terms:"+err.getLocalizedMessage());
							}
						}
						if(is_free) {
							isfree_eligible = true;
							free_plan = plan_id;
							free_period =per_id;
							free_features = jplandetail;
							free_buynow = content1;

							if(monitor_type == 6 && orderAvail && defaultFreePlan) {
								free_trial_days = offer_days;
							}else {
								free_trial_days = defaultFreePlan ? (int) tuple[11] : 0;
							}
						}

						if (i == size - 1) {
							if(monitor_type == 1) {
								if (!(restrict_flexi_plan_period && per_id <= curperiod_id)) {
									periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
											plan_id, period_name, content1, strike_price, plan_price,
											display_price, display_msg, billed_price, month_price, is_best_deal);
									periodlist.add(periodObj);
								}
								planDetail.setTerm_list(term_list);
								planDetail.setFeature_list(jplandetail.getPlans());
								planDetail.setPeriod_list(periodlist);
								planDetail.setPlan_name(plan_name);
								if (plan_name.contains("Flexi")) {
									planDetail.setIs_flexiplan(true);
									planDetail.setPlan_details(flexi_plan_details);
									planDetail.setIs_new_plan(true);
								} else {
									planDetail.setIs_flexiplan(false);
								}
								planDetailList.add(planDetail);

								break outerLoop;
							} else {
								periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
										plan_id, period_name, content1, strike_price, plan_price,
										display_price, display_msg, billed_price, month_price, is_best_deal);
								periodlist.add(periodObj);
								planDetail.setTerm_list(term_list);
								planDetail.setFeature_list(jplandetail.getPlans());
								planDetail.setPeriod_list(periodlist);
								planDetail.setPlan_name(plan_name);

								planDetail.setIs_flexiplan(false);

								planDetailList.add(planDetail);
								break outerLoop;
							}
						} else {
							Object[] tupleNext = (Object[]) planResp.get(i + 1);
							long plan_id_next = ((BigInteger) tupleNext[0]).longValue();
							String plan_name_next = (String) tupleNext[1];
							long per_id_next = ((BigInteger) tupleNext[2]).longValue();
							String feature_list_next = (String) tupleNext[3];
							JPlanDetail jplandetailNext = new JPlanDetail();
							boolean is_free_next = Boolean.parseBoolean(tupleNext[6].toString());
							String content1_next = (String) tupleNext[5];
							String period_name_next = (String) tupleNext[7];
							String strike_price_next = ((int)tupleNext[8] >0) ? "$"+tupleNext[8]: "";
							String plan_price_next = ((String) tupleNext[9])==null ? "" : (String) tupleNext[9] ;
							String display_price_next=(String) tupleNext[10];
							String display_msg_next = (String) tupleNext[12];
							String billed_price_next = (String) tupleNext[13];
							String month_price_next = (String) tupleNext[14];
							boolean is_best_deal_next = Boolean.parseBoolean(tupleNext[15].toString());
							try {
								jplandetailNext = new Gson().fromJson(feature_list_next, JPlanDetail.class);
							} catch (JSONException err) {
								log.error("jplandetail:"+err.getLocalizedMessage());
							}

							ArrayList<Terms> term_list_next = new ArrayList<Terms>();

							if (tuple[16] != null) {
								try {
									JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);
									term_list = terms.getTerm_list();
								} catch (Exception err) {
									log.error("jplan-terms:"+err.getLocalizedMessage());
								}
							}
							if(is_free_next) {
								free_plan = plan_id_next;
								free_period =per_id_next;
								free_features = jplandetailNext;
								if(monitor_type == 6 && orderAvail && defaultFreePlan) {
									free_trial_days = offer_days;
								}else {
									free_trial_days = defaultFreePlan ? (int) tupleNext[11] : 0;
								}
							}
							if ((plan_id != plan_id_next)) {
								if (monitor_type == 1){
									if (isfree_eligible) {
										periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id_next, content1,
												plan_id_next, period_name_next, content1_next,
												strike_price_next, plan_price_next, display_price_next, display_msg_next, billed_price_next, month_price_next, is_best_deal_next);
										periodlist.add(periodObj);
										planDetail.setFeature_list(jplandetail.getPlans());
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name_next);
										if (plan_name.contains("Flexi")) {
											planDetail.setIs_flexiplan(true);
											planDetail.setPlan_details(flexi_plan_details);
											planDetail.setIs_new_plan(true);
										} else {
											planDetail.setIs_flexiplan(false);
										}
									} else {
										if (!(restrict_flexi_plan_period && per_id <= curperiod_id)) {
											periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
													plan_id, period_name, content1,
													strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal);
											periodlist.add(periodObj);
										}
										planDetail.setFeature_list(jplandetail.getPlans());
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name);
										if (plan_name.contains("Flexi")) {
											planDetail.setIs_flexiplan(true);
											planDetail.setPlan_details(flexi_plan_details);
											planDetail.setIs_new_plan(true);
										} else {
											planDetail.setIs_flexiplan(false);
										}
									}
									planDetailList.add(planDetail);
									j = i;
									break innerLoop;
								} else if (monitor_type == 11) {
									periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
											plan_id, period_name, content1,
											strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal);
									periodlist.add(periodObj);
									planDetail.setFeature_list(jplandetail.getPlans());
									planDetail.setTerm_list(term_list);
									planDetail.setPeriod_list(periodlist);
									planDetail.setPlan_name(plan_name);
									planDetailList.add(planDetail);
									j = i;
									break innerLoop;
								} else {
									if (isfree_eligible) {
//										periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
//												plan_id, period_name, content1,
//												strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal);
//										periodlist.add(periodObj);
//										planDetail.setPeriod_list(periodlist);
										isfree_eligible = false;
										has_free = true;
									} else {
										if (has_free){
											content1 = free_buynow;
										}
										periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
												plan_id, period_name, content1,
												strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal);
										periodlist.add(periodObj);
										planDetail.setFeature_list(jplandetail.getPlans());
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name);
										planDetailList.add(planDetail);
									}
								}
							}
							else if (plan_id == plan_id_next && !isfree_eligible){
								if(!(restrict_flexi_plan_period && per_id <= curperiod_id)) {
									periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, has_free ? free_buynow : "", plan_id, period_name, content1,
											strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal);
									periodlist.add(periodObj);
								}
							}
						}
					}
				}
				String free_text = monitor_type == 4 || monitor_type==1 ? "" : " for free";
//				String plan_lbl = plan_name+ (defaultFreePlan ? free_text :"") +" benefits";
				String plan_lbl = "Wag-worthy"+ (defaultFreePlan ? free_text :"") +" benefits";
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("planlist", planDetailList);
				response.put("plan_lbl",plan_lbl);

				response.put("popup", "Worried about your furry friend's well-being? Enjoy $~"+free_trial_days+"-days trial of the "+plan_name+", for your pet's health.");
				// for custom plan
				response.put("planname", "Please contact Support");
				response.put("contactnumber", supportcontactnumber.get(country));
				response.put("email", supportemail.get(country));
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Contact Support");
			return response;
		}
		return response;
	}

	@Override
	public FlexiPlanHistory getFlexiplandetailsbySubid(String sub_id) {
		return crDao.getFlexiplandetailsbySubid(sub_id);
	}

	@Override
	public boolean saveorupdateflexiplanhistory(FlexiPlanHistory flexiPlan) {
		return crDao.saveorupdateflexiplanhistory(flexiPlan);
	}

	@Override
	public JProductSubResV2 getFlexiPlanHistory(Long gatewayId, String subscriptionId) {
		return crDao.getFlexiPlanHistory(gatewayId, subscriptionId);
	}

	@Override
	public ArrayList<JVetPlanDetails>  getVetPlanTerms(String plan_type) {
		return crDao.getVetPlanTerms(plan_type);
	}
	
	@Override
	public JResponse getUpgradeSubPlanV7(UserV4 user, long curplan_id, long curperiod_id, long monitor_type, String country,
		boolean defaultFreePlan, long gatewayid, String type, String plantype, String sub_id, boolean restrict_flexi_plan_period,
		boolean is_flexi) {
		log.info("Entered into upgradePlanList_v7 :: user_id : " + user.getId());
		JResponse response = new JResponse();
		try {
			JPlanInfo plan = crService.getPlan(curplan_id);
			if (plan == null) {
				plan = new JPlanInfo();
			}
			List<Object[]> planResp = crDao.getUpgradeSubPlanV7(user, curplan_id, curperiod_id, monitor_type, 
					country, defaultFreePlan, gatewayid, type,plantype, is_flexi);
			if (planResp != null) {
				long plan_id = 0;
				long per_id = 0;
				String content1 = "";
				String free_buynow="";
				String period_name = "";
				String strike_price="";
				String plan_price = "";
				String display_price="";
				boolean is_free = false;
				boolean is_best_deal_plan = false;
				List<String> description;
				String compare_image="";
				String product_image="";
				
				ArrayList<JSubPlanDetailsV3> planlist_new = new ArrayList<JSubPlanDetailsV3>(); //Final list
				
				ArrayList<JSubPlanDetailsV3> proPlanList = new ArrayList<JSubPlanDetailsV3>();
				ArrayList<JSubPlanDetailsV3> flexiPlanList = new ArrayList<JSubPlanDetailsV3>();
				ArrayList<JSubPlanDetailsV3> comboPlanList = new ArrayList<JSubPlanDetailsV3>();
				ArrayList<JSubPlanDetailsV3> vetPlanList = new ArrayList<JSubPlanDetailsV3>();

				String plan_name ="";
				int free_trial_days = 0;
				String display_msg="";
				String billed_price = "";
				String month_price = "";
				boolean is_best_deal = false;
				boolean free_minicam_avil = false;
				boolean has_free = false;
				int cur_monitor_id = 1; // for deciding combo-plan. If -1, its for other products
				
				outerLoop : for(int j=0;j<planResp.size();j++) {
					int size = planResp.size();
					log.info("upgradePlanListV7 : size:" + size);
					JSubPlanDetailsV3 planDetail = new JSubPlanDetailsV3();
					Object[] tuple = (Object[]) planResp.get(0);

					long free_plan = 0;
					long free_period = 0;
					boolean isfree_eligible = false;
					JPlanDetail free_features=null;
					JSubPeriodDetail periodObj = new JSubPeriodDetail();
					ArrayList<JSubPeriodDetail> periodlist = new ArrayList<JSubPeriodDetail>();

					boolean orderAvail = false;
					if(monitor_type == 6 && gatewayid>0 && defaultFreePlan) {
						String[] dtRange = orderdate_trial.split(":");

						orderAvail = gatewayServiceV4.checkOrderWithinRange(gatewayid, dtRange[0], dtRange[1]);
					}
					if (size == 1) {
						plan_id = ((BigInteger) tuple[0]).longValue();
						plan_name = (String) tuple[1];
						per_id = ((BigInteger) tuple[2]).longValue();
						String feature_list = (String) tuple[3];
						JPlanDetail jplandetail = new JPlanDetail();

						if(restrict_flexi_plan_period && per_id <= curperiod_id){
							continue;
						}
						try {

							jplandetail = new Gson().fromJson(feature_list, JPlanDetail.class);

						} catch (JSONException err) {
							log.error("jplandetail:"+err.getLocalizedMessage());
						}

						content1 = (String) tuple[5];
						is_free = tuple[6] instanceof Boolean ? (boolean) tuple[6] : ((byte) tuple[6]) > 0;
						period_name = (String) tuple[7];
						strike_price= ((int)tuple[8] >0) ? "$"+tuple[8]: "";
						plan_price = ((String) tuple[9])==null ? "" : (String) tuple[9] ;
						display_price=(String) tuple[10];
						display_msg = (String) tuple[12];
						billed_price = (String) tuple[13];
						month_price = (String) tuple[14];
						is_best_deal = tuple[15] instanceof Boolean ? (boolean) tuple[15] : ((byte) tuple[15]) > 0;
						String planType =  (String) tuple[17];
						description = Arrays.asList(((String) tuple[18]).split("\\,"));
						is_best_deal_plan =  tuple[19] instanceof Boolean ? (boolean) tuple[19] : ((byte) tuple[19]) > 0;
						compare_image = (String)  tuple[20];
						product_image = (String) tuple[21];


						ArrayList<Terms> term_list = new ArrayList<Terms>();

						if (tuple[16] != null) {
							try {						
								JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);	
								term_list = terms.getTerm_list();
							} catch (Exception err) {
								log.error("jplan-terms:"+err.getLocalizedMessage());
							}
						}
						
						if(is_free) {
							isfree_eligible = true;
							free_plan = plan_id;
							free_period =per_id;
							free_features = jplandetail;
							periodObj = new JSubPeriodDetail(free_plan, free_period, content1, 0,period_name, "",
									strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal);
							periodlist.add(periodObj);

							if(monitor_type == 6 && orderAvail && defaultFreePlan) {
								free_trial_days = offer_days;
							}else {
								free_trial_days = defaultFreePlan ? (int) tuple[11] : 0;
							}

						}else {
							periodObj = new JSubPeriodDetail( 0,per_id, "",plan_id,period_name,content1,
									strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal,
									description, monitor_type==1 ? compare_image : compare_image_other_products, product_image);
							periodlist.add(periodObj);
						}

						planDetail.setFeature_list(jplandetail.getPlans());
						planDetail.setTerm_list(term_list);
						planDetail.setPeriod_list(periodlist);
						planDetail.setIs_best_seller(is_best_deal_plan);
						planDetail.setPlan_name(plan_name);

						if(planType.equalsIgnoreCase("data-plan")) {
							if (monitor_type == 1)
								planDetail.setPlan_name("Pro");
							proPlanList.add(planDetail);}
						else if(planType.equalsIgnoreCase("combo-plan")) {
							planDetail.setPlan_name("Combo");
							planDetail.setIs_combo_plan(true);
							comboPlanList.add(planDetail);}
						else if(planType.equalsIgnoreCase("vet-plan")){
							vetPlanList.add(planDetail);
						}
						else {
							planDetail.setPlan_name("Flexi");
							planDetail.setIs_flexiplan(true);
							planDetail.setPlan_details(flexi_plan_details);
							flexiPlanList.add(planDetail);
						}
						break outerLoop;
					}

					innerLoop : for (int i = j; i < size; i++) {
						tuple = (Object[]) planResp.get(i);
						plan_id = ((BigInteger) tuple[0]).longValue();
						plan_name = (String) tuple[1];
						per_id = ((BigInteger) tuple[2]).longValue();
						String feature_list = (String) tuple[3];
						JPlanDetail jplandetail = new JPlanDetail();
						content1 = (String) tuple[5];
						is_free = tuple[6] instanceof Boolean ? (boolean) tuple[6] : ((byte) tuple[6]) > 0;
						period_name = (String) tuple[7];
						strike_price= ((int)tuple[8] >0) ? "$"+tuple[8]: "";
						plan_price = ((String) tuple[9])==null ? "" : (String) tuple[9];

						display_price=(String) tuple[10];
						display_msg = (String) tuple[12];
						billed_price = (String) tuple[13];
						month_price = (String) tuple[14];
						is_best_deal = tuple[15] instanceof Boolean ? (boolean) tuple[15] : ((byte) tuple[15]) > 0;
						String planType =  (String) tuple[17];
						description = Arrays.asList(((String) tuple[18]).split("\\,"));
						is_best_deal_plan =  tuple[19] instanceof Boolean ? (boolean) tuple[19] : ((byte) tuple[19]) > 0;
						compare_image = (String)  tuple[20];
						product_image = (String) tuple[21];
						cur_monitor_id = ((BigInteger) tuple[22]).intValue();
						free_minicam_avil = tuple[23] instanceof Boolean ? (boolean) tuple[23] : ((byte) tuple[23]) > 0;
						if(monitor_type == 1){
							String sub = cbService.getProductSubscriptionStatus(gatewayid,user.getChargebeeid());
							if (sub.equalsIgnoreCase("NA") || !sub.equalsIgnoreCase("CANCELLED")) {
								free_minicam_avil = false;
							}
						}

						if(monitor_type == 1 && cur_monitor_id == -1)
							break innerLoop;

						try {
							jplandetail = new Gson().fromJson(feature_list, JPlanDetail.class);						
						} catch (JSONException err) {
							log.error("jplandetail:"+err.getLocalizedMessage());
						}
						
						ArrayList<Terms> term_list = new ArrayList<Terms>();

						if (tuple[16] != null) {
							try {						
								JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);	
								term_list = terms.getTerm_list();
							} catch (Exception err) {
								log.error("jplan-terms:"+err.getLocalizedMessage());
							}
						}
						if(is_free) {
							isfree_eligible = true;
							free_plan = plan_id;
							free_period =per_id;
							free_features = jplandetail;
							free_buynow = content1;

							if(monitor_type == 6 && orderAvail && defaultFreePlan) {
								free_trial_days = offer_days;
							}else {
								free_trial_days = defaultFreePlan ? (int) tuple[11] : 0;
							}
						}

						if (i == size - 1) {
							if(monitor_type == 1) {
								if(!(restrict_flexi_plan_period && per_id <= curperiod_id)){
									periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
											plan_id,period_name , content1, strike_price, plan_price,
											display_price,display_msg,billed_price,month_price,is_best_deal,
											description, compare_image, product_image,free_minicam_avil);
									periodlist.add(periodObj);
								}
								planDetail.setTerm_list(term_list);
								planDetail.setFeature_list(jplandetail.getPlans());
								planDetail.setPeriod_list(periodlist);
								planDetail.setPlan_name("Pro");
								planDetail.setIs_best_seller(is_best_deal_plan);

								if(planType.equalsIgnoreCase("data-plan")) {
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("combo-plan")) {
									planDetail.setPlan_name("Combo");
									planDetail.setIs_combo_plan(true);
									comboPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("vet-plan")){
									vetPlanList.add(planDetail);
								}
								else {
									planDetail.setPlan_name("Flexi");
									planDetail.setIs_flexiplan(true);
									planDetail.setPlan_details(flexi_plan_details);
									flexiPlanList.add(planDetail);
								}
								has_free = false;
								break outerLoop;
							} else {
								periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
										plan_id, period_name, content1, strike_price, plan_price,
										display_price, display_msg, billed_price, month_price, is_best_deal,
										description, compare_image_other_products, product_image,free_minicam_avil);
								periodlist.add(periodObj);
								planDetail.setTerm_list(term_list);
								planDetail.setFeature_list(jplandetail.getPlans());
								planDetail.setPeriod_list(periodlist);
								planDetail.setPlan_name(plan_name);

								planDetail.setIs_flexiplan(false);

								if(planType.equalsIgnoreCase("data-plan")) {
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("combo-plan")) {
									planDetail.setPlan_name("Combo");
									planDetail.setIs_combo_plan(true);
									comboPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("vet-plan")){
									vetPlanList.add(planDetail);
								}
								else {
									planDetail.setPlan_name("Flexi");
									planDetail.setIs_flexiplan(true);
									planDetail.setPlan_details(flexi_plan_details);
									flexiPlanList.add(planDetail);
								}
								has_free = false;
								break outerLoop;
							}
						} else {
							Object[] tupleNext = (Object[]) planResp.get(i + 1);
							long plan_id_next = ((BigInteger) tupleNext[0]).longValue();
							String plan_name_next = (String) tupleNext[1];
							long per_id_next = ((BigInteger) tupleNext[2]).longValue();
							String feature_list_next = (String) tupleNext[3];
							JPlanDetail jplandetailNext = new JPlanDetail();
							boolean is_free_next = tupleNext[6] instanceof Boolean ? (boolean) tupleNext[6] : ((byte) tupleNext[6]) > 0;
							String content1_next = (String) tupleNext[5];
							String period_name_next = (String) tupleNext[7];
							String strike_price_next = ((int)tupleNext[8] >0) ? "$"+tupleNext[8]: "";
							String plan_price_next = ((String) tupleNext[9])==null ? "" : (String) tupleNext[9] ;
							String display_price_next=(String) tupleNext[10];
							String display_msg_next = (String) tupleNext[12];
							String billed_price_next = (String) tupleNext[13];
							String month_price_next = (String) tupleNext[14];
							boolean is_best_deal_next =tupleNext[15] instanceof Boolean ? (boolean) tupleNext[15] : ((byte) tupleNext[15]) > 0;

							try {						
								jplandetailNext = new Gson().fromJson(feature_list_next, JPlanDetail.class);						
							} catch (JSONException err) {
								log.error("jplandetail:"+err.getLocalizedMessage());
							}

							if (tuple[16] != null) {
								try {						
									JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);	
									term_list = terms.getTerm_list();
								} catch (Exception err) {
									log.error("jplan-terms:"+err.getLocalizedMessage());
								}
							}
							if(is_free_next) {
								free_plan = plan_id_next;
								free_period =per_id_next;
								free_features = jplandetailNext;
								if(monitor_type == 6 && orderAvail && defaultFreePlan) {
									free_trial_days = offer_days;
								}else {
									free_trial_days = defaultFreePlan ? (int) tupleNext[11] : 0;
								}
							}
							if ((plan_id != plan_id_next)) {
								if (monitor_type == 1) {
									if (isfree_eligible) {
										periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id_next, content1,
												plan_id_next,period_name_next , content1_next,
												strike_price_next, plan_price_next,  display_price_next,display_msg_next,billed_price_next,month_price_next,is_best_deal_next);
										periodlist.add(periodObj);
										planDetail.setFeature_list(jplandetail.getPlans());
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name_next);

									} else {
										if(!(restrict_flexi_plan_period && per_id <= curperiod_id)){
											periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
													plan_id,period_name , content1,
													strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal,
													description, compare_image, product_image,free_minicam_avil);
											periodlist.add(periodObj);
										}
										planDetail.setFeature_list(jplandetail.getPlans());
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name);
										planDetail.setIs_best_seller(is_best_deal_plan);
									}
									if(planType.equalsIgnoreCase("data-plan")) {
										planDetail.setPlan_name("Pro");
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("combo-plan")) {
										planDetail.setPlan_name("Combo");
										planDetail.setIs_combo_plan(true);
										comboPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("vet-plan")){
										vetPlanList.add(planDetail);
									}
									else {
										planDetail.setPlan_name("Flexi");
										planDetail.setIs_flexiplan(true);
										planDetail.setPlan_details(flexi_plan_details);
										flexiPlanList.add(planDetail);
									}
									j=i;
									break innerLoop;
								} else if(monitor_type == 11) {
									periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
											plan_id, period_name, content1,
											strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal);
									periodlist.add(periodObj);
									planDetail.setFeature_list(jplandetail.getPlans());
									planDetail.setTerm_list(term_list);
									planDetail.setPeriod_list(periodlist);
									planDetail.setPlan_name(plan_name);

									if(planType.equalsIgnoreCase("data-plan")) {
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("combo-plan")) {
										planDetail.setPlan_name("Combo");
										planDetail.setIs_combo_plan(true);
										comboPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("vet-plan")){
										vetPlanList.add(planDetail);
									}
									else {
										planDetail.setPlan_name("Flexi");
										planDetail.setIs_flexiplan(true);
										planDetail.setPlan_details(flexi_plan_details);
										flexiPlanList.add(planDetail);
									}
									j = i;
									break innerLoop;
								} else {
									if (isfree_eligible) {
//										periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
//												plan_id, period_name, content1,
//												strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal);
//										periodlist.add(periodObj);
//										planDetail.setPeriod_list(periodlist);
										isfree_eligible = false;
										has_free = true;
									} else {
//										if (has_free) {
//											content1 = free_buynow;
//										}
										periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, has_free ? free_buynow : "",
												plan_id, period_name, content1,
												strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal,
												description, compare_image_other_products, product_image,free_minicam_avil);
										periodlist.add(periodObj);
										planDetail.setFeature_list(jplandetail.getPlans());
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name);
										if(planType.equalsIgnoreCase("data-plan")) {
											proPlanList.add(planDetail);
											j=i;
											has_free = false;
											break innerLoop;
										}
										else if(planType.equalsIgnoreCase("combo-plan")) {
											planDetail.setPlan_name("Combo");
											planDetail.setIs_combo_plan(true);
											comboPlanList.add(planDetail);
											has_free = false;
											break innerLoop;
										} else if(planType.equalsIgnoreCase("vet-plan")){
											vetPlanList.add(planDetail);
										}
										else {
											planDetail.setPlan_name("Flexi");
											planDetail.setIs_flexiplan(true);
											planDetail.setPlan_details(flexi_plan_details);
											flexiPlanList.add(planDetail);
										}
									}
								}
							} else if (!isfree_eligible){
								if(!(restrict_flexi_plan_period && per_id <= curperiod_id)) {
									periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, has_free ? free_buynow : "", plan_id, period_name, content1,
											strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal,
											description, compare_image_other_products, product_image,free_minicam_avil);
									periodlist.add(periodObj);
								}
							}
						}
					}
				}
				
				if(!proPlanList.isEmpty())
					planlist_new.addAll(proPlanList);
				if (!comboPlanList.isEmpty()) {
					JSubPlanDetailsV3 comboObj = new JSubPlanDetailsV3();
					comboObj.setIs_combo_plan(true);
					comboObj.setPlan_name("Combo");
					planlist_new.add(comboObj);
				}
				if (!flexiPlanList.isEmpty())
					planlist_new.addAll(flexiPlanList);
				if(!vetPlanList.isEmpty())
					planlist_new.addAll(vetPlanList);
				
				String free_text = monitor_type == 4 || monitor_type==1 ? "" : " for free";
				String plan_lbl = "Wag-worthy"+ (defaultFreePlan ? free_text :"") +" benefits";
				
				boolean show_coupon = true;
				JCouponInfo coupon_info = new JCouponInfo();
				String btn_text = "Continue";
				String currentSubStatus ="";
				
				if(plan.isIs_freeplan()&& !sub_id.equalsIgnoreCase("NA"))
					currentSubStatus = crService.getCurrentSubStatusForChargebeeUser(user.getChargebeeid());
				
				if( currentSubStatus.equalsIgnoreCase("NA") || currentSubStatus.isEmpty() )
						currentSubStatus =crDao.getSubscriptionStatus(sub_id).trim();				
					
				if(plan.isIs_freeplan() && sub_id.equalsIgnoreCase("NA")) {
					coupon_info = new JCouponInfo(coupon_img_new, coupon_desc_new, coupon_code_new,btn_text );
				}
				else if (currentSubStatus.equalsIgnoreCase("active") || currentSubStatus.equalsIgnoreCase("non_renewing")){
					coupon_info = new JCouponInfo(coupon_img_upgrade, coupon_desc_upgrade, coupon_code_upgrade,btn_text );
				}
				else if(currentSubStatus.equalsIgnoreCase("cancelled")){
					coupon_info = new JCouponInfo(coupon_img_update, coupon_desc_update, coupon_code_update,btn_text );
				}else {
					coupon_info = new JCouponInfo(coupon_img_new, coupon_desc_new, coupon_code_new,btn_text );
				}
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("coupon_info", coupon_info);
				response.put("show_coupon", show_coupon);
				response.put("planlist", planlist_new);
				response.put("planlist_comb",comboPlanList);
				response.put("plan_lbl",plan_lbl);
				response.put("popup", "Worried about your furry friend's well-being? Enjoy $~"+free_trial_days+"-days trial of the "+plan_name+", for your pet's health.");
				// for custom plan
				response.put("planname", "Please contact Support");
				response.put("contactnumber", supportcontactnumber.get(country));
				response.put("email", supportemail.get(country));
			}
		} catch (Exception e) {
			e.printStackTrace();
			response.put("Status", 0);
			response.put("Msg", "Contact Support");
			return response;
		}
		return response;
	}

	@Override
	public String getCurrentSubStatusForChargebeeUser(String chargeBeeId) {
		return crDao.getCurrentSubStatusForChargebeeUser(chargeBeeId);
	}

	@Override
	public String getSubscriptionStatus(String subId) {
		return crDao.getSubscriptionStatus(subId);
	}

	@Override
	public JResponse getUpgradeSubPlanV8(UserV4 user, long curplan_id, long curperiod_id, long monitor_type, String country,
										 boolean defaultFreePlan, long gatewayid, String type, String plantype, String sub_id, boolean restrict_flexi_plan_period,
										 boolean is_flexi) {
		log.info("Entered into getUpgradeSubPlanV8 :: user_id : " + user.getId());
		JResponse response = new JResponse();
		try {
			JPlanInfo plan = crService.getPlan(curplan_id);
			if (plan == null) {
				plan = new JPlanInfo();
			}
			List<Object[]> planResp = crDao.getUpgradeSubPlanV7(user, curplan_id, curperiod_id, monitor_type,
					country, defaultFreePlan, gatewayid, type,plantype, is_flexi);
			if (planResp != null) {
				long plan_id = 0;
				long per_id = 0;
				String content1 = "";
				String free_buynow="";
				String period_name = "";
				String strike_price="";
				String plan_price = "";
				String display_price="";
				boolean is_free = false;
				boolean is_best_deal_plan = false;
				List<String> description;
				String compare_image="";
				String product_image="";

				List<JSubPlanDetailsV3> planlist_new = new ArrayList<JSubPlanDetailsV3>(); //Final list
				ArrayList<JSubPlanDetailsV3> comboPlanList = new ArrayList<JSubPlanDetailsV3>();
				ArrayList<JSubPlanDetailsV3> proPlanList = new ArrayList<JSubPlanDetailsV3>();
				ArrayList<JSubPlanDetailsV3> vetPlanList = new ArrayList<JSubPlanDetailsV3>();

				String plan_name ="";
				int free_trial_days = 0;
				String display_msg="";
				String billed_price = "";
				String month_price = "";
				boolean is_best_deal = false;
				boolean free_minicam_avil = false;
				boolean has_free = false;
				int cur_monitor_id = 1; // for deciding combo-plan. If -1, its for other products

				outerLoop : for(int j=0;j<planResp.size();j++) {
					int size = planResp.size();
					log.info("upgradePlanListV7 : size:" + size);
					JSubPlanDetailsV3 planDetail = new JSubPlanDetailsV3();
					Object[] tuple = (Object[]) planResp.get(0);

					long free_plan = 0;
					long free_period = 0;
					boolean isfree_eligible = false;
					JPlanDetail free_features=null;
					JSubPeriodDetail periodObj = new JSubPeriodDetail();
					ArrayList<JSubPeriodDetail> periodlist = new ArrayList<JSubPeriodDetail>();

					boolean orderAvail = false;
					if(monitor_type == 6 && gatewayid>0 && defaultFreePlan) {
						String[] dtRange = orderdate_trial.split(":");

						orderAvail = gatewayServiceV4.checkOrderWithinRange(gatewayid, dtRange[0], dtRange[1]);
					}
					if (size == 1) {
						plan_id = ((BigInteger) tuple[0]).longValue();
						plan_name = (String) tuple[1];
						per_id = ((BigInteger) tuple[2]).longValue();
						String feature_list = (String) tuple[3];
						JPlanDetail jplandetail = new JPlanDetail();

						if(restrict_flexi_plan_period && per_id <= curperiod_id){
							continue;
						}
						try {

							jplandetail = new Gson().fromJson(feature_list, JPlanDetail.class);

						} catch (JSONException err) {
							log.error("jplandetail:"+err.getLocalizedMessage());
						}

						content1 = (String) tuple[5];
						is_free = tuple[6] instanceof Boolean ? (boolean) tuple[6] : ((byte) tuple[6]) > 0;
						period_name = (String) tuple[7];
						strike_price= ((int)tuple[8] >0) ? "$"+tuple[8]: "";
						plan_price = ((String) tuple[9])==null ? "" : (String) tuple[9] ;
						display_price=(String) tuple[10];
						display_msg = (String) tuple[12];
						billed_price = (String) tuple[13];
						month_price = (String) tuple[14];
						is_best_deal = tuple[15] instanceof Boolean ? (boolean) tuple[15] : ((byte) tuple[15]) > 0;
						String planType =  (String) tuple[17];
						description = Arrays.asList(((String) tuple[18]).split("\\,"));
						is_best_deal_plan =  tuple[19] instanceof Boolean ? (boolean) tuple[19] : ((byte) tuple[19]) > 0;
						compare_image = (String)  tuple[20];
						product_image = (String) tuple[21];


						ArrayList<Terms> term_list = new ArrayList<Terms>();

						if (tuple[16] != null) {
							try {
								JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);
								term_list = terms.getTerm_list();
							} catch (Exception err) {
								log.error("jplan-terms:"+err.getLocalizedMessage());
							}
						}

						if(is_free) {
							isfree_eligible = true;
							free_plan = plan_id;
							free_period =per_id;
							free_features = jplandetail;
							periodObj = new JSubPeriodDetail(free_plan, free_period, content1, 0,period_name, "",
									strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal);
							periodlist.add(periodObj);

							if(monitor_type == 6 && orderAvail && defaultFreePlan) {
								free_trial_days = offer_days;
							}else {
								free_trial_days = defaultFreePlan ? (int) tuple[11] : 0;
							}

						}else {
							periodObj = new JSubPeriodDetail( 0,per_id, "",plan_id,period_name,content1,
									strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal,
									description, monitor_type==1 ? compare_image : compare_image_other_products, product_image);
							periodlist.add(periodObj);
						}

						planDetail.setFeature_list(jplandetail.getPlans());
						planDetail.setTerm_list(term_list);
						planDetail.setPeriod_list(periodlist);
						planDetail.setIs_best_seller(is_best_deal_plan);
						planDetail.setPlan_name(plan_name);

						if(planType.equalsIgnoreCase("data-plan")) {
							if (monitor_type == 1)
								planDetail.setPlan_name("Pro");
							proPlanList.add(planDetail);}
						else if(planType.equalsIgnoreCase("combo-plan")) {
							//planDetail.setPlan_name("Combo");
							planDetail.setIs_combo_plan(true);
							proPlanList.add(planDetail);}
						else if(planType.equalsIgnoreCase("vet-plan")){
							proPlanList.add(planDetail);
						}
						else {
							planDetail.setPlan_name("Flexi");
							planDetail.setIs_flexiplan(true);
							planDetail.setPlan_details(flexi_plan_details);
							proPlanList.add(planDetail);
						}
						break outerLoop;
					}

					innerLoop : for (int i = j; i < size; i++) {
						tuple = (Object[]) planResp.get(i);
						plan_id = ((BigInteger) tuple[0]).longValue();
						plan_name = (String) tuple[1];
						per_id = ((BigInteger) tuple[2]).longValue();
						String feature_list = (String) tuple[3];
						JPlanDetail jplandetail = new JPlanDetail();
						content1 = (String) tuple[5];
						is_free = tuple[6] instanceof Boolean ? (boolean) tuple[6] : ((byte) tuple[6]) > 0;
						period_name = (String) tuple[7];
						strike_price= ((int)tuple[8] >0) ? "$"+tuple[8]: "";
						plan_price = ((String) tuple[9])==null ? "" : (String) tuple[9];

						display_price=(String) tuple[10];
						display_msg = (String) tuple[12];
						billed_price = (String) tuple[13];
						month_price = (String) tuple[14];
						is_best_deal = tuple[15] instanceof Boolean ? (boolean) tuple[15] : ((byte) tuple[15]) > 0;
						String planType =  (String) tuple[17];
						description = Arrays.asList(((String) tuple[18]).split("\\,"));
						is_best_deal_plan =  tuple[19] instanceof Boolean ? (boolean) tuple[19] : ((byte) tuple[19]) > 0;
						compare_image = (String)  tuple[20];
						product_image = (String) tuple[21];
						cur_monitor_id = ((BigInteger) tuple[22]).intValue();
						free_minicam_avil = tuple[23] instanceof Boolean ? (boolean) tuple[23] : ((byte) tuple[23]) > 0;
						if(monitor_type == 1){
							String sub = cbService.getProductSubscriptionStatus(gatewayid,user.getChargebeeid());
							if (sub.equalsIgnoreCase("NA") || !sub.equalsIgnoreCase("CANCELLED")) {
								free_minicam_avil = false;
							}
						}

						if(monitor_type == 1 && cur_monitor_id == -1)
							break innerLoop;

						try {
							jplandetail = new Gson().fromJson(feature_list, JPlanDetail.class);
						} catch (JSONException err) {
							log.error("jplandetail:"+err.getLocalizedMessage());
						}

						ArrayList<Terms> term_list = new ArrayList<Terms>();

						if (tuple[16] != null) {
							try {
								JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);
								term_list = terms.getTerm_list();
							} catch (Exception err) {
								log.error("jplan-terms:"+err.getLocalizedMessage());
							}
						}
						if(is_free) {
							isfree_eligible = true;
							free_plan = plan_id;
							free_period =per_id;
							free_features = jplandetail;
							free_buynow = content1;

							if(monitor_type == 6 && orderAvail && defaultFreePlan) {
								free_trial_days = offer_days;
							}else {
								free_trial_days = defaultFreePlan ? (int) tuple[11] : 0;
							}
						}

						if (i == size - 1) {
							if(monitor_type == 1) {
								if(!(restrict_flexi_plan_period && per_id <= curperiod_id)){
									periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
											plan_id,period_name , content1, strike_price, plan_price,
											display_price,display_msg,billed_price,month_price,is_best_deal,
											description, compare_image, product_image,free_minicam_avil);
									periodlist.add(periodObj);
								}
								planDetail.setTerm_list(term_list);
								planDetail.setFeature_list(jplandetail.getPlans());
								planDetail.setPeriod_list(periodlist);
								planDetail.setPlan_name("Pro");
								planDetail.setIs_best_seller(is_best_deal_plan);

								if(planType.equalsIgnoreCase("data-plan")) {
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("combo-plan")) {
									planDetail.setPlan_name(plan_name);
									planDetail.setIs_combo_plan(true);
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("vet-plan")){
									proPlanList.add(planDetail);
								}
								else {
									planDetail.setPlan_name("Flexi");
									planDetail.setIs_flexiplan(true);
									planDetail.setPlan_details(flexi_plan_details);
									proPlanList.add(planDetail);
								}
								has_free = false;
								break outerLoop;
							} else {
								periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
										plan_id, period_name, content1, strike_price, plan_price,
										display_price, display_msg, billed_price, month_price, is_best_deal,
										description, compare_image_other_products, product_image,free_minicam_avil);
								periodlist.add(periodObj);
								planDetail.setTerm_list(term_list);
								planDetail.setFeature_list(jplandetail.getPlans());
								planDetail.setPeriod_list(periodlist);
								planDetail.setPlan_name(plan_name);

								planDetail.setIs_flexiplan(false);

								if(planType.equalsIgnoreCase("data-plan")) {
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("combo-plan")) {
									//planDetail.setPlan_name("Combo");
									planDetail.setIs_combo_plan(true);
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("vet-plan")){
									proPlanList.add(planDetail);
								}
								else {
									planDetail.setPlan_name("Flexi");
									planDetail.setIs_flexiplan(true);
									planDetail.setPlan_details(flexi_plan_details);
									proPlanList.add(planDetail);
								}
								has_free = false;
								break outerLoop;
							}
						} else {
							Object[] tupleNext = (Object[]) planResp.get(i + 1);
							long plan_id_next = ((BigInteger) tupleNext[0]).longValue();
							String plan_name_next = (String) tupleNext[1];
							long per_id_next = ((BigInteger) tupleNext[2]).longValue();
							String feature_list_next = (String) tupleNext[3];
							JPlanDetail jplandetailNext = new JPlanDetail();
							boolean is_free_next = tupleNext[6] instanceof Boolean ? (boolean) tupleNext[6] : ((byte) tupleNext[6]) > 0;
							String content1_next = (String) tupleNext[5];
							String period_name_next = (String) tupleNext[7];
							String strike_price_next = ((int)tupleNext[8] >0) ? "$"+tupleNext[8]: "";
							String plan_price_next = ((String) tupleNext[9])==null ? "" : (String) tupleNext[9] ;
							String display_price_next=(String) tupleNext[10];
							String display_msg_next = (String) tupleNext[12];
							String billed_price_next = (String) tupleNext[13];
							String month_price_next = (String) tupleNext[14];
							boolean is_best_deal_next =tupleNext[15] instanceof Boolean ? (boolean) tupleNext[15] : ((byte) tupleNext[15]) > 0;

							try {
								jplandetailNext = new Gson().fromJson(feature_list_next, JPlanDetail.class);
							} catch (JSONException err) {
								log.error("jplandetail:"+err.getLocalizedMessage());
							}

							if (tuple[16] != null) {
								try {
									JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);
									term_list = terms.getTerm_list();
								} catch (Exception err) {
									log.error("jplan-terms:"+err.getLocalizedMessage());
								}
							}
							if(is_free_next) {
								free_plan = plan_id_next;
								free_period =per_id_next;
								free_features = jplandetailNext;
								if(monitor_type == 6 && orderAvail && defaultFreePlan) {
									free_trial_days = offer_days;
								}else {
									free_trial_days = defaultFreePlan ? (int) tupleNext[11] : 0;
								}
							}
							if ((plan_id != plan_id_next)) {
								if (monitor_type == 1) {
									if (isfree_eligible) {
										periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id_next, content1,
												plan_id_next,period_name_next , content1_next,
												strike_price_next, plan_price_next,  display_price_next,display_msg_next,billed_price_next,month_price_next,is_best_deal_next);
										periodlist.add(periodObj);
										planDetail.setFeature_list(jplandetail.getPlans());
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name_next);

									} else {
										if(!(restrict_flexi_plan_period && per_id <= curperiod_id)){
											periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
													plan_id,period_name , content1,
													strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal,
													description, compare_image, product_image,free_minicam_avil);
											periodlist.add(periodObj);
										}
										planDetail.setFeature_list(jplandetail.getPlans());
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name);
										planDetail.setIs_best_seller(is_best_deal_plan);
									}
									if(planType.equalsIgnoreCase("data-plan")) {
										planDetail.setPlan_name("Pro");
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("combo-plan")) {
										//planDetail.setPlan_name("Combo");
										planDetail.setIs_combo_plan(true);
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("vet-plan")){
										proPlanList.add(planDetail);
									}
									else {
										planDetail.setPlan_name("Flexi");
										planDetail.setIs_flexiplan(true);
										planDetail.setPlan_details(flexi_plan_details);
										proPlanList.add(planDetail);
									}
									j=i;
									break innerLoop;
								} else if(monitor_type == 11) {
									periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
											plan_id, period_name, content1,
											strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal);
									periodlist.add(periodObj);
									planDetail.setFeature_list(jplandetail.getPlans());
									planDetail.setTerm_list(term_list);
									planDetail.setPeriod_list(periodlist);
									planDetail.setPlan_name(plan_name);

									if(planType.equalsIgnoreCase("data-plan")) {
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("combo-plan")) {
										//planDetail.setPlan_name("Combo");
										planDetail.setIs_combo_plan(true);
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("vet-plan")){
										proPlanList.add(planDetail);
									}
									else {
										planDetail.setPlan_name("Flexi");
										planDetail.setIs_flexiplan(true);
										planDetail.setPlan_details(flexi_plan_details);
										proPlanList.add(planDetail);
									}
									j = i;
									break innerLoop;
								} else {
									if (isfree_eligible) {
//										periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
//												plan_id, period_name, content1,
//												strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal);
//										periodlist.add(periodObj);
//										planDetail.setPeriod_list(periodlist);
										isfree_eligible = false;
										has_free = true;
									} else {
//										if (has_free) {
//											content1 = free_buynow;
//										}
										periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, has_free ? free_buynow : "",
												plan_id, period_name, content1,
												strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal,
												description, compare_image_other_products, product_image,free_minicam_avil);
										periodlist.add(periodObj);
										planDetail.setFeature_list(jplandetail.getPlans());
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name);
										if(planType.equalsIgnoreCase("data-plan")) {
											proPlanList.add(planDetail);
											j=i;
											has_free = false;
											break innerLoop;
										}
										else if(planType.equalsIgnoreCase("combo-plan")) {
											//planDetail.setPlan_name("Combo");
											planDetail.setIs_combo_plan(true);
											proPlanList.add(planDetail);
											has_free = false;
											break innerLoop;
										} else if(planType.equalsIgnoreCase("vet-plan")){
											proPlanList.add(planDetail);
										}
										else {
											planDetail.setPlan_name("Flexi");
											planDetail.setIs_flexiplan(true);
											planDetail.setPlan_details(flexi_plan_details);
											proPlanList.add(planDetail);
										}
									}
								}
							} else if (!isfree_eligible){
								if(!(restrict_flexi_plan_period && per_id <= curperiod_id)) {
									periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, has_free ? free_buynow : "", plan_id, period_name, content1,
											strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal,
											description, compare_image_other_products, product_image,free_minicam_avil);
									periodlist.add(periodObj);
								}
							}
						}
					}
				}





				JSubPlanDetailsV3 proPlanObjectFinal = new JSubPlanDetailsV3();
				JSubPlanDetailsV3 basicPlanObjectFinal = new JSubPlanDetailsV3();
				JSubPlanDetailsV3 comboPlanObjectFinal = new JSubPlanDetailsV3();
				ArrayList<JSubPeriodDetail> monthlyPeriods = new ArrayList<>();
				ArrayList<JSubPeriodDetail> yearlyPeriods = new ArrayList<>();
				ArrayList<JSubPeriodDetail> comboPeriods = new ArrayList<>();
				ArrayList<JMenuLst>  yearlyFeatureList=new ArrayList<>();
				ArrayList<JMenuLst> monthlyFeatureList=new ArrayList<>();
				ArrayList<JMenuLst> comboFeatureList=new ArrayList<>();
				if(!proPlanList.isEmpty() && monitor_type==1){
					for (JSubPlanDetailsV3 subPlan : proPlanList) {
						ArrayList<JSubPeriodDetail> periodList=subPlan.getPeriod_list();
						for(JSubPeriodDetail subPeriodDetail:periodList){
							if(subPeriodDetail.getPeriod_id()>=4 && !subPlan.isIs_combo_plan()){
								if(defaultFreePlan)
									subPeriodDetail.setPaid_buynow("Continue");
								else
									subPeriodDetail.setPaid_buynow("Upgrade Now");
								yearlyPeriods.add(subPeriodDetail);
								yearlyFeatureList.addAll(subPlan.getFeature_list());
								proPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
								if(!proPlanObjectFinal.isIs_best_seller())
								 proPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
								proPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());
								if( comboPlanList.size()>0)
								{
									proPlanObjectFinal.setShow_combo_content(true);
								}
							}else if(subPeriodDetail.getPeriod_id()>=4 && subPlan.isIs_combo_plan()){
								if(defaultFreePlan)
									subPeriodDetail.setPaid_buynow("Continue");
								else
									subPeriodDetail.setPaid_buynow("Upgrade Now");

								comboPeriods.add(subPeriodDetail);
								comboFeatureList.addAll(subPlan.getFeature_list());
								comboPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
								if(!comboPlanObjectFinal.isIs_best_seller())
									comboPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
								comboPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());

							}
							else{
								if(defaultFreePlan)
									subPeriodDetail.setPaid_buynow("Continue");
								else
									subPeriodDetail.setPaid_buynow("Upgrade Now");
								monthlyPeriods.add(subPeriodDetail);
								monthlyFeatureList.addAll(subPlan.getFeature_list());
								basicPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
								if(!proPlanObjectFinal.isIs_best_seller())
									basicPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
								basicPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());

								if(subPeriodDetail.getPeriod_id()==3)
									subPeriodDetail.setFlexiPlan(true);
								if(subPlan.isIs_flexiplan())
									basicPlanObjectFinal.setPlan_details(flexi_plan_details);

							}

						}
					}
					Set<JMenuLst> setyearly = new LinkedHashSet<>(yearlyFeatureList);
					ArrayList<JMenuLst> featureListWithoutDuplicatesYearly = new ArrayList<>(setyearly);
					proPlanObjectFinal.setFeature_list(featureListWithoutDuplicatesYearly);
					proPlanObjectFinal.setPeriod_list(yearlyPeriods);
					proPlanObjectFinal.setPlan_name("Pro");

					Set<JMenuLst> setcombo = new LinkedHashSet<>(comboFeatureList);
					ArrayList<JMenuLst> featureListWithoutDuplicatesCombo = new ArrayList<>(setcombo);
					comboPlanObjectFinal.setFeature_list(featureListWithoutDuplicatesCombo);
					comboPlanObjectFinal.setPeriod_list(comboPeriods);
					comboPlanObjectFinal.setPlan_name("Combo");


					// Handled for Monthly plan to remove duplicate feature Plan
					Set<JMenuLst> setmonthly = new LinkedHashSet<>(monthlyFeatureList);
					ArrayList<JMenuLst> featureListWithoutDuplicatesMonthly = new ArrayList<>(setmonthly);
					basicPlanObjectFinal.setFeature_list( featureListWithoutDuplicatesMonthly);
					basicPlanObjectFinal.setPeriod_list(monthlyPeriods);
					basicPlanObjectFinal.setPlan_name("Basic");

					if(!proPlanObjectFinal.getPeriod_list().isEmpty()
							&& !proPlanObjectFinal.getFeature_list().isEmpty()) {
						planlist_new.add(proPlanObjectFinal);
					}

					if(!comboPlanObjectFinal.getPeriod_list().isEmpty()
							&& !comboPlanObjectFinal.getFeature_list().isEmpty()) {
						planlist_new.add(comboPlanObjectFinal);
					}

					if(!basicPlanObjectFinal.getPeriod_list().isEmpty()
							&& !basicPlanObjectFinal.getFeature_list().isEmpty()) {
						planlist_new.add(basicPlanObjectFinal);
					}
				}
				else if(!proPlanList.isEmpty() && monitor_type==11){

					for (JSubPlanDetailsV3 subPlan : proPlanList) {
						ArrayList<JSubPeriodDetail> periodList=subPlan.getPeriod_list();
						for(JSubPeriodDetail subPeriodDetail:periodList){

							if(curplan_id==1 && curperiod_id==1)
								subPeriodDetail.setPaid_buynow("Continue");
							else
								subPeriodDetail.setPaid_buynow("Upgrade Now");

							yearlyPeriods.add(subPeriodDetail);
							yearlyFeatureList.addAll(subPlan.getFeature_list());
							proPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
							if(!proPlanObjectFinal.isIs_best_seller())
								proPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
							proPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());
//							if( comboPlanList.size()>0)
//							{
//								proPlanObjectFinal.setShow_combo_content(true);
//							}
						}
					}
					Set<JMenuLst> set = new LinkedHashSet<>(yearlyFeatureList);
					ArrayList<JMenuLst> featureListWithoutDuplicates = new ArrayList<>(set);
					proPlanObjectFinal.setFeature_list(featureListWithoutDuplicates);
					proPlanObjectFinal.setPeriod_list(yearlyPeriods);
					proPlanObjectFinal.setPlan_name("Pro");

					if(!proPlanObjectFinal.getFeature_list().isEmpty() &&
							!proPlanObjectFinal.getPeriod_list().isEmpty()) {
						planlist_new.add(proPlanObjectFinal);
					}


				}
				else{
					// Other than pet monitor all feature i.e monthy/yearly will listed under one object
					for (JSubPlanDetailsV3 subPlan : proPlanList) {
						ArrayList<JSubPeriodDetail> periodList=subPlan.getPeriod_list();
						for(JSubPeriodDetail subPeriodDetail:periodList){
						if(subPlan.isIs_combo_plan() ) {
							if(defaultFreePlan)
								subPeriodDetail.setPaid_buynow("Continue");
							else
								subPeriodDetail.setPaid_buynow("Upgrade Now");
							comboPeriods.add(subPeriodDetail);
							comboFeatureList.addAll(subPlan.getFeature_list());
							comboPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
							if (!comboPlanObjectFinal.isIs_best_seller())
								comboPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
							comboPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());

						}else if(!subPlan.isIs_combo_plan()) {
							yearlyPeriods.add(subPeriodDetail);
							if(defaultFreePlan)
								subPeriodDetail.setPaid_buynow("Continue");
							else
								subPeriodDetail.setPaid_buynow("Upgrade Now");
							yearlyFeatureList.addAll(subPlan.getFeature_list());
							proPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
							if (!proPlanObjectFinal.isIs_best_seller())
								proPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
							proPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());
							if (comboPlanList.size() > 0) {
								proPlanObjectFinal.setShow_combo_content(true);
							}
						}
						}
					}
					Set<JMenuLst> set = new LinkedHashSet<>(yearlyFeatureList);
					ArrayList<JMenuLst> featureListWithoutDuplicates = new ArrayList<>(set);
					proPlanObjectFinal.setFeature_list(featureListWithoutDuplicates);
					proPlanObjectFinal.setPeriod_list(yearlyPeriods);
					proPlanObjectFinal.setPlan_name("Pro");

					Set<JMenuLst> setcombo = new LinkedHashSet<>(comboFeatureList);
					ArrayList<JMenuLst> featureListWithoutDuplicatesCombo = new ArrayList<>(setcombo);
					comboPlanObjectFinal.setFeature_list(featureListWithoutDuplicatesCombo);
					comboPlanObjectFinal.setPeriod_list(comboPeriods);
					comboPlanObjectFinal.setPlan_name("Combo");


					if(!proPlanObjectFinal.getFeature_list().isEmpty() &&
									!proPlanObjectFinal.getPeriod_list().isEmpty()) {
						planlist_new.add(proPlanObjectFinal);
					}

					if(!comboPlanObjectFinal.getPeriod_list().isEmpty()
							&& !comboPlanObjectFinal.getFeature_list().isEmpty()) {
						planlist_new.add(comboPlanObjectFinal);
					}

				}

				List<ComboContent> comboContentList=new ArrayList<>();

				comboContentList=crDao.getComboPlanContent();

				if(comboContentList.size()>0) {
					response.put("ComboContent", comboContentList.get(0));

				}

//				String free_text = monitor_type == 4 || monitor_type==5 ? "" : " for free";
//				String plan_lbl = "Wag-worthy"+ (defaultFreePlan ? free_text :"") +" benefits";

				// Changed conent for subscription plan Changes
				String plan_lbl="Plan Benefits";
				boolean show_coupon = true;
				JCouponInfo coupon_info = new JCouponInfo();
				String btn_text = "Continue";
				String currentSubStatus ="";

				if(plan.isIs_freeplan()&& !sub_id.equalsIgnoreCase("NA"))
					currentSubStatus = crService.getCurrentSubStatusForChargebeeUser(user.getChargebeeid());

				if( currentSubStatus.equalsIgnoreCase("NA") || currentSubStatus.isEmpty() )
					currentSubStatus =crDao.getSubscriptionStatus(sub_id).trim();

				if(plan.isIs_freeplan() && sub_id.equalsIgnoreCase("NA")) {
					coupon_info = new JCouponInfo(coupon_img_new, coupon_desc_new, coupon_code_new,btn_text );
				}
				else if (currentSubStatus.equalsIgnoreCase("active") || currentSubStatus.equalsIgnoreCase("non_renewing")){
					coupon_info = new JCouponInfo(coupon_img_upgrade, coupon_desc_upgrade, coupon_code_upgrade,btn_text );
				}
				else if(currentSubStatus.equalsIgnoreCase("cancelled")){
					coupon_info = new JCouponInfo(coupon_img_update, coupon_desc_update, coupon_code_update,btn_text );
				}else {
					coupon_info = new JCouponInfo(coupon_img_new, coupon_desc_new, coupon_code_new,btn_text );
				}
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("coupon_info", coupon_info);
				response.put("show_coupon", show_coupon);
				response.put("planlist", planlist_new);
				response.put("planlist_comb",comboPlanList);
				response.put("plan_lbl",plan_lbl);
				response.put("popup", "Worried about your furry friend's well-being? Enjoy $~"+free_trial_days+"-days trial of the "+plan_name+", for your pet's health.");
				// for custom plan
				response.put("planname", "Please contact Support");
				response.put("contactnumber", supportcontactnumber.get(country));
				response.put("email", supportemail.get(country));
			}
		} catch (Exception e) {
			e.printStackTrace();
			response.put("Status", 0);
			response.put("Msg", "Contact Support");
			return response;
		}
			return response;
	}

	@Override
	public boolean checkComboExists(String cbid) {
		return crDao.checkComboExists(cbid);
	}

	@Override
	public boolean checkComboExistsinallchargebee(String cbid) {
		return crDao.checkComboExistsinallchargebee(cbid);
	}

	@Override
	public List<GatewaytoFeature> listGatewaytoFeature(long gatewayId)  {
		return crDao.listGatewaytoFeature(gatewayId);
	}

	@Override
	public boolean createGatewaytoFeature(GatewaytoFeature gatewayfeature)  {
		return crDao.saveOrUpdateGatewaytoFeature(gatewayfeature);
	}

	@Override
	public GatewaytoFeature getGFByGatewayFeature(long gatewayId, long featureid){

		return crDao.getGFByGatewayFeature(gatewayId,featureid);
	}

	@Override
	public String getplantypebychargebeeid(String planId) {
		return crDao.getplantypebychargebeeid(planId);
	}

	@Override
	public boolean deleteGatewaytoFeature(long id){
		return crDao.deleteGatewaytoFeature(id);
	}

	@Override
	public List<Feature> listFeatureByGatewayId(int gatewayId){

		return crDao.listFeatureByGatewayId(gatewayId);

	}

	@Override
	public JResponse getUpgradeSubPlanV9(UserV4 user, long curplan_id, long curperiod_id, long monitor_type, String country,
										 boolean defaultFreePlan, long gatewayid, String type, String plantype, String sub_id, boolean restrict_flexi_plan_period,
										 boolean is_flexi) {
		log.info("Entered into getUpgradeSubPlanV9 :: user_id : " + user.getId());
		JResponse response = new JResponse();
		try {
			JPlanInfo plan = crService.getPlan(curplan_id);
			if (plan == null) {
				plan = new JPlanInfo();
			}
			List<Object[]> planResp = crDao.getUpgradeSubPlanV7(user, curplan_id, curperiod_id, monitor_type,
					country, defaultFreePlan, gatewayid, "upgrade",plantype, is_flexi);

			List<Object[]> downplanResp = crDao.getUpgradeSubPlanV7(user, curplan_id, curperiod_id, monitor_type,
					country, defaultFreePlan, gatewayid, "downgrade",plantype, is_flexi);

			List<JSubPeriodDetailV1> planlist_new = new ArrayList<>(); //Final list

			if (planResp != null) {
				long plan_id = 0;
				long per_id = 0;
				String content1 = "";
				String free_buynow="";
				String period_name = "";
				String strike_price="";
				String plan_price = "";
				String display_price="";
				boolean is_free = false;
				boolean is_best_deal_plan = false;
				List<String> description;
				String compare_image="";
				String product_image="";


				ArrayList<JSubPlanDetailsV4> comboPlanList = new ArrayList<JSubPlanDetailsV4>();
				ArrayList<JSubPlanDetailsV4> proPlanList = new ArrayList<JSubPlanDetailsV4>();
				ArrayList<JSubPlanDetailsV4> vetPlanList = new ArrayList<JSubPlanDetailsV4>();

				String plan_name ="";
				int free_trial_days = 0;
				String display_msg="";
				String billed_price = "";
				String month_price = "";
				boolean is_best_deal = false;
				boolean free_minicam_avil = false;
				boolean has_free = false;
				int cur_monitor_id = 1; // for deciding combo-plan. If -1, its for other products
				String desc_content="";
				ArrayList<Object> productList = new ArrayList<Object>();

				outerLoop : for(int j=0;j<planResp.size();j++) {
					int size = planResp.size();
					log.info("upgradePlanListV7 : size:" + size);
					JSubPlanDetailsV4 planDetail = new JSubPlanDetailsV4();
					Object[] tuple = (Object[]) planResp.get(0);

					long free_plan = 0;
					long free_period = 0;
					boolean isfree_eligible = false;
					JPlanDetail free_features=null;
					JSubPeriodDetailV1 periodObj = new JSubPeriodDetailV1();
					ArrayList<JSubPeriodDetailV1> periodlist = new ArrayList<JSubPeriodDetailV1>();

					boolean orderAvail = false;
					if(monitor_type == 6 && gatewayid>0 && defaultFreePlan) {
						String[] dtRange = orderdate_trial.split(":");

						orderAvail = gatewayServiceV4.checkOrderWithinRange(gatewayid, dtRange[0], dtRange[1]);
					}
					if (size == 1) {
						plan_id = ((BigInteger) tuple[0]).longValue();
						plan_name = (String) tuple[1];
						per_id = ((BigInteger) tuple[2]).longValue();
						String feature_list = (String) tuple[3];
						JPlanDetail jplandetail = new JPlanDetail();

						if(restrict_flexi_plan_period && per_id <= curperiod_id){
							continue;
						}
						try {

							jplandetail = new Gson().fromJson(feature_list, JPlanDetail.class);

						} catch (JSONException err) {
							log.error("jplandetail:"+err.getLocalizedMessage());
						}

						content1 = (String) tuple[5];
						is_free = tuple[6] instanceof Boolean ? (boolean) tuple[6] : ((byte) tuple[6]) > 0;
						period_name = (String) tuple[7];
						strike_price= ((int)tuple[8] >0) ? "$"+tuple[8]: "";
						plan_price = ((String) tuple[9])==null ? "" : (String) tuple[9] ;
						display_price=(String) tuple[10];
						display_msg = (String) tuple[12];
						billed_price = (String) tuple[13];
						month_price = (String) tuple[14];
						is_best_deal = tuple[15] instanceof Boolean ? (boolean) tuple[15] : ((byte) tuple[15]) > 0;
						String planType =  (String) tuple[17];
						description = Arrays.asList(((String) tuple[18]).split("\\,"));
						is_best_deal_plan =  tuple[19] instanceof Boolean ? (boolean) tuple[19] : ((byte) tuple[19]) > 0;
						compare_image = (String)  tuple[20];
						product_image = (String) tuple[21];
						desc_content = (String) tuple[24];
						String productList1 = (String) tuple[25];
						if(productList1 != null) {
							ObjectMapper mapper = new ObjectMapper();
							try {
								productList = mapper.readValue(productList1, new TypeReference<ArrayList<Object>>() {});
							} catch (Exception e) {
								e.printStackTrace();
							}
						}
						ArrayList<Terms> term_list = new ArrayList<Terms>();

						if (tuple[16] != null) {
							try {
								JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);
								term_list = terms.getTerm_list();
							} catch (Exception err) {
								log.error("jplan-terms:"+err.getLocalizedMessage());
							}
						}

						if(is_free) {
							isfree_eligible = true;
							free_plan = plan_id;
							free_period =per_id;
							free_features = jplandetail;
							periodObj = new JSubPeriodDetailV1(free_plan, free_period, content1, 0,period_name, "",
									strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal);
							periodlist.add(periodObj);

							if(monitor_type == 6 && orderAvail && defaultFreePlan) {
								free_trial_days = offer_days;
							}else {
								free_trial_days = defaultFreePlan ? (int) tuple[11] : 0;
							}

						}else {
							periodObj = new JSubPeriodDetailV1( 0,per_id, "",plan_id,period_name,content1,
									strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal,
									description, monitor_type==1 ? compare_image : compare_image_other_products, product_image);
							periodlist.add(periodObj);
						}

						planDetail.setTerm_list(term_list);
						planDetail.setPeriod_list(periodlist);
						planDetail.setIs_best_seller(is_best_deal_plan);
						planDetail.setPlan_name(plan_name);
						planDetail.setDisplay_content(desc_content);
						planDetail.setProduct_list(productList);
						if(planType.equalsIgnoreCase("data-plan")) {
							if (monitor_type == 1)
								planDetail.setPlan_name("Pro");
							planDetail.setManagePlanId(2);
							proPlanList.add(planDetail);}
						else if(planType.equalsIgnoreCase("combo-plan")) {
							planDetail.setPlan_name(plan_name);
							planDetail.setManagePlanId(1);
							planDetail.setIs_combo_plan(true);
							proPlanList.add(planDetail);}
						else if(planType.equalsIgnoreCase("vet-plan")){
							planDetail.setManagePlanId(2);
							proPlanList.add(planDetail);
						}
						else {
							planDetail.setPlan_name(plan_name);
							planDetail.setIs_flexiplan(true);
							planDetail.setManagePlanId(3);
							planDetail.setPlan_details(flexi_plan_details);
							proPlanList.add(planDetail);
						}
						break outerLoop;
					}

					innerLoop : for (int i = j; i < size; i++) {
						tuple = (Object[]) planResp.get(i);
						plan_id = ((BigInteger) tuple[0]).longValue();
						plan_name = (String) tuple[1];
						per_id = ((BigInteger) tuple[2]).longValue();
						String feature_list = (String) tuple[3];
						JPlanDetail jplandetail = new JPlanDetail();
						content1 = (String) tuple[5];
						is_free = tuple[6] instanceof Boolean ? (boolean) tuple[6] : ((byte) tuple[6]) > 0;
						period_name = (String) tuple[7];
						strike_price= ((int)tuple[8] >0) ? "$"+tuple[8]: "";
						plan_price = ((String) tuple[9])==null ? "" : (String) tuple[9];

						display_price=(String) tuple[10];
						display_msg = (String) tuple[12];
						billed_price = (String) tuple[13];
						month_price = (String) tuple[14];
						is_best_deal = tuple[15] instanceof Boolean ? (boolean) tuple[15] : ((byte) tuple[15]) > 0;
						String planType =  (String) tuple[17];
						description = Arrays.asList(((String) tuple[18]).split("\\,"));
						is_best_deal_plan =  tuple[19] instanceof Boolean ? (boolean) tuple[19] : ((byte) tuple[19]) > 0;
						compare_image = (String)  tuple[20];
						product_image = (String) tuple[21];
						cur_monitor_id = ((BigInteger) tuple[22]).intValue();
						free_minicam_avil = tuple[23] instanceof Boolean ? (boolean) tuple[23] : ((byte) tuple[23]) > 0;
						desc_content = (String) tuple[24];
						String productList1 = (String) tuple[25];
						if(productList1 != null) {
							ObjectMapper mapper = new ObjectMapper();
							try {
								productList = mapper.readValue(productList1, new TypeReference<ArrayList<Object>>() {});
							} catch (Exception e) {
								e.printStackTrace();
							}
						}
						if(monitor_type == 1){
							String sub = cbService.getProductSubscriptionStatus(gatewayid,user.getChargebeeid());
							if (sub.equalsIgnoreCase("NA") || !sub.equalsIgnoreCase("CANCELLED")) {
								free_minicam_avil = false;
							}
						}

						if(monitor_type == 1 && cur_monitor_id == -1)
							break innerLoop;

						try {
							jplandetail = new Gson().fromJson(feature_list, JPlanDetail.class);
						} catch (JSONException err) {
							log.error("jplandetail:"+err.getLocalizedMessage());
						}

						ArrayList<Terms> term_list = new ArrayList<Terms>();

						if (tuple[16] != null) {
							try {
								JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);
								term_list = terms.getTerm_list();
							} catch (Exception err) {
								log.error("jplan-terms:"+err.getLocalizedMessage());
							}
						}
						if(is_free) {
							isfree_eligible = true;
							free_plan = plan_id;
							free_period =per_id;
							free_features = jplandetail;
							free_buynow = content1;

							if(monitor_type == 6 && orderAvail && defaultFreePlan) {
								free_trial_days = offer_days;
							}else {
								free_trial_days = defaultFreePlan ? (int) tuple[11] : 0;
							}
						}

						if (i == size - 1) {
							if(monitor_type == 1) {
								if(!(restrict_flexi_plan_period && per_id <= curperiod_id)){
									periodObj = new JSubPeriodDetailV1(!defaultFreePlan ? 0 : free_plan, per_id, content1,
											plan_id,period_name , content1, strike_price, plan_price,
											display_price,display_msg,billed_price,month_price,is_best_deal,
											description, compare_image, product_image,free_minicam_avil);
									periodlist.add(periodObj);
								}
								planDetail.setTerm_list(term_list);
								planDetail.setPeriod_list(periodlist);
								planDetail.setPlan_name("Pro");
								planDetail.setIs_best_seller(is_best_deal_plan);
								planDetail.setDisplay_content(desc_content);
								planDetail.setProduct_list(productList);
								if(planType.equalsIgnoreCase("data-plan")) {
									planDetail.setManagePlanId(2);
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("combo-plan")) {
									planDetail.setPlan_name(plan_name);
									planDetail.setManagePlanId(1);
									planDetail.setIs_combo_plan(true);
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("vet-plan")){
									planDetail.setManagePlanId(2);
									proPlanList.add(planDetail);
								}
								else {
									planDetail.setPlan_name(plan_name);
									planDetail.setIs_flexiplan(true);
									planDetail.setManagePlanId(3);
									planDetail.setPlan_details(flexi_plan_details);
									proPlanList.add(planDetail);
								}
								has_free = false;
								break outerLoop;
							} else {
								periodObj = new JSubPeriodDetailV1(!defaultFreePlan ? 0 : free_plan, per_id, content1,
										plan_id, period_name, content1, strike_price, plan_price,
										display_price, display_msg, billed_price, month_price, is_best_deal,
										description, compare_image_other_products, product_image,free_minicam_avil);
								periodlist.add(periodObj);
								planDetail.setTerm_list(term_list);
								planDetail.setPeriod_list(periodlist);
								planDetail.setPlan_name(plan_name);
								planDetail.setDisplay_content(desc_content);
								planDetail.setProduct_list(productList);
								planDetail.setIs_flexiplan(false);

								if(planType.equalsIgnoreCase("data-plan")) {
									planDetail.setManagePlanId(2);
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("combo-plan")) {
									planDetail.setPlan_name(plan_name);
									planDetail.setManagePlanId(1);
									planDetail.setIs_combo_plan(true);
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("vet-plan")){
									planDetail.setManagePlanId(2);
									proPlanList.add(planDetail);
								}
								else {
									planDetail.setPlan_name(plan_name);
									planDetail.setIs_flexiplan(true);
									planDetail.setManagePlanId(3);
									planDetail.setPlan_details(flexi_plan_details);
									proPlanList.add(planDetail);
								}
								has_free = false;
								break outerLoop;
							}
						} else {
							Object[] tupleNext = (Object[]) planResp.get(i + 1);
							long plan_id_next = ((BigInteger) tupleNext[0]).longValue();
							String plan_name_next = (String) tupleNext[1];
							long per_id_next = ((BigInteger) tupleNext[2]).longValue();
							String feature_list_next = (String) tupleNext[3];
							JPlanDetail jplandetailNext = new JPlanDetail();
							boolean is_free_next = tupleNext[6] instanceof Boolean ? (boolean) tupleNext[6] : ((byte) tupleNext[6]) > 0;
							String content1_next = (String) tupleNext[5];
							String period_name_next = (String) tupleNext[7];
							String strike_price_next = ((int)tupleNext[8] >0) ? "$"+tupleNext[8]: "";
							String plan_price_next = ((String) tupleNext[9])==null ? "" : (String) tupleNext[9] ;
							String display_price_next=(String) tupleNext[10];
							String display_msg_next = (String) tupleNext[12];
							String billed_price_next = (String) tupleNext[13];
							String month_price_next = (String) tupleNext[14];
							boolean is_best_deal_next =tupleNext[15] instanceof Boolean ? (boolean) tupleNext[15] : ((byte) tupleNext[15]) > 0;

							try {
								jplandetailNext = new Gson().fromJson(feature_list_next, JPlanDetail.class);
							} catch (JSONException err) {
								log.error("jplandetail:"+err.getLocalizedMessage());
							}

							if (tuple[16] != null) {
								try {
									JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);
									term_list = terms.getTerm_list();
								} catch (Exception err) {
									log.error("jplan-terms:"+err.getLocalizedMessage());
								}
							}
							if(is_free_next) {
								free_plan = plan_id_next;
								free_period =per_id_next;
								free_features = jplandetailNext;
								if(monitor_type == 6 && orderAvail && defaultFreePlan) {
									free_trial_days = offer_days;
								}else {
									free_trial_days = defaultFreePlan ? (int) tupleNext[11] : 0;
								}
							}
							if ((plan_id != plan_id_next)) {
								if (monitor_type == 1) {
									if (isfree_eligible) {
										periodObj = new JSubPeriodDetailV1(!defaultFreePlan ? 0 : free_plan, per_id_next, content1,
												plan_id_next,period_name_next , content1_next,
												strike_price_next, plan_price_next,  display_price_next,display_msg_next,billed_price_next,month_price_next,is_best_deal_next);
										periodlist.add(periodObj);
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name_next);

									} else {
										if(!(restrict_flexi_plan_period && per_id <= curperiod_id)){
											periodObj = new JSubPeriodDetailV1(!defaultFreePlan ? 0 : free_plan, per_id, content1,
													plan_id,period_name , content1,
													strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal,
													description, compare_image, product_image,free_minicam_avil);
											periodlist.add(periodObj);
										}
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name);
										planDetail.setDisplay_content(desc_content);
										planDetail.setProduct_list(productList);
										planDetail.setIs_best_seller(is_best_deal_plan);
									}
									if(planType.equalsIgnoreCase("data-plan")) {
										planDetail.setManagePlanId(2);
										planDetail.setPlan_name("Pro");
										planDetail.setDisplay_content(desc_content);
										planDetail.setProduct_list(productList);
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("combo-plan")) {
										planDetail.setPlan_name(plan_name);
										planDetail.setManagePlanId(1);
										planDetail.setDisplay_content(desc_content);
										planDetail.setProduct_list(productList);
										planDetail.setIs_combo_plan(true);
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("vet-plan")){
										planDetail.setManagePlanId(2);
										proPlanList.add(planDetail);
									}
									else {
										planDetail.setPlan_name(plan_name);
										planDetail.setIs_flexiplan(true);
										planDetail.setDisplay_content(desc_content);
										planDetail.setProduct_list(productList);
										planDetail.setManagePlanId(3);
										planDetail.setPlan_details(flexi_plan_details);
										proPlanList.add(planDetail);
									}
									j=i;
									break innerLoop;
								} else if(monitor_type == 11) {
									periodObj = new JSubPeriodDetailV1(!defaultFreePlan ? 0 : free_plan, per_id, content1,
											plan_id, period_name, content1,
											strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal);
									periodlist.add(periodObj);
									planDetail.setTerm_list(term_list);
									planDetail.setPeriod_list(periodlist);
									planDetail.setPlan_name(plan_name);
									planDetail.setDisplay_content(desc_content);
									planDetail.setProduct_list(productList);
									if(planType.equalsIgnoreCase("data-plan")) {
										planDetail.setManagePlanId(2);
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("combo-plan")) {
										planDetail.setPlan_name(plan_name);
										planDetail.setManagePlanId(1);
										planDetail.setIs_combo_plan(true);
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("vet-plan")){
										planDetail.setManagePlanId(2);
										proPlanList.add(planDetail);
									}
									else {
										planDetail.setPlan_name(plan_name);
										planDetail.setIs_flexiplan(true);
										planDetail.setManagePlanId(3);
										planDetail.setPlan_details(flexi_plan_details);
										proPlanList.add(planDetail);
									}
									j = i;
									break innerLoop;
								} else {
									if (isfree_eligible) {
//										periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
//												plan_id, period_name, content1,
//												strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal);
//										periodlist.add(periodObj);
//										planDetail.setPeriod_list(periodlist);
										isfree_eligible = false;
										has_free = true;
									} else {
//										if (has_free) {
//											content1 = free_buynow;
//										}
										periodObj = new JSubPeriodDetailV1(!defaultFreePlan ? 0 : free_plan, per_id, has_free ? free_buynow : "",
												plan_id, period_name, content1,
												strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal,
												description, compare_image_other_products, product_image,free_minicam_avil);
										periodlist.add(periodObj);
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name);
										planDetail.setDisplay_content(desc_content);
										planDetail.setProduct_list(productList);
										if(planType.equalsIgnoreCase("data-plan")) {
											planDetail.setManagePlanId(2);
											proPlanList.add(planDetail);
											j=i;
											has_free = false;
											break innerLoop;
										}
										else if(planType.equalsIgnoreCase("combo-plan")) {
											planDetail.setPlan_name(plan_name);
											planDetail.setManagePlanId(1);
											planDetail.setIs_combo_plan(true);
											proPlanList.add(planDetail);
											has_free = false;
											break innerLoop;
										} else if(planType.equalsIgnoreCase("vet-plan")){
											planDetail.setManagePlanId(2);
											proPlanList.add(planDetail);
										}
										else {
											planDetail.setPlan_name(plan_name);
											planDetail.setIs_flexiplan(true);
											planDetail.setManagePlanId(3);
											planDetail.setPlan_details(flexi_plan_details);
											proPlanList.add(planDetail);
										}
									}
								}
							} else if (!isfree_eligible){
								if(!(restrict_flexi_plan_period && per_id <= curperiod_id)) {
									periodObj = new JSubPeriodDetailV1(!defaultFreePlan ? 0 : free_plan, per_id, has_free ? free_buynow : "", plan_id, period_name, content1,
											strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal,
											description, compare_image_other_products, product_image,free_minicam_avil);
									periodlist.add(periodObj);
								}
							}
						}
					}
				}

				JSubPlanDetailsV4 basicPlanObjectFinal = new JSubPlanDetailsV4();
				ArrayList<JMenuLst>  yearlyFeatureList=new ArrayList<>();
				ArrayList<JMenuLst> monthlyFeatureList=new ArrayList<>();
				if(!proPlanList.isEmpty() && monitor_type==1){
					for (JSubPlanDetailsV4 subPlan : proPlanList) {
						ArrayList<JSubPeriodDetailV1> periodList=subPlan.getPeriod_list();
						for(JSubPeriodDetailV1 subPeriodDetail:periodList){
							if(subPeriodDetail.getPeriod_id() < 3 && !subPlan.isIs_flexiplan()){
								subPeriodDetail.setManagePlanId(4);
							}else{
								subPeriodDetail.setManagePlanId(subPlan.getManagePlanId());
							}

							subPeriodDetail.setPlan_name(subPlan.getPlan_name());
							subPeriodDetail.setDisplay_content(subPlan.getDisplay_content());
							subPeriodDetail.setProduct_list(subPlan.getProduct_list());
							if(subPeriodDetail.getPaidplan_id() > 0)
								planlist_new.add(subPeriodDetail);
								basicPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
							    basicPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
								basicPlanObjectFinal.setManagePlanId(subPlan.getManagePlanId());
								basicPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());
								if(subPeriodDetail.getPeriod_id()==3)
									subPeriodDetail.setFlexiPlan(true);
								if(subPlan.isIs_flexiplan())
									basicPlanObjectFinal.setPlan_details(flexi_plan_details);

						}
					}
					Set<JMenuLst> setyearly = new LinkedHashSet<>(yearlyFeatureList);
					ArrayList<JMenuLst> featureListWithoutDuplicatesYearly = new ArrayList<>(setyearly);

					// Handled for Monthly plan to remove duplicate feature Plan
					ArrayList<JMenuLst> featureListWithoutDuplicatesMonthly = new ArrayList<>(
							monthlyFeatureList.stream()
									.collect(Collectors.toMap(
											JMenuLst::getId, // key selector
											Function.identity(),      // keep the first object with this key
											(existing, replacement) -> existing, // merge function for duplicates
											LinkedHashMap::new        // preserve order
									))
									.values()
					);

				}
				else{
					// Other than pet monitor all feature i.e monthy/yearly will listed under one object
					for (JSubPlanDetailsV4 subPlan : proPlanList) {
						ArrayList<JSubPeriodDetailV1> periodList=subPlan.getPeriod_list();
						for(JSubPeriodDetailV1 subPeriodDetail:periodList){
							if(subPeriodDetail.getPeriod_id() < 3 && !subPlan.isIs_flexiplan()){
								subPeriodDetail.setManagePlanId(4);
							}else{
								subPeriodDetail.setManagePlanId(subPlan.getManagePlanId());
							}
							subPeriodDetail.setPlan_name(subPlan.getPlan_name());
							subPeriodDetail.setDisplay_content(subPlan.getDisplay_content());
							subPeriodDetail.setProduct_list(subPlan.getProduct_list());
							if(subPeriodDetail.getPaidplan_id() > 0)
								planlist_new.add(subPeriodDetail);
							basicPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
							basicPlanObjectFinal.setManagePlanId(subPlan.getManagePlanId());
							if(!basicPlanObjectFinal.isIs_best_seller())
								basicPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
							basicPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());
						}
					}

				}

				List<ComboContent> comboContentList=new ArrayList<>();

				comboContentList=crDao.getComboPlanContent();

				if(comboContentList.size()>0) {
					response.put("ComboContent", comboContentList.get(0));

				}

//				String free_text = monitor_type == 4 || monitor_type==5 ? "" : " for free";
//				String plan_lbl = "Wag-worthy"+ (defaultFreePlan ? free_text :"") +" benefits";

				// Changed conent for subscription plan Changes
				String plan_lbl="View Plan Benefits";
				boolean show_coupon = true;
				String btn_text = "Continue";
				String currentSubStatus ="";

				if(plan.isIs_freeplan()&& !sub_id.equalsIgnoreCase("NA"))
					currentSubStatus = crService.getCurrentSubStatusForChargebeeUser(user.getChargebeeid());

				if( currentSubStatus.equalsIgnoreCase("NA") || currentSubStatus.isEmpty() )
					currentSubStatus =crDao.getSubscriptionStatus(sub_id).trim();
			}

			if (downplanResp != null) {
				long plan_id = 0;
				long per_id = 0;
				String content1 = "";
				String free_buynow="";
				String period_name = "";
				String strike_price="";
				String plan_price = "";
				String display_price="";
				boolean is_free = false;
				boolean is_best_deal_plan = false;
				List<String> description;
				String compare_image="";
				String product_image="";


				ArrayList<JSubPlanDetailsV4> comboPlanList = new ArrayList<JSubPlanDetailsV4>();
				ArrayList<JSubPlanDetailsV4> proPlanList = new ArrayList<JSubPlanDetailsV4>();
				ArrayList<JSubPlanDetailsV4> vetPlanList = new ArrayList<JSubPlanDetailsV4>();

				String plan_name ="";
				int free_trial_days = 0;
				String display_msg="";
				String billed_price = "";
				String month_price = "";
				boolean is_best_deal = false;
				boolean free_minicam_avil = false;
				boolean has_free = false;
				int cur_monitor_id = 1; // for deciding combo-plan. If -1, its for other products
				String desc_content = "";
				ArrayList<Object> productList = new ArrayList<Object>();

				outerLoop : for(int j=0;j<downplanResp.size();j++) {
					int size = downplanResp.size();
					log.info("upgradePlanListV7 : size:" + size);
					JSubPlanDetailsV4 planDetail = new JSubPlanDetailsV4();
					Object[] tuple = (Object[]) downplanResp.get(0);

					long free_plan = 0;
					long free_period = 0;
					boolean isfree_eligible = false;
					JPlanDetail free_features=null;
					JSubPeriodDetailV1 periodObj = new JSubPeriodDetailV1();
					ArrayList<JSubPeriodDetailV1> periodlist = new ArrayList<JSubPeriodDetailV1>();

					boolean orderAvail = false;
					if(monitor_type == 6 && gatewayid>0 && defaultFreePlan) {
						String[] dtRange = orderdate_trial.split(":");

						orderAvail = gatewayServiceV4.checkOrderWithinRange(gatewayid, dtRange[0], dtRange[1]);
					}
					if (size == 1) {
						plan_id = ((BigInteger) tuple[0]).longValue();
						plan_name = (String) tuple[1];
						per_id = ((BigInteger) tuple[2]).longValue();
						String feature_list = (String) tuple[3];
						JPlanDetail jplandetail = new JPlanDetail();

						if(restrict_flexi_plan_period && per_id <= curperiod_id){
							continue;
						}
						try {

							jplandetail = new Gson().fromJson(feature_list, JPlanDetail.class);

						} catch (JSONException err) {
							log.error("jplandetail:"+err.getLocalizedMessage());
						}

						content1 = (String) tuple[5];
						is_free = tuple[6] instanceof Boolean ? (boolean) tuple[6] : ((byte) tuple[6]) > 0;
						period_name = (String) tuple[7];
						strike_price= ((int)tuple[8] >0) ? "$"+tuple[8]: "";
						plan_price = ((String) tuple[9])==null ? "" : (String) tuple[9] ;
						display_price=(String) tuple[10];
						display_msg = (String) tuple[12];
						billed_price = (String) tuple[13];
						month_price = (String) tuple[14];
						is_best_deal = tuple[15] instanceof Boolean ? (boolean) tuple[15] : ((byte) tuple[15]) > 0;
						String planType =  (String) tuple[17];
						description = Arrays.asList(((String) tuple[18]).split("\\,"));
						is_best_deal_plan =  tuple[19] instanceof Boolean ? (boolean) tuple[19] : ((byte) tuple[19]) > 0;
						compare_image = (String)  tuple[20];
						product_image = (String) tuple[21];
						desc_content = (String) tuple[24];
						String productList1 = (String) tuple[25];
						if(productList1 != null) {
							ObjectMapper mapper = new ObjectMapper();
							try {
								productList = mapper.readValue(productList1, new TypeReference<ArrayList<Object>>() {});
							} catch (Exception e) {
								e.printStackTrace();
							}
						}

						ArrayList<Terms> term_list = new ArrayList<Terms>();

						if (tuple[16] != null) {
							try {
								JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);
								term_list = terms.getTerm_list();
							} catch (Exception err) {
								log.error("jplan-terms:"+err.getLocalizedMessage());
							}
						}

						if(is_free) {
							isfree_eligible = true;
							free_plan = plan_id;
							free_period =per_id;
							free_features = jplandetail;
							periodObj = new JSubPeriodDetailV1(free_plan, free_period, content1, 0,period_name, "",
									strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal);
							periodlist.add(periodObj);

							if(monitor_type == 6 && orderAvail && defaultFreePlan) {
								free_trial_days = offer_days;
							}else {
								free_trial_days = defaultFreePlan ? (int) tuple[11] : 0;
							}

						}else {
							periodObj = new JSubPeriodDetailV1( 0,per_id, "",plan_id,period_name,content1,
									strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal,
									description, monitor_type==1 ? compare_image : compare_image_other_products, product_image);
							periodlist.add(periodObj);
						}

						planDetail.setTerm_list(term_list);
						planDetail.setPeriod_list(periodlist);
						planDetail.setIs_best_seller(is_best_deal_plan);
						planDetail.setPlan_name(plan_name);
						planDetail.setDisplay_content(desc_content);
						planDetail.setProduct_list(productList);
						if(planType.equalsIgnoreCase("data-plan")) {
							if (monitor_type == 1)
								planDetail.setPlan_name("Pro");
							planDetail.setManagePlanId(4);
							proPlanList.add(planDetail);}
						else if(planType.equalsIgnoreCase("combo-plan")) {
							planDetail.setPlan_name(plan_name);
							planDetail.setManagePlanId(1);
							planDetail.setIs_combo_plan(true);
							proPlanList.add(planDetail);}
						else if(planType.equalsIgnoreCase("vet-plan")){
							planDetail.setManagePlanId(2);
							proPlanList.add(planDetail);
						}
						else {
							planDetail.setPlan_name(plan_name);
							planDetail.setIs_flexiplan(true);
							planDetail.setManagePlanId(3);
							planDetail.setPlan_details(flexi_plan_details);
							proPlanList.add(planDetail);
						}
						break outerLoop;
					}

					innerLoop : for (int i = j; i < size; i++) {
						tuple = (Object[]) downplanResp.get(i);
						plan_id = ((BigInteger) tuple[0]).longValue();
						plan_name = (String) tuple[1];
						per_id = ((BigInteger) tuple[2]).longValue();
						String feature_list = (String) tuple[3];
						JPlanDetail jplandetail = new JPlanDetail();
						content1 = (String) tuple[5];
						is_free = tuple[6] instanceof Boolean ? (boolean) tuple[6] : ((byte) tuple[6]) > 0;
						period_name = (String) tuple[7];
						strike_price= ((int)tuple[8] >0) ? "$"+tuple[8]: "";
						plan_price = ((String) tuple[9])==null ? "" : (String) tuple[9];

						display_price=(String) tuple[10];
						display_msg = (String) tuple[12];
						billed_price = (String) tuple[13];
						month_price = (String) tuple[14];
						is_best_deal = tuple[15] instanceof Boolean ? (boolean) tuple[15] : ((byte) tuple[15]) > 0;
						String planType =  (String) tuple[17];
						description = Arrays.asList(((String) tuple[18]).split("\\,"));
						is_best_deal_plan =  tuple[19] instanceof Boolean ? (boolean) tuple[19] : ((byte) tuple[19]) > 0;
						compare_image = (String)  tuple[20];
						product_image = (String) tuple[21];
						cur_monitor_id = ((BigInteger) tuple[22]).intValue();
						free_minicam_avil = tuple[23] instanceof Boolean ? (boolean) tuple[23] : ((byte) tuple[23]) > 0;
						desc_content = (String) tuple[24];
						String productList1 = (String) tuple[25];
						if(productList1 != null) {
							ObjectMapper mapper = new ObjectMapper();
							try {
								productList = mapper.readValue(productList1, new TypeReference<ArrayList<Object>>() {});
							} catch (Exception e) {
								e.printStackTrace();
							}
						}
						if(monitor_type == 1){
							String sub = cbService.getProductSubscriptionStatus(gatewayid,user.getChargebeeid());
							if (sub.equalsIgnoreCase("NA") || !sub.equalsIgnoreCase("CANCELLED")) {
								free_minicam_avil = false;
							}
						}

						if(monitor_type == 1 && cur_monitor_id == -1)
							break innerLoop;

						try {
							jplandetail = new Gson().fromJson(feature_list, JPlanDetail.class);
						} catch (JSONException err) {
							log.error("jplandetail:"+err.getLocalizedMessage());
						}

						ArrayList<Terms> term_list = new ArrayList<Terms>();

						if (tuple[16] != null) {
							try {
								JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);
								term_list = terms.getTerm_list();
							} catch (Exception err) {
								log.error("jplan-terms:"+err.getLocalizedMessage());
							}
						}
						if(is_free) {
							isfree_eligible = true;
							free_plan = plan_id;
							free_period =per_id;
							free_features = jplandetail;
							free_buynow = content1;

							if(monitor_type == 6 && orderAvail && defaultFreePlan) {
								free_trial_days = offer_days;
							}else {
								free_trial_days = defaultFreePlan ? (int) tuple[11] : 0;
							}
						}

						if (i == size - 1) {
							if(monitor_type == 1) {
								if(!(restrict_flexi_plan_period && per_id <= curperiod_id)){
									periodObj = new JSubPeriodDetailV1(!defaultFreePlan ? 0 : free_plan, per_id, content1,
											plan_id,period_name , content1, strike_price, plan_price,
											display_price,display_msg,billed_price,month_price,is_best_deal,
											description, compare_image, product_image,free_minicam_avil);
									periodlist.add(periodObj);
								}
								planDetail.setTerm_list(term_list);
								planDetail.setPeriod_list(periodlist);
								planDetail.setPlan_name("Pro");
								planDetail.setIs_best_seller(is_best_deal_plan);
								planDetail.setDisplay_content(desc_content);
								planDetail.setProduct_list(productList);
								if(planType.equalsIgnoreCase("data-plan")) {
									planDetail.setManagePlanId(4);
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("combo-plan")) {
									planDetail.setPlan_name(plan_name);
									planDetail.setManagePlanId(1);
									planDetail.setIs_combo_plan(true);
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("vet-plan")){
									planDetail.setManagePlanId(2);
									proPlanList.add(planDetail);
								}
								else {
									planDetail.setPlan_name(plan_name);
									planDetail.setIs_flexiplan(true);
									planDetail.setManagePlanId(3);
									planDetail.setPlan_details(flexi_plan_details);
									proPlanList.add(planDetail);
								}
								has_free = false;
								break outerLoop;
							} else {
								periodObj = new JSubPeriodDetailV1(!defaultFreePlan ? 0 : free_plan, per_id, content1,
										plan_id, period_name, content1, strike_price, plan_price,
										display_price, display_msg, billed_price, month_price, is_best_deal,
										description, compare_image_other_products, product_image,free_minicam_avil);
								periodlist.add(periodObj);
								planDetail.setTerm_list(term_list);
								planDetail.setPeriod_list(periodlist);
								planDetail.setPlan_name(plan_name);
								planDetail.setDisplay_content(desc_content);
								planDetail.setProduct_list(productList);
								planDetail.setIs_flexiplan(false);

								if(planType.equalsIgnoreCase("data-plan")) {
									planDetail.setManagePlanId(4);
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("combo-plan")) {
									planDetail.setPlan_name(plan_name);
									planDetail.setManagePlanId(1);
									planDetail.setIs_combo_plan(true);
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("vet-plan")){
									planDetail.setManagePlanId(2);
									proPlanList.add(planDetail);
								}
								else {
									planDetail.setPlan_name(plan_name);
									planDetail.setManagePlanId(3);
									planDetail.setIs_flexiplan(true);
									planDetail.setPlan_details(flexi_plan_details);
									proPlanList.add(planDetail);
								}
								has_free = false;
								break outerLoop;
							}
						} else {
							Object[] tupleNext = (Object[]) downplanResp.get(i + 1);
							long plan_id_next = ((BigInteger) tupleNext[0]).longValue();
							String plan_name_next = (String) tupleNext[1];
							long per_id_next = ((BigInteger) tupleNext[2]).longValue();
							String feature_list_next = (String) tupleNext[3];
							JPlanDetail jplandetailNext = new JPlanDetail();
							boolean is_free_next = tupleNext[6] instanceof Boolean ? (boolean) tupleNext[6] : ((byte) tupleNext[6]) > 0;
							String content1_next = (String) tupleNext[5];
							String period_name_next = (String) tupleNext[7];
							String strike_price_next = ((int)tupleNext[8] >0) ? "$"+tupleNext[8]: "";
							String plan_price_next = ((String) tupleNext[9])==null ? "" : (String) tupleNext[9] ;
							String display_price_next=(String) tupleNext[10];
							String display_msg_next = (String) tupleNext[12];
							String billed_price_next = (String) tupleNext[13];
							String month_price_next = (String) tupleNext[14];
							boolean is_best_deal_next =tupleNext[15] instanceof Boolean ? (boolean) tupleNext[15] : ((byte) tupleNext[15]) > 0;

							try {
								jplandetailNext = new Gson().fromJson(feature_list_next, JPlanDetail.class);
							} catch (JSONException err) {
								log.error("jplandetail:"+err.getLocalizedMessage());
							}

							if (tuple[16] != null) {
								try {
									JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);
									term_list = terms.getTerm_list();
								} catch (Exception err) {
									log.error("jplan-terms:"+err.getLocalizedMessage());
								}
							}
							if(is_free_next) {
								free_plan = plan_id_next;
								free_period =per_id_next;
								free_features = jplandetailNext;
								if(monitor_type == 6 && orderAvail && defaultFreePlan) {
									free_trial_days = offer_days;
								}else {
									free_trial_days = defaultFreePlan ? (int) tupleNext[11] : 0;
								}
							}
							if ((plan_id != plan_id_next)) {
								if (monitor_type == 1) {
									if (isfree_eligible) {
										periodObj = new JSubPeriodDetailV1(!defaultFreePlan ? 0 : free_plan, per_id_next, content1,
												plan_id_next,period_name_next , content1_next,
												strike_price_next, plan_price_next,  display_price_next,display_msg_next,billed_price_next,month_price_next,is_best_deal_next);
										periodlist.add(periodObj);
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name_next);

									} else {
										if(!(restrict_flexi_plan_period && per_id <= curperiod_id)){
											periodObj = new JSubPeriodDetailV1(!defaultFreePlan ? 0 : free_plan, per_id, content1,
													plan_id,period_name , content1,
													strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal,
													description, compare_image, product_image,free_minicam_avil);
											periodlist.add(periodObj);
										}
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name);
										planDetail.setIs_best_seller(is_best_deal_plan);
									}
									if(planType.equalsIgnoreCase("data-plan")) {
										planDetail.setPlan_name("Pro");
										planDetail.setManagePlanId(4);
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("combo-plan")) {
										planDetail.setPlan_name(plan_name);
										planDetail.setIs_combo_plan(true);
										planDetail.setManagePlanId(1);
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("vet-plan")){
										planDetail.setManagePlanId(2);
										proPlanList.add(planDetail);
									}
									else {
										planDetail.setPlan_name(plan_name);
										planDetail.setIs_flexiplan(true);
										planDetail.setManagePlanId(3);
										planDetail.setPlan_details(flexi_plan_details);
										proPlanList.add(planDetail);
									}
									j=i;
									break innerLoop;
								} else if(monitor_type == 11) {
									periodObj = new JSubPeriodDetailV1(!defaultFreePlan ? 0 : free_plan, per_id, content1,
											plan_id, period_name, content1,
											strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal);
									periodlist.add(periodObj);
									planDetail.setTerm_list(term_list);
									planDetail.setPeriod_list(periodlist);
									planDetail.setPlan_name(plan_name);
									planDetail.setDisplay_content(desc_content);
									planDetail.setProduct_list(productList);
									if(planType.equalsIgnoreCase("data-plan")) {
										planDetail.setManagePlanId(4);
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("combo-plan")) {
										planDetail.setPlan_name(plan_name);
										planDetail.setManagePlanId(1);
										planDetail.setIs_combo_plan(true);
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("vet-plan")){
										planDetail.setManagePlanId(2);
										proPlanList.add(planDetail);
									}
									else {
										planDetail.setPlan_name(plan_name);
										planDetail.setIs_flexiplan(true);
										planDetail.setManagePlanId(3);
										planDetail.setPlan_details(flexi_plan_details);
										proPlanList.add(planDetail);
									}
									j = i;
									break innerLoop;
								} else {
									if (isfree_eligible) {
//										periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
//												plan_id, period_name, content1,
//												strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal);
//										periodlist.add(periodObj);
//										planDetail.setPeriod_list(periodlist);
										isfree_eligible = false;
										has_free = true;
									} else {
//										if (has_free) {
//											content1 = free_buynow;
//										}
										periodObj = new JSubPeriodDetailV1(!defaultFreePlan ? 0 : free_plan, per_id, has_free ? free_buynow : "",
												plan_id, period_name, content1,
												strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal,
												description, compare_image_other_products, product_image,free_minicam_avil);
										periodlist.add(periodObj);
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name);
										planDetail.setDisplay_content(desc_content);
										planDetail.setProduct_list(productList);
										if(planType.equalsIgnoreCase("data-plan")) {
											planDetail.setManagePlanId(4);
											proPlanList.add(planDetail);
											j=i;
											has_free = false;
											break innerLoop;
										}
										else if(planType.equalsIgnoreCase("combo-plan")) {
											planDetail.setPlan_name(plan_name);
											planDetail.setIs_combo_plan(true);
											planDetail.setManagePlanId(1);
											proPlanList.add(planDetail);
											has_free = false;
											break innerLoop;
										} else if(planType.equalsIgnoreCase("vet-plan")){
											planDetail.setManagePlanId(2);
											proPlanList.add(planDetail);
										}
										else {
											planDetail.setPlan_name(plan_name);
											planDetail.setIs_flexiplan(true);
											planDetail.setManagePlanId(3);
											planDetail.setPlan_details(flexi_plan_details);
											proPlanList.add(planDetail);
										}
									}
								}
							} else if (!isfree_eligible){
								if(!(restrict_flexi_plan_period && per_id <= curperiod_id)) {
									periodObj = new JSubPeriodDetailV1(!defaultFreePlan ? 0 : free_plan, per_id, has_free ? free_buynow : "", plan_id, period_name, content1,
											strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal,
											description, compare_image_other_products, product_image,free_minicam_avil);
									periodlist.add(periodObj);
								}
							}
						}
					}
				}

				JSubPlanDetailsV4 basicPlanObjectFinal = new JSubPlanDetailsV4();
				ArrayList<JMenuLst>  yearlyFeatureList=new ArrayList<>();
				ArrayList<JMenuLst> monthlyFeatureList=new ArrayList<>();
				if(!proPlanList.isEmpty() && monitor_type==1){
					for (JSubPlanDetailsV4 subPlan : proPlanList) {
						ArrayList<JSubPeriodDetailV1> periodList=subPlan.getPeriod_list();
						for(JSubPeriodDetailV1 subPeriodDetail:periodList){
							if(subPeriodDetail.getPeriod_id() < 3 && !subPlan.isIs_flexiplan()){
								subPeriodDetail.setManagePlanId(4);
							}else{
								subPeriodDetail.setManagePlanId(subPlan.getManagePlanId());
							}
							subPeriodDetail.setPlan_name(subPlan.getPlan_name());
							subPeriodDetail.setDisplay_content(subPlan.getDisplay_content());
							subPeriodDetail.setProduct_list(subPlan.getProduct_list());
							if(subPeriodDetail.getPaidplan_id() > 0)
								planlist_new.add(subPeriodDetail);
							basicPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
							basicPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
							basicPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());
							basicPlanObjectFinal.setManagePlanId(subPlan.getManagePlanId());
							if(subPeriodDetail.getPeriod_id()==3)
								subPeriodDetail.setFlexiPlan(true);
							if(subPlan.isIs_flexiplan())
								basicPlanObjectFinal.setPlan_details(flexi_plan_details);

						}
					}
					Set<JMenuLst> setyearly = new LinkedHashSet<>(yearlyFeatureList);
					ArrayList<JMenuLst> featureListWithoutDuplicatesYearly = new ArrayList<>(setyearly);

					// Handled for Monthly plan to remove duplicate feature Plan
					ArrayList<JMenuLst> featureListWithoutDuplicatesMonthly = new ArrayList<>(
							monthlyFeatureList.stream()
									.collect(Collectors.toMap(
											JMenuLst::getId, // key selector
											Function.identity(),      // keep the first object with this key
											(existing, replacement) -> existing, // merge function for duplicates
											LinkedHashMap::new        // preserve order
									))
									.values()
					);

				}
				else{
					// Other than pet monitor all feature i.e monthy/yearly will listed under one object
					for (JSubPlanDetailsV4 subPlan : proPlanList) {
						ArrayList<JSubPeriodDetailV1> periodList=subPlan.getPeriod_list();
						for(JSubPeriodDetailV1 subPeriodDetail:periodList){
							if(subPeriodDetail.getPeriod_id() < 3 && !subPlan.isIs_flexiplan()){
								subPeriodDetail.setManagePlanId(4);
							}else{
								subPeriodDetail.setManagePlanId(subPlan.getManagePlanId());
							}
							subPeriodDetail.setPlan_name(subPlan.getPlan_name());
							subPeriodDetail.setDisplay_content(subPlan.getDisplay_content());
							subPeriodDetail.setProduct_list(subPlan.getProduct_list());
							if(subPeriodDetail.getPaidplan_id() > 0)
								planlist_new.add(subPeriodDetail);
							basicPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
							basicPlanObjectFinal.setManagePlanId(subPlan.getManagePlanId());
							if(!basicPlanObjectFinal.isIs_best_seller())
								basicPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
							basicPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());
						}
					}

				}

				List<ComboContent> comboContentList=new ArrayList<>();

				comboContentList=crDao.getComboPlanContent();

				if(comboContentList.size()>0) {
					response.put("ComboContent", comboContentList.get(0));

				}

//				String free_text = monitor_type == 4 || monitor_type==5 ? "" : " for free";
//				String plan_lbl = "Wag-worthy"+ (defaultFreePlan ? free_text :"") +" benefits";

				// Changed conent for subscription plan Changes
				String plan_lbl="View Plan Benefits";
				boolean show_coupon = true;
				String btn_text = "Continue";
				String currentSubStatus ="";

				if(plan.isIs_freeplan()&& !sub_id.equalsIgnoreCase("NA"))
					currentSubStatus = crService.getCurrentSubStatusForChargebeeUser(user.getChargebeeid());

				if( currentSubStatus.equalsIgnoreCase("NA") || currentSubStatus.isEmpty() )
					currentSubStatus =crDao.getSubscriptionStatus(sub_id).trim();
			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("planlist", planlist_new);
			response.put("planname", "Please contact Support");
			response.put("contactnumber", supportcontactnumber.get(country));
			response.put("email", supportemail.get(country));
		} catch (Exception e) {
			e.printStackTrace();
			response.put("Status", 0);
			response.put("Msg", "Contact Support");
			return response;
		}
		return response;
	}

	@Override
	public JResponse getUpgradeSubPlanV10(UserV4 user, long curplan_id, long curperiod_id, long monitor_type, String country,
										 boolean defaultFreePlan, long gatewayid, String type, String plantype, String sub_id, boolean restrict_flexi_plan_period,
										 boolean is_flexi) {

		log.info("Entered into getUpgradeSubPlanV10 :: user_id : " + user.getId());
		JResponse response = new JResponse();
		try {
			JPlanInfo plan = crService.getPlan(curplan_id);
			if (plan == null) {
				plan = new JPlanInfo();
			}
			List<Object[]> planResp = crDao.getUpgradeSubPlanV7(user, curplan_id, curperiod_id, monitor_type,
					country, defaultFreePlan, gatewayid, type,plantype, is_flexi);
			if (planResp != null) {
				long plan_id = 0;
				long per_id = 0;
				String content1 = "";
				String free_buynow="";
				String period_name = "";
				String strike_price="";
				String plan_price = "";
				String display_price="";
				boolean is_free = false;
				boolean is_best_deal_plan = false;
				List<String> description;
				String compare_image="";
				String product_image="";

				List<JSubPlanDetailsV3> planlist_new = new ArrayList<JSubPlanDetailsV3>(); //Final list
				ArrayList<JSubPlanDetailsV3> comboPlanList = new ArrayList<JSubPlanDetailsV3>();
				ArrayList<JSubPlanDetailsV3> proPlanList = new ArrayList<JSubPlanDetailsV3>();
				ArrayList<JSubPlanDetailsV3> vetPlanList = new ArrayList<JSubPlanDetailsV3>();

				String plan_name ="";
				int free_trial_days = 0;
				String display_msg="";
				String billed_price = "";
				String month_price = "";
				boolean is_best_deal = false;
				boolean free_minicam_avil = false;
				boolean has_free = false;
				int cur_monitor_id = 1; // for deciding combo-plan. If -1, its for other products
				List<Object> productList = new ArrayList<>();
				List<Integer> monitorTypeIdList = new ArrayList<>();

				outerLoop : for(int j=0;j<planResp.size();j++) {
					int size = planResp.size();
					log.info("upgradePlanListV7 : size:" + size);
					JSubPlanDetailsV3 planDetail = new JSubPlanDetailsV3();
					Object[] tuple = (Object[]) planResp.get(0);

					long free_plan = 0;
					long free_period = 0;
					boolean isfree_eligible = false;
					JPlanDetail free_features=null;
					JSubPeriodDetail periodObj = new JSubPeriodDetail();
					ArrayList<JSubPeriodDetail> periodlist = new ArrayList<JSubPeriodDetail>();

					boolean orderAvail = false;
					if(monitor_type == 6 && gatewayid>0 && defaultFreePlan) {
						String[] dtRange = orderdate_trial.split(":");

						orderAvail = gatewayServiceV4.checkOrderWithinRange(gatewayid, dtRange[0], dtRange[1]);
					}
					if (size == 1) {
						plan_id = ((BigInteger) tuple[0]).longValue();
						plan_name = (String) tuple[1];
						per_id = ((BigInteger) tuple[2]).longValue();
						String feature_list = (String) tuple[3];
						JPlanDetail jplandetail = new JPlanDetail();

						if(restrict_flexi_plan_period && per_id <= curperiod_id){
							continue;
						}
						try {

							jplandetail = new Gson().fromJson(feature_list, JPlanDetail.class);

						} catch (JSONException err) {
							log.error("jplandetail:"+err.getLocalizedMessage());
						}

						content1 = (String) tuple[5];
						is_free = tuple[6] instanceof Boolean ? (boolean) tuple[6] : ((byte) tuple[6]) > 0;
						period_name = (String) tuple[7];
						strike_price= ((int)tuple[8] >0) ? "$"+tuple[8]: "";
						plan_price = ((String) tuple[9])==null ? "" : (String) tuple[9] ;
						display_price=(String) tuple[10];
						display_msg = (String) tuple[12];
						billed_price = (String) tuple[13];
						month_price = (String) tuple[14];
						is_best_deal = tuple[15] instanceof Boolean ? (boolean) tuple[15] : ((byte) tuple[15]) > 0;
						String planType =  (String) tuple[17];
						description = Arrays.asList(((String) tuple[18]).split("\\,"));
						is_best_deal_plan =  tuple[19] instanceof Boolean ? (boolean) tuple[19] : ((byte) tuple[19]) > 0;
						compare_image = (String)  tuple[20];
						product_image = (String) tuple[21];
						String productListObject = (String) tuple[25];
						monitorTypeIdList = getMonitorTypeIdList((String) tuple[26]);

						if(productListObject != null) {
							ObjectMapper mapper = new ObjectMapper();
							try {
								productList = mapper.readValue(productListObject, new TypeReference<ArrayList<Object>>() {});
							} catch (Exception e) {
								log.error("Error in getUpgradeSubPlanV10 : " + e);
							}
						}

						ArrayList<Terms> term_list = new ArrayList<Terms>();

						if (tuple[16] != null) {
							try {
								JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);
								term_list = terms.getTerm_list();
							} catch (Exception err) {
								log.error("jplan-terms:"+err.getLocalizedMessage());
							}
						}

						if(is_free) {
							isfree_eligible = true;
							free_plan = plan_id;
							free_period =per_id;
							free_features = jplandetail;
							periodObj = new JSubPeriodDetail(free_plan, free_period, content1, 0,period_name, "",
									strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal);
							periodObj.setMonitorIdList(monitorTypeIdList);
							periodObj.setProductList(productList);
							periodObj.setPlanName(plan_name);
							periodlist.add(periodObj);

							if(monitor_type == 6 && orderAvail && defaultFreePlan) {
								free_trial_days = offer_days;
							}else {
								free_trial_days = defaultFreePlan ? (int) tuple[11] : 0;
							}

						}else {
							periodObj = new JSubPeriodDetail( 0,per_id, "",plan_id,period_name,content1,
									strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal,
									description, monitor_type==1 ? compare_image : compare_image_other_products, product_image);
							periodObj.setMonitorIdList(monitorTypeIdList);
							periodObj.setProductList(productList);
							periodObj.setPlanName(plan_name);
							periodlist.add(periodObj);
						}

						planDetail.setFeature_list(jplandetail.getPlans());
						planDetail.setTerm_list(term_list);
						planDetail.setPeriod_list(periodlist);
						planDetail.setIs_best_seller(is_best_deal_plan);
						planDetail.setPlan_name(plan_name);

						if(planType.equalsIgnoreCase("data-plan")) {
							if (monitor_type == 1)
								planDetail.setPlan_name("Pro");
							proPlanList.add(planDetail);}
						else if(planType.equalsIgnoreCase("combo-plan")) {
							//planDetail.setPlan_name("Combo");
							planDetail.setIs_combo_plan(true);
							proPlanList.add(planDetail);}
						else if(planType.equalsIgnoreCase("vet-plan")){
							proPlanList.add(planDetail);
						}
						else {
							planDetail.setPlan_name("Flexi");
							planDetail.setIs_flexiplan(true);
							planDetail.setPlan_details(flexi_plan_details);
							proPlanList.add(planDetail);
						}
						break outerLoop;
					}

					innerLoop : for (int i = j; i < size; i++) {
						tuple = (Object[]) planResp.get(i);
						plan_id = ((BigInteger) tuple[0]).longValue();
						plan_name = (String) tuple[1];
						per_id = ((BigInteger) tuple[2]).longValue();
						String feature_list = (String) tuple[3];
						JPlanDetail jplandetail = new JPlanDetail();
						content1 = (String) tuple[5];
						is_free = tuple[6] instanceof Boolean ? (boolean) tuple[6] : ((byte) tuple[6]) > 0;
						period_name = (String) tuple[7];
						strike_price= ((int)tuple[8] >0) ? "$"+tuple[8]: "";
						plan_price = ((String) tuple[9])==null ? "" : (String) tuple[9];

						display_price=(String) tuple[10];
						display_msg = (String) tuple[12];
						billed_price = (String) tuple[13];
						month_price = (String) tuple[14];
						is_best_deal = tuple[15] instanceof Boolean ? (boolean) tuple[15] : ((byte) tuple[15]) > 0;
						String planType =  (String) tuple[17];
						description = Arrays.asList(((String) tuple[18]).split("\\,"));
						is_best_deal_plan =  tuple[19] instanceof Boolean ? (boolean) tuple[19] : ((byte) tuple[19]) > 0;
						compare_image = (String)  tuple[20];
						product_image = (String) tuple[21];
						cur_monitor_id = ((BigInteger) tuple[22]).intValue();
						free_minicam_avil = tuple[23] instanceof Boolean ? (boolean) tuple[23] : ((byte) tuple[23]) > 0;
						String productListObject = (String) tuple[25];
						monitorTypeIdList = getMonitorTypeIdList((String) tuple[26]);

						if(productListObject != null) {
							ObjectMapper mapper = new ObjectMapper();
							try {
								productList = mapper.readValue(productListObject, new TypeReference<ArrayList<Object>>() {});
							} catch (Exception e) {
								log.error("Error in getUpgradeSubPlanV10 : " + e);
							}
						}

						if(monitor_type == 1){
							String sub = cbService.getProductSubscriptionStatus(gatewayid,user.getChargebeeid());
							if (sub.equalsIgnoreCase("NA") || !sub.equalsIgnoreCase("CANCELLED")) {
								free_minicam_avil = false;
							}
						}

						if(monitor_type == 1 && cur_monitor_id == -1)
							break innerLoop;

						try {
							jplandetail = new Gson().fromJson(feature_list, JPlanDetail.class);
						} catch (JSONException err) {
							log.error("jplandetail:"+err.getLocalizedMessage());
						}

						ArrayList<Terms> term_list = new ArrayList<Terms>();

						if (tuple[16] != null) {
							try {
								JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);
								term_list = terms.getTerm_list();
							} catch (Exception err) {
								log.error("jplan-terms:"+err.getLocalizedMessage());
							}
						}
						if(is_free) {
							isfree_eligible = true;
							free_plan = plan_id;
							free_period =per_id;
							free_features = jplandetail;
							free_buynow = content1;

							if(monitor_type == 6 && orderAvail && defaultFreePlan) {
								free_trial_days = offer_days;
							}else {
								free_trial_days = defaultFreePlan ? (int) tuple[11] : 0;
							}
						}

						if (i == size - 1) {
							if(monitor_type == 1) {
								if(!(restrict_flexi_plan_period && per_id <= curperiod_id)){
									periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
											plan_id,period_name , content1, strike_price, plan_price,
											display_price,display_msg,billed_price,month_price,is_best_deal,
											description, compare_image, product_image,free_minicam_avil);
									periodObj.setMonitorIdList(monitorTypeIdList);
									periodObj.setProductList(productList);
									periodObj.setPlanName(plan_name);
									periodlist.add(periodObj);
								}
								planDetail.setTerm_list(term_list);
								planDetail.setFeature_list(jplandetail.getPlans());
								planDetail.setPeriod_list(periodlist);
								planDetail.setPlan_name("Pro");
								planDetail.setIs_best_seller(is_best_deal_plan);

								if(planType.equalsIgnoreCase("data-plan")) {
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("combo-plan")) {
									planDetail.setPlan_name(plan_name);
									planDetail.setIs_combo_plan(true);
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("vet-plan")){
									proPlanList.add(planDetail);
								}
								else {
									planDetail.setPlan_name("Flexi");
									planDetail.setIs_flexiplan(true);
									planDetail.setPlan_details(flexi_plan_details);
									proPlanList.add(planDetail);
								}
								has_free = false;
								break outerLoop;
							} else {
								periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
										plan_id, period_name, content1, strike_price, plan_price,
										display_price, display_msg, billed_price, month_price, is_best_deal,
										description, compare_image_other_products, product_image,free_minicam_avil);
								periodObj.setMonitorIdList(monitorTypeIdList);
								periodObj.setProductList(productList);
								periodObj.setPlanName(plan_name);
								periodlist.add(periodObj);
								planDetail.setTerm_list(term_list);
								planDetail.setFeature_list(jplandetail.getPlans());
								planDetail.setPeriod_list(periodlist);
								planDetail.setPlan_name(plan_name);
								planDetail.setIs_flexiplan(false);

								if(planType.equalsIgnoreCase("data-plan")) {
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("combo-plan")) {
									//planDetail.setPlan_name("Combo");
									planDetail.setIs_combo_plan(true);
									proPlanList.add(planDetail);}
								else if(planType.equalsIgnoreCase("vet-plan")){
									proPlanList.add(planDetail);
								}
								else {
									planDetail.setPlan_name("Flexi");
									planDetail.setIs_flexiplan(true);
									planDetail.setPlan_details(flexi_plan_details);
									proPlanList.add(planDetail);
								}
								has_free = false;
								break outerLoop;
							}
						} else {
							Object[] tupleNext = (Object[]) planResp.get(i + 1);
							long plan_id_next = ((BigInteger) tupleNext[0]).longValue();
							String plan_name_next = (String) tupleNext[1];
							long per_id_next = ((BigInteger) tupleNext[2]).longValue();
							String feature_list_next = (String) tupleNext[3];
							JPlanDetail jplandetailNext = new JPlanDetail();
							boolean is_free_next = tupleNext[6] instanceof Boolean ? (boolean) tupleNext[6] : ((byte) tupleNext[6]) > 0;
							String content1_next = (String) tupleNext[5];
							String period_name_next = (String) tupleNext[7];
							String strike_price_next = ((int)tupleNext[8] >0) ? "$"+tupleNext[8]: "";
							String plan_price_next = ((String) tupleNext[9])==null ? "" : (String) tupleNext[9] ;
							String display_price_next=(String) tupleNext[10];
							String display_msg_next = (String) tupleNext[12];
							String billed_price_next = (String) tupleNext[13];
							String month_price_next = (String) tupleNext[14];
							boolean is_best_deal_next =tupleNext[15] instanceof Boolean ? (boolean) tupleNext[15] : ((byte) tupleNext[15]) > 0;

							try {
								jplandetailNext = new Gson().fromJson(feature_list_next, JPlanDetail.class);
							} catch (JSONException err) {
								log.error("jplandetail:"+err.getLocalizedMessage());
							}

							if (tuple[16] != null) {
								try {
									JTerms terms = new Gson().fromJson((String)tuple[16], JTerms.class);
									term_list = terms.getTerm_list();
								} catch (Exception err) {
									log.error("jplan-terms:"+err.getLocalizedMessage());
								}
							}
							if(is_free_next) {
								free_plan = plan_id_next;
								free_period =per_id_next;
								free_features = jplandetailNext;
								if(monitor_type == 6 && orderAvail && defaultFreePlan) {
									free_trial_days = offer_days;
								}else {
									free_trial_days = defaultFreePlan ? (int) tupleNext[11] : 0;
								}
							}
							if ((plan_id != plan_id_next)) {
								if (monitor_type == 1) {
									if (isfree_eligible) {
										periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id_next, content1,
												plan_id_next,period_name_next , content1_next,
												strike_price_next, plan_price_next,  display_price_next,display_msg_next,billed_price_next,month_price_next,is_best_deal_next);
										periodObj.setMonitorIdList(monitorTypeIdList);
										periodObj.setProductList(productList);
										periodObj.setPlanName(plan_name);
										periodlist.add(periodObj);
										planDetail.setFeature_list(jplandetail.getPlans());
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name_next);

									} else {
										if(!(restrict_flexi_plan_period && per_id <= curperiod_id)){
											periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
													plan_id,period_name , content1,
													strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal,
													description, compare_image, product_image,free_minicam_avil);
											periodObj.setMonitorIdList(monitorTypeIdList);
											periodObj.setProductList(productList);
											periodObj.setPlanName(plan_name);
											periodlist.add(periodObj);
										}
										planDetail.setFeature_list(jplandetail.getPlans());
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name);
										planDetail.setIs_best_seller(is_best_deal_plan);
									}
									if(planType.equalsIgnoreCase("data-plan")) {
										planDetail.setPlan_name("Pro");
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("combo-plan")) {
										//planDetail.setPlan_name("Combo");
										planDetail.setIs_combo_plan(true);
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("vet-plan")){
										proPlanList.add(planDetail);
									}
									else {
										planDetail.setPlan_name("Flexi");
										planDetail.setIs_flexiplan(true);
										planDetail.setPlan_details(flexi_plan_details);
										proPlanList.add(planDetail);
									}
									j=i;
									break innerLoop;
								} else if(monitor_type == 11) {
									periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
											plan_id, period_name, content1,
											strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal);
									periodObj.setMonitorIdList(monitorTypeIdList);
									periodObj.setProductList(productList);
									periodObj.setPlanName(plan_name);
									periodlist.add(periodObj);
									planDetail.setFeature_list(jplandetail.getPlans());
									planDetail.setTerm_list(term_list);
									planDetail.setPeriod_list(periodlist);
									planDetail.setPlan_name(plan_name);

									if(planType.equalsIgnoreCase("data-plan")) {
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("combo-plan")) {
										//planDetail.setPlan_name("Combo");
										planDetail.setIs_combo_plan(true);
										proPlanList.add(planDetail);}
									else if(planType.equalsIgnoreCase("vet-plan")){
										proPlanList.add(planDetail);
									}
									else {
										planDetail.setPlan_name("Flexi");
										planDetail.setIs_flexiplan(true);
										planDetail.setPlan_details(flexi_plan_details);
										proPlanList.add(planDetail);
									}
									j = i;
									break innerLoop;
								} else {
									if (isfree_eligible) {
//										periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, content1,
//												plan_id, period_name, content1,
//												strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal);
//										periodlist.add(periodObj);
//										planDetail.setPeriod_list(periodlist);
										isfree_eligible = false;
										has_free = true;
									} else {
//										if (has_free) {
//											content1 = free_buynow;
//										}
										periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, has_free ? free_buynow : "",
												plan_id, period_name, content1,
												strike_price, plan_price, display_price, display_msg, billed_price, month_price, is_best_deal,
												description, compare_image_other_products, product_image,free_minicam_avil);
										periodObj.setMonitorIdList(monitorTypeIdList);
										periodObj.setProductList(productList);
										periodObj.setPlanName(plan_name);
										periodlist.add(periodObj);
										planDetail.setFeature_list(jplandetail.getPlans());
										planDetail.setTerm_list(term_list);
										planDetail.setPeriod_list(periodlist);
										planDetail.setPlan_name(plan_name);

										if(planType.equalsIgnoreCase("data-plan")) {
											proPlanList.add(planDetail);
											j=i;
											has_free = false;
											break innerLoop;
										}
										else if(planType.equalsIgnoreCase("combo-plan")) {
											//planDetail.setPlan_name("Combo");
											planDetail.setIs_combo_plan(true);
											proPlanList.add(planDetail);
											has_free = false;
											break innerLoop;
										} else if(planType.equalsIgnoreCase("vet-plan")){
											proPlanList.add(planDetail);
										}
										else {
											planDetail.setPlan_name("Flexi");
											planDetail.setIs_flexiplan(true);
											planDetail.setPlan_details(flexi_plan_details);
											proPlanList.add(planDetail);
										}
									}
								}
							} else if (!isfree_eligible){
								if(!(restrict_flexi_plan_period && per_id <= curperiod_id)) {
									periodObj = new JSubPeriodDetail(!defaultFreePlan ? 0 : free_plan, per_id, has_free ? free_buynow : "", plan_id, period_name, content1,
											strike_price, plan_price,  display_price,display_msg,billed_price,month_price,is_best_deal,
											description, compare_image_other_products, product_image,free_minicam_avil);
									periodObj.setMonitorIdList(monitorTypeIdList);
									periodObj.setProductList(productList);
									periodObj.setPlanName(plan_name);
									periodlist.add(periodObj);
								}
							}
						}
					}
				}





				JSubPlanDetailsV3 proPlanObjectFinal = new JSubPlanDetailsV3();
				JSubPlanDetailsV3 basicPlanObjectFinal = new JSubPlanDetailsV3();
				JSubPlanDetailsV3 comboPlanObjectFinal = new JSubPlanDetailsV3();
				JSubPlanDetailsV3 proPlanObjectFinalCopy = new JSubPlanDetailsV3();
				ArrayList<JSubPeriodDetail> monthlyPeriods = new ArrayList<>();
				ArrayList<JSubPeriodDetail> yearlyPeriods = new ArrayList<>();
				ArrayList<JSubPeriodDetail> comboPeriods = new ArrayList<>();
				ArrayList<JMenuLst>  yearlyFeatureList=new ArrayList<>();
				ArrayList<JMenuLst> monthlyFeatureList=new ArrayList<>();
				ArrayList<JMenuLst> comboFeatureList=new ArrayList<>();
				if(!proPlanList.isEmpty() && monitor_type==1){
					for (JSubPlanDetailsV3 subPlan : proPlanList) {
						ArrayList<JSubPeriodDetail> periodList=subPlan.getPeriod_list();
						for(JSubPeriodDetail subPeriodDetail:periodList){
							if(subPeriodDetail.getPeriod_id()>=4 && !subPlan.isIs_combo_plan()){
								if(defaultFreePlan)
									subPeriodDetail.setPaid_buynow("Continue");
								else
									subPeriodDetail.setPaid_buynow("Upgrade Now");
								yearlyPeriods.add(subPeriodDetail);
								yearlyFeatureList.addAll(subPlan.getFeature_list());
								proPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
								if(!proPlanObjectFinal.isIs_best_seller())
									proPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
								proPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());
								if( comboPlanList.size()>0)
								{
									proPlanObjectFinal.setShow_combo_content(true);
								}
							}else if(subPeriodDetail.getPeriod_id()>=4 && subPlan.isIs_combo_plan()){
								if(defaultFreePlan)
									subPeriodDetail.setPaid_buynow("Continue");
								else
									subPeriodDetail.setPaid_buynow("Upgrade Now");

								comboPeriods.add(subPeriodDetail);
								comboFeatureList.addAll(subPlan.getFeature_list());
								comboPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
								if(!comboPlanObjectFinal.isIs_best_seller())
									comboPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
								comboPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());
							}
							else{
								if(defaultFreePlan)
									subPeriodDetail.setPaid_buynow("Continue");
								else
									subPeriodDetail.setPaid_buynow("Upgrade Now");
								monthlyPeriods.add(subPeriodDetail);
								monthlyFeatureList.addAll(subPlan.getFeature_list());
								basicPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
								if(!proPlanObjectFinal.isIs_best_seller())
									basicPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
								basicPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());

								if(subPeriodDetail.getPeriod_id()==3)
									subPeriodDetail.setFlexiPlan(true);
								if(subPlan.isIs_flexiplan())
									basicPlanObjectFinal.setPlan_details(flexi_plan_details);

							}

						}
					}
					Set<JMenuLst> setyearly = new LinkedHashSet<>(yearlyFeatureList);
					ArrayList<JMenuLst> featureListWithoutDuplicatesYearly = new ArrayList<>(setyearly);
					proPlanObjectFinal.setFeature_list(featureListWithoutDuplicatesYearly);
					proPlanObjectFinal.setPeriod_list(yearlyPeriods);
					proPlanObjectFinal.setPlan_name("Pro");
					proPlanObjectFinal.setUserPlanType("I’m a full-time RVer");

					Set<JMenuLst> setcombo = new LinkedHashSet<>(comboFeatureList);
					ArrayList<JMenuLst> featureListWithoutDuplicatesCombo = new ArrayList<>(setcombo);
					comboPlanObjectFinal.setFeature_list(featureListWithoutDuplicatesCombo);
					comboPlanObjectFinal.setPeriod_list(comboPeriods);
					comboPlanObjectFinal.setUserPlanType("I own multiple Waggle Products");
					comboPlanObjectFinal.setPlan_name("Combo");


					// Handled for Monthly plan to remove duplicate feature Plan
					Set<JMenuLst> setmonthly = new LinkedHashSet<>(monthlyFeatureList);
					ArrayList<JMenuLst> featureListWithoutDuplicatesMonthly = new ArrayList<>(setmonthly);
					basicPlanObjectFinal.setFeature_list( featureListWithoutDuplicatesMonthly);
					basicPlanObjectFinal.setPeriod_list(monthlyPeriods);
					basicPlanObjectFinal.setUserPlanType("I travel occasionally");
					basicPlanObjectFinal.setPlan_name("Basic");

					// Copy of pro plan for pet monitor for other category
					Set<JMenuLst> setYearlyCopy = new LinkedHashSet<>(yearlyFeatureList);
					ArrayList<JMenuLst> featureListWithoutDuplicatesYearlyCopy = new ArrayList<>(setYearlyCopy);
					proPlanObjectFinalCopy.setFeature_list(featureListWithoutDuplicatesYearlyCopy);
					proPlanObjectFinalCopy.setPeriod_list(yearlyPeriods);
					proPlanObjectFinalCopy.setPlan_name("Pro");
					proPlanObjectFinalCopy.setUserPlanType("I monitor my pet from Home");
					proPlanObjectFinalCopy.setShowPlanList(false);

					if(!proPlanObjectFinal.getPeriod_list().isEmpty()
							&& !proPlanObjectFinal.getFeature_list().isEmpty()) {
						planlist_new.add(proPlanObjectFinal);
					}

					if(!comboPlanObjectFinal.getPeriod_list().isEmpty()
							&& !comboPlanObjectFinal.getFeature_list().isEmpty()) {
						planlist_new.add(comboPlanObjectFinal);
					}

					if(!basicPlanObjectFinal.getPeriod_list().isEmpty()
							&& !basicPlanObjectFinal.getFeature_list().isEmpty()) {
						planlist_new.add(basicPlanObjectFinal);
					}

					if(!proPlanObjectFinalCopy.getPeriod_list().isEmpty()
							&& !proPlanObjectFinalCopy.getFeature_list().isEmpty()) {
						planlist_new.add(proPlanObjectFinalCopy);
					}

				}
				else if(!proPlanList.isEmpty() && monitor_type==11){

					for (JSubPlanDetailsV3 subPlan : proPlanList) {
						ArrayList<JSubPeriodDetail> periodList=subPlan.getPeriod_list();
						for(JSubPeriodDetail subPeriodDetail:periodList){

							if(curplan_id==1 && curperiod_id==1)
								subPeriodDetail.setPaid_buynow("Continue");
							else
								subPeriodDetail.setPaid_buynow("Upgrade Now");

							yearlyPeriods.add(subPeriodDetail);
							yearlyFeatureList.addAll(subPlan.getFeature_list());
							proPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
							if(!proPlanObjectFinal.isIs_best_seller())
								proPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
							proPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());
//							if( comboPlanList.size()>0)
//							{
//								proPlanObjectFinal.setShow_combo_content(true);
//							}
						}
					}
					Set<JMenuLst> set = new LinkedHashSet<>(yearlyFeatureList);
					ArrayList<JMenuLst> featureListWithoutDuplicates = new ArrayList<>(set);
					proPlanObjectFinal.setFeature_list(featureListWithoutDuplicates);
					proPlanObjectFinal.setPeriod_list(yearlyPeriods);
					proPlanObjectFinal.setUserPlanType("I’m a full-time RVer");
					proPlanObjectFinal.setPlan_name("Pro");

					if(!proPlanObjectFinal.getFeature_list().isEmpty() &&
							!proPlanObjectFinal.getPeriod_list().isEmpty()) {
						planlist_new.add(proPlanObjectFinal);
					}


				}
				else{
					// Other than pet monitor all feature i.e monthy/yearly will listed under one object
					for (JSubPlanDetailsV3 subPlan : proPlanList) {
						ArrayList<JSubPeriodDetail> periodList=subPlan.getPeriod_list();
						for(JSubPeriodDetail subPeriodDetail:periodList){
							if(subPlan.isIs_combo_plan() ) {
								if(defaultFreePlan)
									subPeriodDetail.setPaid_buynow("Continue");
								else
									subPeriodDetail.setPaid_buynow("Upgrade Now");
								comboPeriods.add(subPeriodDetail);
								comboFeatureList.addAll(subPlan.getFeature_list());
								comboPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
								if (!comboPlanObjectFinal.isIs_best_seller())
									comboPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
								comboPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());

							}else if(!subPlan.isIs_combo_plan()) {
								yearlyPeriods.add(subPeriodDetail);
								if(defaultFreePlan)
									subPeriodDetail.setPaid_buynow("Continue");
								else
									subPeriodDetail.setPaid_buynow("Upgrade Now");
								yearlyFeatureList.addAll(subPlan.getFeature_list());
								proPlanObjectFinal.setIs_combo_plan(subPlan.isIs_combo_plan());
								if (!proPlanObjectFinal.isIs_best_seller())
									proPlanObjectFinal.setIs_best_seller(subPlan.isIs_best_seller());
								proPlanObjectFinal.setIs_flexiplan(subPlan.isIs_flexiplan());
								if (comboPlanList.size() > 0) {
									proPlanObjectFinal.setShow_combo_content(true);
								}
							}
						}
					}
					Set<JMenuLst> set = new LinkedHashSet<>(yearlyFeatureList);
					ArrayList<JMenuLst> featureListWithoutDuplicates = new ArrayList<>(set);
					proPlanObjectFinal.setFeature_list(featureListWithoutDuplicates);
					proPlanObjectFinal.setPeriod_list(yearlyPeriods);
					proPlanObjectFinal.setPlan_name("Pro");
					proPlanObjectFinal.setUserPlanType("I’m a full-time RVer");

					Set<JMenuLst> setcombo = new LinkedHashSet<>(comboFeatureList);
					ArrayList<JMenuLst> featureListWithoutDuplicatesCombo = new ArrayList<>(setcombo);
					comboPlanObjectFinal.setFeature_list(featureListWithoutDuplicatesCombo);
					comboPlanObjectFinal.setPeriod_list(comboPeriods);
					comboPlanObjectFinal.setUserPlanType("I own multiple Waggle Products");
					comboPlanObjectFinal.setPlan_name("Combo");

					if(!proPlanObjectFinal.getFeature_list().isEmpty() &&
							!proPlanObjectFinal.getPeriod_list().isEmpty()) {
						planlist_new.add(proPlanObjectFinal);
					}

					if(!comboPlanObjectFinal.getPeriod_list().isEmpty()
							&& !comboPlanObjectFinal.getFeature_list().isEmpty()) {
						planlist_new.add(comboPlanObjectFinal);
					}

				}

				List<ComboContent> comboContentList=new ArrayList<>();

				comboContentList=crDao.getComboPlanContent();

				if(comboContentList.size()>0) {
					response.put("ComboContent", comboContentList.get(0));

				}

//				String free_text = monitor_type == 4 || monitor_type==5 ? "" : " for free";
//				String plan_lbl = "Wag-worthy"+ (defaultFreePlan ? free_text :"") +" benefits";

				// Changed conent for subscription plan Changes
				String plan_lbl="Plan Benefits";
				boolean show_coupon = true;
				JCouponInfo coupon_info = new JCouponInfo();
				String btn_text = "Continue";
				String currentSubStatus ="";

				if(plan.isIs_freeplan()&& !sub_id.equalsIgnoreCase("NA"))
					currentSubStatus = crService.getCurrentSubStatusForChargebeeUser(user.getChargebeeid());

				if( currentSubStatus.equalsIgnoreCase("NA") || currentSubStatus.isEmpty() )
					currentSubStatus =crDao.getSubscriptionStatus(sub_id).trim();

				if(plan.isIs_freeplan() && sub_id.equalsIgnoreCase("NA")) {
					coupon_info = new JCouponInfo(coupon_img_new, coupon_desc_new, coupon_code_new,btn_text,coupon_note );
				}
				else if (currentSubStatus.equalsIgnoreCase("active") || currentSubStatus.equalsIgnoreCase("non_renewing")){
					coupon_info = new JCouponInfo(coupon_img_upgrade, coupon_desc_upgrade, coupon_code_upgrade,btn_text, coupon_note );
				}
				else if(currentSubStatus.equalsIgnoreCase("cancelled")){
					coupon_info = new JCouponInfo(coupon_img_update, coupon_desc_update, coupon_code_update,btn_text,coupon_note );
				}else {
					coupon_info = new JCouponInfo(coupon_img_new, coupon_desc_new, coupon_code_new,btn_text, coupon_note );
				}

				//new list to map the plan types of plan list
				List<Object> planTypeList = getPlanTypeList();

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("coupon_info", coupon_info);
				response.put("show_coupon", show_coupon);
				response.put("planlist", planlist_new);
				response.put("planlist_comb",comboPlanList);
				response.put("planTypeList", planTypeList);
				response.put("plan_lbl",plan_lbl);
				response.put("popup", "Worried about your furry friend's well-being? Enjoy $~"+free_trial_days+"-days trial of the "+plan_name+", for your pet's health.");
				// for custom plan
				response.put("planname", "Please contact Support");
				response.put("contactnumber", supportcontactnumber.get(country));
				response.put("email", supportemail.get(country));
			}
		} catch (Exception e) {
			log.error("getupgradesubplansv10: " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Contact Support");
			return response;
		}
		return response;
	}

	private static List<Object> getPlanTypeList() {

		List<Object> planTypeList = new ArrayList<>();
		Map<Object, Object> proPlanTypeMap = new HashMap<>();
		Map<Object, Object> proPlanCopyTypeMap = new HashMap<>();
		Map<Object, Object> basicTypeMap = new HashMap<>();

		proPlanTypeMap.put("planName", "Pro");
		proPlanTypeMap.put("userPlanType", "I'm a full-time RVer");
		proPlanCopyTypeMap.put("planName", "Pro");
		proPlanCopyTypeMap.put("userPlanType", "I monitor my pet from Home");
		basicTypeMap.put("planName", "Basic");
		basicTypeMap.put("userPlanType", "I travel occasionally");

		planTypeList.add(proPlanTypeMap);
		planTypeList.add(proPlanCopyTypeMap);
		planTypeList.add(basicTypeMap);

		return planTypeList;
	}

    private List<Integer> getMonitorTypeIdList(String monitor_type) {

		List<Integer> monitorTypeList = new ArrayList<>();

		if (monitor_type != null && !monitor_type.isEmpty()) {
			String[] groups = monitor_type.split("/");
			for (String group : groups) {
				String[] ids = group.split(",");
				for (String id : ids) {
					try {
						monitorTypeList.add(Integer.parseInt(id.trim()));
					} catch (NumberFormatException e) {
						// handle invalid integers
						log.error("Invalid monitor type ID: {}", id);
					}
				}
			}
		}

		return monitorTypeList;
	}



    @Override
    public boolean isEligibleForVetChatFreeTrial(String chargebeeid) {
        return crDao.isEligibleForVetChatFreeTrial(chargebeeid);
    }

    @Override
    public PlanToPeriod getPlanToPeriodById(Long id) {
        return crDao.getPlanToPeriodById(id);
    }

    @Override
    public List<JVetChatFreeTrialContent> getVetChatFreeTrialContent() {
        return crDao.getVetChatFreeTrialContent();
    }

    @Override
    public HashMap<String, Object> setVetChatFreeTrialContent(HashMap<String, Object> vetChatFreeTrialObject) {

            log.info("Entered into setVetChatFreeTrialContent");

            PlanToPeriod planToPeriod = crService.getPlanToPeriodById(175L);
            List<JVetChatFreeTrialContent> contentList = crService.getVetChatFreeTrialContent();
            vetChatFreeTrialObject.put("freeVetChatTrial_ButtonText","Activate Free Plan");
            vetChatFreeTrialObject.put("freeVetChatTrial_Content", contentList);
            vetChatFreeTrialObject.put("freeVetChatTrial_PlanId", planToPeriod.getPlan_id().getId());
            vetChatFreeTrialObject.put("freeVetChatTrial_PeriodId", planToPeriod.getSub_period_id().getId());
            vetChatFreeTrialObject.put("freeVetChatTrial_PopupImage", planToPeriod.getProduct_image());

            return vetChatFreeTrialObject;

    }
}
