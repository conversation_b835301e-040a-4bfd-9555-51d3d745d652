package com.nimble.irisservices.controller;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.lang.reflect.Type;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.GZIPOutputStream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;
import javax.validation.Valid;

import com.chargebee.Result;
import com.nimble.irisservices.Util.SecretManagerService;
import com.nimble.irisservices.dto.*;
import com.nimble.irisservices.entity.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.chargebee.Environment;
import com.chargebee.ListResult;
import com.chargebee.Result;
import com.chargebee.filters.enums.SortOrder;
import com.chargebee.models.CreditNote;
import com.chargebee.models.Customer;
import com.chargebee.models.Plan;
import com.chargebee.models.Subscription.Status;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.constant.CountryCode;
import com.nimble.irisservices.dao.IThrottlingDao;
import com.nimble.irisservices.dto.AmazonReviewList;
import com.nimble.irisservices.dto.Configuration;
import com.nimble.irisservices.dto.EnableOrDisablePetProfile;
import com.nimble.irisservices.dto.FotaUpgrade;
import com.nimble.irisservices.dto.GatewayV4Web;
import com.nimble.irisservices.dto.JAlert;
import com.nimble.irisservices.dto.JAlertCfg;
import com.nimble.irisservices.dto.JAlertOverview;
import com.nimble.irisservices.dto.JAlertRange;
import com.nimble.irisservices.dto.JAssetDescription;
import com.nimble.irisservices.dto.JAssetLastReport;
import com.nimble.irisservices.dto.JAssetReport;
import com.nimble.irisservices.dto.JFurBitLastGatewayReport;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JGatewayConfig;
import com.nimble.irisservices.dto.JGatewayFeature;
import com.nimble.irisservices.dto.JGatewayOverview;
import com.nimble.irisservices.dto.JGatewaySubSetup;
import com.nimble.irisservices.dto.JGroups;
import com.nimble.irisservices.dto.JNode;
import com.nimble.irisservices.dto.JRVAnswer;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSGateway;
import com.nimble.irisservices.dto.JSensorReport;
import com.nimble.irisservices.dto.JSentMessage;
import com.nimble.irisservices.dto.JSreiAlertOverview;
import com.nimble.irisservices.dto.JSreiAssetLastReport;
import com.nimble.irisservices.dto.JSreiGatewayOverview;
import com.nimble.irisservices.dto.JSubscriptionPlanReport;
import com.nimble.irisservices.dto.JUser;
import com.nimble.irisservices.dto.JUserEmailUpdate;
import com.nimble.irisservices.dto.JVpmSubscription;
import com.nimble.irisservices.dto.MeidData;
import com.nimble.irisservices.dto.SignUp;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AlertType;
import com.nimble.irisservices.entity.AllProductSubscription;
import com.nimble.irisservices.entity.AllSubscription;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.BatteryLookup;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.entity.CompanyType;
import com.nimble.irisservices.entity.DeviceReplaced;
import com.nimble.irisservices.entity.Feature;
import com.nimble.irisservices.entity.FeatureType;
import com.nimble.irisservices.entity.FeedbackForm;
import com.nimble.irisservices.entity.FotaModel;
import com.nimble.irisservices.entity.FotaVersion;
import com.nimble.irisservices.entity.FurbitLastGatewayReport;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.Groups;
import com.nimble.irisservices.entity.MonitorType;
import com.nimble.irisservices.entity.Ordermap;
import com.nimble.irisservices.entity.PetNews;
import com.nimble.irisservices.entity.PlanToFeature;
import com.nimble.irisservices.entity.PlanToMonitorType;
import com.nimble.irisservices.entity.PlanToPeriod;
import com.nimble.irisservices.entity.PlanToUpgrade;
import com.nimble.irisservices.entity.ProbeCategory;
import com.nimble.irisservices.entity.ProbeLookup;
import com.nimble.irisservices.entity.RVAnswer;
import com.nimble.irisservices.entity.ReportType;
import com.nimble.irisservices.entity.ResetType;
import com.nimble.irisservices.entity.SubscriptionPeriod;
import com.nimble.irisservices.entity.SubscriptionPlan;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.entity.UnpaidInvoices;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.UserDltInfo;
import com.nimble.irisservices.entity.UserEntity;
import com.nimble.irisservices.entity.UsertoFeature;
import com.nimble.irisservices.entity.VersionMapping;
import com.nimble.irisservices.error.RegisterUserError;
import com.nimble.irisservices.exception.InvalidAlertTypeException;
import com.nimble.irisservices.exception.InvalidAsseIdException;
import com.nimble.irisservices.exception.InvalidAssetGroupIdException;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.exception.InvalidGroupIdException;
import com.nimble.irisservices.exception.InvalidModelIdException;
import com.nimble.irisservices.exception.InvalidSubgroupIdException;
import com.nimble.irisservices.exception.InvalidUsernameException;
import com.nimble.irisservices.helper.Email;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.niom.entity.Orders;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAlertService;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICompanyServiceV4;
import com.nimble.irisservices.service.ICompanyTypeService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IDynamicCmdService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IFetchDropdownService;
import com.nimble.irisservices.service.IFotaService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IGroupServices;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.IMonitorTypeService;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.INodeService;
import com.nimble.irisservices.service.IOAuth2Service;
import com.nimble.irisservices.service.IPetNewsServiceV4;
import com.nimble.irisservices.service.IProbeCategoryService;
import com.nimble.irisservices.service.IRVCentricDetailsService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IThrottlingService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.impl.OAuth2ServiceImpl;

import freemarker.template.Template;

@RestController
@RequestMapping("/web")
public class ManageController {

	private static final Logger log = LogManager.getLogger(ManageController.class);

	@Autowired
	ICompanyTypeService companyTypeServ;

	@Autowired
	SecretManagerService secretManagerService;

	@Value("${aws_s3_secret_name}")
	private String S3_SECRET_NAME;

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IFetchDropdownService fetchDropdownService;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	IGroupServices groupservices;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IReportService reportService;

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IAlertService alertService;

	@Autowired
	@Lazy
	INodeService nodeService;

	@Autowired
	@Lazy
	IAlertCfgService alertCfgService;

	@Autowired
	IThrottlingService throttlingService;

	@Autowired
	IRVCentricDetailsService rvcentricServ;

	@Autowired
	@Lazy
	IMessagingService messagingService;

	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Autowired
	@Lazy
	IChargebeeService cbService;

	@Autowired
	IMonitorTypeService MonitorTypeService;

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;

	@Autowired
	@Lazy
	IDynamicCmdService dynamicServ;

	@Autowired
	@Lazy
	IThrottlingDao throttlingdao;

	@Autowired
	@Lazy
	ICompanyServiceV4 companyServicev4;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Autowired
	@Lazy
	IFotaService fotaService;

	@Autowired
	@Lazy
	INiomDataBaseService niomService;

	@Autowired
	@Lazy
	IRVCentricDetailsService rvCentService;

	@Autowired
	@Lazy
	IProbeCategoryService probService;

	@Autowired
	Email email_helper;

	@Value("${buynowfurbit}")
	private String buynowfurbit;

	@Value("#{${buynowpetsafety}}")
	private Map<String, String> buynowpetsafety;

	@Autowired
	@Lazy
	ICompanyTypeService companytypeservice;

	@Autowired
	Helper _helper;

	@Autowired
	IOAuth2Service oAuth2Service;

	@Autowired
	@Lazy
	IChargebeeService chargebeeService;

	@Value("${show_nextrenewal_popup}")
	private boolean show_nextrenewal_popup;

	@Value("${days_tohandle_nextrenewal}")
	private int days_tohandle_nextrenewal;

	@Value("${config.oauth2.clientid.web}")
	private String clientidWeb;

	@Value("${config.oauth2.clientsecret.web}")
	private String clientSecretWeb;

	@Value("${verificationtime}")
	private long verifytime;

	@Value("${niomip}")
	private String niomIP;

	@Value("${niomauthkey}")
	private String niomAuthKey;

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	@Value("${freeplan}")
	private String freeplan;

	@Value("${omitplan}")
	private String omitplan;

	@Value("${vpmplan}")
	private String vpmplan;

	@Value("${addonplan}")
	private String addonplan;

	@Value("${show_upgrade_msg}")
	private boolean show_upgrade_msg;

	@Value("${show_addon_button}")
	private boolean show_addon_button;

	IrisservicesUtil irisUtil;

	private String delete_user_emailsub = "Waggle Account is Deleted";

	@Autowired
	freemarker.template.Configuration templates;

	@Autowired
	IPetNewsServiceV4 pnService;

	@Value("#{${supportcontactnumber}}")
	private Map<String, String> supportContactNumber;

	@Value("#{${supportemail}}")
	private Map<String, String> supportContactEmail;

	@Value("${schedule_url}")
	private String waggletxnsrv_url = "";

	@Value("${returnsubuser}")
	private String configUsername;

	@Value("${returnsubpass}")
	private String configPassword;

	@Autowired
	OAuth2ServiceImpl oauthDAOService;

	@Value("${txnservice_url}")
	private String txnservice_url;

	@RequestMapping("/login") // for session based security
	public boolean login(@RequestBody UserEntity user) {
		return user.getUserName().equals("user") && user.getPassword().equals("password");
	}

	@RequestMapping(value = "/v3.0/logout", method = RequestMethod.GET) // for session based security
	public JResponse logoutPage(HttpServletRequest request, HttpServletResponse response) {
		JResponse jResponse = new JResponse();
		Authentication auth = SecurityContextHolder.getContext().getAuthentication();
		if (auth != null) {
			new SecurityContextLogoutHandler().logout(request, response, auth);

			jResponse.put("Status", 1);
			jResponse.put("Msg", "Success");

		} else {

			jResponse.put("Status", 1);
			jResponse.put("Msg", "Error");

		}

		return jResponse;

	}

	@GetMapping("/check")
	public String check() {
		return "hello world";
	}

	// ========get reporttype================
	@RequestMapping(value = "v3.0/reporttype/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getReportType(@RequestHeader HttpHeaders header, Authentication authentication) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<ReportType> reporttypes = fetchDropdownService.getReportTypes();
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("reporttype", reporttypes);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;

	}

	@RequestMapping(value = "v3.0/cmpalerttype/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertTypeByCmp(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		try {
			String autho = header.getFirst("auth");
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<AlertType> alerttypes = fetchDropdownService.getAlertTypesByCmp(user.getCmpId());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("cmpalerttype", alerttypes);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;

	}

	// ========get company ================
	@RequestMapping(value = "v3.0/company/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCompanyById(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		UserV4 user;
		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			Company cmp = companyService.getCompany(user.getCmpId());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("company", cmp);

		} catch (InvalidAuthoException e) {

			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;
	}

	// ==========get companyconfig========
	@RequestMapping(value = "v3.0/companyconfig/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getCompanyConfig(@RequestHeader HttpHeaders header, Authentication authentication) {

		JResponse response = new JResponse();
		Gson _gson = new Gson();
		String autho = header.getFirst("auth");
		User user;
		try {

			user = userService.verifyAuthKey(autho);

//			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
//			if (errResponse != null) {
//				return errResponse;
//			}
			String country = user.getCountry().toUpperCase();
			if (country.equalsIgnoreCase("US") || country.equalsIgnoreCase("NA")
					|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in") || country.isEmpty()
					|| country == null) {
				country = "US";
			}

			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());

			response.put("Status", 1);

			response.put("Msg", "Success");

			response.put("company config", cmp_cfg);
			response.put("companyconfig", cmp_cfg);
			if (user.giveCompany().getCompanytype().getId() == 3) {

				boolean registerProduct = true;

				if ((user.getUsername().matches("[0-9]+") && user.getUsername().length() == 6)) {

					// String niomIP = _helper.getExternalConfigValue("niomip",
					// externalConfigService);
					// String niomAuthKey = _helper.getExternalConfigValue("niomauthkey",
					// externalConfigService);
					String niomCheckMEIDURL = _helper.getExternalConfigValue("niomcheckmappedorderurl",
							externalConfigService);

					Set<Gateway> gatewayList = user.getGateways();

					if (gatewayList.size() > 1 || gatewayList.size() < 1) {
						registerProduct = true;
					} else if (gatewayList.size() == 1) {

						Gateway gat = gatewayList.iterator().next();

						List<Ordermap> orderMapList = new ArrayList<Ordermap>();

						JSONObject _response = new JSONObject();

						String IsCheckMEIDMappedResponse = null;
						try {
							String url = niomIP + niomCheckMEIDURL + niomAuthKey + "/" + gat.getMeid();

							IsCheckMEIDMappedResponse = _helper.getURL(url);

							JSONObject meidDetailsResponse = new JSONObject(IsCheckMEIDMappedResponse);
							_response = meidDetailsResponse.getJSONObject("response");

							int _status = _response.getInt("Status");

							if (_status > 0 ? true : false) {

								Type orderListType = new TypeToken<ArrayList<Ordermap>>() {
								}.getType();
								orderMapList = _gson.fromJson(_response.getJSONArray("OrderMap").toString(),
										orderListType);
								if (orderMapList.size() > 0) {
									registerProduct = true;
								} else {
									registerProduct = false;
								}
							} else {

								registerProduct = false;
							}

						} catch (Exception e) {
							registerProduct = true;
						}
					}
				}

				/*
				 * user token verification 2 - user token verification Status pending and with
				 * in x[ config param] hrs 1 - user token verification Status verified 0 - user
				 * token verification Status pending and more than x[ config param] hrs
				 * 
				 */

				String userStatus = "0";
				try {
					Calendar curCal = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
					System.out.println(curCal);
					DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
					Date date = sdf.parse(user.getCreatedOn());
					Calendar createdCal = Calendar.getInstance();
					createdCal.setTime(date);

					long timeMills = curCal.getTimeInMillis() - createdCal.getTimeInMillis();
					long verifyMills = verifytime * 60 * 60 * 1000;
					// user verification time - config parameter from properties file

					if (user.isVerified() == true) {
						userStatus = "1";
					} else if (timeMills < verifyMills && (user.isVerified() == false)) {
						userStatus = "1";
					} else if (timeMills > verifyMills && (user.isVerified() == false)) {
						userStatus = "1";
					}

				} catch (Exception e) {
					log.error("getCompanyConfig : " + e.getMessage());

				}

				response.put("notificationStatus", user.isNotification());
				response.put("isProductRegistered", registerProduct);

				response.put("upgradeSubscriptionUrl",
						_helper.getExternalConfigValue("upgradesubscriptionurl", externalConfigService));

				response.put("activateSubscriptionUrl",
						_helper.getExternalConfigValue("activatesubscriptionurl", externalConfigService));

				response.put("registerProductUrl",
						_helper.getExternalConfigValue("registerproducturl", externalConfigService));

				response.put("buynowfurbit", buynowfurbit);

				response.put("buynowpetsafety", buynowpetsafety.get(country));

				response.put("userverificationStatus", userStatus);

			}

		} catch (InvalidAuthoException e) {

			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");

			return response;
		}
		return response;
	}

	// ========get group userId================
	@RequestMapping(value = "v3.0/usergroup/{levelid}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGroupByUserid(@RequestHeader HttpHeaders header, Authentication authentication,
			@PathVariable String levelid, @RequestParam("groupid") String groupid,
			@RequestParam("topgroupid") String topgroupid) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<JGroups> jgroups = groupservices.getUserJGroup(groupid, topgroupid, levelid, user.getId());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("userGroups", jgroups);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;

	}

	/* =============== Asset Overview ================ */
	@RequestMapping(value = "v3.0/gatewayoverview/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGatewayOverview(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("groupid") String groupid, @RequestParam("subgroupid") String subgroupid,
			@RequestParam("levelid") String levelid) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();

		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			JGatewayOverview goverview = gatewayService.getgatewayoverview(groupid, subgroupid, user.getId(), levelid);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("gatewayoverview", goverview);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;
	}

	/* =============== check meid enabled status ================ */
	@GetMapping(value = "v5.0/getskipotastatus", headers = "Accept=application/json")
	@ResponseBody
	public JResponse getSkipOtaStatus(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("meid") String meid) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();

		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			boolean data = false;

			data = gatewayService.getSkipOtaStatus(meid);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("activation_status", data);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "Please try again");
			return response;
		}

		return response;
	}

	// ======== update skipupdateOTA table ================
	@PostMapping(value = "v5.0/updateskipotastatus", headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateskipotastatus(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody MeidData statusData) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {

			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			boolean result = gatewayService.updateskipotastatus(statusData.getMeid(), statusData.getEnable());

			if (result) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 1);
				response.put("Msg", "Failed");
				log.error("skipOtaUpdateion_Failed");
			}

		} catch (Exception e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "Please try again!");
			return response;
		}
		return response;
	}

	// ========get lastgatewayreport ================
	@RequestMapping(value = "v3.0/gatewaysummary/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGatewaySummary(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("groupid") String groupid, @RequestParam("subgroupid") String subgroupid,
			@RequestParam("assetgroupid") String assetgroupid, @RequestParam("gatewayid") String gatewayid,
			@RequestParam("offset") String offset, @RequestParam("limit") String limit,
			@RequestParam(value = "zip", defaultValue = "1", required = false) String zip) {

		System.out.println("gateway summary");
		log.info("called gateway summary services" + "groupid: " + groupid + ", subgroupid: " + subgroupid
				+ ", assetgroupid: " + assetgroupid + " , gatewayid: " + gatewayid);
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		try {

			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.getCmpId());
			List<JAssetLastReport> reportsummmary = reportService.getLastgatewayreport(groupid, subgroupid,
					assetgroupid, gatewayid, user.getId(), offset, limit, cmp_cfg.getTemperatureunit(), "");
			log.info("received gateway summary , report length: " + reportsummmary.size());

			JAssetDescription assetDescrip = reportService.getAssetDescription(user.getCmpId());

			long gateway_id = 0l;
			if (!gatewayid.isEmpty())
				gateway_id = Long.parseLong(gatewayid);

			List<JSensorReport> bowlRpts = reportService.getGatewaySmartBowlReport(user.getId(), gateway_id,
					"lastsensorreport", "", "");

			if (!bowlRpts.isEmpty()) {
				for (JSensorReport br : bowlRpts) {
					long assetid = br.getGateway_id();
					String assetname = br.getAssetname();
					String datetime = br.getDatetime();
					int batt = br.getBattery();
					String fota_ver = br.getFota_ver();
					String rssi = br.getRssi();
					String rpt_timezone = br.getRpt_timezone();
					int groupid1 = br.getGroups_id();
					String groupname = br.getGroupname();
					JAssetLastReport rpt = new JAssetLastReport(assetid, assetname, datetime, batt, fota_ver, rssi,
							rpt_timezone, groupid1, groupname);
					reportsummmary.add(rpt);
				}
			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("assetdescription", assetDescrip);

			/* To reverse the collection of latest reported gateway */
			Collections.sort(reportsummmary, new Comparator<JAssetLastReport>() {
				@Override
				public int compare(JAssetLastReport a, JAssetLastReport b) {
					DateFormat format = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
					try {
						return format.parse(b.getDatetime()).compareTo(format.parse(a.getDatetime()));
					} catch (ParseException e) {
						log.error("getAssetsmry:" + e.getLocalizedMessage());
					}
					return 0;
				}
			});

			/* If zip params is 1.Report is zipped */

			if (zip.equalsIgnoreCase("1")) {
				// response.put("bowlRpt", zipContent(bowlRpt));
				response.put("lastgatewayreport", zipContent(reportsummmary));
			} else {
				// response.put("bowlRpt", bowlRpt);
				response.put("lastgatewayreport", reportsummmary);
			}
		} catch (InvalidAuthoException e) {
			log.error("getAssetSummary:in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (IOException e) {
			log.error("getAssetSummary: " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid content for compress");
			return response;
		} catch (Exception e) {
			// e.printStackTrace();
			log.error("getAssetSummary: " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid content for compress");
			return response;
		}
		return response;
	}

	public static byte[] zipContent(Object obj) throws IOException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		GZIPOutputStream gzipOut = new GZIPOutputStream(baos);

		Gson gson = new Gson();
		String json = gson.toJson(obj);
		gzipOut.write(json.getBytes());

		gzipOut.close();
		// objectOut.close();
		byte[] bytes = baos.toByteArray();

		return bytes;
	}

	// ========Alert Overview================
	@RequestMapping(value = "v3.0/alertoverview/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertOverview(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("groupid") String groupid, @RequestParam("subgroupid") String subgroupid) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			JAlertOverview altoverview = alertService.getalertoverview(groupid, subgroupid, user.getId());
			JSreiAlertOverview saltoverview = alertService.getsreialertoverview(groupid, subgroupid, user.getId());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("gatewayoverview", altoverview);
			response.put("sreialtoverview", saltoverview);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;

	}

	// ========get alerts ================
	@RequestMapping(value = "v3.0/alert/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertSummary(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("groupid") String groupid, @RequestParam("subgroupid") String subgroupid,
			@RequestParam("alerttypeid") String alerttypeid, @RequestParam("gatewayid") String gatewayid,
			@RequestParam("deliquencystatus") String deliquencystatus,
			@RequestParam(value = "nodeid", defaultValue = "", required = false) String nodeid,
			@RequestParam(value = "id", defaultValue = "", required = false) String id) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.getCmpId());

			List<JAlert> alerts = alertService.getalerts(groupid, subgroupid, gatewayid, alerttypeid, user.getId(),
					deliquencystatus, cmp_cfg.getTemperatureunit(), "", "", nodeid, id);
			JAssetDescription assetDescrip = reportService.getAssetDescription(user.getCmpId());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("assetdescription", assetDescrip);
			response.put("Alters", alerts);
			return response;
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
	}

	// ========get reporttype================
	@RequestMapping(value = "v3.0/cmpreporttype/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getReportTypeByCmp(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<ReportType> reporttypes = fetchDropdownService.getReportTypesByCmp(user.getCmpId());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("cmpreporttype", reporttypes);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;

	}

	// ========get alerts Version 2================
	@RequestMapping(value = "v3.0/alertV2/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetSummaryV2(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("groupid") String groupid, @RequestParam("subgroupid") String subgroupid,
			@RequestParam("alerttypeid") String alerttypeid, @RequestParam("gatewayid") String gatewayid,
			@RequestParam("deliquencystatus") String deliquencystatus, @RequestParam("fromtime") String fromtime,
			@RequestParam("totime") String totime,
			@RequestParam(value = "nodeid", defaultValue = "", required = false) String nodeid,
			@RequestParam(value = "id", defaultValue = "", required = false) String id) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.getCmpId());

			List<JAlert> alerts = alertService.getalerts(groupid, subgroupid, gatewayid, alerttypeid, user.getId(),
					deliquencystatus, cmp_cfg.getTemperatureunit(), fromtime, totime, nodeid, id);
			JAssetDescription assetDescrip = reportService.getAssetDescription(user.getCmpId());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("assetdescription", assetDescrip);
			response.put("Alters", alerts);
			return response;
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
	}

	// ========get alerts Version 2================
	@RequestMapping(value = "v3.0/furbitalertV2/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFurbitalertV2(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("groupid") String groupid, @RequestParam("subgroupid") String subgroupid,
			@RequestParam("alerttypeid") String alerttypeid, @RequestParam("gatewayid") String gatewayid,
			@RequestParam("deliquencystatus") String deliquencystatus, @RequestParam("fromtime") String fromtime,
			@RequestParam("totime") String totime,
			@RequestParam(value = "nodeid", defaultValue = "", required = false) String nodeid,
			@RequestParam(value = "id", defaultValue = "", required = false) String id) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			Map<String, String> resMaps = userServiceV4.getUserId_cmpIdByAuth(autho);

			JResponse errResponse = _helper.validateUser(resMaps.get("username"), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			long cmp_id = Long.valueOf(resMaps.get("cmp_id"));
			long userId = Long.valueOf(resMaps.get("user_id"));
			String tempUnit = resMaps.get("tempunit");

			List<JAlert> alerts = alertService.getFurbitalerts(groupid, subgroupid, gatewayid, alerttypeid, userId,
					deliquencystatus, tempUnit, fromtime, totime, nodeid, id);
			JAssetDescription assetDescrip = reportService.getAssetDescription(cmp_id);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("assetdescription", assetDescrip);
			response.put("Alters", alerts);
			return response;
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
	}

	// ========get gatewayreport ================
	@RequestMapping(value = "v3.0/gatewayreport/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetReport(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("fromtime") String fromtime, @RequestParam("totime") String totime,
			@RequestParam("assetgroupid") String assetgroupid, @RequestParam("gatewayid") String gatewayid,
			@RequestParam("offset") String offset, @RequestParam("limit") String limit,
			@RequestParam(value = "zip", defaultValue = "1", required = false) String zip) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.getCmpId());

			List<JAssetReport> reportsummmary = reportService.getgatewayreport(fromtime, totime, assetgroupid,
					gatewayid, user.getCmpId(), offset, limit, cmp_cfg.getTemperatureunit());
			log.info("received gateway summary , report length: " + reportsummmary.size());
			// List<JSensorReport> bowlRpt = new ArrayList<JSensorReport>();

			long gateway_id = 0l;
			if (!gatewayid.isEmpty())
				gateway_id = Long.parseLong(gatewayid);

			List<JSensorReport> bowlRpt = reportService.getGatewaySmartBowlReport(user.getId(), gateway_id,
					"sensorreport", fromtime, totime);

			response.put("Status", 1);
			response.put("Msg", "Success");

			/* If zip params is 1.Report is zipped */
			if (zip.equalsIgnoreCase("1")) {
				response.put("gatewayreports", zipContent(reportsummmary));
				response.put("bowlRpts", zipContent(bowlRpt));

			} else {
				response.put("gatewayreports", reportsummmary);
				response.put("bowlRpts", bowlRpt);

			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (IOException e) {
			// TODO Auto-generated catch block
			log.error(e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid content for compress");
			return response;
		}

		return response;

	}

	@RequestMapping(value = "v3.0/furbitlastgatewayreport/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFurBitLastGatewayReport(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (Exception e) {
				user = null;
			}
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			// if(user != null) {
			List<FurbitLastGatewayReport> rpt = gatewayService.getFLastGatewayReportByUser(0l);
			List<JFurBitLastGatewayReport> jRptList = convertJFurBitLastGatewayReport(rpt);

			if (jRptList != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("rpt", jRptList);
			} else {
				response.put("Status", 1);
				response.put("Msg", "No Report found");
			}
			// }
			// else
			// {
			// response.put("Status", 0);
			// response.put("Msg", "User not found");
			// }
		} catch (Exception e) {
			log.error("getFurBitLastGatewayReport : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to process the report at this time.");
		}

		return response;
	}

	public List<JFurBitLastGatewayReport> convertJFurBitLastGatewayReport(List<FurbitLastGatewayReport> rptList) {
		List<JFurBitLastGatewayReport> jRptList = new ArrayList<JFurBitLastGatewayReport>();
		try {
			for (FurbitLastGatewayReport fRpt : rptList) {
				JFurBitLastGatewayReport lastgateway = new JFurBitLastGatewayReport(fRpt.getVersion(),
						fRpt.getDatetime(), fRpt.getDate(), fRpt.getTime(), fRpt.getTimezone(), fRpt.getLat(),
						fRpt.getLatdir(), fRpt.getLon(), fRpt.getLondir(), fRpt.getGpsstatus(), fRpt.getGpsinfo(),
						fRpt.getEventid1(), fRpt.getEventid2(), fRpt.getIostatus(), fRpt.getBattery(),
						fRpt.getRawrssi(), fRpt.getRssi(), fRpt.getGpsmode(), fRpt.getTxnMode(), fRpt.getLastpkt(),
						fRpt.getGateway().getId(), fRpt.getGateway().getMeid());

				jRptList.add(lastgateway);
			}
		} catch (Exception e) {
			log.error("convertJFurBitLastGatewayReport : ", e.getLocalizedMessage());
			jRptList = null;
		}
		return jRptList;
	}

	// ========get gateway================
	@RequestMapping(value = "v3.0/gateway/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGatewayById(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("assetgroupid") String assetgroupid, @RequestParam("groupid") String groupid,
			@RequestParam("subgroupid") String subgroupid, @RequestParam("gatewayid") String gatewayid,
			@RequestParam("userid") String userid,
			@RequestParam(value = "meid", defaultValue = "", required = false) String meid) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		System.out.println("get gateway");
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
//			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
//			if (errResponse != null) {
//				return errResponse;
//			}

			System.out.println("get gateways gatewayid: " + gatewayid);
			long userId = 0;

			if (!userid.trim().equals(""))
				userId = Long.parseLong(userid);
			else
				userId = user.getId();

			List<JGateway> gateways = gatewayService.getGatewayV1(assetgroupid, groupid, subgroupid, gatewayid, userId,
					meid, user.getChargebeeid());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("gateways", gateways);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;
	}

	// ========get nodes===============
	@RequestMapping(value = "v3.0/node/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getNodeById(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("assetgroupid") String assetgroupid, @RequestParam("groupid") String groupid,
			@RequestParam("subgroupid") String subgroupid, @RequestParam("gatewayid") String gatewayid,
			@RequestParam("nodeid") String nodeid) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<JNode> nodes = nodeService.getNode(assetgroupid, groupid, subgroupid, gatewayid, nodeid, user.getId());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("nodes", nodes);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;
	}

	// ========get AlertCFGs================
	@RequestMapping(value = "v3.0/alertcfg/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertCfgById(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("alertcfgid") String alertcfgid, @RequestParam("alerttypeid") String alerttypeid,
			@RequestParam("assetid") String assetid) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.getCmpId());

			List<JAlertCfg> alertcfgs = alertCfgService.getAlertCfg(alertcfgid, alerttypeid, assetid, user.getId(),
					cmp_cfg.getTemperatureunit());

			Set<Long> assetIds = new HashSet<Long>();

			List<JGateway> userGateway = gatewayService.getGateway(null, null, null, assetid, user.getId(), null);
			List<JGatewayConfig> gatewayConfigs = new ArrayList<JGatewayConfig>();
			if (userGateway != null) {
				for (JGateway jgateway : userGateway) {
					assetIds.add(jgateway.getId());
				}
			}

			for (Long id : assetIds) {

				JGatewayConfig config = new JGatewayConfig();
				Gateway gatewayDetails = gatewayService.getGateway(id);
				config.setGatewayConfig(gatewayDetails.getGatewayConfig());
				config.setOnOffStatus(gatewayDetails.isOnOffStatus());
				config.setAssetid(id);

				gatewayConfigs.add(config);
			}

//				for (JAlertCfg cfg : alertcfgs) {
//					for (Asset asset : cfg.getAssets()) {
//						assetIds.add(asset.getId());
//					}
//				}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("alertcfg", alertcfgs);
			response.put("gatewayConfig", gatewayConfigs);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;

	}

	// ========save or update
	// alertcfg================headers="Content-Type=application/json"
	@RequestMapping(value = "v3.0/alertcfg/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveORupdateGatewayById(@RequestHeader HttpHeaders header, Authentication authentication,
			@ModelAttribute @Valid JAlertCfg jalertcfg, BindingResult res) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {

			/* Create or Update Validation */
			if (res.hasErrors())
				return alertcfgValidation(res);

			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			// Fix to send error response for RV guest User when updating the
			// alert config
			if (user.getUsername().equalsIgnoreCase("RVGuestUser")) {
				response.put("Status", 0);
				// log.info("Invalid**********");
				log.info("RVGuestUser : Need full access? Buy and Subscribe to RV PetSafety!");
				response.put("Msg", "Need full access? Buy and Subscribe to RV PetSafety!");
				return response;

			}

			String mobileNos[] = jalertcfg.getMobilenos().split(",");

			for (String mobileNo : mobileNos) {
				if (!mobileNo.trim().matches("(\\+1-|1-|91-|\\+91-|61-|\\+61-|52-|\\+52-|64-|\\+64-).*")) {
					response.put("Status", 0);
					response.put("Msg",
							"You are not allowed to enter mobile number other than (+1) US,(+1) Canada, (+61)Australia, (+64)New Zealand, (+52)Mexico and (+91)India.");
					return response;
				}
			}

			Company company = user.giveCompany();
			ThrottlingSettings throttle = company.getThrotsettings();

			long mobile_count = throttle.getMobileNos();
			long email_count = throttle.getEmailIds();

			String mailids = jalertcfg.getEmailids().replaceAll("\\s", "");
			long entered_mobileNos = jalertcfg.getMobilenos().split(",").length;
			long entered_emailIds = mailids.split(",").length;

			if (mobile_count >= entered_mobileNos && email_count >= entered_emailIds || throttle.getId() == 5) {
				log.info("alert name: " + jalertcfg.getName());
				log.info("alert type id: " + jalertcfg.giveAlerttypeid());
				boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(), "web");
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "You are allowed to enter " + mobile_count + " mobile numbers and " + email_count
						+ " email address");
			}

		} catch (ConstraintViolationException e) {
			log.error("ConstraintViolationException");
			response.put("Status", 0);
			response.put("Msg", "invalid asset id");
			return response;

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (InvalidAlertTypeException e) {
			log.error("invalid alerttype id");
			response.put("Status", 0);
			response.put("Msg", "invalid alerttype id");
			return response;
		} catch (InvalidAsseIdException e) {
			log.error("ConstraintViolationException");
			response.put("Status", 0);
			response.put("Msg", "invalid asset id");
			return response;
		} catch (Exception e) {
			log.info("saveORupdateAlertcfg::::" + e.getMessage());
			log.error("Exception" + e.getMessage());

			response.put("Status", 0);
			response.put("Msg", "alert name cannot be empty and should be unique");
			return response;
		}

		return response;

	}

	/* Create or Update Validation */
	public JResponse alertcfgValidation(BindingResult res) {
		JResponse response = new JResponse();

		response.put("Status", 0);

		if (res.getFieldError("id") != null)
			response.put("Msg", "id should not be empty");
		else if (res.getFieldError("name") != null)
			response.put("Msg", res.getFieldError("name").getDefaultMessage());
		else if (res.getFieldError("minval") != null)
			response.put("Msg", "Min range between -70 to 170");
		else if (res.getFieldError("maxval") != null)
			response.put("Msg", "Max range between -70 to 170");
		else if (res.getFieldError("severity") != null)
			response.put("Msg", "Severity range between 1 to 4");
		else if (res.getFieldError("enable") != null)
			response.put("Msg", "Enable must be true or false");
		else if (res.getFieldError("notifyfreq") != null)
			response.put("Msg", "Alert Frequency Range should be minimum of 10 minutes and maximum of 5 days");
		else if (res.getFieldError("intermittentfreq") != null)
			response.put("Msg", "Intermittent Frequency range between 1 and 1000");
		else if (res.getFieldError("lat") != null)
			response.put("Msg", "Lat should not be empty");
		else if (res.getFieldError("lon") != null)
			response.put("Msg", "Lon should not be empty");
		else if (res.getFieldError("notificationtype") != null)
			response.put("Msg", res.getFieldError("notificationtype").getDefaultMessage());
		else if (res.getFieldError("radius") != null)
			response.put("Msg", "Radius should not be empty");
		else if (res.getFieldError("fencetype") != null)
			response.put("Msg", "Fencetype should not be empty");

		return response;
	}

//	@RequestMapping(value = "v3.0/furbitalertcfg/", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse getFurbitAlertCfg(@RequestHeader HttpHeaders header, Authentication authentication,
//			@ModelAttribute @Valid JFurbitAlertCfg jfurbitAlertCfg, BindingResult res) {
//		String autho = header.getFirst("auth");
//		log.info("Entered into saveOrUpdateFurbitAlertCfg : " + autho);
//		JResponse response = new JResponse();
//		try {
//
//			/* Create or Update Validation */
//			if (res.hasErrors())
//				return alertcfgValidation(res);
//
//			UserV4 user = userServiceV4.verifyAuthV3("authkey", autho);
//			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
//			if (errResponse != null) {
//				return errResponse;
//			}
//
//			long cmp_id = user.getCmpId();
//			// Fix to send error response for RV guest User when updating the
//			// alert config
//			if (user.getUsername().equalsIgnoreCase("RVGuestUser")) {
//				response.put("Status", 0);
//				// log.info("Invalid**********");
//				log.error("RVGuestUser : Need full access? Buy and Subscribe to RV PetSafety!");
//				response.put("Msg", "Need full access? Buy and Subscribe to RV PetSafety!");
//				return response;
//			}
//
//			Company company = companyService.getCompany(cmp_id);
//
//			// get directly using cmp id
//			ThrottlingSettings throttle = throttlingService
//					.getThrotSettingsById(Long.valueOf(company.getThrotsettingsid()));
//
//			long mobile_count = throttle.getMobileNos();
//			long email_count = throttle.getEmailIds();
//
//			long entered_mobileNos = jfurbitAlertCfg.getMobilenos().split(",").length;
//			long entered_emailIds = jfurbitAlertCfg.getEmailids().split(",").length;
//
//			if (mobile_count >= entered_mobileNos && email_count >= entered_emailIds || throttle.getId() == 5) {
//				log.info("alert name: " + jfurbitAlertCfg.getName());
//				log.info("alert type id: " + jfurbitAlertCfg.getAlerttypeid());
//				//
//				boolean result = alertCfgService.saveOrUpdateFurbitAlertCfg(jfurbitAlertCfg, company);
//				response.put("Status", 1);
//				response.put("Msg", "Success");
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "You are allowed to enter " + mobile_count + " mobile numbers and " + email_count
//						+ " email address");
//			}
//
//		} catch (ConstraintViolationException e) {
//			log.error("ConstraintViolationException");
//			response.put("Status", 0);
//			response.put("Msg", "invalid asset id");
//			return response;
//
//		} catch (InvalidAuthoException e) {
//			log.error("in valid auth");
//			response.put("Status", 0);
//			response.put("Msg", "invalid authentication key");
//			return response;
//		} catch (InvalidAlertTypeException e) {
//			log.error("invalid alerttype id");
//			response.put("Status", 0);
//			response.put("Msg", "invalid alerttype id");
//			return response;
//		} catch (InvalidAsseIdException e) {
//			log.error("ConstraintViolationException");
//			response.put("Status", 0);
//			response.put("Msg", "invalid asset id");
//			return response;
//		} catch (Exception e) {
//			log.error("saveORupdateAlertcfg::::" + e.getMessage());
//			response.put("Status", 0);
//			response.put("Msg", "alert name cannot be empty and should be unique");
//			return response;
//		}
//		return response;
//	}

	// ==========delete gateway========
	@RequestMapping(value = "v3.0/gateway/{asset_id}/", method = RequestMethod.DELETE, headers = "Accept=application/json")
	public @ResponseBody JResponse deleteGateway(@RequestHeader HttpHeaders header, Authentication authentication,
			@PathVariable String asset_id) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			gatewayService.delGateway(user, asset_id);
			response.put("Status", 1);
			response.put("Msg", "success");
		} catch (InvalidAuthoException e) {
			response.put("Status", 0);
			response.put("Msg", "invalid user");
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in delete gateway");
			log.error("deleteGateway::::" + e.getMessage());
		}
		return response;
	}

	// ========save or update gateway================
	@RequestMapping(value = "v3.0/gateway/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveORupdateGatewayById(@RequestHeader HttpHeaders header, Authentication authentication,
			@ModelAttribute @Valid JGateway jgateway, BindingResult result) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			/* Create or Update Validation */
			if (result.hasErrors()) {
				return gatewayValidation(result);
			}

			if (jgateway.isStopreport()) {
				if (jgateway.getStarttime().isEmpty()) {
					response.put("Status", 0);
					response.put("Msg", "Starttime should not be empty");
					return response;
				} else if (jgateway.getStoptime().isEmpty()) {
					response.put("Status", 0);
					response.put("Msg", "Stoptime should not be empty");
					return response;
				}
			}

			if (!(jgateway.getModelid() > 0)) {
				response.put("Status", 0);
				response.put("Msg", "Model id should not be null!");
				return response;
			}

			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			jgateway = gatewayService.gatewayExitsinDB(jgateway, user.giveCompany().getId());

			// Check if gateway name contains special characters
			String gateway_name = jgateway.getName();
			Pattern special = Pattern.compile("[!@#$%&*()+=|<>?{}.,\\[\\]~'\"]");
			Matcher hasSpecial = special.matcher(gateway_name);
			if (hasSpecial.find()) {

				log.info("Gateway name contains special chars");

				response.put("Status", 0);
				response.put("Msg", "Gateway name contains special chars other than hyphen(-) and underscore(_)");
				return response;
			}

			if (jgateway.getQrcode().equals("NA")) {
				Helper _helper = new Helper();

				String niomResponse = _helper
						.getURL(niomIP + "v1.0/getinventory/" + niomAuthKey + "/" + jgateway.getMeid());

				JSONObject nresponse = new JSONObject(niomResponse.toString());
				JSONObject jresponse = new JSONObject();

				jresponse = nresponse.getJSONObject("response");
				int status = jresponse.getInt("Status");
				if (status > 0) {
					jgateway.setQrcode(jresponse.getString("qrCode"));
				} else {
					jgateway.setQrcode("NA");
				}

			}

			Gateway gateway = gatewayService.saveORupdateGateway(jgateway, user.giveCompany().getId());

			if (jgateway.isUserGatDis()) {

				Company usrCompany = user.giveCompany();
				Company gatCop = gateway.getCompany();

				user.getGateways().add(gateway);
				// Set<Gateway> gateways =user.getGateways();
				// gateways.add(gateway);
				// user.setGateways(gateways);
				userService.updateUser(user);
			}
			gatewayService.updateGatewayCredit(gateway.getId(), user.giveCompany().getId());
			if (jgateway.getId() == 0) {
				try {
					// gatewayService.updateGatewayCredit(gateway.getId(),
					// user.giveCompany().getId());
					reportService.saveGatewayReport(gateway, 270.0, 270.0);
					reportService.saveLastGatewayReport(gateway, 270.0, 270.0);

					String message1 = "gpsmode=standalone";
					String message2 = "SFWSTORAGE=ON";
					String message3 = "reportinterval=900";
					String message4 = "maxsleeptime=900";
					String message5 = "modemresetint=3600";
					String n7Cmd = "batoffset=-0.7,chgoffset=1.9,tempoffset=-1.2,fullchgoffset=-2.5";

					String messages = "";
					String deviceModel = gateway.getModel().getModel().toLowerCase();

					if (deviceModel.contains("n13")) {
						message3 = "reportinterval=600";
						message4 = "maxsleeptime=600";
					}

					if (deviceModel.contains("nt3d")) {
						messages = message3 + "," + message4 + "," + message2 + "," + message5;
					} else if (deviceModel.contains("nt3f")) {
						messages = message2 + "," + message3 + "," + message4 + "," + message5;
					} else if (deviceModel.toLowerCase().contains("n13-500")
							|| deviceModel.toLowerCase().contains("n13-503")
							|| deviceModel.toLowerCase().contains("n13g-503")) {
						messages = message1 + "," + message2 + "," + message3;
					} else if (deviceModel.toLowerCase().contains("n13-502")
							|| deviceModel.toLowerCase().contains("n13g-502")) {
						messages = message2 + "," + message3;
					} else if (gateway.getModel().getInventorymodelname().toLowerCase().contains("n1")
							&& !(gateway.getModel().getModel().toLowerCase().contains("n13"))) {
						messages = message1 + "," + message2;
					} else if (gateway.getModel().getInventorymodelname().toLowerCase().contains("w5")) {
						messages = message2;
					} else if (deviceModel.contains("n7")) {
						messages = message2 + "," + message3 + "," + message4 + "," + message5;

//				if( !(gateway.getModel().getModel().equalsIgnoreCase("n7a-503 nt3k") || gateway.getModel().getModel().equalsIgnoreCase("n7-504 nt3k")
//						|| gateway.getModel().getModel().equalsIgnoreCase("n7g-504-m nt3k"))) {
						if (deviceModel.equalsIgnoreCase("n7-504 nt3g")) {
							messages = messages + "," + n7Cmd;
						}

					}

					if (!jgateway.getQrcode().isEmpty() && !jgateway.getQrcode().equalsIgnoreCase("NA"))
						messages = messages + ",setqrc=" + jgateway.getQrcode().trim();

					if (gateway.getModel().getIsgps().equalsIgnoreCase("1") && !(deviceModel.contains("n13")))
						messages = messages + ",gpstrackmode=onetime";

					messagingService.saveMessageV2(Long.toString(gateway.getId()), messages, 0L);

					response.put("Status", 1);
					response.put("Msg", "Success");

					return response;

				} catch (Exception e) {
					// e.printStackTrace();
					response.put("Status", 0);
					response.put("Msg", "Default Reports Cannot be Generated.");
					log.error("3saveORupdateGateway-Default Reports Canot be Generated::::" + e.getLocalizedMessage());
					return response;
				}
			}

//			UserRvDetails rvObj = userServiceV4.getUserRvDetails(user.getId());
//			boolean rvStatus = (rvObj != null) ? true : false;

//			boolean stat1 = rvcentricServ.saveUserBadgeTxn(user.getId(), "NA", 1, rvStatus, user.getChargebeeid());
//			log.info("in add gateway:" + user.getId() + " Badge created:" + stat1);

			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (InvalidAsseIdException be) {
			log.error("in valid assetid");
			response.put("Status", 0);
			response.put("Msg", "invalid id");
			return response;
		} catch (ConstraintViolationException e) {
			log.error("gateway : constraint violation ");
			response.put("Status", 0);
			response.put("Msg", "MEID already exist");
			return response;
		} catch (InvalidAssetGroupIdException e) {
			response.put("Status", 0);
			response.put("Msg", "invalid assetgroupid exception");
			return response;
		} catch (InvalidSubgroupIdException e) {
			response.put("Status", 0);
			response.put("Msg", "invalid subgroupid exception");
			return response;
		} catch (InvalidGroupIdException e) {
			response.put("Status", 0);
			response.put("Msg", "invalid groupid exception");
			return response;
		} catch (InvalidModelIdException e) {
			response.put("Status", 0);
			response.put("Msg", "invalid modelid exception");
			return response;
		} catch (DataIntegrityViolationException e) {
			response.put("Status", 0);
			response.put("Msg", "Asset name or MEID already exits");
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			log.info("saveORupdateGateway::::" + e.getMessage());
			response.put("Msg", "gateway name cannot be empty , and unique for every group");
			return response;
		}
		return response;
	}

	/* Create or Update Validation */
	public JResponse gatewayValidation(BindingResult result) {
		JResponse response = new JResponse();

		response.put("Status", 0);
		if (result.getFieldError("id") != null)
			response.put("Msg", "id should not be empty");
		else if (result.getFieldError("name") != null)
			response.put("Msg", result.getFieldError("name").getDefaultMessage());
		else if (result.getFieldError("meid") != null)
			response.put("Msg", result.getFieldError("meid").getDefaultMessage());
		else if (result.getFieldError("modelid") != null)
			response.put("Msg", "Model should not be empty");
		else if (result.getFieldError("groupid") != null)
			response.put("Msg", "Groupid should not be empty");
		else if (result.getFieldError("mdn") != null)
			response.put("Msg", result.getFieldError("mdn").getDefaultMessage());
		else if (result.getFieldError("enable") != null)
			response.put("Msg", "Enable must be true or false");
		else if (result.getFieldError("alive") != null)
			response.put("Msg", "Alive must be true or false");
		else if (result.getFieldError("assetgroupid") != null)
			response.put("Msg", "assetgroupid should not be empty");
		else if (result.getFieldError("sensorEnable") != null)
			response.put("Msg", result.getFieldError("sensorEnable").getDefaultMessage());
		else if (result.getFieldError("stopreport") != null)
			response.put("Msg", "stopreport must be true or false");
		return response;
	}

	// userUpdateV4 - By Savitha
	@RequestMapping(value = "v4.0/userupdateweb/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse userUpdateWebV4(@RequestBody UserV4 user, @RequestHeader HttpHeaders header,
			Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering userUpdateWebV4 : " + autho);
		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.error("Invalid UserId :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}

			long user_id = userServiceV4.getUserByUNameOrEmailV4(user.getEmail());
			if (user_id > 0 && (usr.getId() != user_id)) {
				response.put("Status", 0);
				response.put("Msg", "Email  already exist. Please enter alternate Email!");
				return response;
			}

			String password = user.getPassword();
//			UserV4 existingUser = userServiceV4.verifyAuthV3("authkey", user.getAuthKey());
//			if( !password.equals( existingUser.getPassword() ) ) {
//				
//				JValidateString validatePassword = userServiceV4.validatePassword(password);
//				if( !validatePassword.isValid() ) {
//					response.put("Status", 0);
//					response.put("Msg", validatePassword.getMsg());
//					return response;
//				}

			user.setPassword(_helper.bCryptEncoder(password));
//			}

			user.setUpdatedOn(_helper.getCurrentTimeinUTC());
			String mobileNo = user.getMobileno();

			if (!mobileNo.contains("-") && mobileNo.length() > 0) {
				mobileNo = new CountryCode().getCountryCode(user.getCountry().toUpperCase()) + user.getMobileno();
				user.setMobileno(mobileNo);
			}

			if (user.getEmail() == null || user.getEmail().equalsIgnoreCase("NA"))
				user.setEmail(usr.getEmail());

			boolean Status = userServiceV4.updateUserv4byuseridWeb(user);
			if (Status)
				async.updateEvalidation(user.getId(), password);

			if (Status) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "UnExcepted Error in User Update WebV4");
			}

		} catch (Exception e) {
			log.error("Exception : userUpdateWebV4 : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in User Update WebV4");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	// v4.0/userV2/ - Savitha
	@RequestMapping(value = "v4.0/userV2web/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserV2_V4Web(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("userid") String userid, @RequestParam("cmpid") String cmpid,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		String autho = header.getFirst("auth");
		log.info("Entering getUserV2_V4_web : " + autho);
		JResponse response = new JResponse();
		UserV4 user = null;
		List<UserV4> usersList = new ArrayList<UserV4>();
		List<UserV4> responseList = new ArrayList<UserV4>();
		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);
		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + autho);
			return response;
		}
		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			long cmp_id = 0;
			if (user != null) {
				cmp_id = user.getCmpId();
				if (cmpid.trim().isEmpty())
					cmpid = String.valueOf(user.getCmpId());

				usersList = userServiceV4.getUsersByUserId_CmpId(userid, Long.parseLong(cmpid));

				if (usersList != null && usersList.size() > 0) {
					for (UserV4 thisUser : usersList) {
						if (userid.trim().isEmpty())
							userid = String.valueOf(thisUser.getId());
						String mobile = "";
						UserV4 newUser = thisUser;
						if (thisUser != null) {
							String mobileNumber = thisUser.getMobileno();
							String[] phoneNumber = null;

							if (!mobileNumber.contains("-")) {
								mobileNumber = "-" + mobileNumber;
							}
							phoneNumber = mobileNumber.split("-");
							String cont = phoneNumber[0];
							String mob = phoneNumber[1];

							if (thisUser.getCountry().equalsIgnoreCase("NA") || thisUser.getCountry() == null) {
								if (cont.equalsIgnoreCase("+91") || cont.equalsIgnoreCase("91")) {
									thisUser.setCountry("IN");
								} else if (cont.equalsIgnoreCase("+44") || cont.equalsIgnoreCase("44")) {
									thisUser.setCountry("GB");
								} else {
									thisUser.setCountry("US");
								}
							}
							mobile = mob.replaceAll("\\W", "");
							thisUser.setMobileno(mobile);
							responseList.add(newUser);
						}
					}
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("users", responseList);

				} else {
					response.put("Status", 0);
					response.put("Msg", "User not found!");
					response.put("users", responseList);
				}
			}
//			else {
//				response.put("Status", 0);
//				response.put("Msg", "User not found!");
//				response.put("users", responseList);
//			}
		} catch (Exception e) {
			log.error("Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occured");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	// ==========save / update user========
	@RequestMapping(value = "v3.0/user/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updateUser(@RequestHeader HttpHeaders header, Authentication authentication,
			@ModelAttribute @Valid JUser juser, BindingResult result) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		try {

			/* Create or Update Validation */
			if (result.hasErrors()) {
				response.put("Status", 0);

				if (result.getFieldError("id") != null)
					response.put("Msg", "id should not be empty");
				else if (result.getFieldError("username") != null)
					response.put("Msg", result.getFieldError("username").getDefaultMessage());
				else if (result.getFieldError("password") != null)
					response.put("Msg", result.getFieldError("password").getDefaultMessage());
				else if (result.getFieldError("roleid") != null)
					response.put("Msg", "Invalid RoleId.Range between 1 and 6");
				else if (result.getFieldError("webappid") != null)
					response.put("Msg", "Invalid Webappid.Range between 1 and 2");
				else if (result.getFieldError("mobileappid") != null)
					response.put("Msg", "Invalid Mobileappid.Range between 1 and 5");
				else if (result.getFieldError("enable") != null)
					response.put("Msg", "Enable must be true or false");
				return response;
			}

			// changes done

			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			if (juser.getEmail().contains("null")) {
				juser.setEmail(null);
			}
			if (juser.getAlternateemail().equalsIgnoreCase("NA") || juser.getAlternateemail().isEmpty()) {
				juser.setAlternateemail(user.getAlternateemail());
			}
			if (juser.getAlternatephone().equalsIgnoreCase("NA") || juser.getAlternatephone().isEmpty()) {
				juser.setAlternatephone(user.getAlternatephone());
			}
			if (juser.getFirstname().equalsIgnoreCase("NA") || juser.getFirstname().isEmpty()) {
				juser.setFirstname(user.getFirstname());
			}
			if (juser.getLastname().equalsIgnoreCase("NA") || juser.getLastname().isEmpty()) {
				juser.setLastname(user.getLastname());
			}
			if (juser.getZipcode().equalsIgnoreCase("NA") || juser.getZipcode().isEmpty()) {
				juser.setZipcode(user.getZipcode());
			}
			if (juser.getCity().equalsIgnoreCase("NA") || juser.getCity().isEmpty()) {
				juser.setCity(user.getCity());
			}
			if (juser.getState().equalsIgnoreCase("NA") || juser.getState().isEmpty()) {
				juser.setState(user.getState());
			}
			if (juser.getCountry().equalsIgnoreCase("NA") || juser.getCountry().isEmpty()) {
				juser.setCountry(user.getCountry());
			}

			if (juser.getEmail() != null) {
				try {
					// User usr = userService.getUserByEmail(juser.getEmail());
					User usr = userService.getUserByUNameOrEmail(juser.getEmail());
					if (usr != null && !(user.getId() == usr.getId())) {

						response.put("Msg", "Email already exist. Please enter alternate Email");
						return response;
					}
				} catch (Exception ex) {

					log.error("user update failed : " + ex.getMessage());
				}
			}

			// Fix to send error response for RV guest User when updating the
			// password

			if (user.getUsername().equalsIgnoreCase("RVGuestUser")) {
				response.put("Status", 0);
				// System.out.println("Invalid**********");
				log.error("RVGuestUser : Need full access? Buy and Subscribe to RV PetSafety!");
				response.put("Msg", "Need full access? Buy and Subscribe to RV PetSafety!");
				return response;

			}

			juser.setCmp(user.giveCompany());
			juser.setGateways(user.getGateways());
			juser.setAuthKey(user.getAuthKey());

			juser.setVerified(user.isVerified());

			juser.setNotification(user.isNotification());

			if (juser.getId() == 0) {
				long cmptype_id = user.giveCompany().getCompanytype().getId();
				if (cmptype_id == 3) {
					juser.setMobileappid(3); // RvPet
				} else if (cmptype_id == 5) {
					juser.setMobileappid(4); // ResMonitor
				} else if (cmptype_id == 6) { // White Label
					juser.setMobileappid(6);
					juser.setWebappid(3);
				}
			}

			// 1- disable, 2 - iris, 3, - RvPet, 4 - ResMonitor,
			// 5-ResMonitor-Background, 6-White Label

			log.info("updateUser: company id - " + juser.giveCmp().getCompanytype().getId() + " mobileappid - "
					+ juser.getMobileappid());

			if ((juser.giveCmp().getCompanytype().getId() == 3 && juser.getMobileappid() != 3)
					|| (juser.giveCmp().getCompanytype().getId() != 3 && juser.getMobileappid() == 3) ||

					(juser.giveCmp().getCompanytype().getId() == 5
							&& ((juser.getMobileappid() != 4) && (juser.getMobileappid() != 5)))
					|| (juser.giveCmp().getCompanytype().getId() != 5
							&& ((juser.getMobileappid() == 4) || (juser.getMobileappid() == 5)))
					||

					(juser.giveCmp().getCompanytype().getId() == 6 && juser.getMobileappid() != 6)
					|| (juser.giveCmp().getCompanytype().getId() != 6 && juser.getMobileappid() == 6)) {
				response.put("Status", 0);
				log.error("updateUser:Invalid combination of company type id and mobileapp id");
				response.put("Msg", "Invalid Mobile Application. Please Change Your Company Type!");
				return response;
			}
			userService.saveOrUpdateJUser(juser);

			response.put("Status", 1);
			response.put("Msg", "success");

		} catch (InvalidAuthoException e) {
			log.error("updateUser :" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid authentication key");

		} catch (DataIntegrityViolationException e) {
			response.put("Status", 0);
			response.put("Msg", "Username already exits");
			log.error("updateUser :" + e.getMessage());
		}

		catch (Exception e) {
			log.error("updateUser :" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExpected Error in Create User");
		}
		return response;
	}

	// ========save/update groups================
	@RequestMapping(value = "v3.0/group/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveorUpdateGroup(@RequestHeader HttpHeaders header, Authentication authentication,
			@ModelAttribute @Valid Groups groups, BindingResult res) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			/* Create or Update Validation */
			if (res.hasErrors()) {
				response.put("Status", 0);
				if (res.getFieldError("name") != null)
					response.put("Msg",
							res.getFieldError("name").getField() + " " + res.getFieldError("name").getDefaultMessage());
				else if (res.getFieldError("id") != null)
					response.put("Msg", "id should not be empty");
				return response;
			}

			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
//			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
//			if (errResponse != null) {
//				return errResponse;
//			}

			boolean result = groupservices.saveOrUpdateGroups(groups, user.getCmpId());
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error("saveorUpdateGroup::::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "group name cannot be empty , and also be unique ");
			return response;
		}

		return response;

	}

	@RequestMapping(value = "v3.0/getcurrentsubscriptionplan/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCurrentSubscriptionPlan(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("userid") long userid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "backing", defaultValue = "MT", required = false) String backing) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		User user = null;
		Timestamp start = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("start : " + start + " getcurrentsubscriptionplan: " + auth);
		try {

			Helper _helper = new Helper();
			log.info("backing key : " + backing);

			user = userService.verifyAuthKey(auth);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

//				AES aes = new AES();
//				if (backing != null) {
//					if (!backing.equals("MT")) {
//						String[] credential = _helper.decodeInternalKey(backing);
//						String finalOut = aes.decode(credential[0], credential[1]);
//						
//						if (finalOut == null) {
//							response.put("Status", 0);
//							response.put("Msg", "Authentication Error");
//							return response;
//						}
//						log.info("AES decryption success : "+backing+" : "+finalOut);
//					}
//				} else {
//					response.put("Status", 0);
//					response.put("Msg", "Authentication Error");
//					return response;
//				}

			user = userService.getUserById(userid);

		} catch (Exception e) {
			log.error("getCurrentSubscriptionPlan: user by id : " + e.getMessage());
		}

		try {
			if (user != null) {
				if (user.isEnable()) {
					int inapp_redirect = 1;
					boolean cb_checkout = true;
					VersionMapping verObj = crService.getVersionMapping(app_ver, os);

					if (verObj != null) {
						inapp_redirect = verObj.getInapp_redirect();
						cb_checkout = verObj.isCb_checkout();
					}

					if (user.getInapp_purchase() == 1) {
						response = getSubscription(user, inapp_redirect);
						response.put("cb_checkout", cb_checkout);
					} else if (user.getInapp_purchase() == 2) {
						response = getIosSubscription(user, inapp_redirect);
						response.put("cb_checkout", cb_checkout);
					} else {
						response.put("Status", 0);
						response.put("Msg", "Subscription Details not found");

					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "User not enabled");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");
			log.error("getCurrentSubscriptionPlan:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		return response;
	}

	public JResponse getIosSubscription(User user, int inapp_redirect) {
		String vpmplan = _helper.getExternalConfigValue("vpmplan", externalConfigService);

		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		try {

			// initialize values
			JVpmSubscription vpmSubs = new JVpmSubscription();
			String strprice1 = "NA";
			int availCnt = 0;
			int totalCnt = 0;
			int usedCnt = 0;

			int iris_splan = 1;
			int iris_speriod = 1;
			boolean vpm_enable = companyService.getVetCallStaus(user.giveCompany().getId());

			JSubscriptionPlanReport rpt = crService.getInappSubscriptionByUser(user.getId());

			if (rpt != null) {

				iris_splan = Integer.valueOf(rpt.getPlanid());
				iris_speriod = Integer.valueOf(rpt.getPeriodid());

				List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(rpt.getPlanid(), user.getId(),
						rpt.getDays_remaining());
				rpt.setListJGatewaySubSetup(setupList);

			} else {
				response = getSubscription(user, inapp_redirect);
				return response;
			}

			if (vpm_enable) {
				ArrayList<Integer> vpmlist = crService.getVPMAvailabilty(user.getId(), "VPM", 0, "NA");
				availCnt = vpmlist.get(0);
				totalCnt = vpmlist.get(1);
				usedCnt = totalCnt - availCnt;
			}
			vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, "NA", "NA", "NA", 0, "NA", "NA",
					false, "", "", false);

			boolean is_upgrade = true;
			int redirect = user.getInapp_purchase();
			String upgrade_msg = "Success";

			if (iris_splan != 1 && user.getInapp_purchase() == 2 && inapp_redirect == 1) {
				is_upgrade = false;
				redirect = user.getInapp_purchase();
				upgrade_msg = " You have purchased in App Store. Pls cancel subscription in app store and then purchase in android ";
			} else if (iris_splan == 1 && user.getInapp_purchase() == 2 && inapp_redirect == 1) {
				user.setInapp_purchase(inapp_redirect);
				redirect = inapp_redirect;
				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("planid", iris_splan);
			response.put("periodid", iris_speriod);
			response.put("subscriptionplan", rpt);
			response.put("vpm_enable", vpm_enable);
			response.put("vpmsubs", vpmSubs);
			response.put("is_upgrade", is_upgrade);
			response.put("redirect_inapp", redirect);
			response.put("upgrade_msg", upgrade_msg);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error while getting Subscription plan");
			log.error("getSubscription:" + e.getMessage());
		}

		return response;
	}

	public JResponse getSubscription(User user, int inapp_redirect) {
//			String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
//			String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);
//			String vpmplan = _helper.getExternalConfigValue("vpmplan", externalConfigService);

		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		try {
			String cbId = "NA";
//			if (user.getChargebeeid().equalsIgnoreCase("NA")) {
//				cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
//						user.getMobileno(), user.getUsername(), 0, "NA");
//				user.setChargebeeid(cbId);
//			}
			if (user.getChargebeeid().equalsIgnoreCase("NA")) {
				ListResult rs = null;
				int i = 1;
				Loop: while (rs == null || i <= 3) {
					i = i + 1;
					Thread.sleep(2000);
					rs = Customer.list().email().is(user.getEmail()).sortByCreatedAt(SortOrder.ASC).request();

					if (!rs.isEmpty()) {
						for (ListResult.Entry entry : rs) {
							Customer customer = entry.customer();
							user.setChargebeeid(customer.id());
						}
						break Loop;
					}
				}
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
							user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}
			}
			com.chargebee.models.Subscription subscrip = null;
			com.chargebee.models.Subscription vpmSubscrip = null;
			boolean isPaidPlan = false;
			boolean payment_due = false;

			if (!user.getChargebeeid().equalsIgnoreCase("NA")) {
				ListResult result = com.chargebee.models.Subscription.list().customerId().is(user.getChargebeeid())
						.status().in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL)
						.sortByUpdatedAt(SortOrder.DESC).request();
				int ssize = 0;

				if (!result.isEmpty()) {
					ssize = result.size();
					for (ListResult.Entry subs : result) {
						if (ssize == 1 && !omitplan.contains(subs.subscription().planId())
								&& !vpmplan.contains(subs.subscription().planId())
								&& !addonplan.contains(subs.subscription().planId())) {
							subscrip = subs.subscription();

							if (!freeplan.contains(subs.subscription().planId())
									&& !vpmplan.contains(subs.subscription().planId())
									&& !addonplan.contains(subs.subscription().planId()))
								isPaidPlan = true;

						} else if (!freeplan.contains(subs.subscription().planId())
								&& !omitplan.contains(subs.subscription().planId())
								&& !vpmplan.contains(subs.subscription().planId())
								&& !addonplan.contains(subs.subscription().planId())) {
							subscrip = subs.subscription();
							isPaidPlan = true;
							break;
						}
					}

					if (subscrip == null) {
						for (ListResult.Entry subs : result) {
							if (freeplan.contains(subs.subscription().planId())) {
								subscrip = subs.subscription();
								break;
							}
						}
					}
					if (vpmSubscrip == null) {
						for (ListResult.Entry subs : result) {
							if (vpmplan.contains(subs.subscription().planId())) {
								vpmSubscrip = subs.subscription();
								break;
							}
						}
					}
				}
				if ((result.isEmpty() || subscrip == null) && (inapp_redirect != 2)) {
					// ios inapp user.here checking is there any CB subscription available
					log.info("CB sub_create : getSubscription : userid : " + user.getId());
					subscrip = cbService.createDefaultSubsPlan(user.getChargebeeid());
				}
			}
			// initialize values
			JVpmSubscription vpmSubs = new JVpmSubscription();
			String strprice1 = "NA";
			int availCnt = 0;
			int totalCnt = 0;
			int usedCnt = 0;

			String billingPeriod = "NA";
			String periodUnit = "MONTH";
			String planid = "chum";
			String planname = "Chum";
			String availCredit = "0";
			int period = 0;

			float price = (float) 0.0;
			String strprice = "$0.0";
			String status = "ACTIVE";
			int daysBetween = -1;
			int days_remaining = -1;
			String startedAt = "NA";
			String nextPaymentDate = "NA";
			String createDate = "NA";
			String updateDate = "NA";
			String cbSubId = "NA";
			String cbSubStatus = "NA";
			boolean setupAutoRenewal = false;
			String autoRenewalStatus = "NA";
			boolean cancel = false;
			boolean vetchat_cancel = false;
			String cbvet_cancelId = "";
			String cbvet_planId = "";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date dateobj = new Date();
			Timestamp nextpaymentTS;
			boolean cb_vetchat = false;

			int iris_splan = 1;
			int iris_speriod = 1;
			String desc = "Free Plan";
			JSubscriptionPlanReport rpt = new JSubscriptionPlanReport();
			boolean vpm_enable = companyService.getVetCallStaus(user.giveCompany().getId());
			int substatus_code = 0;

			if (subscrip != null || vpmSubscrip != null) {
				if (subscrip != null) {
					CreditNote cr = null;
					ListResult crLst = CreditNote.list().customerId().is(user.getChargebeeid()).request();
					cr = crLst.isEmpty() ? null : crLst.get(0).creditNote();
					ListResult planRes = Plan.list().id().is(subscrip.planId()).request();
					availCredit = (cr != null) ? String.valueOf(cr.amountAvailable() / 100.0) : "0";

					for (ListResult.Entry planR : planRes) {
						Plan plan = planR.plan();
						period = plan.period();
						periodUnit = plan.periodUnit().name();
						planid = plan.id();
						planname = plan.name();

						if (periodUnit.equalsIgnoreCase("YEAR")) {
							if (period == 2)
								billingPeriod = "2 Years";
							else if (period >= 5)
								billingPeriod = planname;
							else
								billingPeriod = "Yearly";
						} else if (periodUnit.equalsIgnoreCase("MONTH")) {
							if (period == 3)
								billingPeriod = "Quarterly";
							else if (period == 6)
								billingPeriod = "Half-Yearly";
							else if (period == 12)
								billingPeriod = "Yearly";
							else
								billingPeriod = "Monthly";
						} else if (periodUnit.equalsIgnoreCase("DAY")) {
							billingPeriod = "Daily";
						} else if (periodUnit.equalsIgnoreCase("WEEK")) {
							billingPeriod = "Weekly";
						}

						price = (float) subscrip.planUnitPrice() / 100;
						strprice = "$" + String.valueOf(price);
						status = subscrip.status().name();

						if ((status.equalsIgnoreCase("ACTIVE")) || (status.equalsIgnoreCase("IN_TRIAL"))) {
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							if (status.equalsIgnoreCase("IN_TRIAL")) {
								cancel = true;
							}
							substatus_code = 1;
							try {
								nextpaymentTS = subscrip.nextBillingAt();

								if (nextpaymentTS == null)
									nextpaymentTS = subscrip.currentTermEnd();

							} catch (Exception ex) {
								nextpaymentTS = subscrip.currentTermEnd();
							}
							nextPaymentDate = sdf.format(nextpaymentTS.getTime());

							Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
							Date todayDate = sdf.parse(sdf.format(dateobj));

							long difference = nextPaymentDate1.getTime() - todayDate.getTime();
							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
							days_remaining = daysBetween;

							sdf = new SimpleDateFormat("yyyy-MM-dd");
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							nextPaymentDate = sdf.format(nextpaymentTS.getTime());
							// status = "ACTIVE";
							autoRenewalStatus = "Enabled";

							if (freeplan.contains(subscrip.planId())) {
								nextPaymentDate = "NA";
								days_remaining = -1;
								autoRenewalStatus = "NA";
								billingPeriod = "NA";
							}

						} else if (status.equalsIgnoreCase("NON_RENEWING")) {
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							status = "ACTIVE";
							nextPaymentDate = sdf.format(subscrip.cancelledAt().getTime());
							Date cancelledAt = sdf.parse(nextPaymentDate);
							Date todayDate = sdf.parse(sdf.format(dateobj));

							long difference = cancelledAt.getTime() - todayDate.getTime();
							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
							days_remaining = daysBetween;

							sdf = new SimpleDateFormat("yyyy-MM-dd");
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							nextPaymentDate = sdf.format(subscrip.cancelledAt().getTime());
							substatus_code = 1;
							autoRenewalStatus = "Disabled";
						}

						if (daysBetween < 0)
							days_remaining = -1;

						ArrayList<Integer> ids = crService.getPlanAndPeriod(planid);
						boolean alert_setting = true;
						if (!ids.isEmpty()) {
							iris_splan = ids.get(0);
							iris_speriod = ids.get(1);

							SubscriptionPlan splan = crService.getSubsPlanById(ids.get(0));
							desc = splan.getDescription();
							planname = splan.getPlan_name();
							alert_setting = splan.isAlert_setting();
						}

						// safety
						try {
							sdf = new SimpleDateFormat("yyyy-MM-dd");
							createDate = sdf.format(subscrip.createdAt().getTime());
							updateDate = sdf.format(subscrip.updatedAt().getTime());
							startedAt = sdf.format(subscrip.startedAt().getTime());
							cbSubId = subscrip.id();
							cbSubStatus = subscrip.status().name();
						} catch (Exception e) {
							log.error("subs dates: ", e.getLocalizedMessage());
						}

						List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(String.valueOf(iris_splan),
								user.getId(), days_remaining);

						rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
								days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus,
								createDate, updateDate, cbSubId, cbSubStatus, availCredit, alert_setting,
								user.getChargebeeid(), startedAt, planid, String.valueOf(iris_speriod), cancel,
								substatus_code, payment_due);
					}
				} else {
					List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(String.valueOf(iris_splan), user.getId(),
							days_remaining);

					rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
							days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus, createDate,
							updateDate, cbSubId, cbSubStatus, availCredit, true, user.getChargebeeid(), startedAt, "",
							String.valueOf(iris_speriod), cancel, substatus_code, payment_due);
				}
				int vpmstatus_code = 0;
				String str_total_cnt = "0";
				float price1 = 0;
				if (vpmSubscrip != null) {
					price1 = (float) vpmSubscrip.planUnitPrice() / 100;
					if (vpmSubscrip.planId().equalsIgnoreCase("vet-chat")) {
						strprice1 = "$" + String.valueOf(price1);
						nextPaymentDate = "NA";
						autoRenewalStatus = "Disabled";
						billingPeriod = "NA";
						vpmstatus_code = 1;
					} else {
						sdf = new SimpleDateFormat("yyyy-MM-dd");
						sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
						cb_vetchat = true;
						if (status.equalsIgnoreCase("ACTIVE") || (status.equalsIgnoreCase("IN_TRIAL"))) {
							if (status.equalsIgnoreCase("IN_TRIAL")) {
								cancel = true;
								cbvet_cancelId = vpmSubscrip.id();
								cbvet_planId = vpmSubscrip.planId();
							}
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							try {
								autoRenewalStatus = "Enabled";
								nextpaymentTS = vpmSubscrip.nextBillingAt();

								if (nextpaymentTS == null)
									nextpaymentTS = vpmSubscrip.currentTermEnd();

							} catch (Exception ex) {
								nextpaymentTS = vpmSubscrip.currentTermEnd();
							}

							nextPaymentDate = sdf.format(nextpaymentTS.getTime());

							Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
							Date todayDate = sdf.parse(sdf.format(dateobj));

							long difference = nextPaymentDate1.getTime() - todayDate.getTime();
							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
							days_remaining = daysBetween;
							autoRenewalStatus = "Enabled";
							vpmstatus_code = 1;

						} else if (status.equalsIgnoreCase("NON_RENEWING")) {
							autoRenewalStatus = "Disabled";
							status = "ACTIVE";
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							nextPaymentDate = sdf.format(vpmSubscrip.cancelledAt().getTime());
							Date cancelledAt = sdf.parse(nextPaymentDate);
							Date todayDate = sdf.parse(sdf.format(dateobj));

							long difference = cancelledAt.getTime() - todayDate.getTime();
							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
							days_remaining = daysBetween;
							vpmstatus_code = 1;
							autoRenewalStatus = "Disabled";
						} else {
							status = "INACTIVE";
							vpmstatus_code = 0;
							strprice1 = "$0.0";
						}
						billingPeriod = "Monthly";
						strprice1 = "$" + String.valueOf(price1);
						str_total_cnt = "Unlimited";
					}

					ArrayList<Integer> vpmlist = crService.getVPMAvailabilty(user.getId(), "VPM", 0,
							vpmSubscrip.planId());

					if (!vpmlist.isEmpty()) {
						availCnt = vpmlist.get(0);
						totalCnt = vpmlist.get(1);
						usedCnt = totalCnt - availCnt;

						if (availCnt == 0) {
							status = "INACTIVE";
							vpmstatus_code = 0;
							strprice1 = "$0.0";
							nextPaymentDate = "NA";
							billingPeriod = "NA";
							str_total_cnt = "0";
						}
						if (vpmSubscrip.planId().equalsIgnoreCase("vet-chat") && totalCnt > 0)
							str_total_cnt = totalCnt + "";
					}

					vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
							str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod, vetchat_cancel,
							cbvet_cancelId, cbvet_planId, cb_vetchat);
				} else {

					if (vpm_enable) {
						ArrayList<Integer> vpmlist = crService.getVPMAvailabilty(user.getId(), "VPM", 0, "");
						availCnt = vpmlist.get(0);
						totalCnt = vpmlist.get(1);
						usedCnt = totalCnt - availCnt;
						status = "ACTIVE";
						vpmstatus_code = 1;
						str_total_cnt = availCnt + "";

						if (availCnt == 0) {
							status = "INACTIVE";
							vpmstatus_code = 0;
							strprice1 = "$0.0";
							nextPaymentDate = "NA";
							billingPeriod = "NA";
							str_total_cnt = "0";
						}
					} else {
						availCnt = 0;
						totalCnt = 0;
						usedCnt = 0;
						status = "INACTIVE";
						vpmstatus_code = 0;
						strprice1 = "$0.0";
						nextPaymentDate = "NA";
						billingPeriod = "NA";
						str_total_cnt = "0";
					}
					autoRenewalStatus = "Disabled";
					nextPaymentDate = "NA";
					billingPeriod = "NA";
					strprice1 = "$0.0";
					vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
							str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod, vetchat_cancel,
							cbvet_cancelId, cbvet_planId, cb_vetchat);
				}

			} else {

				List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(String.valueOf(iris_splan), user.getId(),
						days_remaining);

				rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
						days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus, createDate,
						updateDate, cbSubId, cbSubStatus, availCredit, true, user.getChargebeeid(), startedAt, "",
						String.valueOf(iris_speriod), cancel, substatus_code, payment_due);

				availCnt = 0;
				totalCnt = 0;
				usedCnt = 0;
				status = "INACTIVE";
				autoRenewalStatus = "Disabled";
				nextPaymentDate = "NA";
				int vpmstatus_code = 0;
				String str_total_cnt = "0";
				billingPeriod = "NA";
				strprice1 = "0";

				vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
						str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod, vetchat_cancel, cbvet_cancelId,
						cbvet_planId, cb_vetchat);

			}

			boolean is_upgrade = true;
			int redirect = user.getInapp_purchase(); // default 1
			String upgrade_msg = "Success";

			if (isPaidPlan == true && (inapp_redirect == 2 || user.getInapp_purchase() == 2)) {
				inapp_redirect = 1;
				redirect = inapp_redirect;
				user.setInapp_purchase(inapp_redirect);
				// call user update method
				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());

			} else if ((isPaidPlan == false && inapp_redirect == 2 && user.getInapp_purchase() == 1)
					|| (isPaidPlan == false && inapp_redirect == 1 && user.getInapp_purchase() == 2)) {
				// update CB purchase status to 2[inapp] in user table
				user.setInapp_purchase(inapp_redirect);
				redirect = inapp_redirect;
				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());
			}

			redirect = user.getInapp_purchase();

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("planid", iris_splan);
			response.put("periodid", iris_speriod);
			response.put("subscriptionplan", rpt);
			response.put("vpm_enable", vpm_enable);
			response.put("vpmsubs", vpmSubs);

			response.put("is_upgrade", is_upgrade);
			response.put("redirect_inapp", redirect);
			response.put("upgrade_msg", upgrade_msg);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error while getting Subscription plan");
			log.error("getSubscription:" + e.getMessage());
		}

		return response;
	}

	public List<JGatewaySubSetup> checkDeviceConfigStatus(String planid, long userid, int days_remaining) {
		List<JGatewaySubSetup> setupList = new ArrayList<JGatewaySubSetup>();

		ArrayList<String> config = null;
		String mType = null;
		String[] dConfig = null;
		String[] devCount = null;
		int maxDevCnt = 0;
		boolean setupActivate = false;
		HashMap<String, Long> newDevice = new HashMap<String, Long>();
		List<Gateway> gateways = gatewayService.getGatewayByUser("", "", "", "", userid, "");

		long cnt = 0;
		long curCnt = 0;
		long add_device_cnt = 0;
		int remaindays = -1;
		String showNextRenewal_withContent = "";

		if (!gateways.isEmpty()) {
			cnt = gateways.size();
		}

		// ArrayList<Integer> ids = crService.getPlanAndPeriod(planid);

//		if (!planid.isEmpty()) {
//			config = crService.getDeviceConfig(Long.parseLong(planid));
//			mType = config.get(0);
//			dConfig = config.get(1).split(",");
//			maxDevCnt = Integer.parseInt(config.get(2));
//
//			if (dConfig[0].length() > 1) {
//				devCount = dConfig[0].split("/");
//
//				newDevice.put("1", Long.parseLong(devCount[0])); // key - monitortype , value - device count
//				newDevice.put("2", Long.parseLong(devCount[1]));
//			}
//		}
		// Reading device count from plan table
		if (!planid.isEmpty()) {
			mType = "1";
			dConfig = new String[1];
			dConfig[0] = "0";
			maxDevCnt = crService.getMaxDeviceCount(Long.parseLong(planid));
		}
		LinkedHashMap<String, Gateway> maplastGateway = crService.getGatewaysByReportTime(userid, 1);
		// LinkedHashMap<String, Gateway> mapfurbitlastGateway =
		// crService.getGatewaysByReportTime(userid, 2);

		// maplastGateway.putAll(mapfurbitlastGateway);

		for (Gateway gateway : gateways) {
			maplastGateway.put(gateway.getId() + "", gateway);
		}

		gateways = new ArrayList<Gateway>(maplastGateway.values());

		for (Gateway gateway : gateways) {
			if (gateway.getModel().getMonitor_type().getId() == 1) {
				if (!planid.isEmpty()) {
					if (Long.parseLong(planid) == 1) {
						setupActivate = true;
					} else {
						String gmonitor = String.valueOf(gateway.getModel().getMonitor_type().getId());

						if (mType.contains(gmonitor)) {
							// if dConfig contains 0 means can add any type upto no_cnt value
							if (dConfig[0].length() == 1 && dConfig[0].equalsIgnoreCase("0")) {

								if (maxDevCnt > add_device_cnt) {
									setupActivate = false;
									add_device_cnt = add_device_cnt + 1;
									remaindays = days_remaining;
								} else {
									setupActivate = true;
									remaindays = -1;
								}

							} else {
								curCnt = newDevice.get(gmonitor);

								if (curCnt >= 1) {
									newDevice.put(gmonitor, curCnt - 1);
									add_device_cnt = add_device_cnt + 1;
									setupActivate = false;
									remaindays = days_remaining;
								} else {
									setupActivate = true;
									remaindays = -1;
								}
							}
						} else {
							setupActivate = true;
							remaindays = -1;
						}
					}
				} else {
					setupActivate = true;
					remaindays = -1;
				}

				if ((remaindays < days_tohandle_nextrenewal) && (remaindays > 0) && show_nextrenewal_popup) {
					showNextRenewal_withContent = "Subscription renews in " + remaindays + " days";
				}

				JGatewaySubSetup setup = new JGatewaySubSetup(gateway.getId(), setupActivate, remaindays,
						gateway.getMeid(), "NA");

				setupList.add(setup);
			}
		}
		return setupList;
	}

	// ========get user by userId or company id================
	@RequestMapping(value = "v3.0/user/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserById(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("userid") String userid,
			@RequestParam(value = "roleid", defaultValue = "", required = false) String roleid,
			@RequestParam(value = "zip", defaultValue = "0", required = false) String zip) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			List<User> users = userService.getUserInRole(userid, user.giveCompany().getId(), user.getRole().getId(),
					roleid);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("users", users);

		} catch (InvalidAuthoException e) {
			log.error("InvalidAuthoException:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (InvalidUsernameException e) {
			log.error("in valid userid :" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid userid");
			return response;
		}
		return response;

	}

	@RequestMapping(value = "v3.0/listfeature/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listFeature(@RequestHeader HttpHeaders header, Authentication authentication,@RequestParam (value = "gatewayid", required = false,defaultValue ="0") String gatewayid) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			List<Feature> featureList=new ArrayList<Feature>();
			if(gatewayid.equals("0")){
				featureList = crService.listFeature();
			}
			else if (!gatewayid.equals("0")){
				int gatewayId = Integer.parseInt(gatewayid);
				featureList = crService.listFeatureByGatewayId(gatewayId);

			}


			response.put("featureList", featureList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in list feature");
			response.put("Error", e.getLocalizedMessage());
			log.error("list Feature : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Plan vs Feature
	@RequestMapping(value = "v3.0/listresettype/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listResetType(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			List<ResetType> resetList = crService.listResetType();

			response.put("resetList", resetList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in reset type list");
			response.put("Error", e.getLocalizedMessage());

			log.error("reset List : " + e.getLocalizedMessage());
		}

		return response;
	}

	@GetMapping("v3.0/getallmonitortypes")
	private JResponse getAllMonitorTypes(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse jres = new JResponse();
		String autho = header.getFirst("auth");

		UserV4 user = null;
		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);
		} catch (InvalidAuthoException e) {
			log.info("Invalid Authkey : " + autho);
			jres.put("Status", 0);
			jres.put("Msg", "Invalid Authkey");
			return jres;
		}

		List<MonitorType> monitorTypeLists = MonitorTypeService.getAllMonitorTypes();

		if (monitorTypeLists.size() > 0 && monitorTypeLists != null) {
			jres.put("status", 1);
			jres.put("msg", "success");
			jres.put("monitorTypes", monitorTypeLists);
		} else {
			jres.put("status", 0);
			jres.put("msg", "failed");
		}
		return jres;
	}

	@RequestMapping(value = "v3.0/createusertofeature/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createUsertoFeature(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody List<UsertoFeature> userFeatureList) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		boolean status = false;
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (Exception e) {
				log.error("createusertofeature:user by auth : " + e.getMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid User");
				response.put("Error", e.getLocalizedMessage());
			}

			long userid = user.getId();

			for (int i = 0; i < userFeatureList.size(); i++) {
				ResetType reset = crService.viewResetType(userFeatureList.get(i).getResettype());
				Feature feature = crService.viewFeature(userFeatureList.get(i).getFeatureid());
				UsertoFeature userFeature = null;

				userFeature = crService.getUFByUserFeature(user.getId(), feature.getId());

				if (userFeature == null)
					userFeature = new UsertoFeature();

				userFeature.setFeature_id(feature);
				userFeature.setUser_id(userid);
				userFeature.setResettype_id(reset);
				userFeature.setTxn_limit(userFeatureList.get(i).getTxn_limit());
				userFeature.setExtra_txn_limit(userFeatureList.get(i).getExtra_txn_limit());
				userFeature.setEnable(userFeatureList.get(i).isEnable());
				userFeature.setRemaining_limit(userFeatureList.get(i).getRemaining_limit());
				userFeature.setEnable(userFeatureList.get(i).isEnable());
				userFeature.setLast_reset_on(userFeatureList.get(i).getLast_reset_on());
				userFeature.setFeature_code(userFeatureList.get(i).getFeature_code());
				userFeature.setAddon_limit(userFeatureList.get(i).getAddon_limit());
				userFeature.setUnlimited_cr(userFeatureList.get(i).isUnlimited_cr());
//					userFeature.setMonitortype_id(userFeatureList.get(i).getMonitortype_id());
//					userFeature.setDevice_config(userFeatureList.get(i).getDevice_config());

				status = crService.createUsertoFeature(userFeature);
			}
			response.put("Status", status);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", 0);
			response.put("Msg", "Selected combination already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("createUsertoFeature : " + ex.getLocalizedMessage());
		} catch (Exception e) {

			response.put("Status", status);
			response.put("Msg", "creation of UsertoFeature failed");
			response.put("Error", e.getLocalizedMessage());
			log.error("create UsertoFeature : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/listusertofeature/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listUsertoFeature(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam long userid) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			if (userid > 0) {
				List<UsertoFeature> userFeatureList = crService.listUsertoFeature(userid);

				response.put("userFeatureList", userFeatureList);
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not found");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in list UsertoFeature");
			response.put("Error", e.getLocalizedMessage());
			log.error("list UsertoFeature : " + e.getLocalizedMessage());
		}

		return response;
	}

	// ========get user by id -By Savitha ================
	@RequestMapping(value = "v4.0/getuserbyidweb/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserByIdWebV4(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("userid") Long userid) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering getUserByIdWebV4 : " + userid);
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV3("id", userid.toString());

			} catch (InvalidAuthoException e) {
				log.error("Invalid UserId :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid User!");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}

			if (!(user.getAuthKey().equalsIgnoreCase(autho))) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
				return response;
			}

//			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
//			if (errResponse != null) {
//				return errResponse;
//			}

			String password = user.getPassword();
			if (user.getPassword_ver().equalsIgnoreCase("V2") || user.getPassword_ver().equalsIgnoreCase("V3")) {
				password = userServiceV4.getEValidationPassword(user.getId());
				user.setPassword(password);
			}

			byte[] token = oAuth2Service.generateOauth2Token(user.getUsername(), user.getPassword(), clientidWeb,
					clientSecretWeb);

			if (token == null) {
				log.info("Error while getting token :: username : " + user.getUsername());
				response.put("Status", -1);
				response.put("Msg", "Error while getting token");
				return response;
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("users", user);
			response.put("Token", token);

		} catch (Exception e) {
			log.error("Exception : getUserByIdWebV4 :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

//	@RequestMapping(value = "v3.0/furbitalertcfgweb/", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse getFurbitalertCfgWeb(@RequestHeader HttpHeaders header, Authentication authentication,
//			@RequestParam("alertcfgid") String alertcfgid, @RequestParam("alerttypeid") String alerttypeid,
//			@RequestParam("assetid") String assetid) {
//		JResponse response = new JResponse();
//		String autho = header.getFirst("auth");
//		try {
//			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
//			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
//			if (errResponse != null) {
//				return errResponse;
//			}
//
//			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.getCmpId());
//
//			List<JFurbitAlertCfg> alertcfgs = alertCfgService.getFurbitAlertCfg(alertcfgid, alerttypeid, assetid,
//					user.getId(), cmp_cfg.getTemperatureunit());
//
//			Set<Long> assetIds = new HashSet<Long>();
//
//			List<JGateway> userGateway = gatewayService.getGateway(null, null, null, assetid, user.getId(), null);
//			List<JGatewayConfig> gatewayConfigs = new ArrayList<JGatewayConfig>();
//			if (userGateway != null) {
//				for (JGateway jgateway : userGateway) {
//					assetIds.add(jgateway.getId());
//				}
//			}
//
//			for (Long id : assetIds) {
//
//				JGatewayConfig config = new JGatewayConfig();
//				Gateway gatewayDetails = gatewayService.getGateway(id);
//				config.setGatewayConfig(gatewayDetails.getGatewayConfig());
//				config.setOnOffStatus(gatewayDetails.isOnOffStatus());
//				config.setAssetid(id);
//
//				gatewayConfigs.add(config);
//			}
//
//			response.put("Status", 1);
//			response.put("Msg", "Success");
//			response.put("furbitalertcfg", alertcfgs);
//			response.put("gatewayConfig", gatewayConfigs);
//
//		} catch (InvalidAuthoException e) {
//			response.put("Status", 0);
//			response.put("Msg", "invalid authentication key");
//			return response;
//		}
//
//		return response;
//
//	}

	// ========get groups================
	@RequestMapping(value = "v3.0/group/{levelid}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGroupById(@RequestHeader HttpHeaders header, Authentication authentication,
			@PathVariable String levelid, @RequestParam("groupid") String groupid,
			@RequestParam("topgroupid") String topgroupid) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
//			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
//			if (errResponse != null) {
//				return errResponse;
//			}

			List<JGroups> jgroups = groupservices.getGroups(groupid, topgroupid, levelid, user.getCmpId());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("groups", jgroups);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;

	}

	// ========get user by id -By Savitha ================
	@RequestMapping(value = "v4.0/getamazonreviewlist/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAmazonReviewList(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		log.info("Entering getAmazonReviewList : ");
		String autho = header.getFirst("auth");
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<AmazonReviewList> amazonReviewList = userServiceV4.getAmazonReviewList();
			if (amazonReviewList != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("amazonreviewlist", amazonReviewList);
				log.info("Amazon Review list found!");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Exception Occurred/No reviews found!");
				log.info("No Amazon Review List found!");
			}
			log.info("Exit :: AmazonReviewList ::");

		} catch (Exception e) {
			log.error("Exception : AmazonReviewList :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured !" + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	// Get user pet profile for web - Savitha
	@RequestMapping(value = "v4.0/listuserpetprofileweb/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse listuserpetprofileweb(@RequestHeader HttpHeaders header,
			Authentication authentication, @RequestParam(value = "userid", required = false) String userId) {
		String autho = header.getFirst("auth");
		log.info("Entered into listUserPetProfileWeb : " + userId);
		JResponse response = new JResponse();
		UserV4 usr = null;
		try {
			usr = userServiceV4.verifyAuthV4("authkey", autho);
		} catch (InvalidAuthoException e) {
			log.error("Invalid Authkey :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}

		JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			response = gatewayServiceV4.listUserPetProfileWeb(usr.getId(), response);
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting listUserPetProfileWeb");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : listUserPetProfileWeb : " + e.getLocalizedMessage());
		}
		return response;
	}

	// ========send message through ota or plivo gateway================
	@RequestMapping(value = "v3.0/message/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendMessageversion(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("transporttype") int transporttype,
			@RequestParam("message") String message) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
//			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
//			if (errResponse != null) {
//				return errResponse;
//			}
			long cmpId = user.getCmpId();
			boolean isvalid;
			int status = 1;
			String res_msg = "success";
			/*
			 * if(message.contains("maxadcthr") || message.contains("minadcthr")) { message
			 * = messagingService.formMessage(gatewayid, message, cmpId); }
			 */

			String gatewayIds[] = gatewayid.split(","); /* Select Multiple Assets */

			for (int i = 0; i < gatewayIds.length; i++) {
				isvalid = true;

				if (message.contains("fotaupg=upgrade")) {
					String[] params = message.split("\\$");
					String upgVer = params[1];

					isvalid = dynamicServ.checkVaildUpgradePacket(Long.parseLong(gatewayIds[i]), upgVer);

					if (isvalid == false) {
						log.info("saveDynamicCmd: " + message + " : "
								+ "Firmware mapping data not configured. Pls configure version & model.");

						status = 0;
						res_msg = "Firmware mapping data not configured";
					}
				}
				if (isvalid) {
					if (transporttype == 0) {
						messagingService.sendMessage(gatewayIds[i], message, user.getId());
					} else if (transporttype == 1) {
						messagingService.saveMessage(gatewayIds[i], message, user.getId());
					}
				}
			}
			response.put("Status", status);
			response.put("Msg", res_msg);
			response.put("gatewayid", gatewayid);
			response.put("sms", message);
			return response;
		} catch (InvalidAuthoException iae) {
			log.error("sendMessageversion:" + iae.getLocalizedMessage());
			response.put("Status", 0);
			response.put("User", "invalid authentication key");

			return response;
		} catch (Exception e) {
			log.error("sendMessageversion:" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "invalidmeid");
			return response;
		}
//			try{
//				User user = userService.verifyAuthKey(autho);
//				long cmpId = user.giveCompany().getId();
//				
//				/*if(message.contains("maxadcthr") || message.contains("minadcthr"))
//				{
//					message = messagingService.formMessage(gatewayid, message, cmpId);			
//				}*/
//				
//				String gatewayIds [] = gatewayid.split(","); /*Select Multiple Assets*/
//				
//				for(int i=0;i<gatewayIds.length;i++)
//				{
//					System.out.println("Assetid :::: " +gatewayIds[i]);
//					if(transporttype == 0){
//						messagingService.sendMessage(gatewayIds[i], message, user.getId());
//					}
//					else if(transporttype == 1){
//						messagingService.saveMessage(gatewayIds[i], message, user.getId()); 
//					}
//				}
//				response.put("Status", 1);
//				response.put("Msg", "success");
//				response.put("gatewayid", gatewayid);
//				response.put("sms", message);
//				return response;
//			}
//			catch(InvalidAuthoException iae)
//			{
		//
//				iae.printStackTrace();
//				
//				response.put("Status", 0);
//				response.put("User", "invalid authentication key");
//				
//				return response;
//			}
//			catch(InvalidGatewayIdException ide)
//			{
//				response.put("Status", 0);
//				response.put("Msg", "invalidmeid");
//				return response;
//			} 

	}

	// Used in web
	// ========sent messages================
	@RequestMapping(value = "v3.0/message/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse sentMessages(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("subgroupid") String subgroupid,
			@RequestParam("groupid") String groupid,
			@RequestParam(value = "zip", defaultValue = "0", required = false) String zip) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
//			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
//			if (errResponse != null) {
//				return errResponse;
//			}
			long cmpId = user.getCmpId();

			List<JSentMessage> messages = messagingService.sentMessages(groupid, subgroupid, gatewayid, user.getId());

			response.put("Status", 1);
			response.put("Msg", "success");

			if (zip.equalsIgnoreCase("1"))
				response.put("sms", zipContent(messages));
			else
				response.put("sms", messages);

			return response;
		} catch (InvalidAuthoException iae) {

			log.error("sentMessages:" + iae.getLocalizedMessage());
			response.put("Status", 0);
			response.put("User", "invalid authentication key");

			return response;
		} catch (IOException e) {
			log.error("sentMessages:" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid content for compress");
			return response;
		}
	}

	// ========get companyType================
	@RequestMapping(value = "v3.0/companytype/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCompanyType(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("cmptypeid") String cmptypeid) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<CompanyType> companytypes = companytypeservice.getCompanyType(cmptypeid, user.getRole().getId());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("companytype", companytypes);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;

	}

	@RequestMapping(value = "v3.0/throttlingsettings/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getThrotSettingById(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("throttleid") String throttleid) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<ThrottlingSettings> throtsettings = throttlingdao.getThrotSettings(throttleid);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("throtsettings", throtsettings);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid throttleid");
			return response;
		}
		return response;
	}

	// ==========signup========
	@RequestMapping(value = "v3.0/signup", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse saveUser(@ModelAttribute @Valid SignUp signUp, BindingResult result,
			@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		Company company = null;
		try {
			User checkUser = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(checkUser.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (signUp.getPassword() == null || signUp.getPassword().trim().isEmpty()) {
				String pwd = signUp.getPhoneno();
				pwd = pwd.replaceAll("[^\\d.]", ""); // remove other than numbers
				if (pwd.length() > 10) {
					pwd = pwd.substring(pwd.length() - 10);
				}

				signUp.setPassword(pwd);
			}
			long timeMilli = Calendar.getInstance().getTimeInMillis();
			String cmpName = signUp.getCompanyname() + "-" + timeMilli;
			signUp.setCompanyname(cmpName);
			try {
				ThrottlingSettings throtsettings = throttlingService.getThrotSettings(signUp.getThrotsettingsid())
						.get(0);

				CompanyType cmpType = companyTypeServ.getCompanyType(signUp.getCmptype_id(), 1).get(0);
				company = companyService.createCompany(signUp, throtsettings, cmpType);
			} catch (Exception e) {
				log.error("Error in creating the company . Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Not able to process the request, Please try later");
				return response;
			}

			CompanyConfig cfg = new CompanyConfig(company);
			boolean companyConfigCreationStatus = companyService.saveCompanyConfg(cfg);
			log.info("Company Config Creation Status : " + companyConfigCreationStatus);
			if (!companyConfigCreationStatus && company.getId() != 0) {
				companyService.deleteCompany(company.getId());
			}

			Groups group = new Groups();
			group.setName("Default");
			boolean groupCreationStatus = groupservices.saveORupdateGroups(group, company.getId());
			if (!groupCreationStatus & company != null) {
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}

			boolean res = userService.signUp(signUp, company);

			User user = userService.getUserByUNameOrEmail(signUp.getEmail());
			// updateChargebeeUser(user);
			if (user.getChargebeeid().trim().equalsIgnoreCase("NA") || user.getChargebeeid() == null)
				userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
						user.getMobileno(), user.getUsername(), 0, "NA");

//			boolean stat = rvcentricServ.saveUserBadgeTxn(user.getId(), "chum", 0, false, "NA");
//			log.info("in signup:" + user.getId() + " Badge created:" + stat);

			response.put("Status", 1);
			response.put("Msg", "Success");
		} catch (ConstraintViolationException ce) {
			log.error("signup: ConstraintViolationException:\n" + ce.getStackTrace());
			response.put("Status", 0);
			response.put("Msg", "Username/Company name already exists");
			if (company != null && company.getId() != 0) {
				groupservices.deleteGroups(company.getId());
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
		} catch (Exception e) {
			log.error("signup: " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in Signup");
			if (company != null && company.getId() != 0) {
				groupservices.deleteGroups(company.getId());
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
		}
		return response;
	}

	// ========get company List================
	@RequestMapping(value = "v3.0/companylist/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCompanyList(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		User user;
		try {
			user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<Company> cmplist = companyService.getCompanyList(user.getRole().getId(), user.giveCompany().getId());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("companylist", cmplist);

		} catch (InvalidAuthoException e) {

			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;
	}

	// ==========update company========
	@RequestMapping(value = "v3.0/company/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse saveCompany(@RequestHeader HttpHeaders header, Authentication authentication,
			@ModelAttribute Company cmp, BindingResult result) {
		System.out.println("save user");
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			companyService.updateCompany(cmp);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("Company", cmp);
		} catch (Exception e) {
			log.error("saveCompany:::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in Company");

		}

		return response;

	}

	// ========get user by userId or company id ================
	@RequestMapping(value = "v3.0/userV2/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserByIdV2(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("userid") String userid, @RequestParam("cmpid") String cmpid) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			long cmp_id = user.giveCompany().getId();
			if (!cmpid.isEmpty())
				cmp_id = Long.valueOf(cmpid);
			List<User> users = userService.getUser(userid, cmp_id);
			List<User> users1 = new ArrayList<>();
			for (User user1 : users) {
				String mobile = "";
				if (user1 != null) {
					if (userid.trim().isEmpty())
						userid = String.valueOf(user1.getId());

					Map<Integer, String> getnumber = _helper.getPhoneAndCountryCode(user1.getMobileno());
					if (user1.getCountry().equalsIgnoreCase("NA")) {
						if (getnumber.get(0).equalsIgnoreCase("+91") || getnumber.get(0).equalsIgnoreCase("91")) {
							user1.setCountry("IN");
						} else if (getnumber.get(0).equalsIgnoreCase("+44")
								|| getnumber.get(0).equalsIgnoreCase("44")) {
							user1.setCountry("GB");
						} else {
							user1.setCountry("US");
						}
					}
					mobile = getnumber.get(1);
					if (mobile == null) {
						user1.setMobileno("NA");
					} else {
						user1.setMobileno(getnumber.get(1));
					}
					users1.add(user1);
				}
			}

			ArrayList<RVAnswer> ansList = new ArrayList<RVAnswer>();// rvcentricServ.listRVAnswer();
			ArrayList<JRVAnswer> rv_cat_list = new ArrayList<JRVAnswer>();
			ArrayList<JRVAnswer> rv_type_list = new ArrayList<JRVAnswer>();
			ArrayList<JRVAnswer> pet_avail_list = new ArrayList<JRVAnswer>();
			ArrayList<JRVAnswer> no_travels_list = new ArrayList<JRVAnswer>();

			if (ansList != null) {

				for (int i = 0; i < ansList.size(); i++) {
					long ques_id = ansList.get(i).getQues_id();
					long ans_id = ansList.get(i).getId();
					String ans_value = ansList.get(i).getAns_value();
					JRVAnswer ansObj = new JRVAnswer(ans_id, ans_value);

					if (ques_id == 1) {
						rv_cat_list.add(ansObj);
					} else if (ques_id == 2) {
						rv_type_list.add(ansObj);
					} else if (ques_id == 3) {
						pet_avail_list.add(ansObj);
					} else {
						no_travels_list.add(ansObj);
					}
				}
			}
			boolean is_travelprofile = false;

//			UserRvDetails rvObj = userServiceV4.getUserRvDetails(Long.parseLong(userid));
//			JUserRVDetail travelprofile = new JUserRVDetail();
//
//			if (rvObj != null) {
//				travelprofile = new JUserRVDetail(rvObj.getId(), rvObj.getOwn_rv(), rvObj.getRvtype(),
//						rvObj.getWithPet(), rvObj.getHow_often(), rvObj.getOthers_type());
//				is_travelprofile = true;
//			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("users", users1);
			response.put("travelprofile", null);
			response.put("is_travelprofile", is_travelprofile);

			response.put("rv_cat_list", rv_cat_list);
			response.put("rv_type_list", rv_type_list);
			response.put("pet_avail_list", pet_avail_list);
			response.put("no_travels_list", no_travels_list);

		} catch (InvalidAuthoException e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid userid");
			return response;
		}
		return response;
	}

	@RequestMapping(value = "v3.0/companyconfigv2/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse saveCompanyConfigV2(@RequestHeader HttpHeaders header, Authentication authentication,
			@ModelAttribute CompanyConfig cmpcfg, BindingResult result, @RequestParam("cmpid") String cmpid) {
		System.out.println("save user");
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			cmpcfg.setCompany(user.giveCompany());
			if (!cmpid.isEmpty())
				cmpcfg.setCompany(companyService.getCompany(Long.valueOf(cmpid)));

			boolean prevRealTimeMonitor = cmpcfg.isRealtimemonitor();

			if (cmpcfg.getId() > 0) {
				CompanyConfig prevcmp_cfg = companyService.getCompanyConfig(cmpcfg.giveCompany().getId());
				prevRealTimeMonitor = prevcmp_cfg.isRealtimemonitor();
			}

			companyService.updateCompanyCfg(cmpcfg, prevRealTimeMonitor);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("CompanyConfig", cmpcfg);

		} catch (InvalidAuthoException e) {

			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");

			return response;
		} catch (Exception e) {
			log.error("saveCompanyConfig:::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in Companyconfig");
		}
		return response;
	}

	// Get company List By Filter - Savitha
	@RequestMapping(value = "v4.0/companylistbyfilter/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCompanyListByFilter(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam(value = "skey", required = false) String sKey,
			@RequestParam(value = "svalue", required = false) String sValue,
			@RequestParam(value = "ftype", defaultValue = "equals", required = false) String fType,
			@RequestParam(value = "offset") long offset, @RequestParam(value = "limit") long limit,
			@RequestParam(value = "otype", defaultValue = "desc", required = false) String otype,
			@RequestParam(value = "okey", required = false) String oKey,
			@RequestParam(value = "cmpid", required = false) String cmpid) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		try {
			if (cmpid != null && cmpid != "") {
				UserV4 user = null;
				long cmp_id;
				try {
					user = userServiceV4.verifyAuthV4("authkey", autho);
					JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}
					cmp_id = user.getCmpId();
				} catch (InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "Invalid Authkey!!!");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting company for company id : " + autho);
					return response;
				}
				if (Long.parseLong(cmpid) != cmp_id) {
					response.put("Status", 0);
					response.put("Msg", "Invalid company id!!!");
					return response;
				}
			}

			response = companyServicev4.getCompanyListByFilter(sKey, sValue, fType, otype, offset, limit, oKey, cmpid,
					response);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting company list by fFilter");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : CompanyListByFilter : " + e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	// Get companyconfig web - Savitha
	@RequestMapping(value = "v4.0/companyconfigweb/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getCompanyConfigListWeb(@RequestHeader HttpHeaders header,
			Authentication authentication, @RequestParam("cmpid") String cmpid) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for auth : " + autho);
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			long cmp_id = user.getCmpId();
			if (!cmpid.isEmpty())
				cmp_id = Long.valueOf(cmpid);

			List<CompanyConfig> cmp_cfglist = companyServicev4.getCompanyConfigListWeb(cmp_id);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("companyconfig", cmp_cfglist);

		} catch (Exception e) {
			log.error("Exception : Company config list : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occured!!!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	// allGatewaysListByFilter for web - Savitha
	@RequestMapping(value = "v4.0/allgatewayslistbyfilter/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse allGatewaysListByFilter(@RequestHeader HttpHeaders header,
			Authentication authentication, @RequestParam(value = "skey", required = false) String sKey,
			@RequestParam(value = "svalue", required = false) String sValue,
			@RequestParam(value = "ftype", defaultValue = "equals", required = false) String fType,
			@RequestParam(value = "offset") long offset, @RequestParam(value = "limit") long limit,
			@RequestParam(value = "otype", defaultValue = "desc", required = false) String otype,
			@RequestParam(value = "okey", required = false) String oKey) {
		log.info("Entered into allGatewaysListByFilter : " + sKey + " - " + sValue);
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			response = gatewayServiceV4.getGatewayListByFilter(sKey, sValue, fType, otype, offset, limit, oKey,
					response);

			if ((Integer) response.getResponse().get("Status") > 0) {
				List<GatewayV4Web> gatewayV4WebList = (List<GatewayV4Web>) response.getResponse().get("gatewaylist");
				gatewayV4WebList = gatewayServiceV4.setReplacedDevice(gatewayV4WebList);
				response.put("gatewaylist", gatewayV4WebList);
			}
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting allGatewaysListByFilter");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : allGatewaysListByFilter : " + e.getLocalizedMessage());
		}
		return response;
	}

	// getGateways by gateway id for web - Savitha
	@RequestMapping(value = "v4.0/getgatewaybyidweb/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getGatewayByIdWeb(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam(value = "gatewayid", required = true) String gatewayId) {
		log.info("Entered into getGatewaysWithGatewayId : " + gatewayId);
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			response = gatewayServiceV4.getGatewayByIdWeb(gatewayId, response);
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting getGatewayByIdWeb");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : getGatewayByIdWeb : " + e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v3.0/listplan/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listPlan(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			List<SubscriptionPlan> planList = crService.listSubscriptionPlan();

			response.put("planList", planList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in list");
			response.put("Error", e.getLocalizedMessage());
			log.error("listPlan : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/listsubperiod/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listSubPeriod(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			List<SubscriptionPeriod> periodList = crService.listSubPeriod();

			response.put("periodList", periodList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in subs period list");
			response.put("Error", e.getLocalizedMessage());

			log.error("periodList : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/listplantoperiod/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listPlanToPeriod(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			List<PlanToPeriod> ptpList = crService.listPlanToPeriod();

			response.put("ptpList", ptpList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured. Unable list plantoperiod");
			response.put("Error", e.getLocalizedMessage());
			log.error("listPlanToPeriod : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/listfeaturetype/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listFeatureType(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			List<FeatureType> featuretypelist = crService.listFeatureType();

			response.put("featuretypelist", featuretypelist);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in feature type list");
			response.put("Error", e.getLocalizedMessage());

			log.error("periodList : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/createplan/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createPlan(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody SubscriptionPlan plan) {
		JResponse response = new JResponse();
		boolean status = false;
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			plan.setPlan_name(plan.getPlan_name().trim());
			status = crService.createSubscriptionPlan(plan);

			response.put("Status", status);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", status);
			response.put("Msg", "SubscriptionPlan already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("createPlan : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "SubscriptionPlan creation failed");
			log.error("createPlan : " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/deleteplan/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deletePlan(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam long id) {
		JResponse response = new JResponse();
		boolean status = false;
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			status = crService.deleteSubscriptionPlan(id);

			response.put("Status", status);
			if (status == true)
				response.put("Msg", "Success");
			else
				response.put("Msg", "Selected data not exists");

		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "Error in delete. Invalid plan id");
			response.put("Error", e.getLocalizedMessage());
			log.error("deletePlan : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/listplantomonitortype/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listPlanToMonitorType(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			List<PlanToMonitorType> planMTypeList = crService.listPlanToMonitorType();

			response.put("planMTypeList", planMTypeList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in list");
			response.put("Error", e.getLocalizedMessage());
			log.error("listPlanToMonitorType : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/createplantomonitor/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createPlanToMonitor(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody PlanToMonitorType plan, @RequestParam long splanid) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		boolean status = false;
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			SubscriptionPlan sp = crService.getSubsPlanById(splanid);
			plan.setPlan_id(sp);
			status = crService.createPlanToMonitorType(plan);

			response.put("Status", status);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", status);
			response.put("Msg", "Selected combination already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("createPlanToMonitor : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "creation of PlanToMonitor failed");
			response.put("Error", e.getLocalizedMessage());
			log.error("createPlanToMonitor : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/deleteplantomonitortype/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deletePlanToMonitorType(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam long id) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		boolean status = false;
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			status = crService.deletePlanToMonitorType(id);

			response.put("Status", status);
			if (status == true)
				response.put("Msg", "Success");
			else
				response.put("Msg", "Selected data not exists");

		} catch (Exception e) {
			response.put("Status", status);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Invalid Plan to monitor type id");
			log.error("delete plantomonitortype : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/createplantoperiod/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createPlanToPeriod(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody PlanToPeriod plan, @RequestParam long planid, @RequestParam long periodid,
			@RequestParam(value = "updatePrice", defaultValue = "false", required = false) boolean updatePrice) {
		JResponse response = new JResponse();
		boolean status = false;
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			SubscriptionPlan sp = crService.getSubsPlanById(planid);
			SubscriptionPeriod period = crService.getSubsPeriodById(periodid);
			plan.setPlan_id(sp);
			plan.setSub_period_id(period);

			status = crService.createPlanToPeriod(plan);

			if (updatePrice && status) {
				Environment.configure(chargebeeSiteName, chargebeeSiteKey);
				try {
					int plan_price = 0;
					String priceStr = plan.getContent_2().replace("$", "").split("/")[0].trim();
					float price = Float.parseFloat(priceStr);
					plan_price = Math.round(price * 100);
							
					Result result = Plan.update(plan.getChargebee_planid()).price(plan_price).request();
					Plan pl = result.plan();
				} catch (Exception e) {
					log.error("Exception in Updating Plan Price in CB : " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "Failed to update plan price in chargebee!");
					response.put("Error", e.getLocalizedMessage());
				}
			}

			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", status);
			response.put("Msg", "Selected combination already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("createPlanToPeriod : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "creation of PlanToPeriod failed");
			response.put("Error", e.getLocalizedMessage());
			log.error("create PlanToPeriod : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/deleteplantoperiod/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deletePlanToPeriod(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam long id) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		boolean status = false;
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			status = crService.deletePlanToPeriod(id);

			response.put("Status", status);
			if (status == true)
				response.put("Msg", "Success");
			else
				response.put("Msg", "Selected data not exists");

		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "Error occured in delete .Invalid Plantoperiod id");
			response.put("Error", e.getLocalizedMessage());
			log.error("deleteplantoperiod : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/listplantoupgrade/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listPlanToUpgrade(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			List<PlanToUpgrade> ptuList = crService.listPlanToUpgrade();
			response.put("pupList", ptuList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured. Unable list PlanToUpgrade");
			response.put("Error", e.getLocalizedMessage());

			log.error("list PlanToPeriod : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/createplantoupgrade/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createPlanToUpgrade(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody PlanToUpgrade plan, @RequestParam long ptpid, @RequestParam String upgradeid) {
		JResponse response = new JResponse();
		boolean status = false;
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			String[] upgradeidList = upgradeid.split(",");
			PlanToPeriod ptp = crService.viewPlanToPeriod(ptpid);

			for (int i = 0; i < upgradeidList.length; i++) {
				PlanToPeriod upgradeplan = crService.viewPlanToPeriod(Long.parseLong(upgradeidList[i]));

				PlanToUpgrade upgradeObj = new PlanToUpgrade();

				upgradeObj.setPlan_to_period_id(ptp);
				upgradeObj.setUpgradeplan_id(upgradeplan);
				upgradeObj.setPrice(plan.getPrice());
				upgradeObj.setDescription(plan.getDescription());

				status = crService.createPlanToUpgrade(upgradeObj);
			}

			response.put("Status", status);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", status);
			response.put("Msg", "Selected combination already exist");
			response.put("Error", ex.getLocalizedMessage());

			log.error("create PlanToUpgrade : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "creation of PlanToUpgrde failed");
			response.put("Error", e.getLocalizedMessage());

			log.error("create PlanToUpgrade : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/createfeature/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createFeature(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody Feature featureObj) {
		JResponse response = new JResponse();
		boolean status = false;
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			String featurename = featureObj.getFeature_name().trim();
			featurename = featurename.replaceAll("\\s", "");

			FeatureType type = crService.viewFeatureType(featureObj.getFeaturetype());
			featureObj.setType_id(type);
			featureObj.setFeature_name(featurename);

			status = crService.createFeature(featureObj);

			response.put("Status", status);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", status);
			response.put("Msg", "Feature already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("create Feature : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "invalid plan id");
			response.put("Error", e.getLocalizedMessage());
			log.error("create feature : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/deleteplantoupgrade/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deletePlanToUpgrade(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam long id) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		boolean status = false;
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			status = crService.deletePlanToUpgrade(id);

			response.put("Status", status);
			if (status == true)
				response.put("Msg", "Success");
			else
				response.put("Msg", "Selected data not exists");

		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "Error occured in delete.Invalid Plan to Upgrade id");
			response.put("Error", e.getLocalizedMessage());
			log.error("delete plantoupgrade : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/deletefeature/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deleteFeature(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam long id) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		boolean status = false;
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			status = crService.deleteFeature(id);

			response.put("Status", status);
			if (status == true)
				response.put("Msg", "Success");
			else
				response.put("Msg", "Selected data not exists");

		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "invalid featureid");
			log.error("delete Feature : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/listplantoperiodbyplanid/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listPlanToPeriodByPlanId(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam(value = "plan_id", required = true) long plan_id) {
		log.info("Entered get plan to period by plan id.");
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			List<PlanToPeriod> ptpList = crService.listPlanToPeriodByPlanId(plan_id);

			if (ptpList != null) {
				response.put("ptpList", ptpList);
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Unable list plan to period!!!");
				log.error("Error in getting list plan to period by plan id");
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured. Unable list plantoperiod");
			response.put("Error", e.getLocalizedMessage());
			log.error("listPlanToPeriod : " + e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v3.0/createplantofeature/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createPlantoFeature(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody List<PlanToFeature> planFeatureList) {
		JResponse response = new JResponse();
		boolean status = false;
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			if (!planFeatureList.isEmpty()) {
				SubscriptionPlan plan = crService.getSubsPlanById(planFeatureList.get(0).getPlanid());

				for (int i = 0; i < planFeatureList.size(); i++) {
					ResetType reset = crService.viewResetType(planFeatureList.get(i).getResettype());
					Feature feature = crService.viewFeature(planFeatureList.get(i).getFeatureid());

					PlanToFeature planFeature = null;
					planFeature = crService.getPFByPlanFeature(plan.getId(), feature.getId());

					if (planFeature == null)
						planFeature = new PlanToFeature();

					planFeature.setFeature_id(feature);
					planFeature.setPlan_id(plan);
					planFeature.setResettype_id(reset);
					planFeature.setTxn_limit(planFeatureList.get(i).getTxn_limit());
					planFeature.setExtra_txn_limit(planFeatureList.get(i).getExtra_txn_limit());
					planFeature.setEnable(planFeatureList.get(i).isEnable());
					planFeature.setUnlimited_cr(planFeatureList.get(i).isUnlimited_cr());
//						planFeature.setPlan_period_id(planFeatureList.get(i).getPlan_period_id());

					status = crService.createPlantoFeature(planFeature);
				}
			}
			response.put("Status", status);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", status);
			response.put("Msg", "Selected combination already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("createPlantoFeature : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "creation of PlantoFeature failed");
			response.put("Error", e.getLocalizedMessage());
			log.error("create PlantoFeature : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/listplantofeature/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listPlantoFeature(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam long planid) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			List<PlanToFeature> ptfList = crService.listPlantoFeature(planid);

			response.put("ptfList", ptfList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in list PlantoFeature");
			response.put("Error", e.getLocalizedMessage());
			log.error("list PlantoFeature : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/deleteplantofeature/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deletePlantoFeature(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam long id) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		boolean status = false;
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			status = crService.deletePlantoFeature(id);

			response.put("Status", status);
			if (status == true)
				response.put("Msg", "Success");
			else
				response.put("Msg", "Selected data not exists");

		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "Error occured in delete .Invalid PlantoFeature id");
			response.put("Error", e.getLocalizedMessage());
			log.error("delete PlantoFeature : " + e.getLocalizedMessage());
		}

		return response;
	}

//	@RequestMapping(value = "v4.0/savervdetails/", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse saveRVDetails(@RequestHeader HttpHeaders header, Authentication authentication,
//			@RequestParam("os") String os, @RequestBody RVCentricDetails rvBlogsTips) {
//		String auth = header.getFirst("auth");
//		log.info("Entered saveRVDetails: " + auth);
//		JResponse response = new JResponse();
//		boolean status = false;
//		try {
//			UserV4 user = null;
//			try {
//				user = userServiceV4.verifyAuthV4("authkey", auth);
//			} catch (InvalidAuthoException e) {
//				log.info("Invalid Authkey : " + auth);
//				response.put("Status", 0);
//				response.put("Msg", "Invalid Authkey");
//				return response;
//			}
//
//			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
//			if (errResponse != null) {
//				return errResponse;
//			}
//
//			status = rvcentricServ.saveOrUpdateRVCentricDetails(rvBlogsTips);
//
//			if (status == true) {
//				response.put("Msg", "Success");
//				response.put("Status", 1);
//			} else {
//				response.put("Msg", "Please try after some time!");
//				response.put("Status", 0);
//			}
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Error occured");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("saveRVDetails : " + e.getLocalizedMessage());
//		}
//
//		return response;
//	}

//	@RequestMapping(value = "v4.0/listallrvdetails/", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse listAllRVDetails(@RequestHeader HttpHeaders header, Authentication authentication,
//			@RequestParam("os") String os) {
//		String auth = header.getFirst("auth");
//		log.error("Entered listAllRVDetails :" + auth);
//		JResponse response = new JResponse();
//		try {
//			UserV4 user = null;
//			try {
//				user = userServiceV4.verifyAuthV4("authkey", auth);
//			} catch (InvalidAuthoException e) {
//				log.info("Invalid Authkey : " + auth);
//				response.put("Status", 0);
//				response.put("Msg", "Invalid Authkey");
//				return response;
//			}
//
//			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
//			if (errResponse != null) {
//				return errResponse;
//			}
//
//			List<RVCentricDetails> rvList = rvcentricServ.listAllRVCentricDetailsList();
//
//			if (rvList != null) {
//				response.put("Status", 1);
//				response.put("Msg", "success");
//				response.put("rvList", rvList);
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "Please try after some time!");
//			}
//
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Error occured");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("listAllRVDetails : " + e.getLocalizedMessage());
//		}
//
//		return response;
//	}

	// List feedback form web - Savitha.
	@RequestMapping(value = "v4.0/listfeedbackweb/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listFeedbackWeb(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<FeedbackForm> feedbackList = userServiceV4.listFeedbackWeb();

			response.put("feedbackList", feedbackList);
			response.put("Status", 1);
			response.put("Msg", "Success");
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in getting feedback list");
			response.put("Error", e.getLocalizedMessage());
			log.error("listPlan : " + e.getLocalizedMessage());
		}
		return response;
	}

	// Create/Update feedback form web - Savitha.
	@RequestMapping(value = "v4.0/createfeedbackformweb/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createFeedbackFormWeb(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody FeedbackForm feedback) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		boolean status = false;
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			status = userServiceV4.createFeedbackFormWeb(feedback);
			response.put("Status", status);
			response.put("Msg", "Success");
		} catch (ConstraintViolationException ex) {
			response.put("Status", status);
			response.put("Msg", "Feedback form already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("createFeedbackFormWeb : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "Feedback form creation failed");
			log.error("createFeedbackFormWeb : " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	// ========get assetmodels================
	@RequestMapping(value = "v3.0/assetmodel/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetModel(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<AssetModel> assetmodel = fetchDropdownService.getAssetModel();
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("assetmodel", assetmodel);
//				System.out.println("AsyncStarted");
//				asyncs.runAsync();

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

//			System.out.println("===========================================================");
		return response;

	}

	/* listfotamodel API */
	@RequestMapping(value = "v4.0/listfotamodel/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listFotaModel(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering listFotaModel : ");

		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<FotaModel> fotaModelList = fotaService.getFotaModelList();

			for (FotaModel model : fotaModelList) {
				long modelid = model.getAssetmodel().getId();
				model.setModel_id(modelid);
			}

			if (fotaModelList != null && !fotaModelList.isEmpty()) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("fotamodellist", fotaModelList);
				log.info("Fota Model list found!");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Exception Occurred/No fotamodel found!");
				log.info("No fotamodel found!");
			}
			log.info("Exit :: fotaModelList ::");

		} catch (Exception e) {
			log.error("Exception : listfotamodel :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;

	}

	/* savefotamodel API */
	@RequestMapping(value = "v4.0/savefotamodel/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse saveFotaModel(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody FotaModel ftModel) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		log.info("Entering saveFotaModel : ");

		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			FotaModel fota = new FotaModel();

			long id = ftModel.getId();
			fota.setId(id);

			String fota_ver = ftModel.getFota_ver();
			long model_id = ftModel.getModel_id();

			if (fota_ver == "NA" || model_id == 0) {
				response.put("Status", 0);
				response.put("Msg", "Invalid data");
				return response;
			}
			fota.setFota_ver(fota_ver);
			fota.setModel_id(model_id);

			AssetModel assetModel = fotaService.getAssetModelById(model_id);

			if (assetModel == null) {
				response.put("Status", 0);
				response.put("Msg", "modelid does not exist");
				return response;
			}

			fota.setAssetmodel(assetModel);

			boolean Status = fotaService.createOrUpdateModel(fota);

			if (Status) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Fotaversion already exist!!!");
			}

		} catch (DataIntegrityViolationException e) {
			log.error("createOrUpdateModel : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Fotaversion already exist!!!");
			response.put("Error", e.getLocalizedMessage());
		} catch (ConstraintViolationException e) {
			log.error("createOrUpdateModel : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Fotaversion already exist!!!");
			response.put("Error", e.getLocalizedMessage());
		} catch (Exception e) {
			log.error("Exception : saveFotaModel : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExpected Error in Fota Model");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	/* listfotaversion API */
	@RequestMapping(value = "v4.0/listfotaversion/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listFotaVersion(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering listFotaVersion : ");

		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<FotaVersion> fotaVersionList = fotaService.getFotaVersionList();

			for (FotaVersion model : fotaVersionList) {
				long modelid = model.getAssetmodel().getId();
				model.setCurr_modelid(modelid);
			}

			if (fotaVersionList != null && !fotaVersionList.isEmpty()) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("fotaversionlist", fotaVersionList);
				log.info("Fota Version list found!");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Exception Occurred/No fotaversion found!");
				log.info("No fotaversion found!");
			}
			log.info("Exit :: fotaVersionList ::");

		} catch (Exception e) {
			log.error("Exception : listfotaversion :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;

	}

	/* savefotaversion API */
	@RequestMapping(value = "v4.0/savefotaversion/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse saveFotaVersion(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody FotaVersion ftVersion) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering saveFotaVersion : ");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			FotaVersion version = new FotaVersion();

			long id = ftVersion.getId();
			String fota_ver = ftVersion.getFota_version();
			String file_path = ftVersion.getFilename();
			long file_size = ftVersion.getFilesize();
			String curr_ver = ftVersion.getCurr_version();
			String create_date = ftVersion.getCreatedon();
			if (create_date == "NA" || create_date == "") {
				create_date = IrisservicesUtil.getCurrentTimeUTC();
			}
			long currModelId = ftVersion.getCurr_modelid();
			int enable = ftVersion.getEnable();

			if (fota_ver == "NA" || file_path == "NA" || file_size == 0 || curr_ver == "NA" || create_date == "NA"
					|| currModelId == 0 || enable == -1) {
				response.put("Status", 0);
				response.put("Msg", "Invalid data");
				return response;
			}

			version.setId(id);
			version.setFota_version(fota_ver);
			version.setFilename(file_path);
			version.setFilesize(file_size);
			version.setCurr_version(curr_ver);
			version.setCreatedon(create_date);
			version.setCurr_modelid(currModelId);
			version.setEnable(enable);
			version.setShowBtUpdate(ftVersion.isShowBtUpdate());

			AssetModel assetModel = fotaService.getAssetModelById(currModelId);

			if (assetModel == null) {
				response.put("Status", 0);
				response.put("Msg", "curr_modelid does not exist");
				return response;
			}

			version.setAssetmodel(assetModel);

			boolean Status = fotaService.createOrUpdateVersion(version);

			if (Status) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "New and Old Fotaversion already exist!!!");
			}

		} catch (DataIntegrityViolationException e) {
			log.error("createOrUpdateModel : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "New and Old Fotaversion already exist!!!");
			response.put("Error", e.getLocalizedMessage());
		} catch (ConstraintViolationException e) {
			log.error("createOrUpdateModel : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "New and Old Fotaversion already exist!!!");
			response.put("Error", e.getLocalizedMessage());
		} catch (Exception e) {
			log.error("Exception : saveFotaVersion : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExpected Error in Fota Model");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	// Get fota details - Savitha
	@RequestMapping(value = "v4.0/getfotadetails/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFotaDetails(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam(value = "skey", required = false) String sKey,
			@RequestParam(value = "svalue", required = false) String sValue,
			@RequestParam(value = "ftype", defaultValue = "equals", required = false) String fType,
			@RequestParam(value = "offset") long offset, @RequestParam(value = "limit") long limit,
			@RequestParam(value = "otype", defaultValue = "desc", required = false) String otype,
			@RequestParam(value = "okey", required = false) String oKey,
			@RequestParam(value = "os", required = false) String os,
			@RequestParam(value = "model_id", required = true) long model_id) {
		JResponse response = new JResponse();
		log.info("Entering getfotadetails : ");
		String autho = header.getFirst("auth");

		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			response = fotaService.getFotaDetails(sKey, sValue, fType, otype, offset, limit, oKey, model_id, response,
					null);
		} catch (Exception e) {
			log.error("Exception : getfotadetails : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	// By karthik
	@RequestMapping(value = "v4.0/getfotadetailsV2/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAllMeids(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody(required = false) FotaUpgrade meid,
			@RequestParam(value = "skey", required = false) String sKey,
			@RequestParam(value = "svalue", required = false) String sValue,
			@RequestParam(value = "ftype", defaultValue = "equals", required = false) String fType,
			@RequestParam(value = "offset") long offset, @RequestParam(value = "limit") long limit,
			@RequestParam(value = "otype", defaultValue = "desc", required = false) String otype,
			@RequestParam(value = "okey", required = false) String oKey,
			@RequestParam(value = "os", required = false) String os,
			@RequestParam(value = "model_id", required = true) long model_id) {
		JResponse response = new JResponse();
		log.info("Entering getfotadetails : ");
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			response = fotaService.getFotaDetails(sKey, sValue, fType, otype, offset, limit, oKey, model_id, response,
					meid);
		} catch (Exception e) {
			log.error("Exception : getfotadetails : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	@RequestMapping(value = "v3.0/fotamessage/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendMessageFotaVersion(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("transporttype") int transporttype,
			@RequestParam("message") String message) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			boolean isvalid;
			int status = 1;
			String res_msg = "success";
			String gatewayIds[] = gatewayid.split(","); /* Select Multiple Assets */

			for (int i = 0; i < gatewayIds.length; i++) {
				isvalid = true;

				if (message.contains("fotaupg=upgrade")) {
					String[] params = message.split("\\$");
					String upgVer = params[1];

					isvalid = dynamicServ.checkVaildUpgradePacket(Long.parseLong(gatewayIds[i]), upgVer);

					if (isvalid == false) {
						log.info("saveDynamicCmd: " + message + " : "
								+ "Firmware mapping data not configured. Pls configure version & model.");

						status = 0;
						res_msg = "Firmware mapping data not configured";
					}
				}
				if (isvalid) {
					if (transporttype == 1) {
						long gatewayId = Long.parseLong(gatewayIds[i]);
						messagingService.saveFotaMessage(gatewayId, message);
					}
				}
			}
			response.put("Status", status);
			response.put("Msg", res_msg);
			response.put("gatewayid", gatewayid);
			response.put("sms", message);
			return response;
		} catch (InvalidAuthoException e) {
			log.error("sendMessageFotaVersion:" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("User", "invalid authentication key");

			return response;
		} catch (Exception e) {
			log.error("sendMessageFotaVersion:" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "invalidmeid");
			return response;
		}
	}

	// ========Srei Asset Overview================
	@RequestMapping(value = "v3.0/sreigatewayoverview/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getSreiGatewayOverview(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("groupid") String groupid, @RequestParam("subgroupid") String subgroupid,
			@RequestParam("levelid") String levelid) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			JSreiGatewayOverview sgoverview = gatewayService.getSreiGatewayOverview(groupid, subgroupid, user.getId(),
					levelid);
			JSreiGatewayOverview sRgoverview = gatewayService.getSreiRGatewayOverview(groupid, subgroupid, user.getId(),
					levelid);
			JSreiGatewayOverview sNRgoverview = gatewayService.getSreiNRGatewayOverview(groupid, subgroupid,
					user.getId(), levelid);
			JSreiGatewayOverview sInbuiltDevice = gatewayService.getSreiInbuiltDevicesOverview(groupid, subgroupid,
					user.getId(), levelid);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("sreigatewayoverview", sgoverview);
			response.put("sreiRgatewayoverview", sRgoverview);
			response.put("sreiNRgatewayoverview", sNRgoverview);
			response.put("sreiInBuiltDevice", sInbuiltDevice);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;
	}

	// ========get lastgatewayreport for srei ================
	@RequestMapping(value = "v3.0/sreigatewaysummary/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getSreiAssetSummary(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("groupid") String groupid, @RequestParam("subgroupid") String subgroupid,
			@RequestParam("assetgroupid") String assetgroupid, @RequestParam("gatewayid") String gatewayid,
			@RequestParam("deliquencystatus") String deliquencystatus, @RequestParam("levelid") String levelid,
			@RequestParam("offset") String offset, @RequestParam("limit") String limit,
			@RequestParam(value = "zip", defaultValue = "1", required = false) String zip) {
		System.out.println("gateway summary");
		log.info("called gateway summary services" + "groupid: " + groupid + ", subgroupid: " + subgroupid
				+ ", assetgroupid: " + assetgroupid + " , gatewayid: " + gatewayid);
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {

			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<JSreiAssetLastReport> reportsummmary = reportService.getSreiLastgatewayreport(groupid, subgroupid,
					assetgroupid, gatewayid, user.getId(), deliquencystatus, levelid, offset, limit);
			System.out.println("received gateway summary , report length: " + reportsummmary.size());
			log.info("received gateway summary , report length: " + reportsummmary.size());

			JAssetDescription assetDescrip = reportService.getAssetDescription(user.giveCompany().getId());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("assetdescription", assetDescrip);

			/* To reverse the collection of latest reported gateway */
			Collections.sort(reportsummmary, new Comparator<JSreiAssetLastReport>() {
				@Override
				public int compare(JSreiAssetLastReport a, JSreiAssetLastReport b) {
					DateFormat format = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
					try {
						return format.parse(b.getDatetime()).compareTo(format.parse(a.getDatetime()));
					} catch (ParseException e) {
						// e.printStackTrace();
					}
					return 0;
				}
			});

			if (zip.equalsIgnoreCase("1"))
				response.put("lastgatewayreport", zipContent(reportsummmary));
			else
				response.put("lastgatewayreport", reportsummmary);
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (IOException e) {
			log.error(e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid content for compress");
			return response;
		}
		return response;
	}

	// ========check version================
	@RequestMapping(value = "v3.0/config", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getVersionValid(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		UserV4 user = null;
		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);
		} catch (InvalidAuthoException e) {
			log.info("Invalid Authkey : " + autho);
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			return response;
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		String ip = "***************";
		String rv_android_V = "4.0.8";
		String rv_android_force = "0";
		String rv_android_force_V = "0";

		String rv_ios_V = "6.0.8";
		String rv_ios_force = "0";
		String rv_ios_force_V = "0";

		String iris3_android_V = "3.0.6";
		String iris3_android_force = "0";
		String iris3_android_force_V = "0";

		String iris3_ios_V = "5.0.8";
		String iris3_ios_force = "0";
		String iris3_ios_force_V = "0";

		String iris3_wl_android_V = "3.0.6";
		String iris3_wl_android_force = "0";
		String iris3_wl_android_force_V = "0";

		String iris3_wl_ios_V = "5.0.8";
		String iris3_wl_ios_force = "0";
		String iris3_wl_ios_force_V = "0";

		String rm_android_V = "1.0.4";
		String rm_android_force = "0";
		String rm_android_force_V = "0";

		String rm_ios_V = "1.0.4";
		String rm_ios_force = "0";
		String rm_ios_force_V = "0";

		String webservice_V = "3.2.0";
		String listener_V = "3.1.19";
		String webapp_V = "3.1.13";
		String database_V = "3.1.19";

		String rv_hotline_AppId = "cedda3bc-c628-4bac-8f65-9b408d437614_1"; /*
																			 * hotline username - mmarimut
																			 * 
																			 * @ nimblewireless . com
																			 */
		String rv_hotline_AppKey = "3ef7a41f-9419-4bbd-ba18-437f8eb83341_1"; /*
																				 * hotline username - mmarimut
																				 * 
																				 * @ nimblewireless . com
																				 */

		String rm_bg_ios_V = "1.0.2";
		String rm_bg_ios_force = "1.0.2";

		String rm_bg_android_V = "1.0.1";
		String rm_bg_android_force = "1.0.1";

		String rv_petprofile_force = "1";

		String s3bucketname_iris = "iris3.nimblewireless.com";
		String s3_key = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_access_key");
		String s3_secret = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_secret_key");

		Configuration config = new Configuration(ip, rv_android_V, rv_android_force, rv_android_force_V, rv_ios_V,
				rv_ios_force, rv_ios_force_V, iris3_android_V, iris3_android_force, iris3_android_force_V, iris3_ios_V,
				iris3_ios_force, iris3_ios_force_V, iris3_wl_android_V, iris3_wl_android_force,
				iris3_wl_android_force_V, iris3_wl_ios_V, iris3_wl_ios_force, iris3_wl_ios_force_V, rm_android_V,
				rm_android_force, rm_android_force_V, rm_ios_V, rm_ios_force, rm_ios_force_V, listener_V, webservice_V,
				webapp_V, database_V, rv_hotline_AppId, rv_hotline_AppKey, rm_bg_ios_V, rm_bg_ios_force,
				rm_bg_android_V, rm_bg_android_force, rv_petprofile_force, s3bucketname_iris, s3_key, s3_secret);
		try {
			Properties prop = new Properties();

			try {
				/* load a properties file */
				// prop.load(new FileInputStream("iris3.properties"));
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				prop.load(new FileInputStream(file));
				ip = prop.getProperty("ip");
				rv_android_V = prop.getProperty("rv_android_V");
				rv_android_force = prop.getProperty("rv_android_force");
				rv_android_force_V = prop.getProperty("rv_android_force_V");

				rv_ios_V = prop.getProperty("rv_ios_V");
				rv_ios_force = prop.getProperty("rv_ios_force");
				rv_ios_force_V = prop.getProperty("rv_ios_force_V");

				iris3_android_V = prop.getProperty("iris3_android_V");
				iris3_android_force = prop.getProperty("iris3_android_force");
				iris3_android_force_V = prop.getProperty("iris3_android_force_V");

				iris3_ios_V = prop.getProperty("iris3_ios_V");
				iris3_ios_force = prop.getProperty("iris3_ios_force");
				iris3_ios_force_V = prop.getProperty("iris3_ios_force_V");

				iris3_wl_android_V = prop.getProperty("iris3_wl_android_V");
				iris3_wl_android_force = prop.getProperty("iris3_wl_android_force");
				iris3_wl_android_force_V = prop.getProperty("iris3_wl_android_force_V");

				iris3_wl_ios_V = prop.getProperty("iris3_wl_ios_V");
				iris3_wl_ios_force = prop.getProperty("iris3_wl_ios_force");
				iris3_wl_ios_force_V = prop.getProperty("iris3_wl_ios_force_V");

				rm_android_V = prop.getProperty("rm_android_V");
				rm_android_force = prop.getProperty("rm_android_force");
				rm_android_force_V = prop.getProperty("rm_android_force_V");

				rm_ios_V = prop.getProperty("rm_ios_V");
				rm_ios_force = prop.getProperty("rm_ios_force");
				rm_ios_force_V = prop.getProperty("rm_ios_force_V");

				webservice_V = prop.getProperty("webservice_V");
				listener_V = prop.getProperty("listener_V");
				webapp_V = prop.getProperty("webapp_V");
				database_V = prop.getProperty("database_V");

				rv_hotline_AppId = prop.getProperty("rv_hotline_AppId");
				rv_hotline_AppKey = prop.getProperty("rv_hotline_AppKey");

				rm_bg_ios_V = prop.getProperty("rm_bg_ios_V");
				rm_bg_ios_force = prop.getProperty("rm_bg_ios_force");

				rm_bg_android_V = prop.getProperty("rm_bg_android_V");
				rm_bg_android_force = prop.getProperty("rm_bg_android_force");

				rv_petprofile_force = prop.getProperty("rv_petprofile_force");

				s3bucketname_iris = prop.getProperty("s3bucketname_iris");
				s3_key = prop.getProperty("s3_key");
				s3_secret = prop.getProperty("s3_secret");

				config = new Configuration(ip, rv_android_V, rv_android_force, rv_android_force_V, rv_ios_V,
						rv_ios_force, rv_ios_force_V, iris3_android_V, iris3_android_force, iris3_android_force_V,
						iris3_ios_V, iris3_ios_force, iris3_ios_force_V, iris3_wl_android_V, iris3_wl_android_force,
						iris3_wl_android_force_V, iris3_wl_ios_V, iris3_wl_ios_force, iris3_wl_ios_force_V,
						rm_android_V, rm_android_force, rm_android_force_V, rm_ios_V, rm_ios_force, rm_ios_force_V,
						listener_V, webservice_V, webapp_V, database_V, rv_hotline_AppId, rv_hotline_AppKey,
						rm_bg_ios_V, rm_bg_ios_force, rm_bg_android_V, rm_bg_android_force, rv_petprofile_force,
						s3bucketname_iris, s3_key, s3_secret);

			} catch (IOException ex) {
				log.error("exception catched 1111");
				log.error(ex.getLocalizedMessage());
			}

			response.put("Status", 1);
			response.put("Msg", "success");
			response.put("config", config);
			return response;
		} catch (Exception e) {
			log.error("GetVersion::::::" + e.getMessage());
			response.put("Status", 1);
			response.put("Msg", "success");
			return response;
		}
	}

	// Used in web
	// Unmapped gateways - Savitha
	@RequestMapping(value = "v4.0/unmappedgatewayslistbyfilter/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse allUnMappedGateways(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam(value = "skey", required = false) String sKey,
			@RequestParam(value = "svalue", required = false) String sValue,
			@RequestParam(value = "ftype", defaultValue = "equals", required = false) String fType,
			@RequestParam(value = "offset") long offset, @RequestParam(value = "limit") long limit,
			@RequestParam(value = "otype", defaultValue = "desc", required = false) String otype,
			@RequestParam(value = "okey", required = false) String oKey) {
		log.info("Entered into allUnMappedGatewaysListByFilter : " + sKey + " - " + sValue);
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			response = gatewayServiceV4.getUnmappedGatewayListByFilter(sKey, sValue, fType, otype, offset, limit, oKey,
					response);
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting allUnMappedGatewaysListByFilter");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : allUnMappedGatewaysListByFilter : " + e.getLocalizedMessage());
		}
		return response;
	}

	// ========get alerttypes================
	@RequestMapping(value = "v3.0/alerttype/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertType(@RequestHeader HttpHeaders header, Authentication authentication) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<AlertType> alerttypes = fetchDropdownService.getAlertTypes();
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("alerttype", alerttypes);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;

	}

	// balaji
	@RequestMapping(value = "v4.0/ordermap/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse orderMap(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam(value = "orderid", required = true) String orderId,
			@RequestParam(value = "purchasedfrom", required = true) String orderChannel,
			@RequestParam(value = "gatewayid", required = true) long gatewayId,
			@RequestParam(value = "remark", required = false, defaultValue = "NA") String remark) {
		String auth = header.getFirst("auth");
		log.info("Entered into orderMap : authKey : " + auth);
		JResponse response = new JResponse();
		UserV4 user = null;
		String to_address = null;
		String cc_address = null;
		String bcc_address = null;
		String mailSub = "";

		boolean pFromStatus = false;
//			String contactMsg = "<br>Please contact us at <br><font color='blue'> <a href=\"tel: ************\"><b>************</b></a></font> or <font color='blue'><a href=\"mailto:<EMAIL>\"><b><EMAIL></b></a></font>";
		String mailContent = "<p>Hi Team,</p>" + "<p>Find the user order mapping details</p>";
		try {
			Properties prop = new Properties();
			File file = ResourceUtils.getFile("classpath:iris3.properties");
			prop.load(new FileInputStream(file));

			to_address = prop.getProperty("to_address");
			cc_address = prop.getProperty("cc_address");
			bcc_address = prop.getProperty("bcc_address");

			try {
				user = userServiceV4.verifyAuthV3("authkey", auth);
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException e) {

				log.error("Invalid Authkey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "<center><p>User not found</p></center>");

				mailSub = "Failed : External Order Mapping Status-Email : ";
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ " <p> Error Msg : User not found </p>";
				return response;
			}
			mailContent = mailContent + "<p>Name : " + user.getFirstname() + " " + user.getLastname() + "</p>"
					+ "<p>Purchased from : " + orderChannel + " </p>" + "<p>Email : " + user.getEmail() + "  </p>"
					+ "<p>Phone : " + user.getMobileno() + "  </p>" + "<p>Order ID : " + orderId + "</p>";

			Gson gson = new Gson();
			Orders order = new Orders();
			boolean orderMappedtatus = false;
			String subscriptionStatus = "0";

			if (orderChannel.equalsIgnoreCase("rv")) {
				subscriptionStatus = "1";
			} else if (orderChannel.equalsIgnoreCase("others")) {
				pFromStatus = true;

				gatewayService.changeGatewayOrderidStatus(gatewayId, pFromStatus);
				response.put("Status", 0);
				response.put("Msg",
						"<center><p>" + RegisterUserError.contactMsg + " to register your product</p></center>");

				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p>Remark : " + remark + "  </p>" + "<p>Order Mapping Status : Failed </p>"
						+ " <p> Error Msg : Order purchased from other source : " + remark + "</p>";
				return response;
			}

			Gateway gateway = gatewayService.getGatewayByid(gatewayId);

			if (gateway == null) {
				log.error("Gateway not found :: gateway id : " + gatewayId);
				response.put("Status", 0);
				response.put("Msg", "<center><p>Unable to fetch your monitor details. " + RegisterUserError.contactMsg
						+ "</p></center>");

				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ "<p> Error Msg : Gateway details not found </p>";
				return response;
			}

			mailContent = mailContent + "<p> Meid : " + gateway.getMeid() + "</p>";
			Inventory inventory = niomService.getInventoryByMeid(gateway.getMeid());

			if (inventory == null) {
				log.error("Meid not found in inventory :: meid : " + gateway.getMeid());
				response.put("Status", 0);
				response.put("Msg", "<center><p>Unable to fetch your monitor details. " + RegisterUserError.contactMsg
						+ ".</p></center>");

				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ "<p> Error Msg : Inventory not found </p>";
				return response;
			}

			mailContent = mailContent + "<p>QRC : " + inventory.getQrc() + " </p>";

			JSONObject jorderIdCheckResponse = new JSONObject();
			jorderIdCheckResponse = userService.getNiomGetOrderCount(orderChannel, orderId);

			int orderIdCheckStatus = jorderIdCheckResponse.getInt("Status");

			if (orderIdCheckStatus > 0) {
				order = gson.fromJson(jorderIdCheckResponse.getJSONObject("Order").toString(), Orders.class);
				int totalOrderCount = Integer.parseInt(jorderIdCheckResponse.getString("Totalordered"));
				int totalMappedCount = Integer.parseInt(jorderIdCheckResponse.getString("Totalmapped"));

				if (totalMappedCount >= totalOrderCount) {
					response.put("Status", 0);
					response.put("Msg",
							"<center><p>Invalid Order ID " + RegisterUserError.contactMsg + ".</p></center>");

					mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
					mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
							+ "<p> Error Msg : Order Id is already registered </p>";
					return response;
				}

				log.info("Order id found in niom. Order Channel : " + order.getOrder_acc_type().getAcc_type()
						+ "Order ID : " + order.getOrder_id() + "Out Order ID  :" + order.getExternal_order_id());

				if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("amazon")) {
					if (order.getBilling_email().toLowerCase().contains("na")
							|| order.getBilling_phone().toLowerCase().contains("na")) {
						order.setBilling_email(user.getEmail());
						order.setBilling_phone(user.getMobileno());
						order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
						order.setWelcome_status(order.getWelcome_status());
						userService.checkFirstNameAndLastNameInOrder(order, user);
						boolean updateExternalOrderData = niomService.updateExternalOrdersInNiom(order);
					}
				} else if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("walmart")) {
					if (order.getBilling_email().toLowerCase().contains("na")
							|| order.getBilling_phone().toLowerCase().contains("na")) {
						order.setBilling_email(user.getEmail());
						order.setBilling_phone(user.getMobileno());
						order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
						order.setWelcome_status(order.getWelcome_status());
						userService.checkFirstNameAndLastNameInOrder(order, user);
						boolean updateExternalOrderData = niomService.updateExternalOrdersInNiom(order);
					}
				} else if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("technorv")) {
					if (order.getBilling_email().toLowerCase().contains("na")
							|| order.getBilling_phone().toLowerCase().contains("na")) {
						order.setBilling_email(user.getEmail());
						order.setBilling_phone(user.getMobileno());
						order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
						order.setWelcome_status(order.getWelcome_status());
						userService.checkFirstNameAndLastNameInOrder(order, user);
						boolean updateExternalOrderData = niomService.updateExternalOrdersInNiom(order);
					}
				}

				orderMappedtatus = userService.orderMapping(inventory.getMeid(), _helper.getCurrentTimeinUTC(),
						inventory.getDevicestate().getId(), order.getOrder_id() + "", order.getDevicemodel(),
						subscriptionStatus, "1", "webapp", gatewayId);

				if (!orderMappedtatus) {
					response.put("Status", 0);
					response.put("Msg", "<center><p>Unable to find your Order ID " + RegisterUserError.contactMsg
							+ ". </p></center>");

					mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
					mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
							+ "<p> Error Msg : Unable to map order </p>";
					return response;
				}

			} else {
				response.put("Status", 0);
				response.put("Msg",
						"<center><p>Unable to find your Order ID " + RegisterUserError.contactMsg + ".</p></center>");

				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ "<p> Error Msg : Order Id not found </p>";

				log.info("Order id not found in niom, Error Code : ER039 :" + RegisterUserError.ER039);

				return response;
			}

			niomService.updateOrdermapUserDetails(order.getOrder_id(), user.getUsername(), user.getId() + "", "NA");

			boolean updateExternalOrderDataStatus = userService.updateOrdersDataV2(orderChannel, order, inventory);

			if (orderMappedtatus) {

				boolean isUpdateOrderMappingDetails = userServiceV4.updateOrderMappingDetails(user.getId(),
						inventory.getQrc(), orderId, orderChannel, "6", order.getOrder_sku(), order.getDatetime());

				gatewayService.changeGatewayOrderidStatus(gateway.getId(), pFromStatus);
				int remainingdays = calculateWarrantyDays(order.getDatetime());
				String msg = "You have " + remainingdays + " days of warranty left";

				if (remainingdays <= 0) {
					msg = "Your warranty has expired";
				}

				// Update sales channel in device subscription table
				async.updateSalesChannel(user.getId() + "", orderChannel, gatewayId, gateway.getMeid(), orderId,
						gateway.getModel().getMonitor_type().getId());

				response.put("Status", 1);
				response.put("Msg", msg);

				mailSub = "Success : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Success </p>";
			}

			// Update sales channel in device subscription table
			if (response.getResponse().get("Status").equals(1)) {
				async.updateSalesChannel(user.getId() + "", orderChannel, gatewayId, gateway.getMeid(), orderId,
						gateway.getModel().getMonitor_type().getId());
			}
		} catch (Exception e) {
			log.error("Error occured in ordermap :: auth : " + auth);
			response.put("Status", 0);
			response.put("Msg", "<center><p>Error! " + RegisterUserError.contactMsg + ".</p></center>");

			mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
			mailContent = mailContent + "<p> Order Mapping Status : Failed </p>";
			mailContent = mailContent + "<p> Error Msg : " + e.getLocalizedMessage() + " </p>";
		} finally {
			mailContent = mailContent + "<br>Thanks,<br> Irisservice ";
			async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);
		}
		return response;
	}

	private int calculateWarrantyDays(String orderDate) {
		String currentDate = irisUtil.getCurrentTimeUTC();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			Date orderdate = sdf.parse(orderDate);
			Date currentdate = sdf.parse(currentDate);

			long difference = currentdate.getTime() - orderdate.getTime();
			int daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
			daysBetween = 365 - daysBetween;
			return daysBetween;
		} catch (Exception e) {
			return 0;
		}
	}

	// Used in web
	@RequestMapping(value = "v3.0/userlistbyfilter/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse userlistbyfilter(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam(value = "ukey", required = false) String uKey,
			@RequestParam(value = "uvalue", required = false) String uValue,
			@RequestParam(value = "ftype", defaultValue = "equal", required = false) String fType,
			@RequestParam(value = "offset") long offset, @RequestParam(value = "limit") long limit,
			@RequestParam(value = "otype", defaultValue = "asc", required = false) String otype,
			@RequestParam(value = "okey", required = false) String oKey) {

		String autho = header.getFirst("auth");
		log.info("Entered into userlistbyfilter : " + uKey + " - " + uValue);
		JResponse response = new JResponse();
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV3("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.error("Invalid Authkey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			response = userServiceV4.getuserlistbyfilter(uKey, uValue, fType, otype, offset, limit, oKey, response);

			return response;

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting userlistbyfilter");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : userlistbyfilter : " + e.getLocalizedMessage());
		}
		return response;
	}

	// Used in web
	// Web API - kalai
//	@RequestMapping(value = "v4.0/getrvdetails/", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse getRVDetails(@RequestHeader HttpHeaders header, Authentication authentication,
//			@RequestParam("os") String os, @RequestParam long rvId) {
//		JResponse response = new JResponse();
//		String auth = header.getFirst("auth");
//		try {
//			log.info("Entered getRVDetails :" + auth);
//
//			UserV4 user = null;
//			try {
//				user = userServiceV4.verifyAuthV4("authkey", auth);
//			} catch (InvalidAuthoException e) {
//				log.info("Invalid Authkey : " + auth);
//				response.put("Status", 0);
//				response.put("Msg", "Invalid Authkey");
//				return response;
//			}
//
//			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
//			if (errResponse != null) {
//				return errResponse;
//			}
//
//			//RVCentricDetails rvObj = rvCentService.getRVCentricDetailsById(rvId);
//
//			response.put("Status", 1);
//			response.put("Msg", "success");
//			response.put("rvObj", rvObj);
//
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Please try after some time!");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("getRVDetails : " + e.getLocalizedMessage());
//		}
//		return response;
//	}

	// ========Gateway Alert Range ================
	@RequestMapping(value = "v3.0/assetalertrange/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGatewayAlertRange(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("assetid") String assetid) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<JAlertRange> altrange = alertService.getAlertRange(assetid, user);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("assetalertrange", altrange);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;
	}

	// Used in web
	// ==========get companyconfig version 3.1========
	@RequestMapping(value = "v3.1/companyconfig/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getCompanyConfigList(@RequestHeader HttpHeaders header,
			Authentication authentication, @RequestParam("cmpid") String cmpid) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		User user;
		try {
			user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			long cmp_id = user.giveCompany().getId();
			if (!cmpid.isEmpty())
				cmp_id = Long.valueOf(cmpid);

			List<CompanyConfig> cmp_cfglist = companyService.getCompanyConfigList(user.getRole().getId(), cmp_id);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("companyconfig", cmp_cfglist);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");

			return response;
		}
		return response;
	}

	// Used in web --> Savitha
	@RequestMapping(value = "v4.0/getallprobecategory/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAllProbeCategory(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam(name = "os", required = false) String os) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering getAllProbeCategory : " + autho);
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<ProbeCategory> getAllProbeCategory = probService.getAllProbeCategory();

			if (getAllProbeCategory != null && getAllProbeCategory.size() > 0) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("probecategory", getAllProbeCategory);
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No probe category found.");
				log.error("No probe category found.");
				return response;
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting getAllProbeCategory.");
			response.put("Error", e.getLocalizedMessage());
			log.error("getAllProbeCategory : Exception : " + e.getLocalizedMessage());
			return response;
		}
	}

	// Used in web
	// Save asset model --> Savitha
	@RequestMapping(value = "v4.0/saveassetmodel/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveAssetModel(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody AssetModel assetModel, @RequestParam long monitortype_id) {
		String autho = header.getFirst("auth");
		log.info("Entered save asset model - " + autho);
		JResponse response = new JResponse();
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			MonitorType monitorType = MonitorTypeService.getMonitorTypeById(monitortype_id);
			if (monitorType != null) {
				assetModel.setMonitor_type(monitorType);
				boolean isSuccess = fetchDropdownService.saveAssetModel(assetModel);
				if (isSuccess) {
					response.put("Status", 1);
					response.put("Msg", "Success");
				} else {
					log.error("Error occurred in save asset model");
					response.put("Status", 0);
					response.put("Msg", "Error occurred!!!");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Monitor type not found!!!");
			}
		} catch (Exception e) {
			log.error("InValid Authkey");
			response.put("Status", 0);
			response.put("Msg", "Exception occurred - " + e.getMessage());
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	// Used in web --> Savitha
	@RequestMapping(value = "v4.0/saveprobelookup/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveProbeLookup(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody List<ProbeLookup> probeLookup, @RequestParam String probecategory,
			@RequestParam(name = "os", required = false) String os) {
		String autho = header.getFirst("auth");
		log.info("Entered save probe lookup - " + autho);
		JResponse response = new JResponse();
		String responseMessage = "Status :" + "\n";
		int i = 0;
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (probeLookup.size() > 0) {
				for (ProbeLookup probe : probeLookup) {
					ProbeCategory probeCategory_type = probService.getProbeCategoryById(probecategory);
					if (probeCategory_type != null) {
						probe.setProbecategory(probeCategory_type);
						if (i == 0) {
							responseMessage += probe.getProbecategory().getType() + " - ";
							i = 1;
						} else
							responseMessage += ", \n" + probe.getProbecategory().getType() + " - ";
						try {
							boolean status = probService.createProbeLookup(probe);
							if (status)
								responseMessage += "Success";
							else
								responseMessage += "Failed";
						} catch (Exception e) {
							responseMessage += "Failed";
							log.error("createProbeLookup : " + e.getLocalizedMessage());
						}
					} else {
						responseMessage += "No probe category found to create probe lookup!!!";
					}
				}
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("ResponseMessage", responseMessage);
			} else {
				responseMessage += "No probe lookup found to create!!!";
				response.put("Status", 0);
				response.put("Msg", "No probe lookup found to create!!!");
				response.put("ResponseMessage", responseMessage);
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Probe lookup creation failed!!!");
			response.put("ResponseMessage", e.getLocalizedMessage());
			log.error("saveprobelookup : " + e.getLocalizedMessage());
		}
		return response;
	}

	// Used in web --> Savitha
	@RequestMapping(value = "v4.0/getallprobelookup/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAllProbeLookup(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam(name = "os", required = false) String os) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering getAllProbeLookup : " + autho);
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<ProbeLookup> getAllProbeCategory = probService.getAllProbeLookup();
			if (getAllProbeCategory != null && getAllProbeCategory.size() > 0) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("probelookup", getAllProbeCategory);
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No probe lookup found.");
				log.error("No probe lookup found.");
				return response;
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting getAllProbeLookup.");
			response.put("Error", e.getLocalizedMessage());
			log.error("getAllProbeLookup : Exception : " + e.getLocalizedMessage());
			return response;
		}
	}

	// Used in web --> Savitha
	@RequestMapping(value = "v4.0/savebatterylookup/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveBatteryLookup(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody List<BatteryLookup> batteryLookup, @RequestParam String probecategory,
			@RequestParam(name = "os", required = false) String os) {
		String autho = header.getFirst("auth");
		log.info("Entered save battery lookup - " + autho);
		JResponse response = new JResponse();
		String responseMessage = "Status :" + "\n";
		int i = 0;
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (batteryLookup.size() > 0) {
				for (BatteryLookup battery : batteryLookup) {
					ProbeCategory probeCategory_type = probService.getProbeCategoryById(probecategory);
					if (probeCategory_type != null) {
						battery.setProbecategory(probeCategory_type);
						if (i == 0) {
							responseMessage += battery.getProbecategory().getType() + " - ";
							i = 1;
						} else
							responseMessage += ", \n" + battery.getProbecategory().getType() + " - ";
						try {
							boolean status = probService.createBatteryLookup(battery);
							if (status)
								responseMessage += "Success";
							else
								responseMessage += "Failed";
						} catch (Exception e) {
							responseMessage += "Failed";
							log.error("saveBatteryLookup : " + e.getLocalizedMessage());
						}
					} else {
						responseMessage += "No probe category found to create battery lookup!!!";
					}
				}
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("ResponseMessage", responseMessage);
			} else {
				responseMessage += "No battery lookup found to create!!!";
				response.put("Status", 0);
				response.put("Msg", "No battery lookup found to create!!!");
				response.put("ResponseMessage", responseMessage);
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Battery lookup creation failed!!!");
			response.put("ResponseMessage", e.getLocalizedMessage());
			log.error("savebatterylookup : " + e.getLocalizedMessage());
		}
		return response;
	}

	// Used in web --> Savitha
	@RequestMapping(value = "v4.0/getallbatterylookup/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAllBatteryLookup(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam(name = "os", required = false) String os) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering getAllProbeLookup : " + autho);
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<BatteryLookup> getAllBatteryCategory = probService.getAllBatteryLookup();
			if (getAllBatteryCategory != null && getAllBatteryCategory.size() > 0) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("batterylookup", getAllBatteryCategory);
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No battery lookup found.");
				log.error("No battery lookup found.");
				return response;
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting getAllBatteryLookup.");
			response.put("Error", e.getLocalizedMessage());
			log.error("getAllBatteryLookup : Exception : " + e.getLocalizedMessage());
			return response;
		}
	}

	// Used in web --> Savitha
	@RequestMapping(value = "v4.0/createprobecategory/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createProbeCategory(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam(name = "os", required = false) String os,
			@RequestParam(name = "model_id", required = true) long model_id,
			@RequestBody List<ProbeCategory> probeList) {
		JResponse response = new JResponse();
		String responseMessage = "Status :" + "\n";
		String autho = header.getFirst("auth");
		log.info("Entering createProbeCategory - " + autho);
		int i = 0;

		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (probeList.size() > 0) {
				for (ProbeCategory probe : probeList) {
					probe.setModel_id(model_id);
					if (i == 0) {
						responseMessage += probe.getType() + " - ";
						i = 1;
					} else
						responseMessage += ", \n" + probe.getType() + " - ";
					try {
						boolean status = probService.createProbeCategory(probe);
						if (status)
							responseMessage += "Success";
						else
							responseMessage += "Failed";
					} catch (Exception e) {
						responseMessage += "Failed";
						log.error("createProbeCategory : " + e.getLocalizedMessage());
					}
				}
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("ResponseMessage", responseMessage);
			} else {
				responseMessage += "No probe category found to create!!!";
				response.put("Status", 0);
				response.put("Msg", "No probe category found to create!!!");
				response.put("ResponseMessage", responseMessage);
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Probe category creation failed!!!");
			response.put("ResponseMessage", e.getLocalizedMessage());
			log.error("createProbeCategory : " + e.getLocalizedMessage());
		}
		return response;
	}

	// To add user and gateway details to Recall device table - Balaji
	@RequestMapping(value = "v4.0/recallreplacegateway/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse addReplaceGateway(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam(value = "action") String action, @RequestParam(value = "gatewayid") long gatewayId,
			@RequestParam(value = "enable") boolean enable) {
		log.info(" Entered into addReplaceGateway :: gateway ID : " + gatewayId);
		JResponse jResponse = new JResponse();
		String auth = header.getFirst("auth");
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (Exception e) {
				jResponse.put("Status", 0);
				jResponse.put("Msg", "Invalid Authkey");
				return jResponse;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			long userId = 0;
			String meid = "NA";
			try {
				JUser userGateway = gatewayServiceV4.getUsergatewaydetails(gatewayId);
				if (userGateway != null) {
					Set<Gateway> gateways = userGateway.giveGateways();
					userId = userGateway.getId();
					Gateway[] setValue = gateways.toArray(new Gateway[gateways.size()]);
					meid = setValue[0].getMeid();
				}
			} catch (Exception e) {
				jResponse.put("Status", 0);
				jResponse.put("Msg", "Invalid GatewayId");
				return jResponse;
			}

			if (enable) {
				DeviceReplaced deviceReplaced = new DeviceReplaced();
				deviceReplaced.setUserId(userId);
				deviceReplaced.setMeid(meid);
				deviceReplaced.setInsertedDate(_helper.getCurrentTimeinUTC());

				Gateway oldGateway = gatewayServiceV4.getGatewayByMeid(meid);
				if (oldGateway != null) {
					deviceReplaced.setMonitorType(oldGateway.getModel().getMonitor_type().getId());
				}

				boolean enableReplace = false;
				if (action.equalsIgnoreCase("recall")) {
					deviceReplaced.setIsReplaced(2);
				} else {
					deviceReplaced.setIsReplaced(1);
					enableReplace = niomService.enableReplaceInOrderMapByMeid(meid, userId);
				}

				boolean savedDeviceReplaced = gatewayServiceV4.saveDeviceReplaced(deviceReplaced);

				log.info(" OrderMap isReplaced status Changed : " + enableReplace);
				log.info(" Saved device data : " + deviceReplaced);

				if (savedDeviceReplaced) {
					jResponse.put("Status", 1);
					jResponse.put("Msg", "Success");
				} else {
					jResponse.put("Status", 0);
					jResponse.put("Msg", "Not updated");
				}
			} else {
				boolean isGatewayRemoved = gatewayServiceV4.removeDeviceReplaced(userId, meid);
				boolean disableReplace = niomService.disableReplaceInOrderMapByMeid(meid, userId);
				if (isGatewayRemoved) {
					jResponse.put("Status", 1);
					jResponse.put("Msg", "Success");
				} else {
					jResponse.put("Status", 0);
					jResponse.put("Msg", "Not updated");
				}
			}
			return jResponse;
		} catch (Exception e) {
			log.error(" Error in addReplaceGateway : " + e.getLocalizedMessage());
			jResponse.put("Status", 0);
			jResponse.put("Msg", "Failed");
			jResponse.put("Error", e.getLocalizedMessage());
			return jResponse;
		}
	}

	// Used in web
	// ========get all gateways================
	@RequestMapping(value = "v3.0/allgateways/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAllGateway(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("cmptype_id") String cmptype_id,
			@RequestParam(value = "zip", defaultValue = "0", required = false) String zip) {

		JResponse response = new JResponse();
		log.info("get gateway");
		String autho = header.getFirst("auth");
		try {
			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());

			if (errResponse != null) {
				return errResponse;
			}
			long roleId = user.getRole().getId();
			// long roleId=1;
			if (roleId == 1) {
				List<JSGateway> gateways = gatewayService.getGateways(cmptype_id);
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("gateways", gateways);

				/* If zip params is 1.Report is zipped */
				// if(zip.equalsIgnoreCase("1"))
				// response.put("gateways", zipContent(gateways));
				// else
				// response.put("gateways", gateways);

			} else {
				response.put("Status", 0);
				response.put("Msg", "invalid role");
				return response;
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		// catch (IOException e) {
		// e.printStackTrace();
		// response.put("Status", 0);
		// response.put("Msg","invalid content for compress");
		// return response;
		// }
		return response;
	}

	@RequestMapping(value = "v3.0/throttlingsettings/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updateThrottlingSettings(@RequestHeader HttpHeaders header,
			Authentication authentication, @ModelAttribute @Valid ThrottlingSettings throtsettings,
			BindingResult result) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {

			User user = userService.verifyAuthKey(autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());

			if (errResponse != null) {
				return errResponse;
			}
			throttlingdao.saveOrUpdateThrotSetting(throtsettings);
			if (throtsettings.getId() != 0) {
				gatewayService.updateGatewayCreditPoints(throtsettings);
			}
			response.put("Status", 1);
			response.put("Msg", "success");

		} catch (InvalidAuthoException e) {
			log.error("updateThrottlingSettings:" + e.getLocalizedMessage());
			response.put("Status", 1);
			response.put("Msg", "success");

		} catch (DataIntegrityViolationException e) {
			log.error("updateThrottlingSettings:" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "name already exits");
		} catch (Exception e) {
			log.error("updateThrottlingSettings:::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in throttling settings");
		}
		return response;
	}

	// Used in web - Savitha
	@RequestMapping(value = "v4.0/getevalidationbyuser", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getEValidationPassword(@RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		String user_id = header.getFirst("user_id");

		log.info("Entering getEValidation by user : auth key : " + auth);
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV3("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.error("Invalid AuthKey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid AuthKey");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}

			if (user != null && user.getRole().getId() != 8) {
				response.put("Status", 0);
				response.put("Msg", "Access denied!");
				return response;
			}

			String evalidPwd = userServiceV4.getEValidationPassword(Long.parseLong(user_id));
			/*
			 * try { String pass = evalidPwd; if(
			 * Base64.isArrayByteBase64(evalidPwd.getBytes()) ) pass =
			 * _helper.base64Decoder(evalidPwd);
			 * 
			 * if( pass != null ) evalidPwd = pass;
			 * 
			 * } catch (Exception e) { log.error("Error while checking is base64"); }
			 */

			if (evalidPwd != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("EValid", evalidPwd);
			} else {
				response.put("Status", 0);
				response.put("Msg", "Exception occurred!");
				response.put("EValid", evalidPwd);
			}
		} catch (Exception e) {
			log.error("Exception : getEValidationPassword :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occurred!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	@RequestMapping(value = "v4.0/getwebuser", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getWebUser(Authentication authentication) {
		log.info("Entered into getWebUser");
		JResponse response = new JResponse();
		try {
			String userName = authentication.getName();
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV3("username", userName);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: username : " + userName + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid username");
				return response;
			}

			byte[] oAuth2token = oAuth2Service.generateOauth2Token(user.getUsername(), user.getPassword(), clientidWeb,
					clientSecretWeb);

			if (oAuth2token != null) {
				response.put("token", oAuth2token);
			} else {
				response.put("token", null);
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("User", user);

		} catch (Exception e) {
			log.error("Error in getWebUser");
			response.put("Status", 0);
			response.put("Msg", "Invalid Session. Please try again later");
		}
		return response;
	}

	// Get gps value based on asset id --> Savitha
	@RequestMapping(value = "v4.0/checkgpsstatus/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse checkGpsStatus(@RequestHeader HttpHeaders header, @RequestParam long model_id,
			Authentication authentication) {
		String autho = header.getFirst("auth");
		log.info("Entered get asset model by id - " + autho);
		JResponse response = new JResponse();
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			AssetModel assetModel = fotaService.getAssetModelById(model_id);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("isgps", assetModel.getIsgps());
		} catch (Exception e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "Exception in get asset model by id!!!");
			return response;
		}
		return response;
	}

	@RequestMapping(value = "v3.0/validateorderid/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse validateOrderId(@RequestHeader HttpHeaders header,
			@RequestParam("orderid") String orderId, @RequestParam("orderchannel") String orderChannel,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		log.info("Entered validateOrderId, orderId : " + orderId + " orderChannel : " + orderChannel);
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			int orderIdLength = orderId.length();
			if (!autho.equalsIgnoreCase("NA")) {
				try {
					UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
				} catch (InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for username : " + autho);
					return response;
				}
			}
			if (orderChannel.equalsIgnoreCase("rv")) {
				Pattern numbers = Pattern.compile("^[0-9]+$");
				Matcher hasNumbers = numbers.matcher(orderId);
				if (hasNumbers.find() && orderIdLength >= 5 && orderIdLength <= 10) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					log.info(orderChannel + " Order ID validated " + orderId);
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "Order ID should be 5 to 10 digits and should contains only numerics.");
					log.info(orderChannel + " Order ID should be 5 to 10 digits and should contains only numerics."
							+ orderId);
				}
			} else if (orderChannel.equalsIgnoreCase("amazon")) {

				Pattern numbersWithHypen = Pattern.compile("^[0-9,-]+$");
				Matcher hasNumbersWithHypen = numbersWithHypen.matcher(orderId);

				Pattern numbers = Pattern.compile("^[0-9]+$");
				Matcher hasNumbers = numbers.matcher(orderId);
				if ((hasNumbers.find() || hasNumbersWithHypen.find()) && orderIdLength >= 7) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					log.info(orderChannel + " Order ID validation success : " + orderId);
				} else {
					response.put("Status", 0);
					response.put("Msg",
							"Order ID should be min 7 characters and should not contains any alphabets or special characters except '-' (hypen)");
					log.info(orderChannel + " Order ID should contains only numerics : " + orderId);
				}
				return response;
			} else if (orderChannel.equalsIgnoreCase("walmart") || orderChannel.equalsIgnoreCase("others")) {
				Pattern numbers = Pattern.compile("^[0-9]+$");
				Matcher hasNumbers = numbers.matcher(orderId);

				if (hasNumbers.find()) {

					if (orderId.length() >= 10) {
						response.put("Status", 1);
						response.put("Msg", "Success");
						log.info(orderChannel + " Order id validated " + orderId);
					} else {
						response.put("Status", 0);
						response.put("Msg",
								"Order ID should be min 10 characters and should not contains any alphabets or special characters for walmart orders");
						log.info(orderChannel
								+ " Order ID should be min 10 characters and should not contains any alphabets or special characters for walmart orders");
					}

				} else {
					String msg = "Please contact us at " + supportContactNumber.get(device_country) + "  or email to "
							+ supportContactEmail.get(device_country) + " to register your product";

					response.put("Status", 1);
					response.put("Msg", msg);
					log.info(orderChannel + msg);
				}
				return response;
			} else if (orderChannel.equalsIgnoreCase("technorv")) {

				Pattern numbersWithHypen = Pattern.compile("^[a-zA-Z0-9,-]+$");
				Matcher hasNumbersWithHypen = numbersWithHypen.matcher(orderId);

				Pattern numbers = Pattern.compile("^[a-zA-Z0-9]+$");
				Matcher hasNumbers = numbers.matcher(orderId);
				if ((hasNumbers.find() || hasNumbersWithHypen.find()) && orderIdLength >= 5) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					log.info(orderChannel + " Order ID validation success : " + orderId);
				} else {
					response.put("Status", 0);
					response.put("Msg",
							"Order ID should be min 5 characters and should not contains any special characters except '-' (hypen)");
					log.info(orderChannel + " Order ID should contains only numerics : " + orderId);
				}
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
//			else if(orderChannel.equalsIgnoreCase("others"))  {
////				Pattern numbers  = Pattern.compile("^[0-9]+$");
////				Matcher hasNumbers  = numbers.matcher(orderId);
////				
////				if(hasNumbers.find() && orderId.length() >= 10 ) {
////					response.put("Status", 1);
////					response.put("Msg", "Success");
////					log.info(orderChannel+" Order id validated "+orderId);					
////				}else {
//					response.put("Status", 0);
//					response.put("Msg", "Please contact us at +1 (855)983-5566  or <NAME_EMAIL> to register your product");
//					log.info(orderChannel+" Please contact us at +1 (855)983-5566  or <NAME_EMAIL> to register your product");
//				//}
//				return response;
//			}
		} catch (Exception e) {
			log.error("Exception occured while validating order id : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unknown error");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v4.0/enableordisablepetprofile/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse enableOrDisablePetprofile(
			@RequestBody List<EnableOrDisablePetProfile> enableOrDisablePetPrfList, @RequestHeader HttpHeaders header,
			Authentication authentication) {

		log.info("Entered into enableOrDisablePetprofile");
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");

		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
			if (enableOrDisablePetPrfList.size() > 0) {
				boolean updatedStatus = gatewayService.enableOrDisablePetProfile(user.getId(),
						enableOrDisablePetPrfList);
				log.info("updated status : " + updatedStatus);

				if (updatedStatus) {
					response.put("Status", 1);
					response.put("Msg", "success");
				} else {
					response.put("Status", 1);
					response.put("Msg", "Failed to update pet profile");
				}
			} else {
				response.put("Status", 1);
				response.put("Msg", "No pet profile selected");
				return response;
			}

		} catch (InvalidAuthoException e) {
			log.error("Invalid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;
	}

	@RequestMapping(value = "v4.0/deleteuserweb/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse deleteUserWeb(@RequestHeader HttpHeaders header, Authentication authentication) {

		log.info("Entered into deleteUserWeb..");
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");

		try {
			User user = userService.verifyAuthKey(autho);
//			if (user != null && user.isDelete_user()) {
			if (user != null) {
				boolean status = true;
				for (Gateway gateway : user.getGateways()) {

					status = userDltObj(user, gateway);

					if (status) {
						int deviceStateId = 0;
						Inventory inventory = niomService.getInventoryByMeid(gateway.getMeid());
						if (inventory != null)
							deviceStateId = (int) inventory.getDevicestate().getId();

						boolean delStatus = userService.deleteordermap(gateway.getMeid(), deviceStateId);
						log.info("Delete ordermap : " + delStatus);

						delStatus = gatewayService.delGateway(user, gateway.getId() + "");
						log.info("Delete gateway : " + delStatus);
					}

				}

				if (user.getGateways().size() == 0) {
					status = userDltObj(user, null);
				}

				if (status) {
					String userStatus = userService.deleteUserv2(user);
					log.info("Delete user : " + userStatus);

					if (userStatus.contains("Success")) {
						response.put("Status", 1);
						response.put("Msg", "User deleted successfully");

						List<AllSubscription> subList = chargebeeService
								.getSubscriptionByChargebeeId(user.getChargebeeid());
						if (subList != null) {
							int count = 1;
							for (AllSubscription sub : subList) {
								chargebeeService.cancelCbSubscription(sub.getSubscriptionId(), sub.getCustomerId(),
										count);
								count--;
							}
						}

						String email = user.getEmail();
						if (email.equalsIgnoreCase("NA"))
							email = user.getUsername();

						Template template = (Template) templates.getTemplate("delete-confirmation.ftl");
						Map<String, String> deleteRequestParams = new HashMap<>();
						deleteRequestParams.put("firstname", user.getFirstname());

						ResponseEntity<String> deleteRequestContent = ResponseEntity
								.ok(FreeMarkerTemplateUtils.processTemplateIntoString(template, deleteRequestParams));
						String emailContent = deleteRequestContent.getBody();
						email_helper.SendEmail_SES(user.getEmail(), "", "", delete_user_emailsub, emailContent);
						log.info("Email sent.");
					} else {
						response.put("Status", 0);
						response.put("Msg", "User not deleted. Please try again");
					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error occurred. Please try again");
				}

			} else {
				log.info("User not found or not requested for delete");
				response.put("Status", 0);
				response.put("Msg", "User not found or not requested for delete");
			}
		} catch (InvalidAuthoException e) {
			log.error("Invalid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error("Exception occurred : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occurred. Please try again");
			return response;
		}
		return response;
	}

	private boolean userDltObj(User user, Gateway gateway) {
		boolean status = true;
		try {
			UserDltInfo thisUserDlt = new UserDltInfo();

			thisUserDlt.setUser_id(user.getId());
			thisUserDlt.setUsername(user.getUsername());
			thisUserDlt.setBilling_email(user.getEmail());
			thisUserDlt.setCmp_id(user.giveCompany().getId());
			if (gateway != null) {
				thisUserDlt.setGateway_id(gateway.getId());
				thisUserDlt.setMeid(gateway.getMeid());
				thisUserDlt.setQrcode(gateway.getQrcode());

				List<com.nimble.irisservices.niom.entity.Ordermap> orderMapList = niomService
						.getMappedOrderByMeid(gateway.getMeid());
				if (orderMapList != null && !orderMapList.isEmpty()) {
					String orderChannel = niomService.getOrderChannelById(orderMapList.get(0).getOrder_id());
					thisUserDlt.setOrder_channel(orderChannel);
					thisUserDlt.setOrder_id(orderMapList.get(0).getOrder_id());
					thisUserDlt.setOrder_date(orderMapList.get(0).getOrder_date());
				}
			}

			long dltInfo = userServiceV4.getUserDltId(thisUserDlt.getMeid());
			thisUserDlt.setId(dltInfo);
			thisUserDlt.setUpdated_date(_helper.getCurrentTimeinUTC());

			status = userServiceV4.saveOrUpdateUserDltDetails(thisUserDlt);
			log.info("Update user_dlt: " + status);
			return status;
		} catch (Exception e) {
			log.error("Error occurred in userDltObj : " + e.getMessage());
		}
		return false;
	}

	@RequestMapping(value = "v4.0/getunpaidinvoices/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUnpaidInvoices(@RequestParam String type, @RequestHeader HttpHeaders header,
			Authentication authentication) {

		log.info("Entered into getUnpaidInvoices");
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");

		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
			if (user != null) {
				List<UnpaidInvoices> upList = chargebeeService.getUnpaidInvoice(type);

				if (!upList.isEmpty()) {
					response.put("Status", 1);
					response.put("Msg", "success");
					response.put("InvoiceList", upList);
				} else {
					response.put("Status", 0);
					response.put("Msg", "List is Empty");
					response.put("InvoiceList", upList);
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid User");
				return response;
			}

		} catch (InvalidAuthoException e) {
			log.error("Invalid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error("Error occurred : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error occurred!!");
			return response;
		}
		return response;
	}

	@RequestMapping(value = "v4.0/listWebPetNews", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listWebPetNews(@RequestHeader HttpHeaders header, Authentication authentication) {

		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			List<PetNews> pnList = pnService.listWebPetNews();
			response.put("Status", 1);
			response.put("Msg", "success");
			response.put("pnList", pnList);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "failure");
			response.put("Error", e.getLocalizedMessage());
			log.error("Error in listWebPetNews : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/createPetNews", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createPetNews(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody PetNews pn) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			status = pnService.saveOrUpdatePetNews(pn);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", 0);
			response.put("Msg", "PetNews already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("createPetNews : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			e.printStackTrace();
			response.put("Status", 0);
			response.put("Msg", "PetNews creation failed");
			log.error("createPetNews : " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v4.0/getWebPetNews", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getWebPetNews(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("id") long id) {
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.error("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			PetNews pn = pnService.getPetNews(id);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("petnews", pn);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Unable to retrive PetNews");
			log.error("Error in getPetNews : " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v4.0/updatewarrantypopup", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateWarrantyPopup(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("gateway_id") long gateway_id, @RequestParam("popup") boolean popup) {
		log.info("Entered updateWarrantyPopup for gateway_id " + gateway_id);
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.error("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			gatewayServiceV4.updateWarrantyPopup(gateway_id, popup);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", 0);
			response.put("Msg", "Failed to update warranty popup!");
			response.put("Error", ex.getLocalizedMessage());
			log.error("updateWarrantyPopup : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Failed to update warranty popup!");
			response.put("Error", e.getLocalizedMessage());
			log.error("updateWarrantyPopup : " + e.getLocalizedMessage());
		}
		return response;
	}

	// Used in web - Savitha
	@RequestMapping(value = "v4.0/removedeleterequest", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse removeDeleteRequest(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("user_id") long user_id, @RequestParam("isdelete") boolean isdelete) {

		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering removeDeleteRequest by user : auth key : " + auth);
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.error("Invalid AuthKey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid AuthKey");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}

			boolean status = userServiceV4.removeDeleteRequest(user_id, isdelete);
			if (status) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Exception occurred!");
			}
		} catch (Exception e) {
			log.error("Exception : removeDeleteRequest :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occurred!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	// Used in web - kalai
	@RequestMapping(value = "v4.0/reassignuserfeature", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse reassignUserFeature(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("email") String email) {
		System.out.println("reassignuserfeature:new_email");
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering reassignUserFeature : auth key : " + auth);
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
				if (!email.equalsIgnoreCase(user.getEmail())) {
					response.put("Status", 0);
					response.put("Msg", "Invalid User");
					response.put("Error", "user email & authkey not matching");

					return response;
				}
			} catch (InvalidAuthoException e) {
				log.error("Invalid AuthKey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid AuthKey");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}

			if (user != null && email.equalsIgnoreCase(user.getEmail())
					&& !user.getChargebeeid().equalsIgnoreCase("NA")) {
				response = cbService.getSubscriptionFromCB(user, 1, "ios", "1.2", "00:00", false, 0L);

				JSubscriptionPlanReport subs = (JSubscriptionPlanReport) response.get("subscriptionplan");

				response = new JResponse();

				if (subs != null) {
					String planId = subs.getPlanid();
					String status = subs.getStatus();

					if (!planId.equalsIgnoreCase("chum") && !planId.equalsIgnoreCase("vet-chat")
							&& (status.equalsIgnoreCase("ACTIVE") || status.equalsIgnoreCase("Payment Due"))) {

						String url2 = waggletxnsrv_url + "/v4.0/userfeature?cbplan=" + planId
								+ "&eventtype=subscription_created&cbemail=" + email + "&isupgrade=false&chargbeeid="
								+ user.getChargebeeid();
						System.out.println("url2 :" + url2);
						String resp2 = new Helper().httpPOSTRequest(url2, null, null);
						System.out.println("resp2 :" + resp2);

						JSONObject res = new JSONObject(resp2);
						res = res.getJSONObject("response");
						int enableStatus = res.getInt("Status");

						if (enableStatus == 1) {
							response.put("EnableStatus", 1);
							response.put("EnableMsg", "Successfully enabled user feature for " + email);

						} else {
							response.put("EnableStatus", 0);
							response.put("EnableMsg", "Failed to enable user feature for " + email);
						}

						response.put("Status", 1);
						response.put("Msg", "Reassign user feature completed");

					} else if (planId.equalsIgnoreCase("chum")) {

						response.put("Status", 0);
						response.put("Msg", "User currently in free plan");

					} else if (!status.equalsIgnoreCase("active")) {

						response.put("Status", 0);
						response.put("Msg", "User current subscription status is " + status);

					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "Subscription not found");
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid AuthKey/CBid");
			}

		} catch (Exception e) {
			log.error("Exception : reassignUserFeature :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occurred!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	// Used in web - kalai
	@RequestMapping(value = "v4.0/disableuserfeature", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse disableUserFeature(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("email") String email) {
		System.out.println();
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering disableUserFeature : auth key : " + auth);
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV3("authkey", auth);
				if (!email.equalsIgnoreCase(user.getEmail())) {
					response.put("Status", 0);
					response.put("Msg", "Invalid User");
					response.put("Error", "user email & authkey not matching");

					return response;
				}

			} catch (InvalidAuthoException e) {
				log.error("Invalid AuthKey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid User");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}

			if (user != null && email.equalsIgnoreCase(user.getEmail())) {

				String url1 = waggletxnsrv_url + "/v4.0/disableuserfeature?cbemail=" + email;
				System.out.println("url1 :" + url1);
				String resp1 = new Helper().httpPOSTRequest(url1, null, user.getAuthKey());
				System.out.println("resp1 :" + resp1);

				JSONObject res = new JSONObject(resp1);
				res = res.getJSONObject("response");
				int disableStatus = res.getInt("Status");

				if (disableStatus == 1) {
					response.put("Status", 1);
					response.put("Msg", "Successfully disabled for " + email);

				} else {
					response.put("Status", 0);
					response.put("Msg", "Failed to disable for" + email);

				}
			}

		} catch (Exception e) {
			log.error("Exception : removeDeleteRequest :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occurred!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	@RequestMapping(value = "/v3.0/otp", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getEmailVerificationOtp(@RequestParam("email") String email) {
		JResponse response = new JResponse();

		try {
			long otp = gatewayService.getOtpByEmail(email);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("OTP", otp);

		} catch (Exception e) {
			response.put("Msg", "Unable to retrieve OTP");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v3.0/invalidpacket", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getInvalidPacket(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam(value = "offset") long offset, @RequestParam(value = "limit") long limit,
			@RequestParam(value = "otype", defaultValue = "desc", required = false) String otype,
			@RequestParam(value = "from_date", required = false) String from_date,
			@RequestParam(value = "to_date", required = false) String to_date,
			@RequestParam(value = "type", required = false, defaultValue = "NA") String type,
			@RequestParam(value = "export", required = false, defaultValue = "false") boolean export) {
		log.info("Entered getInvalidPacket");

		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!!!");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for auth : " + autho);
				return response;
			}

			if (export)
				response = gatewayService.getInvalidPacketExport(from_date, to_date, type, response);
			else
				response = gatewayService.getInvalidPacket(otype, offset, limit, from_date, to_date, type, response);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting getInvalidPacket");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : getInvalidPacket : " + e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v3.0/invalidfieldmeid", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getInvalidFieldMeidCount(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam(value = "from_date", required = false) String from_date,
			@RequestParam(value = "to_date", required = false) String to_date) {
		log.info("Entered getInvalidPacket");

		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!!!");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for auth : " + autho);
				return response;
			}

			Map cnt = gatewayService.getInvalidFieldMeidCount(from_date, to_date);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("Count", cnt);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting getInvalidPacket");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : getInvalidPacket : " + e.getLocalizedMessage());
		}
		return response;
	}
	/*
	 * @RequestMapping(value = "v5.0/returncancelsubplan/", method =
	 * RequestMethod.POST, headers = "Accept=application/json")
	 * 
	 * @ResponseBody public JResponse CancelsubPlanReturnProduct(@RequestHeader
	 * HttpHeaders header,
	 * 
	 * @RequestParam(value = "userid", required = true) long userId,
	 * 
	 * @RequestParam(value = "meid", required = true) String meid) { String auth =
	 * header.getFirst("auth"); log.info(" Entered CancelsubPlanReturnProduct :" +
	 * userId); JResponse response = new JResponse(); boolean refund = false;
	 * 
	 * try { String basicAuth= header.getFirst("authorization");
	 * log.info(" Entered CancelsubPlanReturnProduct auth:" + basicAuth);
	 * 
	 * String deCodeAuth=new String(Base64.decodeBase64(basicAuth.substring(6)));
	 * String userName=deCodeAuth.split(":")[0]; String
	 * password=deCodeAuth.split(":")[1];
	 * 
	 * if (userName == null || password == null || !userName.equals(configUsername)
	 * || !password.equals(configPassword) ) { response.put("Status", 0);
	 * response.put("Msg", "Authentication Error"); response.put("Return Time",
	 * System.currentTimeMillis()); return response; }
	 * 
	 * response = cbService.cancelsubplanByreturn(userId, meid);
	 * 
	 * } catch (Exception e) { response.put("Status", 0); response.put("Msg",
	 * "Error occured"); response.put("Error", e.getLocalizedMessage());
	 * log.error("CancelsubPlanReturnProduct : " + e.getLocalizedMessage()); }
	 * response.put("Return Time", System.currentTimeMillis()); return response; }
	 */

	@RequestMapping(value = "v4.0/updateaccount/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateAccountDetails(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestBody JUserEmailUpdate juser) {
		String autho = header.getFirst("auth");
		log.info("Entered updateAccountDetails");
		JResponse response = new JResponse();
		try {
			UserV4 user = null;

			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.error("Invalid UserId :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}

			if (juser.getUsername() == null || juser.getUsername().trim().equals("") || juser.getNewEmail() == null
					|| juser.getNewEmail().trim().equals("")) {
				response.put("Status", 0);
				response.put("Msg", "Username or new email is empty!");
				return response;
			}

			if (!_helper.isValidEmail(juser.getNewEmail().trim())) {
				response.put("Status", 0);
				response.put("Msg", "Please enter valid new email!");
				return response;
			}

			long touser = userServiceV4.getUserByUNameOrEmailV4(juser.getNewEmail());
			if (touser > 0) {
				response.put("Status", 0);
				response.put("Msg", "To Email  already exist. Please enter alternate Email!");
				return response;
			}

			User usr = userService.getUserByUNameOrEmail(juser.getUsername());
			if (usr != null && user.getChargebeeid() != null) {
				userServiceV4.updateUserInChargebee(juser.getUsername(), juser.getNewEmail(), usr.getChargebeeid());
			} else {
				response.put("Status", 0);
				response.put("Msg", "From Email not exist. Please enter alternate Email!");
				return response;
			}

			gatewayService.updateuseremaildetails(juser.getUsername(), juser.getNewEmail());

			niomService.updateOrdermapEmailDetails(juser.getUsername(), juser.getNewEmail());

			response.put("Status", 1);
			response.put("Msg", "User account updated successfully");

		} catch (Exception e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;

	}

	@RequestMapping(value = "v5.0/getaskfeature", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAskFeature(@RequestHeader HttpHeaders header, @RequestParam(value = "offset") long offset,
			@RequestParam(value = "limit") long limit) {
		log.info("Entered into getaskfeature API...");
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		try {
			UserV4 user = null;

			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.error("Invalid UserId :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}
			response = userServiceV4.getAskFeature(offset, limit);
			return response;
		} catch (Exception e) {
			log.error("Exception in getAskFeature: " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Data");
			return response;
		}

	}

	@RequestMapping(value = "v5.0/updatesubscriptionfeature", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updatesubscriptionfeature(@RequestHeader HttpHeaders header,
			@RequestParam(value = "subId") String subId, @RequestParam(value = "gateway_id") long gateway_id) {
		JResponse response = new JResponse();
		log.info("Entered into updatesubscriptionfeature :: gateway_id : " + gateway_id + " :: subId : " + subId);
		try {
			String auth = header.getFirst("auth");
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Session, Please try again");
				log.error("Invalid authkey : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			Gateway gateway = gatewayService.getActiveGatewayByid(gateway_id);

			if (gateway == null || subId == null) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Gateway Id or Subscription Id , Please try again");
				return response;
			}

			long userId = gatewayService.getUserIdByGatewayId(gateway_id);

			if (userId != user.getId()) {
				response.put("Status", 0);
				response.put("Msg", "User Id mismatch , Please try again");
				return response;
			}

			AllProductSubscription sub = cbService.getProductSubscriptionBySubId(subId);

			JGatewayFeature gatewayFeature = crService.getGatewayFeatureById(gateway_id);

			if (sub != null && sub.getGateway_id() == 0 && gatewayFeature.getGateway_id() == 0
					&& gateway.getModel().getMonitor_type().getId() == sub.getMonitor_type()) {

				if (gateway.getModel().getMonitor_type().getId() == 1) {
					String msurl = txnservice_url + "/v4.0/gatewayfeaturewithmonitor?" + "cbplan=" + sub.getPlanId()
							+ "&chargbeeid=" + user.getChargebeeid()
							+ "&eventtype=subscription_created&isupgrade=false&cbemail=" + user.getEmail()
							+ "&gatewayid=" + gateway_id + "&subId=" + subId;
					log.info("Call txnservice gatewayfeature API :" + msurl);

					String msPlanRes = _helper.httpPOSTRequest(msurl, null, null);
					log.info("Plan Url update : " + msPlanRes);
				} else {

					String msurl = txnservice_url + "/v5.0/gatewayfeature";
					log.info("Call txnservice gatewayfeature API :" + msurl);

					PlanToPeriod planToPeriod = gatewayService.getPlanAndPeriodId(sub.getPlanId());
					Map<String, Object> postParam = new HashMap<>();
					postParam.put("planid", planToPeriod.getPlan_id().getId());
					postParam.put("cbsubid", subId);
					postParam.put("chargebeeid", user.getChargebeeid());
					postParam.put("periodid", planToPeriod.getSub_period_id().getId());
					postParam.put("gatewayid", Arrays.asList(gateway_id));

					JSONObject bodyJson = new JSONObject(postParam);

					// Response from Txn_Service
					String msPlanRes = _helper.httpPOSTRequestFeature(msurl, bodyJson.toString(), null);
					log.info("Plan Url update : " + msPlanRes);
				}
				cbService.updategatewaybysubcriptionId(gateway_id, subId);

				response.put("Status", 1);
				response.put("Msg", "Gateway updated successfully");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Gateway updation failed!");
			}

		} catch (Exception e) {
			log.error("Error in updatesubscriptionfeature :: Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Session, Please try again");
			return response;
		}
		return response;
	}

	/**
	 * API endpoint to enable or disable Bluetooth update for a specific gateway
	 * device.
	 *
	 * @param header    the HTTP headers, containing the "auth" authentication key
	 * @param gatewayId the ID of the gateway/device for which the Bluetooth update
	 *                  setting will be changed
	 * @param bleEnable 1 to enable Bluetooth update, 0 to disable
	 * @return ResponseEntity containing a JResponse object with the status and
	 *         message
	 */
	@PostMapping(value = "v5.0/bleversionupdate", headers = "Accept=application/json")
	@ResponseBody
	public ResponseEntity<JResponse> enableOrDisableBleVersionUpdate(@RequestHeader HttpHeaders header,
			@RequestParam(value = "gateway_id") String gatewayId, @RequestParam(value = "ble_enable") int bleEnable) {

		log.info("Entered into enableOrDisableBleVersionUpdate API...");
		String auth = header.getFirst("auth");
		JResponse response = new JResponse();
		try {
			try {
				userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.error("Invalid UserId : {}", e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
				response.put("Error", e.getLocalizedMessage());

				return new ResponseEntity<>(response, HttpStatus.UNAUTHORIZED);
			}

			boolean result = gatewayService.enableOrDisableBtUpdate(gatewayId, bleEnable);

			if (result) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed to update");
			}
		} catch (Exception e) {
			log.error("Exception in enableOrDisableBleVersionUpdate : {}", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Data");
		}

		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	/**
	 * API endpoint to enable or disable fota Bluetooth version update for a
	 * specific fota version mapping id.
	 *
	 * @param header        the HTTP headers, containing the "auth" authentication
	 *                      key
	 * @param fotaVersionId the ID of the fota version mapping for which the fota
	 *                      Bluetooth version update setting will be changed
	 * @param enable        1 to enable fota version mapping update, 0 to disable
	 * @return ResponseEntity containing a JResponse object with the status and
	 *         message
	 */
	@PostMapping(value = "v5.0/enablefotaversionmapping", headers = "Accept=application/json")
	@ResponseBody
	public ResponseEntity<JResponse> enableOrDisableFotaVersionMapping(@RequestHeader HttpHeaders header,
			@RequestParam(value = "id") String fotaVersionId, @RequestParam(value = "enable") int enable,
			@RequestParam(value = "curr_ver") String currVersion) {

		log.info("Entered into enableOrDisableFotaVersionMapping API...");
		String auth = header.getFirst("auth");
		JResponse response = new JResponse();
		try {
			try {
				userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.error("Invalid UserId : {}", e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
				response.put("Error", e.getLocalizedMessage());

				return new ResponseEntity<>(response, HttpStatus.UNAUTHORIZED);
			}

			boolean result = gatewayService.enableOrDisableFotaVersionMapping(fotaVersionId, enable, currVersion);

			if (result) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed to update");
			}
		} catch (Exception e) {
			log.error("Exception in enableOrDisableFotaVersionMapping : {}", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Data");
		}

		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@PostMapping(value = "v5.0/meariversionupdate", headers = "Accept=application/json")
	@ResponseBody
	public ResponseEntity<JResponse> enableOrDisablemeariversionupdate(@RequestHeader HttpHeaders header,
			@RequestParam(value = "gateway_id") String gatewayId,
			@RequestParam(value = "update_enable") int bleEnable) {

		log.info("Entered into enableOrDisablemeariversionupdate API...");
		String auth = header.getFirst("auth");
		JResponse response = new JResponse();
		try {
			try {
				userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.error("Invalid UserId : {}", e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
				response.put("Error", e.getLocalizedMessage());

				return new ResponseEntity<>(response, HttpStatus.UNAUTHORIZED);
			}

			boolean result = gatewayService.enableOrDisableMeariUpdate(gatewayId, bleEnable);

			if (result) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed to update");
			}
		} catch (Exception e) {
			log.error("Exception in enableOrDisablemeariversionupdate : {}", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Data");
		}

		return new ResponseEntity<>(response, HttpStatus.OK);
	}


	@GetMapping(value = "v3.0/listgatewaytofeature/", headers = "Accept=application/json")
	@ResponseBody
	public JResponse listGatewaytoFeature(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam long gatewayId) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.info("Invalid Authkey : " + autho);
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			if (gatewayId > 0) {
				List<GatewaytoFeature> gatewayFeatureList = crService.listGatewaytoFeature(gatewayId);

				response.put("gatewayFeatureList", gatewayFeatureList);
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Gateway not found");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in list GatewaytoFeature");
			response.put("Error", e.getLocalizedMessage());
			log.error("list GatewaytoFeature : " + e.getLocalizedMessage());
		}

		return response;
	}



	@PostMapping(value = "v3.0/creategatewaytofeature/", headers = "Accept=application/json")
	@ResponseBody
	public JResponse createGatewaytoFeature(@RequestHeader HttpHeaders header, Authentication authentication,
										 @RequestBody List<GatewaytoFeature> gatewayFeatureList) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		boolean status = false;
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (Exception e) {
				log.error("createGatewaytoFeature:user by auth : " + e.getMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Gateway");
				response.put("Error", e.getLocalizedMessage());
			}

			long userid = user.getId();

			for (int i = 0; i < gatewayFeatureList.size(); i++) {
				ResetType reset = crService.viewResetType(gatewayFeatureList.get(i).getResettype());
				Feature feature = crService.viewFeature(gatewayFeatureList.get(i).getFeatureid());
				GatewaytoFeature gatewaytoFeature = null;

				gatewaytoFeature = crService.getGFByGatewayFeature(gatewayFeatureList.get(i).getGateway_id(), feature.getId());

				if (gatewaytoFeature == null)
					gatewaytoFeature = new GatewaytoFeature();

				gatewaytoFeature.setFeature_id(feature);
				gatewaytoFeature.setGateway_id(gatewayFeatureList.get(i).getGateway_id());
				gatewaytoFeature.setResettype_id(reset);
				gatewaytoFeature.setTxn_limit(gatewayFeatureList.get(i).getTxn_limit());
				gatewaytoFeature.setExtra_txn_limit(gatewayFeatureList.get(i).getExtra_txn_limit());
				gatewaytoFeature.setEnable(gatewayFeatureList.get(i).isEnable());
				gatewaytoFeature.setRemaining_limit(gatewayFeatureList.get(i).getRemaining_limit());
				gatewaytoFeature.setEnable(gatewayFeatureList.get(i).isEnable());
				gatewaytoFeature.setLast_reset_on(gatewayFeatureList.get(i).getLast_reset_on());
				gatewaytoFeature.setFeature_code(gatewayFeatureList.get(i).getFeature_code());
				gatewaytoFeature.setAddon_limit(gatewayFeatureList.get(i).getAddon_limit());
				gatewaytoFeature.setUnlimited_cr(gatewayFeatureList.get(i).isUnlimited_cr());

				status = crService.createGatewaytoFeature(gatewaytoFeature);
			}
			response.put("Status", status);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", 0);
			response.put("Msg", "Selected combination already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("createGatewaytoFeature : " + ex.getLocalizedMessage());
		} catch (Exception e) {

			response.put("Status", status);
			response.put("Msg", "creation of UsertoFeature failed");
			response.put("Error", e.getLocalizedMessage());
			log.error(" create GatewaytoFeature : " + e.getLocalizedMessage());
		}

		return response;
	}


	@PostMapping(value = "v3.0/deletegatewaytofeature/", headers = "Accept=application/json")
	@ResponseBody
	public JResponse deletegatewaytoFeature(@RequestParam long id) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			status = crService.deleteGatewaytoFeature(id);

			response.put("Status", status);
			if (status == true)
				response.put("Msg", "Success");
			else
				response.put("Msg", "Selected data not exists");
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "Error occured in delete. Invalid GatewaytoFeature id");
			response.put("Error", e.getLocalizedMessage());
			log.error("delete gatewaytoFeature : " + e.getLocalizedMessage());
		}

		return response;
	}


	/**
	 * Retrieves BLE FOTA (Firmware Over The Air) debug information for a specific gateway.
	 *
	 * @param gatewayId      The ID of the gateway for which BLE FOTA debug information is requested.
	 * @param authentication The authentication object for user validation.
	 * @param header         The HTTP headers containing the "auth" key.
	 * @return               A response containing the BLE FOTA debug information.
	 */
	@GetMapping(value = "v5.0/blefotadebug", headers = "Accept=application/json")
	public ResponseEntity<JResponse> getBleFotaDebug(@RequestParam(value = "gatewayid", required = false, defaultValue = "0") long gatewayId,
													 Authentication authentication, @RequestHeader HttpHeaders header) {

		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering getBleFotaDebug : {}", auth);

		try {
			try {
				userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.error("Invalid UserId : {}", e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
				response.put("Error", e.getLocalizedMessage());

				return new ResponseEntity<>(response, HttpStatus.UNAUTHORIZED);
			}

			List<JBleFotaDebug> bleFotaDebugList = gatewayService.getBleFotaDebug(gatewayId);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("bleFotaDebugList", bleFotaDebugList);

			return ResponseEntity.ok(response);
		} catch (Exception e) {
			log.error("Error in getBleFotaDebug: {}", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error occurred while getting the ble fota debug");

			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
		}
	}

	/**
	 * Saves BLE FOTA (Firmware Over The Air) command for a specific gateway.
	 *
	 * @param authentication The authentication object for user validation.
	 * @param header         The HTTP headers containing the "auth" key.
	 * @param bleFotaCommand The BLE FOTA command to be saved.
	 * @return               A response containing the status and message of the operation.
	 */
	@PostMapping(value = "v5.0/blefotacommand", headers = "Accept=application/json")
	public ResponseEntity<JResponse> saveBleFotaCommand(Authentication authentication, @RequestHeader HttpHeaders header,
														@RequestBody BleFotaCommand bleFotaCommand) {

		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering saveBleFotaCommand : {}", auth);

		try {
			try {
				userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.error("Invalid UserId : {}", e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
				response.put("Error", e.getLocalizedMessage());

				return new ResponseEntity<>(response, HttpStatus.UNAUTHORIZED);
			}

			boolean isInserted = gatewayService.saveBleFotaCommand(bleFotaCommand);

			if(!isInserted) {
				response.put("Status", 0);
				response.put("Msg", "Failed to save the ble fota command");
				return ResponseEntity.ok(response);
			}

			response.put("Status", 1);
			response.put("Msg", "Success");

			return ResponseEntity.ok(response);
		} catch (Exception e) {
			log.error("Error in saveBleFotaCommand: {}", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error occurred while saving the ble fota commands");

			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
		}
	}

}
