package com.nimble.irisservices.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

@Entity
@Table(name="vetchat_freetrial_content")
public class JVetChatFreeTrialContent {

    @Id
    @Column(name="id")
    @GenericGenerator(name="gen",strategy="identity")
    @GeneratedValue(generator="gen")
    private long id ;

    @Column(name="content")
    private String content ;

    @Column(name="tittle")
    private String tittle;

    @Column(name="enable")
    private boolean enable = false;

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public String getTittle() {
        return tittle;
    }

    public void setTittle(String tittle) {
        this.tittle = tittle;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }
}
