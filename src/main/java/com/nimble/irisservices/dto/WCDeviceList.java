package com.nimble.irisservices.dto;

import java.util.ArrayList;
import java.util.List;

import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.entity.WifiInfo;

public class WCDeviceList {

	public String meid;
	public String macid;
	public String gatewayname;
	public String qrcode;
	public int remainingwificount = -1;
	public int totalwificount = -1;
	public long gatewayid;
	public long userid;
	
	private long monitortype = 0;
	
	public String manualconnect = "NA";
	public String imageurl = "NA";

	public boolean is_wifidelete = false;

	List<WifiInfo> wifiinfolist = new ArrayList<WifiInfo>();

	public String wifi_ssid = "NA";

	public String wifi_password = "NA";
	
	public boolean is_wifi_connection = true;

	public boolean is_manual_connection = false;

	public boolean is_static_wifi = false;
	
	public String wifi_pairing_img = "https://tempcubeapi-images.s3.us-west-2.amazonaws.com/mobileappimages/ble_pairing_default.png";
	
	public boolean warranty_claimed;
	
	public boolean purchased_from_others;
	
	public String warranty_claim_msg = "NA";
	
	private boolean streaming = false;
	
	private boolean night_vision = false;
	
	private boolean mic_status = false;
	
	private int speaker_volume = 100;
	
	private String quality = "hd";
	
	private int device_angle = 0;
	
	private int min_rotation = 0;
	
	private int max_rotation = 0;
	
	private boolean auto_update = false;
	
	private String fota_status = "NA";
	
	private String current_fota_version = "NA";
	
	private boolean auto_night_vision = false;
	
	private boolean show_hd = false;
	
	private String device_for = "NA";
	
	private boolean is_sub_user_gateway = false;
	
	private boolean user_time_zone = false;
	
	private boolean is_motion_detection = false;
	
	private boolean show_motion_detection = false;
	
	private boolean show_stored_video = false;
	
	private boolean is_online = false;

	private boolean show_debug = false;
	
	private boolean is_debug = false;
	
	private boolean livetracking_status = false;
	
	private boolean livetracking_person = false;
	
	private boolean livetracking_boundingbox = false;
	
	private boolean livetracking_rotation = false;
	
	private boolean show_live_tracking = false;
	
	private JPetprofileFlutter pet_profile = null;
	
	private boolean show_barking_alert = false;
	
	private boolean barking_alert = false;
	
	private boolean motion_detection_person = false;
	
	private boolean notification = false;
	
	private boolean screen_flip = false;
	
	private int night_vision_mode = 1;
	
	private boolean noise_detection = false;
	
	private String detection_sensitivity = "Medium";
	
	private String serial_number = "NA";
	
	private boolean continuous_playback = true;
	
	private boolean show_noise_detection = false;
	
	private boolean show_motion_detection_person = false;
	
	private boolean show_continuous_playback = false;
	
	private boolean show_screen_flip = false;
	
	private boolean show_activate = false;
	
	private boolean sound_alert = false;
	
	private long planId = 0;
	
	private String sub_id = "NA";
	
	private boolean is_freePlan = true;
	
	private long periodId = 0;
	
	private String meariSubKey = "NA";
	
	private String title="";
	private String desc="Are you worried about your pet's well-being? Try our Bite Plan and keep your pets healthy.";
	private String btn="Say Yes to Pet Healthy";
	
	private String paid_title="";
	private String paid_desc="Are you worried about your pet's well-being? Try our Bite Plan and keep your pets healthy.";
	private String paid_btn="Say Yes to Pet Healthy";
	
	private String noise_sensitivity = "Medium";
	
	private int recording_time = 60;
	
	private int alarm_interval = 60;
	
	private boolean event_recording;
	
	private boolean pet_detection;
	
	private long bark_alert_count = 0;
	
	private long motion_detected_count = 0;
	
	private long naughtiness = 0;
	
	private boolean temperature_model = false;
	
	private boolean motion_detection_dog = false;
	
	private boolean motion_detection_cat = false;
	
	private boolean meowing_alert = false;
	
	private JTemperatureInfo temperature_info = new JTemperatureInfo(); 
	
	private ArrayList<String> alert_email_id = new ArrayList<>(); 
	
	private ArrayList<JMobile> alert_mobile_no = new ArrayList<>();
	
	private String web_rtc_application = "NA";
	
	private String web_rtc_stream_name = "NA";
	
	private boolean throwing_beep = false;
	
	private boolean temp_alert = false;

	private boolean battery_alert = false;

	private boolean on_battery_alert = false;

	private boolean humidity_alert = false;

	private boolean dnr_alert = false;

	private boolean recovery_alert = false;
	
	private boolean is_humidity = false;
	
	private boolean is_battery_mode = false;
	
	private boolean isWebRTC = false;
	
	private boolean email_alert = false;
	
	private boolean sms_alert = false;
	
	private long temp_frequency_value = 3600;
	
	private long power_loss_frequency_value = 3600;
	
	private long motion_alert_frequency_value = 3600;
	
	private long sound_alert_frequency_value = 3600;
	
	private boolean running_on_battery = false;
	
	private String product_model_name = IrisservicesConstants.WAGGLE_CAM_PRO_PLUS;
	
	private String start_video_recording_url = "NA";
	
	private String start_video_recording_url_body = "NA";
	
	private String stop_video_recording_url = "NA";
	
	private boolean show_sd = false;
	
	private boolean battery_available = false;
	
	private boolean vehicle_detection = false;
	
	private String device_unique_id = "NA";
	
	private int hangup_battery_percentage = 0;
	
	private String stream_url = "NA";
	
	private int treat_count = 0;
	
	private boolean aitreat_onoff = false;
	
	private int aitreat_interval = 120;
	
	private int aitreat_maxcount = 10;
	
	private boolean auto_tracking = false;
	
	private boolean showwatermark = true;
	
	private boolean show_vehicle_detection = false;
	
	private boolean show_operation_mode = false;
	
	private boolean show_lighting_settings = false;
	
	private int operation_mode = 0;
	
	private int light_setting = 0;
	
	private boolean is_configured = false;
	
	private String videoencoding_format = "H265";
	
	private int flicker_level = 0;
	
	private boolean smart_light_detection = false;
	
	private boolean is_device_reset = false;

	private boolean show_smart_light_setting = false;

	private boolean is_microphone_enabled  = true;

	private boolean is_recordvideo_enabled = true;

	private boolean is_speaker_enabled = true;

	private boolean human_detection_day = false;

	private boolean human_detection_night = false;

	private boolean is_auto_sensitive_enabled = false;

	private boolean meari_update_popup = false;
	
	public WCDeviceList() {
		super();
	}

	public WCDeviceList(String meid, String gatewayname, String qrcode,
			int remainingwificount, int totalwificount, long gatewayId, long userid, List<WifiInfo> wifiinfolist,
			boolean is_wifidelete, boolean is_wifi_connection, String wifi_pairing_img) {
		super();
		this.meid = meid;
		this.gatewayname = gatewayname;
		this.qrcode = qrcode;
		this.remainingwificount = remainingwificount;
		this.totalwificount = totalwificount;
		this.gatewayid = gatewayId;
		this.userid = userid;
		this.wifiinfolist = wifiinfolist;
		this.is_wifidelete = is_wifidelete;
		this.is_wifi_connection = is_wifi_connection;
		this.wifi_pairing_img = wifi_pairing_img;
	}

	public int getRemainingwificount() {
		return remainingwificount;
	}

	public void setRemainingwificount(int remainingwificount) {
		this.remainingwificount = remainingwificount;
	}

	public long getGatewayid() {
		return gatewayid;
	}

	public void setGatewayid(long gatewayid) {
		this.gatewayid = gatewayid;
	}

	public long getUserid() {
		return userid;
	}

	public void setUserid(long userid) {
		this.userid = userid;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public String getGatewayname() {
		return gatewayname;
	}

	public void setGatewayname(String gatewayname) {
		this.gatewayname = gatewayname;
	}

	public String getQrcode() {
		return qrcode;
	}

	public void setQrcode(String qrcode) {
		this.qrcode = qrcode;
	}

	public int getTotalwificount() {
		return totalwificount;
	}

	public void setTotalwificount(int totalwificount) {
		this.totalwificount = totalwificount;
	}

	public List<WifiInfo> getWifiinfolist() {
		return wifiinfolist;
	}

	public void setWifiinfolist(List<WifiInfo> wifiinfolist) {
		this.wifiinfolist = wifiinfolist;
	}

	public String getManualconnect() {
		return manualconnect;
	}

	public void setManualconnect(String manualconnect) {
		this.manualconnect = manualconnect;
	}

	public String getImageurl() {
		return imageurl;
	}

	public void setImageurl(String imageurl) {
		this.imageurl = imageurl;
	}

	public boolean isIs_wifidelete() {
		return is_wifidelete;
	}

	public void setIs_wifidelete(boolean is_wifidelete) {
		this.is_wifidelete = is_wifidelete;
	}

	public String getWifi_ssid() {
		return wifi_ssid;
	}

	public void setWifi_ssid(String wifi_ssid) {
		this.wifi_ssid = wifi_ssid;
	}

	public String getWifi_password() {
		return wifi_password;
	}

	public void setWifi_password(String wifi_password) {
		this.wifi_password = wifi_password;
	}

	public boolean isIs_manual_connection() {
		return is_manual_connection;
	}

	public void setIs_manual_connection(boolean is_manual_connection) {
		this.is_manual_connection = is_manual_connection;
	}

	public boolean isIs_static_wifi() {
		return is_static_wifi;
	}

	public void setIs_static_wifi(boolean is_static_wifi) {
		this.is_static_wifi = is_static_wifi;
	}	
	
	public boolean isIs_wifi_connection() {
		return is_wifi_connection;
	}

	public void setIs_wifi_connection(boolean is_wifi_connection) {
		this.is_wifi_connection = is_wifi_connection;
	}

	public String getWifi_pairing_img() {
		return wifi_pairing_img;
	}

	public void setWifi_pairing_img(String wifi_pairing_img) {
		this.wifi_pairing_img = wifi_pairing_img;
	}

	public boolean isWarranty_claimed() {
		return warranty_claimed;
	}

	public void setWarranty_claimed(boolean warranty_claimed) {
		this.warranty_claimed = warranty_claimed;
	}

	public boolean isPurchased_from_others() {
		return purchased_from_others;
	}

	public void setPurchased_from_others(boolean purchased_from_others) {
		this.purchased_from_others = purchased_from_others;
	}

	public String getWarranty_claim_msg() {
		return warranty_claim_msg;
	}

	public void setWarranty_claim_msg(String warranty_claim_msg) {
		this.warranty_claim_msg = warranty_claim_msg;
	}

	public String getMacid() {
		return macid;
	}

	public void setMacid(String macid) {
		this.macid = macid;
	}

	public boolean isStreaming() {
		return streaming;
	}

	public void setStreaming(boolean streaming) {
		this.streaming = streaming;
	}

	public boolean isNight_vision() {
		return night_vision;
	}

	public void setNight_vision(boolean night_vision) {
		this.night_vision = night_vision;
	}

	public boolean isMic_status() {
		return mic_status;
	}

	public void setMic_status(boolean mic_status) {
		this.mic_status = mic_status;
	}

	public int getSpeaker_volume() {
		return speaker_volume;
	}

	public void setSpeaker_volume(int speaker_volume) {
		this.speaker_volume = speaker_volume;
	}

	public String getQuality() {
		return quality;
	}

	public void setQuality(String quality) {
		this.quality = quality;
	}

	public int getDevice_angle() {
		return device_angle;
	}

	public void setDevice_angle(int device_angle) {
		this.device_angle = device_angle;
	}

	public int getMin_rotation() {
		return min_rotation;
	}

	public void setMin_rotation(int min_rotation) {
		this.min_rotation = min_rotation;
	}

	public int getMax_rotation() {
		return max_rotation;
	}

	public void setMax_rotation(int max_rotation) {
		this.max_rotation = max_rotation;
	}

	public boolean isAuto_update() {
		return auto_update;
	}

	public void setAuto_update(boolean auto_update) {
		this.auto_update = auto_update;
	}

	public String getFota_status() {
		return fota_status;
	}

	public void setFota_status(String fota_status) {
		this.fota_status = fota_status;
	}

	public String getCurrent_fota_version() {
		return current_fota_version;
	}

	public void setCurrent_fota_version(String current_fota_version) {
		this.current_fota_version = current_fota_version;
	}

	public boolean isAuto_night_vision() {
		return auto_night_vision;
	}

	public void setAuto_night_vision(boolean auto_night_vision) {
		this.auto_night_vision = auto_night_vision;
	}

	public boolean isShow_hd() {
		return show_hd;
	}

	public void setShow_hd(boolean show_hd) {
		this.show_hd = show_hd;
	}

	public String getDevice_for() {
		return device_for;
	}

	public void setDevice_for(String device_for) {
		this.device_for = device_for;
	}

	public boolean isIs_sub_user_gateway() {
		return is_sub_user_gateway;
	}

	public void setIs_sub_user_gateway(boolean is_sub_user_gateway) {
		this.is_sub_user_gateway = is_sub_user_gateway;
	}

	public boolean isUser_time_zone() {
		return user_time_zone;
	}

	public void setUser_time_zone(boolean user_time_zone) {
		this.user_time_zone = user_time_zone;
	}

	public boolean isIs_motion_detection() {
		return is_motion_detection;
	}

	public void setIs_motion_detection(boolean is_motion_detection) {
		this.is_motion_detection = is_motion_detection;
	}

	public boolean isShow_motion_detection() {
		return show_motion_detection;
	}

	public void setShow_motion_detection(boolean show_motion_detection) {
		this.show_motion_detection = show_motion_detection;
	}

	public boolean isShow_stored_video() {
		return show_stored_video;
	}

	public void setShow_stored_video(boolean show_stored_video) {
		this.show_stored_video = show_stored_video;
	}

	public boolean isIs_online() {
		return is_online;
	}

	public void setis_online(boolean is_online) {
		this.is_online = is_online;
	}
	public boolean isShow_debug() {
		return show_debug;
	}

	public void setShow_debug(boolean show_debug) {
		this.show_debug = show_debug;
	}

	public boolean isIs_debug() {
		return is_debug;
	}

	public void setIs_debug(boolean is_debug) {
		this.is_debug = is_debug;
	}

	public JPetprofileFlutter getPet_profile() {
		return pet_profile;
	}

	public void setPet_profile(JPetprofileFlutter pet_profile) {
		this.pet_profile = pet_profile;
	}

	public long getMonitortype() {
		return monitortype;
	}

	public void setMonitortype(long monitortype) {
		this.monitortype = monitortype;
	}

	public boolean isLivetracking_status() {
		return livetracking_status;
	}

	public void setLivetracking_status(boolean livetracking_status) {
		this.livetracking_status = livetracking_status;
	}

	public boolean isLivetracking_person() {
		return livetracking_person;
	}

	public void setLivetracking_person(boolean livetracking_person) {
		this.livetracking_person = livetracking_person;
	}

	public boolean isLivetracking_boundingbox() {
		return livetracking_boundingbox;
	}

	public void setLivetracking_boundingbox(boolean livetracking_boundingbox) {
		this.livetracking_boundingbox = livetracking_boundingbox;
	}

	public boolean isLivetracking_rotation() {
		return livetracking_rotation;
	}

	public void setLivetracking_rotation(boolean livetracking_rotation) {
		this.livetracking_rotation = livetracking_rotation;
	}

	public boolean isShow_live_tracking() {
		return show_live_tracking;
	}

	public void setShow_live_tracking(boolean show_live_tracking) {
		this.show_live_tracking = show_live_tracking;
	}

	public void setIs_online(boolean is_online) {
		this.is_online = is_online;
	}

	public boolean isShow_barking_alert() {
		return show_barking_alert;
	}

	public void setShow_barking_alert(boolean show_barking_alert) {
		this.show_barking_alert = show_barking_alert;
	}

	public boolean isBarking_alert() {
		return barking_alert;
	}

	public void setBarking_alert(boolean barking_alert) {
		this.barking_alert = barking_alert;
	}

	public boolean isMotion_detection_person() {
		return motion_detection_person;
	}

	public void setMotion_detection_person(boolean motion_detection_person) {
		this.motion_detection_person = motion_detection_person;
	}

	public boolean isNotification() {
		return notification;
	}

	public void setNotification(boolean notification) {
		this.notification = notification;
	}

	public boolean isScreen_flip() {
		return screen_flip;
	}

	public void setScreen_flip(boolean screen_flip) {
		this.screen_flip = screen_flip;
	}

	public int getNight_vision_mode() {
		return night_vision_mode;
	}

	public void setNight_vision_mode(int night_vision_mode) {
		this.night_vision_mode = night_vision_mode;
	}

	public boolean isNoise_detection() {
		return noise_detection;
	}

	public void setNoise_detection(boolean noise_detection) {
		this.noise_detection = noise_detection;
	}

	public String getDetection_sensitivity() {
		return detection_sensitivity;
	}

	public void setDetection_sensitivity(String detection_sensitivity) {
		this.detection_sensitivity = detection_sensitivity;
	}

	public String getSerial_number() {
		return serial_number;
	}

	public void setSerial_number(String serial_number) {
		this.serial_number = serial_number;
	}

	public boolean isContinuous_playback() {
		return continuous_playback;
	}

	public void setContinuous_playback(boolean continuous_playback) {
		this.continuous_playback = continuous_playback;
	}

	public boolean isShow_noise_detection() {
		return show_noise_detection;
	}

	public void setShow_noise_detection(boolean show_noise_detection) {
		this.show_noise_detection = show_noise_detection;
	}

	public boolean isShow_motion_detection_person() {
		return show_motion_detection_person;
	}

	public void setShow_motion_detection_person(boolean show_motion_detection_person) {
		this.show_motion_detection_person = show_motion_detection_person;
	}

	public boolean isShow_continuous_playback() {
		return show_continuous_playback;
	}

	public void setShow_continuous_playback(boolean show_continuous_playback) {
		this.show_continuous_playback = show_continuous_playback;
	}

	public boolean isShow_screen_flip() {
		return show_screen_flip;
	}

	public void setShow_screen_flip(boolean show_screen_flip) {
		this.show_screen_flip = show_screen_flip;
	}

	public boolean isShow_activate() {
		return show_activate;
	}

	public void setShow_activate(boolean show_activate) {
		this.show_activate = show_activate;
	}

	public long getPlanId() {
		return planId;
	}

	public void setPlanId(long planId) {
		this.planId = planId;
	}

	public String getSub_id() {
		return sub_id;
	}

	public void setSub_id(String sub_id) {
		this.sub_id = sub_id;
	}

	public boolean isIs_freePlan() {
		return is_freePlan;
	}

	public void setIs_freePlan(boolean is_freePlan) {
		this.is_freePlan = is_freePlan;
	}

	public long getPeriodId() {
		return periodId;
	}

	public void setPeriodId(long periodId) {
		this.periodId = periodId;
	}

	public String getMeariSubKey() {
		return meariSubKey;
	}

	public void setMeariSubKey(String meariSubKey) {
		this.meariSubKey = meariSubKey;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public String getBtn() {
		return btn;
	}

	public void setBtn(String btn) {
		this.btn = btn;
	}

	public String getPaid_title() {
		return paid_title;
	}

	public void setPaid_title(String paid_title) {
		this.paid_title = paid_title;
	}

	public String getPaid_desc() {
		return paid_desc;
	}

	public void setPaid_desc(String paid_desc) {
		this.paid_desc = paid_desc;
	}

	public String getPaid_btn() {
		return paid_btn;
	}

	public void setPaid_btn(String paid_btn) {
		this.paid_btn = paid_btn;
	}

	public String getNoise_sensitivity() {
		return noise_sensitivity;
	}

	public void setNoise_sensitivity(String noise_sensitivity) {
		this.noise_sensitivity = noise_sensitivity;
	}

	public int getRecording_time() {
		return recording_time;
	}

	public void setRecording_time(int recording_time) {
		this.recording_time = recording_time;
	}

	public int getAlarm_interval() {
		return alarm_interval;
	}

	public void setAlarm_interval(int alarm_interval) {
		this.alarm_interval = alarm_interval;
	}

	public boolean isEvent_recording() {
		return event_recording;
	}

	public void setEvent_recording(boolean event_recording) {
		this.event_recording = event_recording;
	}

	public boolean isPet_detection() {
		return pet_detection;
	}

	public void setPet_detection(boolean pet_detection) {
		this.pet_detection = pet_detection;
	}

	public long getBark_alert_count() {
		return bark_alert_count;
	}

	public void setBark_alert_count(long bark_alert_count) {
		this.bark_alert_count = bark_alert_count;
	}

	public long getMotion_detected_count() {
		return motion_detected_count;
	}

	public void setMotion_detected_count(long motion_detected_count) {
		this.motion_detected_count = motion_detected_count;
	}

	public long getNaughtiness() {
		return naughtiness;
	}

	public void setNaughtiness(long naughtiness) {
		this.naughtiness = naughtiness;
	}

	public JTemperatureInfo getTemperature_info() {
		return temperature_info;
	}

	public void setTemperature_info(JTemperatureInfo temperature_info) {
		this.temperature_info = temperature_info;
	}

	public boolean isTemperature_model() {
		return temperature_model;
	}

	public void setTemperature_model(boolean temperature_model) {
		this.temperature_model = temperature_model;
	}

	public boolean isMotion_detection_dog() {
		return motion_detection_dog;
	}

	public void setMotion_detection_dog(boolean motion_detection_dog) {
		this.motion_detection_dog = motion_detection_dog;
	}

	public boolean isMotion_detection_cat() {
		return motion_detection_cat;
	}

	public void setMotion_detection_cat(boolean motion_detection_cat) {
		this.motion_detection_cat = motion_detection_cat;
	}

	public boolean isSound_alert() {
		return sound_alert;
	}

	public void setSound_alert(boolean sound_alert) {
		this.sound_alert = sound_alert;
	}

	public boolean isMeowing_alert() {
		return meowing_alert;
	}

	public void setMeowing_alert(boolean meowing_alert) {
		this.meowing_alert = meowing_alert;
	}

	public ArrayList<String> getAlert_email_id() {
		return alert_email_id;
	}

	public void setAlert_email_id(ArrayList<String> alert_email_id) {
		this.alert_email_id = alert_email_id;
	}

	public ArrayList<JMobile> getAlert_mobile_no() {
		return alert_mobile_no;
	}

	public void setAlert_mobile_no(ArrayList<JMobile> alert_mobile_no) {
		this.alert_mobile_no = alert_mobile_no;
	}

	public String getWeb_rtc_application() {
		return web_rtc_application;
	}

	public void setWeb_rtc_application(String web_rtc_application) {
		this.web_rtc_application = web_rtc_application;
	}

	public String getWeb_rtc_stream_name() {
		return web_rtc_stream_name;
	}

	public void setWeb_rtc_stream_name(String web_rtc_stream_name) {
		this.web_rtc_stream_name = web_rtc_stream_name;
	}

	public boolean isThrowing_beep() {
		return throwing_beep;
	}

	public void setThrowing_beep(boolean throwing_beep) {
		this.throwing_beep = throwing_beep;
	}

	public boolean isTemp_alert() {
		return temp_alert;
	}

	public void setTemp_alert(boolean temp_alert) {
		this.temp_alert = temp_alert;
	}

	public boolean isBattery_alert() {
		return battery_alert;
	}

	public void setBattery_alert(boolean battery_alert) {
		this.battery_alert = battery_alert;
	}

	public boolean isOn_battery_alert() {
		return on_battery_alert;
	}

	public void setOn_battery_alert(boolean on_battery_alert) {
		this.on_battery_alert = on_battery_alert;
	}

	public boolean isHumidity_alert() {
		return humidity_alert;
	}

	public void setHumidity_alert(boolean humidity_alert) {
		this.humidity_alert = humidity_alert;
	}

	public boolean isDnr_alert() {
		return dnr_alert;
	}

	public void setDnr_alert(boolean dnr_alert) {
		this.dnr_alert = dnr_alert;
	}

	public boolean isRecovery_alert() {
		return recovery_alert;
	}

	public void setRecovery_alert(boolean recovery_alert) {
		this.recovery_alert = recovery_alert;
	}

	public boolean isIs_humidity() {
		return is_humidity;
	}

	public void setIs_humidity(boolean is_humidity) {
		this.is_humidity = is_humidity;
	}

	public boolean isIs_battery_mode() {
		return is_battery_mode;
	}

	public void setIs_battery_mode(boolean is_battery_mode) {
		this.is_battery_mode = is_battery_mode;
	}

	public boolean isWebRTC() {
		return isWebRTC;
	}

	public void setWebRTC(boolean isWebRTC) {
		this.isWebRTC = isWebRTC;
	}

	public boolean isEmail_alert() {
		return email_alert;
	}

	public void setEmail_alert(boolean email_alert) {
		this.email_alert = email_alert;
	}

	public boolean isSms_alert() {
		return sms_alert;
	}

	public void setSms_alert(boolean sms_alert) {
		this.sms_alert = sms_alert;
	}

	public long getTemp_frequency_value() {
		return temp_frequency_value;
	}

	public void setTemp_frequency_value(long temp_frequency_value) {
		this.temp_frequency_value = temp_frequency_value;
	}

	public long getPower_loss_frequency_value() {
		return power_loss_frequency_value;
	}

	public void setPower_loss_frequency_value(long power_loss_frequency_value) {
		this.power_loss_frequency_value = power_loss_frequency_value;
	}

	public long getMotion_alert_frequency_value() {
		return motion_alert_frequency_value;
	}

	public void setMotion_alert_frequency_value(long motion_alert_frequency_value) {
		this.motion_alert_frequency_value = motion_alert_frequency_value;
	}

	public long getSound_alert_frequency_value() {
		return sound_alert_frequency_value;
	}

	public void setSound_alert_frequency_value(long sound_alert_frequency_value) {
		this.sound_alert_frequency_value = sound_alert_frequency_value;
	}

	public String getProduct_model_name() {
		return product_model_name;
	}

	public void setProduct_model_name(String product_model_name) {
		this.product_model_name = product_model_name;
	}

	public boolean isRunning_on_battery() {
		return running_on_battery;
	}

	public void setRunning_on_battery(boolean running_on_battery) {
		this.running_on_battery = running_on_battery;
	}

	public String getStart_video_recording_url() {
		return start_video_recording_url;
	}

	public void setStart_video_recording_url(String start_video_recording_url) {
		this.start_video_recording_url = start_video_recording_url;
	}

	public String getStart_video_recording_url_body() {
		return start_video_recording_url_body;
	}

	public void setStart_video_recording_url_body(String start_video_recording_url_body) {
		this.start_video_recording_url_body = start_video_recording_url_body;
	}

	public String getStop_video_recording_url() {
		return stop_video_recording_url;
	}

	public void setStop_video_recording_url(String stop_video_recording_url) {
		this.stop_video_recording_url = stop_video_recording_url;
	}

	public boolean isShow_sd() {
		return show_sd;
	}

	public void setShow_sd(boolean show_sd) {
		this.show_sd = show_sd;
	}

	public boolean isBattery_available() {
		return battery_available;
	}

	public void setBattery_available(boolean battery_available) {
		this.battery_available = battery_available;
	}

	public boolean isVehicle_detection() {
		return vehicle_detection;
	}

	public void setVehicle_detection(boolean vehicle_detection) {
		this.vehicle_detection = vehicle_detection;
	}

	public String getDevice_unique_id() {
		return device_unique_id;
	}

	public void setDevice_unique_id(String device_unique_id) {
		this.device_unique_id = device_unique_id;
	}

	public int getHangup_battery_percentage() {
		return hangup_battery_percentage;
	}

	public void setHangup_battery_percentage(int hangup_battery_percentage) {
		this.hangup_battery_percentage = hangup_battery_percentage;
	}

	public String getStream_url() {
		return stream_url;
	}

	public void setStream_url(String stream_url) {
		this.stream_url = stream_url;
	}

	public void setAlerts( JAlertsWC alertsWC ) {
		
		this.is_motion_detection = alertsWC.isMotionDetection();
		this.motion_detection_person = alertsWC.isMotionDetectionHuman();
		this.motion_detection_dog = alertsWC.isMotionDetectionDog();
		this.motion_detection_cat = alertsWC.isMotionDetectionCat();
		this.sound_alert = alertsWC.isSoundAlert();
		this.barking_alert = alertsWC.isSoundAlertDog();
		this.meowing_alert = alertsWC.isSoundAlertCat();
		this.battery_alert = alertsWC.isBatteryAlertStatus();
		this.on_battery_alert = alertsWC.isOnBatteryAlertStatus();
		this.humidity_alert = alertsWC.isHumidityAlert();
		this.dnr_alert = alertsWC.isDnrAlert();
		this.recovery_alert = alertsWC.isRecoveryAlertStatus();
		this.temp_alert = alertsWC.isTempAlertStatus();
		this.email_alert = alertsWC.isEmail_alert();
		this.sms_alert = alertsWC.isSms_alert();
		this.temp_frequency_value = alertsWC.getTemp_frequency_value();
		this.power_loss_frequency_value = alertsWC.getPower_loss_frequency_value();
		this.motion_alert_frequency_value = alertsWC.getMotion_alert_frequency_value();
		this.sound_alert_frequency_value = alertsWC.getSound_alert_frequency_value();
		
		if( alertsWC.getTemperatureInfo() != null ) {
			this.temperature_info = alertsWC.getTemperatureInfo();
		}
	}

	public int getTreat_count() {
		return treat_count;
	}

	public void setTreat_count(int treat_count) {
		this.treat_count = treat_count;
	}

	public boolean isAitreat_onoff() {
		return aitreat_onoff;
	}

	public void setAitreat_onoff(boolean aitreat_onoff) {
		this.aitreat_onoff = aitreat_onoff;
	}

	public int getAitreat_interval() {
		return aitreat_interval;
	}

	public void setAitreat_interval(int aitreat_interval) {
		this.aitreat_interval = aitreat_interval;
	}

	public int getAitreat_maxcount() {
		return aitreat_maxcount;
	}

	public void setAitreat_maxcount(int aitreat_maxcount) {
		this.aitreat_maxcount = aitreat_maxcount;
	}

	public boolean isAuto_tracking() {
		return auto_tracking;
	}

	public void setAuto_tracking(boolean auto_tracking) {
		this.auto_tracking = auto_tracking;
	}

	public boolean isShowwatermark() {
		return showwatermark;
	}

	public void setShowwatermark(boolean showwatermark) {
		this.showwatermark = showwatermark;
	}

	public boolean isShow_vehicle_detection() {
		return show_vehicle_detection;
	}

	public void setShow_vehicle_detection(boolean show_vehicle_detection) {
		this.show_vehicle_detection = show_vehicle_detection;
	}

	public boolean isShow_operation_mode() {
		return show_operation_mode;
	}

	public void setShow_operation_mode(boolean show_operation_mode) {
		this.show_operation_mode = show_operation_mode;
	}

	public boolean isShow_lighting_settings() {
		return show_lighting_settings;
	}

	public void setShow_lighting_settings(boolean show_lighting_settings) {
		this.show_lighting_settings = show_lighting_settings;
	}

	public int getOperation_mode() {
		return operation_mode;
	}

	public void setOperation_mode(int operation_mode) {
		this.operation_mode = operation_mode;
	}

	public int getLight_setting() {
		return light_setting;
	}

	public void setLight_setting(int light_setting) {
		this.light_setting = light_setting;
	}

	public String getVideoencoding_format() {
		return videoencoding_format;
	}

	public void setVideoencoding_format(String videoencoding_format) {
		this.videoencoding_format = videoencoding_format;
	}

	public int getFlicker_level() {
		return flicker_level;
	}

	public void setFlicker_level(int flicker_level) {
		this.flicker_level = flicker_level;
	}

	public boolean isIs_configured() {
		return is_configured;
	}

	public void setIs_configured(boolean is_configured) {
		this.is_configured = is_configured;
	}

	public boolean isSmart_light_detection() {
		return smart_light_detection;
	}

	public void setSmart_light_detection(boolean smart_light_detection) {
		this.smart_light_detection = smart_light_detection;
	}

	public boolean isIs_device_reset() {
		return is_device_reset;
	}

	public void setIs_device_reset(boolean is_device_reset) {
		this.is_device_reset = is_device_reset;
	}

    public boolean isShow_smart_light_setting() {
        return show_smart_light_setting;
    }

    public void setShow_smart_light_setting(boolean show_smart_light_setting) {
        this.show_smart_light_setting = show_smart_light_setting;
    }

	public boolean getIs_recordvideo_enabled() {
		return is_recordvideo_enabled;
	}

	public void setIs_recordvideo_enabled(boolean is_recordvideo_enabled) {
		this.is_recordvideo_enabled = is_recordvideo_enabled;
	}

	public boolean getIs_microphone_enabled() {
		return is_microphone_enabled;
	}

	public void setIs_microphone_enabled(boolean is_microphone_enabled) {
		this.is_microphone_enabled = is_microphone_enabled;
	}

	public boolean getIs_speaker_enabled() {
		return is_speaker_enabled;
	}

	public void setIs_speaker_enabled(boolean is_speaker_enabled) {
		this.is_speaker_enabled = is_speaker_enabled;
	}

	public boolean getHuman_detection_day() {
		return human_detection_day;
	}

	public void setHuman_detection_day(boolean human_detection_day) {
		this.human_detection_day = human_detection_day;
	}

	public boolean getHuman_detection_night() {
		return human_detection_night;
	}

	public void setHuman_detection_night(boolean human_detection_night) {
		this.human_detection_night = human_detection_night;
	}

	public boolean getAuto_sensitive_enabled() {
		return is_auto_sensitive_enabled;
	}

	public void setAuto_sensitive_enabled(boolean auto_sensitive_enabled) {
		this.is_auto_sensitive_enabled = auto_sensitive_enabled;
	}

	public boolean isMeari_update_popup() {
		return meari_update_popup;
	}

	public void setMeari_update_popup(boolean meari_update_popup) {
		this.meari_update_popup = meari_update_popup;
	}
}
