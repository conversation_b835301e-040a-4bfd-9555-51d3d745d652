package com.nimble.irisservices.dto;

public class JGatewayInfo {


	private long gateway_id = 0;
	
	private String name = "NA";
	
	private String qr_code = "NA";
	
	private String image_url = "NA";

	private long monitorTypeId=0;

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getQr_code() {
		return qr_code;
	}

	public void setQr_code(String qr_code) {
		this.qr_code = qr_code;
	}

	public String getImage_url() {
		return image_url;
	}

	public void setImage_url(String image_url) {
		if( image_url == null ) image_url = "NA";
		this.image_url = image_url;
	}

	public JGatewayInfo() {
		super();
	}
	
	public JGatewayInfo(long gateway_id, String name, String qr_code, String image_url,long monitorTypeId) {
		super();
		this.gateway_id = gateway_id;
		this.name = name;
		this.qr_code = qr_code;
		this.image_url = image_url;
		this.monitorTypeId=monitorTypeId;
	}

	public Long getMonitorTypeId() {
		return monitorTypeId;
	}

	public void setMonitorTypeId(Long monitorTypeId) {
		this.monitorTypeId = monitorTypeId;
	}
	
}
