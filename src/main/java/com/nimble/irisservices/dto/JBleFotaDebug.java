package com.nimble.irisservices.dto;

public class JBleFotaDebug {

    private long userId = 0;

    private String userName = "NA";

    private long gatewayId = 0;

    private String bleId = "NA";

    private String debugFileUrl = "NA";

    private String updatedDate = "NA";

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public long getGatewayId() {
        return gatewayId;
    }

    public void setGatewayId(long gatewayId) {
        this.gatewayId = gatewayId;
    }

    public String getBleId() {
        return bleId;
    }

    public void setBleId(String bleId) {
        this.bleId = bleId;
    }

    public String getDebugFileUrl() {
        return debugFileUrl;
    }

    public void setDebugFileUrl(String debugFileUrl) {
        this.debugFileUrl = debugFileUrl;
    }

    public String getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(String updatedDate) {
        this.updatedDate = updatedDate;
    }
}
