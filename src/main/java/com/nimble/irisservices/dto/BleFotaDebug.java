package com.nimble.irisservices.dto;

import java.util.List;
import java.util.Map;

public class BleFotaDebug {

    private long gatewayId = 0;

    private String bleId = "NA";

    private List<Map<String, String>> debugList;

    public long getGatewayId() {
        return gatewayId;
    }

    public void setGatewayId(long gatewayId) {
        this.gatewayId = gatewayId;
    }

    public String getBleId() {
        return bleId;
    }

    public void setBleId(String bleId) {
        this.bleId = bleId;
    }

    public List<Map<String, String>> getDebugList() {
        return debugList;
    }

    public void setDebugList(List<Map<String, String>> debugList) {
        this.debugList = debugList;
    }
}
