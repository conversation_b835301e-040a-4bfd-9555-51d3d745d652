package com.nimble.irisservices.util;

import com.jayway.jsonpath.JsonPath;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors;
import org.springframework.stereotype.Component;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@Component
public class TestTokenUtil {

    private static final Logger log = LoggerFactory.getLogger(TestTokenUtil.class);

    private String bearerToken;

    private long tokenGenerationTime;

    @Value("${config.oauth2.accesstoken.validation}")
    private int tokenValidityPeriod;

    @Value("${config.oauth2.clientid.app}")
    private String clientId;

    @Value("${config.oauth2.clientsecret.app}")
    private String clientSecret;

    public String getValidToken(MockMvc mockMvc) throws Exception {

        long currentTime = System.currentTimeMillis();

        if (bearerToken == null || isTokenExpired(currentTime)) {
            generateNewToken(mockMvc);
        }

        return bearerToken;
    }

    private boolean isTokenExpired(long currentTime) {

        // Convert tokenValidityPeriod from seconds to milliseconds
        long validityPeriodMs = (tokenValidityPeriod > 0 ? tokenValidityPeriod : 300) * 1000L;
        boolean isExpired = (currentTime - tokenGenerationTime) > validityPeriodMs;

        if (isExpired) {
            log.debug("Token expired. Current time: {}, Token generation time: {}, Validity period: {} seconds",
                    currentTime, tokenGenerationTime, tokenValidityPeriod);
        }

        return isExpired;
    }

    private synchronized void generateNewToken(MockMvc mockMvc) throws Exception {
        if (bearerToken == null || isTokenExpired(System.currentTimeMillis())) {
            try {
                String bearerTokenJson = mockMvc.perform(post("/web/oauth/token")
                                .param("grant_type", "password")
                                .param("username", "nimble")
                                .param("password", "nimble")
                                .with(SecurityMockMvcRequestPostProcessors.httpBasic(
                                        clientId != null ? clientId : "app",
                                        clientSecret != null ? clientSecret : "WagglePetAPP")))
                        .andReturn()
                        .getResponse()
                        .getContentAsString();

                bearerToken = JsonPath.read(bearerTokenJson, "$.access_token");
                tokenGenerationTime = System.currentTimeMillis();

                log.debug("Generated new token at: {}", tokenGenerationTime);
            } catch (Exception e) {
                log.error("Failed to generate new token: {}", e.getMessage());
                throw e;
            }
        }
    }

}