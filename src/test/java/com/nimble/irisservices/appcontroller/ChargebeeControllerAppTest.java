package com.nimble.irisservices.appcontroller;

import com.fasterxml.jackson.databind.JsonNode;
import com.nimble.irisservices.BaseControllerTest;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AllProductSubscription;
import com.nimble.irisservices.entity.FlexiPlanHistory;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IUserServiceV4;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.core.Authentication;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.ResultActions;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class ChargebeeControllerAppTest extends BaseControllerTest {

    @MockBean
    IUserServiceV4 mockUserServiceV4;

    @MockBean
    ICreditSystemService mockCreditSystemService;

    @MockBean
    IChargebeeService mockChargebeeService;

    @MockBean
    Helper mockHelper;

    private static final String USERNAME = "NIMBLE";

    private static final String AUTH_TOKEN = "valid_auth_token";

    private static final String INVALID_AUTH_TOKEN = "invalid_auth_token";

    @Test
    void testGetUpgradeSubPlansV6_Success() throws Exception {

        UserV4 mockUser = getMockUser();
        JResponse mockServiceResponse = getMockServiceResponse();

        when(mockUserServiceV4.verifyAuthV3("authkey", AUTH_TOKEN)).thenReturn(mockUser);
        when(mockHelper.validateUser(mockUser.getUsername(), USERNAME)).thenReturn(null);
        when(mockCreditSystemService.getUpgradeSubPlanV10(
                eq(mockUser), eq(1L), eq(1L), eq(1L), eq("US"), eq(false), 
                eq(0L), eq("upgrade"), eq("Data-Plan,Flexi-Plan"), eq("NA"), 
                eq(false), eq(false))).thenReturn(mockServiceResponse);

        ResultActions resultActions = mockMvc.perform(get("/app/v6.0/getupgradesubplans")
                        .param("planid", "1")
                        .param("period", "1")
                        .param("os", "android")
                        .param("app_ver", "6.0.0")
                        .param("monitortype_id", "1")
                        .header("Accept", "application/json")
                        .header("auth", AUTH_TOKEN)
                        .header("Authorization", "Bearer " + getBearerToken()))
                .andExpect(status().isOk());

        String responseBody = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(responseBody).path("response");

        assertEquals(1, jsonNode.get("Status").asInt());
        assertEquals("Success", jsonNode.get("Msg").asText());
        assertEquals(1, jsonNode.get("monitortype_id").asInt());
        assertNotNull(jsonNode.get("Return Time"));

        verify(mockUserServiceV4).verifyAuthV3("authkey", AUTH_TOKEN);
        verify(mockCreditSystemService).getUpgradeSubPlanV10(
                eq(mockUser), eq(1L), eq(1L), eq(1L), eq("US"), eq(false), 
                eq(0L), eq("upgrade"), eq("Data-Plan,Flexi-Plan"), eq("NA"), 
                eq(false), eq(false));
    }

    @Test
    void testGetUpgradeSubPlansV6_InvalidAuth() throws Exception {

        when(mockUserServiceV4.verifyAuthV3("authkey", INVALID_AUTH_TOKEN))
                .thenThrow(new InvalidAuthoException("Invalid authentication key"));

        ResultActions resultActions = mockMvc.perform(get("/app/v6.0/getupgradesubplans")
                        .param("planid", "1")
                        .param("period", "1")
                        .param("os", "android")
                        .param("app_ver", "6.0.0")
                        .param("monitortype_id", "1")
                        .header("Accept", "application/json")
                        .header("auth", INVALID_AUTH_TOKEN)
                        .header("Authorization", "Bearer " + getBearerToken()))
                .andExpect(status().isUnauthorized());

        String responseBody = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(responseBody).path("response");

        assertEquals(0, jsonNode.get("Status").asInt());
        assertEquals("Invalid authentication key", jsonNode.get("Msg").asText());

        verify(mockUserServiceV4).verifyAuthV3("authkey", INVALID_AUTH_TOKEN);
        verify(mockCreditSystemService, never()).getUpgradeSubPlanV10(
                any(), anyLong(), anyLong(), anyLong(), anyString(), anyBoolean(),
                anyLong(), anyString(), anyString(), anyString(), anyBoolean(), anyBoolean());
    }

    @Test
    void testGetUpgradeSubPlansV6_DefaultFreePlan() throws Exception {

        UserV4 mockUser = getMockUser();
        JResponse mockServiceResponse = getMockServiceResponse();

        when(mockUserServiceV4.verifyAuthV3("authkey", AUTH_TOKEN)).thenReturn(mockUser);
        when(mockHelper.validateUser(mockUser.getUsername(), USERNAME)).thenReturn(null);
        when(mockCreditSystemService.getUpgradeSubPlanV10(
                eq(mockUser), eq(1L), eq(1L), eq(1L), eq("US"), eq(true), 
                eq(0L), eq("upgrade"), eq("Data-Plan,Flexi-Plan"), eq("NA"), 
                eq(false), eq(false))).thenReturn(mockServiceResponse);

        ResultActions resultActions = mockMvc.perform(get("/app/v6.0/getupgradesubplans")
                        .param("planid", "0")
                        .param("period", "2")
                        .param("os", "android")
                        .param("app_ver", "6.0.0")
                        .param("monitortype_id", "1")
                        .header("Accept", "application/json")
                        .header("auth", AUTH_TOKEN)
                        .header("Authorization", "Bearer " + getBearerToken()))
                .andExpect(status().isOk());

        String responseBody = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(responseBody).path("response");

        assertEquals(1, jsonNode.get("Status").asInt());
        assertEquals("Success", jsonNode.get("Msg").asText());

        verify(mockCreditSystemService).getUpgradeSubPlanV10(
                eq(mockUser), eq(1L), eq(1L), eq(1L), eq("US"), eq(true), 
                eq(0L), eq("upgrade"), eq("Data-Plan,Flexi-Plan"), eq("NA"), 
                eq(false), eq(false));
    }

    @Test
    void testGetUpgradeSubPlansV6_ServiceException() throws Exception {

        UserV4 mockUser = getMockUser();

        when(mockUserServiceV4.verifyAuthV3("authkey", AUTH_TOKEN)).thenReturn(mockUser);
        when(mockHelper.validateUser(mockUser.getUsername(), USERNAME)).thenReturn(null);
        when(mockCreditSystemService.getUpgradeSubPlanV10(
                any(), anyLong(), anyLong(), anyLong(), anyString(), anyBoolean(), 
                anyLong(), anyString(), anyString(), anyString(), anyBoolean(), anyBoolean()))
                .thenThrow(new RuntimeException("Database connection error"));

        ResultActions resultActions = mockMvc.perform(get("/app/v6.0/getupgradesubplans")
                        .param("planid", "1")
                        .param("period", "1")
                        .param("os", "android")
                        .param("app_ver", "6.0.0")
                        .param("monitortype_id", "1")
                        .header("Accept", "application/json")
                        .header("auth", AUTH_TOKEN)
                        .header("Authorization", "Bearer " + getBearerToken()))
                .andExpect(status().isOk());

        String responseBody = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(responseBody).path("response");

        assertEquals(0, jsonNode.get("Status").asInt());
        assertEquals("Please try again later.", jsonNode.get("Msg").asText());
        assertEquals("Database connection error", jsonNode.get("Error").asText());

        verify(mockUserServiceV4).verifyAuthV3("authkey", AUTH_TOKEN);
        verify(mockCreditSystemService).getUpgradeSubPlanV10(
                eq(mockUser), eq(1L), eq(1L), eq(1L), eq("US"), eq(false), 
                eq(0L), eq("upgrade"), eq("Data-Plan,Flexi-Plan"), eq("NA"), 
                eq(false), eq(false));
    }

    @Test
    void testGetUpgradeSubPlansV6_FlexiPlanWithRestriction() throws Exception {

        UserV4 mockUser = getMockUser();
        JResponse mockServiceResponse = getMockServiceResponse();
        AllProductSubscription mockSubscription = getMockProductSubscription();
        FlexiPlanHistory mockFlexiHistory = getMockFlexiPlanHistory();

        when(mockUserServiceV4.verifyAuthV3("authkey", AUTH_TOKEN)).thenReturn(mockUser);
        when(mockHelper.validateUser(mockUser.getUsername(), USERNAME)).thenReturn(null);
        when(mockChargebeeService.getProductSubscriptionByGatewayId(123L, "test_chargebee_id", 1L))
                .thenReturn(mockSubscription);
        when(mockCreditSystemService.getFlexiplandetailsbySubid("test_sub_id"))
                .thenReturn(mockFlexiHistory);
        when(mockHelper.getHoursBetweenDate(anyString(), anyString())).thenReturn(24L);
        when(mockCreditSystemService.getUpgradeSubPlanV10(
                eq(mockUser), eq(1L), eq(1L), eq(1L), eq("US"), eq(false),
                eq(123L), eq("upgrade"), eq("Data-Plan,Flexi-Plan"), eq("test_sub_id"),
                eq(true), eq(true))).thenReturn(mockServiceResponse);

        ResultActions resultActions = mockMvc.perform(get("/app/v6.0/getupgradesubplans")
                        .param("planid", "1")
                        .param("period", "1")
                        .param("os", "android")
                        .param("app_ver", "6.0.0")
                        .param("monitortype_id", "1")
                        .param("gatewayid", "123")
                        .param("sub_id", "test_sub_id")
                        .param("is_flexi", "true")
                        .header("Accept", "application/json")
                        .header("auth", AUTH_TOKEN)
                        .header("Authorization", "Bearer " + getBearerToken()))
                .andExpect(status().isOk());

        String responseBody = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(responseBody).path("response");

        assertEquals(1, jsonNode.get("Status").asInt());
        assertEquals("Success", jsonNode.get("Msg").asText());

        verify(mockChargebeeService).getProductSubscriptionByGatewayId(123L, "test_chargebee_id", 1L);
        verify(mockCreditSystemService).getFlexiplandetailsbySubid("test_sub_id");
        verify(mockCreditSystemService).getUpgradeSubPlanV10(
                eq(mockUser), eq(1L), eq(1L), eq(1L), eq("US"), eq(false),
                eq(123L), eq("upgrade"), eq("Data-Plan,Flexi-Plan"), eq("test_sub_id"),
                eq(true), eq(true));
    }

    @Test
    void testGetUpgradeSubPlansV6_FlexiPlanDowngrade() throws Exception {

        UserV4 mockUser = getMockUser();
        JResponse mockServiceResponse = getMockServiceResponse();
        AllProductSubscription mockSubscription = getMockProductSubscription();

        when(mockUserServiceV4.verifyAuthV3("authkey", AUTH_TOKEN)).thenReturn(mockUser);
        when(mockHelper.validateUser(mockUser.getUsername(), USERNAME)).thenReturn(null);
        when(mockChargebeeService.getProductSubscriptionByGatewayId(123L, "test_chargebee_id", 1L))
                .thenReturn(mockSubscription);
        when(mockCreditSystemService.getUpgradeSubPlanV10(
                eq(mockUser), eq(1L), eq(1L), eq(1L), eq("US"), eq(false),
                eq(123L), eq("cancelall"), eq("Data-Plan,Flexi-Plan"), eq("test_sub_id"),
                eq(false), eq(true))).thenReturn(mockServiceResponse);

        ResultActions resultActions = mockMvc.perform(get("/app/v6.0/getupgradesubplans")
                        .param("planid", "1")
                        .param("period", "1")
                        .param("os", "android")
                        .param("app_ver", "6.0.0")
                        .param("monitortype_id", "1")
                        .param("gatewayid", "123")
                        .param("type", "downgrade")
                        .param("sub_id", "test_sub_id")
                        .param("is_flexi", "true")
                        .header("Accept", "application/json")
                        .header("auth", AUTH_TOKEN)
                        .header("Authorization", "Bearer " + getBearerToken()))
                .andExpect(status().isOk());

        String responseBody = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(responseBody).path("response");

        assertEquals(1, jsonNode.get("Status").asInt());
        assertEquals("Success", jsonNode.get("Msg").asText());

        verify(mockCreditSystemService).getUpgradeSubPlanV10(
                eq(mockUser), eq(1L), eq(1L), eq(1L), eq("US"), eq(false),
                eq(123L), eq("cancelall"), eq("Data-Plan,Flexi-Plan"), eq("test_sub_id"),
                eq(false), eq(true));
    }

    @Test
    void testGetUpgradeSubPlansV6_OptionalParameters() throws Exception {

        UserV4 mockUser = getMockUser();
        JResponse mockServiceResponse = getMockServiceResponse();

        when(mockUserServiceV4.verifyAuthV3("authkey", AUTH_TOKEN)).thenReturn(mockUser);
        when(mockHelper.validateUser(mockUser.getUsername(), USERNAME)).thenReturn(null);
        when(mockCreditSystemService.getUpgradeSubPlanV10(
                eq(mockUser), eq(2L), eq(3L), eq(2L), eq("US"), eq(false),
                eq(456L), eq("cancel"), eq("Vet-Plan"), eq("custom_sub_id"),
                eq(false), eq(false))).thenReturn(mockServiceResponse);

        ResultActions resultActions = mockMvc.perform(get("/app/v6.0/getupgradesubplans")
                        .param("planid", "2")
                        .param("period", "3")
                        .param("os", "ios")
                        .param("app_ver", "6.1.0")
                        .param("plan_ver", "V4")
                        .param("monitortype_id", "2")
                        .param("gatewayid", "456")
                        .param("type", "cancel")
                        .param("plantype", "Vet-Plan")
                        .param("sub_id", "custom_sub_id")
                        .param("is_flexi", "false")
                        .header("Accept", "application/json")
                        .header("auth", AUTH_TOKEN)
                        .header("Authorization", "Bearer " + getBearerToken()))
                .andExpect(status().isOk());

        String responseBody = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(responseBody).path("response");

        assertEquals(1, jsonNode.get("Status").asInt());
        assertEquals("Success", jsonNode.get("Msg").asText());
        assertEquals(2, jsonNode.get("monitortype_id").asInt());

        verify(mockCreditSystemService).getUpgradeSubPlanV10(
                eq(mockUser), eq(2L), eq(3L), eq(2L), eq("US"), eq(false),
                eq(456L), eq("cancel"), eq("Vet-Plan"), eq("custom_sub_id"),
                eq(false), eq(false));
    }

    private JResponse getMockServiceResponse() {

        JResponse response = new JResponse();

        response.put("Status", 1);
        response.put("Msg", "Success");
        response.put("plans", "mock_plans_data");

        return response;
    }

    private AllProductSubscription getMockProductSubscription() {

        AllProductSubscription subscription = new AllProductSubscription();

        subscription.setPlanId("flexi-plan-monthly");
        subscription.setSubscriptionStatus("active");

        return subscription;
    }

    private FlexiPlanHistory getMockFlexiPlanHistory() {

        FlexiPlanHistory history = new FlexiPlanHistory();

        history.setCurrent_cycle(2);
        history.setCurrent_cycle_end_at("2024-01-15 12:00:00"); // Future date

        return history;
    }

    private UserV4 getMockUser() {

        UserV4 mockUser = new UserV4();

        mockUser.setUsername(USERNAME);
        mockUser.setId(1L);
        mockUser.setEmail("<EMAIL>");
        mockUser.setCountry("US");
        mockUser.setChargebeeid("test_chargebee_id");

        return mockUser;
    }
}
